import { Route } from '@angular/router';
import { AiAssistantPageComponent } from '../index';
import { AiAssistantFormComponent } from '../components/ai-assistant-configuration/ai-assistant-form/ai-assistant-form.component';
import { UnsavedChangesGuard } from '../core/services/unsaved-changes.guard';
import { AiGoalFormComponent } from '../components/ai-goal-form/ai-goal-form.component';
import { AiFunctionFormComponent } from '../components/ai-function-form/ai-function-form.component';
import { AiAssistantFormV2Component } from '../components/ai-assistant-configuration/ai-assistant-form-v2/ai-assistant-form-v2.component';
import { UnsavedChangesGuardV2 } from '../core/services/unsaved-changes-v2.guard';
import { configPageVersionGuard } from '../core/guards/config-page-version.guard';

export const AI_ASSISTANT_ROUTES: Route[] = [
  {
    path: 'assistants',
    children: [
      {
        path: '',
        component: AiAssistantPageComponent,
      },
      {
        path: ':assistantId/edit',
        component: AiAssistantFormComponent,
        canDeactivate: [UnsavedChangesGuard],
        canActivate: [configPageVersionGuard],
      },
      {
        path: ':assistantId/edit-v2',
        component: AiAssistantFormV2Component,
        canDeactivate: [UnsavedChangesGuardV2],
      },
    ],
    providers: [UnsavedChangesGuard],
  },
  {
    path: 'goals',
    children: [
      {
        path: ':goalId/edit',
        component: AiGoalFormComponent,
        canDeactivate: [UnsavedChangesGuard],
      },
      {
        path: 'create',
        component: AiGoalFormComponent,
        canDeactivate: [UnsavedChangesGuard],
      },
    ],
    providers: [UnsavedChangesGuard],
  },
  {
    path: 'functions',
    children: [
      {
        path: 'create',
        component: AiFunctionFormComponent,
      },
      {
        path: ':functionId/edit',
        component: AiFunctionFormComponent,
      },
    ],
  },
];
