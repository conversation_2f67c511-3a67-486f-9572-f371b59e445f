import { inject, Injectable } from '@angular/core';
import { CanDeactivate } from '@angular/router';
import { map, Observable, of } from 'rxjs';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';

export interface CanComponentDeactivate {
  canDeactivate: () => boolean;
}

@Injectable({
  providedIn: 'root',
})
export class UnsavedChangesGuardV2 implements CanDeactivate<CanComponentDeactivate> {
  private readonly confirmationModal = inject(OpenConfirmationModalService);

  canDeactivate(component: CanComponentDeactivate): Observable<boolean> {
    const canDeactivateResult = component.canDeactivate ? component.canDeactivate() : true;

    if (!canDeactivateResult) {
      return this.openUnsavedChangesModal();
    }
    return of(true);
  }

  private openUnsavedChangesModal(): Observable<boolean> {
    return this.confirmationModal
      .openModal({
        type: 'warn',
        title: 'AI_ASSISTANT.SHARED.UNSAVED_CHANGES.TITLE',
        message: 'AI_ASSISTANT.SHARED.UNSAVED_CHANGES.MESSAGE',
        confirmButtonText: 'AI_ASSISTANT.SHARED.UNSAVED_CHANGES.CONFIRM',
      })
      .pipe(map((confirmation) => !!confirmation));
  }
}
