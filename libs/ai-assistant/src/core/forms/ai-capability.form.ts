import { FormControl, FormGroup, Validators } from '@angular/forms';
import {
  ConfigurableGoalInterface,
  GoalInterface,
  GoalKeyInterface,
  GoalType,
  KeyValuePair,
  NamespaceInterface,
} from '@vendasta/ai-assistants';
import { AiPromptModuleFormArray } from './ai-prompt-module.form';
import { AiToolFormArray } from './ai-tools.form';
import { FormArrayWithDirtyTracking } from './forms';
import { AiToolFormRegistryService } from '../services/ai-tool-form-registry.service';
import { DEFAULT_LEAD_CAPTURE_GOAL_ID, HIDDEN_GOAL_IDS } from '../ai-assistant.constants';
import { Directive, input } from '@angular/core';

export type CapabilityConfigurationFormControl = FormControl<KeyValuePair[] | null>;

export function getKeyValuePairFromCapabilityFormControl(
  formControl: CapabilityConfigurationFormControl,
  key: string,
): KeyValuePair {
  const formValue = formControl.value || [];
  return formValue?.find((pair) => pair.key === key) || new KeyValuePair({ key: key, value: '' });
}

export function setKeyValuePairFromCapabilityFormControl(
  formControl: CapabilityConfigurationFormControl,
  newPair: KeyValuePair,
): void {
  const formValue = formControl.value || [];
  let pairFound = false;
  formValue.map((pair) => {
    if (pair.key === newPair.key) {
      pair.value = newPair.value;
      pairFound = true;
    }
  });
  if (!pairFound) {
    formValue.push(
      new KeyValuePair({
        key: newPair.key,
        value: newPair.value,
      }),
    );
  }
  formControl.setValue(formValue);
}

// Capabilities
export type AiCapabilityFormControls = {
  goalId: FormControl<string | null>;
  // TODO: Can this be non-nullable?
  namespace: FormControl<NamespaceInterface | null>;
  managed: FormControl<boolean>;
  name: FormControl<string | null>;
  description: FormControl<string | null>;
  type: FormControl<GoalType | null>;
  promptModules: AiPromptModuleFormArray;
  tools: AiToolFormArray;
  configuration: CapabilityConfigurationFormControl;
  overrideOf: FormControl<GoalKeyInterface | null>;
};

export class AiCapabilityForm extends FormGroup<AiCapabilityFormControls> {
  constructor(
    goal?: ConfigurableGoalInterface,
    promptModuleContents?: Record<string, string>,
    toolFormRegistry?: AiToolFormRegistryService,
  ) {
    const capability = goal?.goal ?? null;
    const keyValuePairs = goal?.configuration
      ?.map((v): KeyValuePair => {
        if (v instanceof KeyValuePair) {
          return v;
        }
        return new KeyValuePair(v);
      })
      ?.filter((v) => Boolean(v.key && v.value));

    super({
      goalId: new FormControl({ value: capability?.id ?? null, disabled: true }),
      namespace: new FormControl({ value: capability?.namespace ?? null, disabled: true }),
      managed: new FormControl({ value: capability?.managed ?? false, disabled: true }, { nonNullable: true }),
      name: new FormControl(capability?.name ?? null, Validators.required),
      description: new FormControl(capability?.description ?? null),
      type: new FormControl({ value: capability?.type ?? GoalType.GOAL_TYPE_CUSTOM, disabled: true }),
      promptModules: new AiPromptModuleFormArray({
        promptModules: capability?.promptModules || [],
        contents: promptModuleContents || {},
      }),
      tools: new AiToolFormArray(capability?.functions || [], toolFormRegistry),
      configuration: new FormControl<KeyValuePair[] | null>(keyValuePairs ?? null),
      overrideOf: new FormControl<GoalKeyInterface | null>(capability?.overrideOf ?? null),
    });

    this.controls.configuration.enable();
    if (capability?.id === DEFAULT_LEAD_CAPTURE_GOAL_ID) {
      Object.entries(this.controls).forEach(([key, control]) => {
        if (key !== 'configuration') {
          control.disable();
        }
      });
    }
  }

  // TODO: We need to ensure this is called after...
  //  toGoal is called + the new goal is created + the goalID is set on the form
  // in the modified global/managed case
  toConfigurableGoal(fallbackNamespace: NamespaceInterface | undefined): ConfigurableGoalInterface {
    return {
      goal: {
        id: this.controls.goalId.getRawValue() ?? undefined,
        namespace: this.controls.namespace.getRawValue() ?? fallbackNamespace,
      },
      configuration: this.controls.configuration.getRawValue() ?? undefined,
    };
  }

  // toGoal creates a goal that can be used in an create/update/upsert goal API call
  // - If the form contains a global goal, Then a new locally namespaced goal
  //   will be returned with a reference to the goal it is based on.
  // - If the form contains a managed goal, Then a new locally namespaced goal
  //   that is not managed will be returned with a reference to the goal it is based on.
  // In both of the cases above, the form values will be modified
  toGoal(localNamespace: NamespaceInterface): GoalInterface {
    if (this.needsGoalForking()) {
      const originalGoalId = this.controls.goalId.getRawValue();
      const originalGoalNamespace = this.controls.namespace.getRawValue();
      this.controls.overrideOf.setValue({
        id: originalGoalId ?? undefined,
        namespace: originalGoalNamespace ?? undefined,
      });
      this.controls.goalId.setValue(null);
      this.controls.namespace.setValue(localNamespace);
      this.controls.managed.setValue(false);
    }

    return {
      id: this.controls.goalId.getRawValue() ?? undefined,
      name: this.controls.name.getRawValue() ?? undefined,
      namespace: this.controls.namespace.getRawValue() ?? localNamespace,
      description: this.controls.description.getRawValue() ?? undefined,
      type: this.controls.type.getRawValue() ?? undefined,
      functions: this.controls.tools.toFunctionKeys(localNamespace),
      promptModules: this.controls.promptModules.getPromptModuleKeys(localNamespace),
      overrideOf: this.controls.overrideOf.getRawValue() ?? undefined,
    };
  }

  isHidden(): boolean {
    return (
      HIDDEN_GOAL_IDS.includes(this.controls.goalId.getRawValue() ?? '') ||
      this.controls.type.getRawValue() === GoalType.GOAL_TYPE_PERSONALITY
    );
  }

  isOverride(): boolean {
    return Boolean(this.controls.overrideOf.getRawValue());
  }

  isGlobalOrManaged(): boolean {
    return Boolean(this.controls.namespace.getRawValue()?.globalNamespace) || this.controls.managed.getRawValue();
  }

  isLeadCaptureCapability(): boolean {
    return this.controls.goalId.getRawValue() === DEFAULT_LEAD_CAPTURE_GOAL_ID;
  }

  hasOnlyConfigurationChanges(): boolean {
    // If configuration is dirty but the goal definition fields are not dirty
    const configurationDirty = this.controls.configuration.dirty;
    const goalDefinitionDirty = Object.entries(this.controls)
      .filter(([key]) => key !== 'configuration')
      .some(([, control]) => control.dirty);

    return configurationDirty && !goalDefinitionDirty;
  }

  needsGoalForking(): boolean {
    // Only fork the goal if it's global/managed AND has goal definition changes
    // If only configuration changes, we don't need to fork
    return this.isGlobalOrManaged() && !this.hasOnlyConfigurationChanges();
  }
}

export class AiCapabilityFormArray extends FormArrayWithDirtyTracking<AiCapabilityForm> {
  constructor(
    capabilities: ConfigurableGoalInterface[],
    promptModuleContents: Record<string, string>,
    toolFormRegistry?: AiToolFormRegistryService,
  ) {
    super(capabilities.map((capability) => new AiCapabilityForm(capability, promptModuleContents, toolFormRegistry)));
  }

  toConfigurableGoals(localNamespace: NamespaceInterface): ConfigurableGoalInterface[] {
    return this.controls.map((capabilityForm) => capabilityForm.toConfigurableGoal(localNamespace));
  }
}

@Directive()
export abstract class CustomCapabilityConfigurationForm {
  configurationForm = input.required<CapabilityConfigurationFormControl>();
}
