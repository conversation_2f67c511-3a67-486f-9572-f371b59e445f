import { Component, computed, DestroyRef, effect, HostListener, inject, Injector, signal } from '@angular/core';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { AssistantType, PromptModuleKey } from '@vendasta/ai-assistants';
import { ACCOUNT_GROUP_ID_TOKEN, PARTNER_ID_TOKEN } from '../../../core/tokens';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ActivatedRoute, Router } from '@angular/router';
import { catchError, firstValueFrom, map, of, switchMap, take } from 'rxjs';
import { CommonModule } from '@angular/common';
import { MatInputModule } from '@angular/material/input';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { AiAssistantI18nModule } from '../../../assets/i18n/ai-assistant-i18n.module';
import { ImageUploadComponent } from '../image-upload/image-upload.component';
import { StickyFooterComponent } from '../../sticky-footer/sticky-footer.component';
import { GalaxyAvatarModule } from '@vendasta/galaxy/avatar';
import {
  DEFAULT_ASSISTANT_NAMES,
  DEFAULT_AVATAR_SVG_ICON,
  LANGCHAIN_FEATURE_FLAG,
} from '../../../core/ai-assistant.constants';
import { PageService } from '@vendasta/galaxy/page/src/page.service';
import { toSignal } from '@angular/core/rxjs-interop';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { MatIconModule } from '@angular/material/icon';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GalaxyAiIconService } from '@vendasta/galaxy/ai-icon';
import { AiAssistantService } from '../../../core/services/ai-assistant.service';
import { CanComponentDeactivate } from '../../../core/services/unsaved-changes-v2.guard';
import { AiAssistantFormV2Service } from './ai-assistant-form-v2.service';
import { Clipboard } from '@angular/cdk/clipboard';
import { AiToolCurlService } from '../../../core/services/ai-tool-curl.service';

import { AiCapabilitiesListComponent } from '../../capabilities/ai-capabilities-list/ai-capabilities-list.component';
import { getNamespace } from '../../../core/services/ai-assistant-utils';
import { AiCapabilityForm, AiToolForm, getDirtyStateMap, setupFormDirtyTracking } from '../../../core/forms';
import { AiToolFormComponent } from '../../tools/ai-tool-form/ai-tool-form.component';
import { AiConnectionsFormComponent } from '../../ai-connections-form/ai-connections-form.component';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { AiAssistantVoiceUsageComponent } from '../../ai-assistant-voice-usage/ai-assistant-voice-usage.component';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { UnsavedChangesGuard } from '../../../core/services/unsaved-changes.guard';
import { v4 as uuidv4 } from 'uuid';
import { AiToolFormRegistryService } from '../../../core/services/ai-tool-form-registry.service';
import { AiProfileAccordionComponent } from '../../capabilities/ai-profile-accordion/ai-profile-accordion.component';
import { StandardIds } from '@galaxy/crm/static';
import { GalaxyFilterOperator } from '@vendasta/galaxy/filter/chips';
import { encodeFilters } from '@vendasta/galaxy/filter/chips/src/utils';
import { ApplicationKnowledgeComponent } from '@galaxy/ai-knowledge';
import { KnowledgeSource } from '@vendasta/embeddings';
import { FeatureFlagService } from '@galaxy/partner';
import { AiCapabilityFormComponent } from '../../capabilities/ai-capability-form/ai-capability-form.component';
import { GalaxyPopoverModule, PopoverPositions } from '@vendasta/galaxy/popover';
import { AUTONOMY_LEVELS } from '../../../pipes/src/autonomy-level.pipe';
import { AiAssistantPipesModule } from '../../../pipes/src/pipes.module';
import { CdkOverlayOrigin } from '@angular/cdk/overlay';

const FALLBACK_BACK_URL = '../..';

const BETA_ASSISTANT_TYPES = [AssistantType.ASSISTANT_TYPE_VOICE_RECEPTIONIST, AssistantType.ASSISTANT_TYPE_CUSTOM];

type ActiveSubForms =
  | {
      toolForm?: undefined;
      capabilityForm?: undefined;
    }
  | {
      toolForm?: undefined;
      capabilityForm: AiCapabilityForm;
    }
  | {
      toolForm: { parentCapabilityForm?: AiCapabilityForm; form: AiToolForm };
      // We should be asserting this is defined but to simplify the implementation for now I'm leaving that out
      capabilityForm?: AiCapabilityForm;
    };

@Component({
  selector: 'ai-ai-assistant-form-v2',
  imports: [
    CommonModule,
    MatInputModule,
    MatCardModule,
    ReactiveFormsModule,
    FormsModule,
    MatButtonModule,
    MatChipsModule,
    GalaxyFormFieldModule,
    GalaxyLoadingSpinnerModule,
    AiAssistantI18nModule,
    ImageUploadComponent,
    StickyFooterComponent,
    GalaxyPageModule,
    GalaxyAvatarModule,
    GalaxyTooltipModule,
    MatIconModule,
    GalaxyBadgeModule,
    AiConnectionsFormComponent,
    AiCapabilitiesListComponent,
    GalaxyAlertModule,
    AiAssistantVoiceUsageComponent,
    AiToolFormComponent,
    AiProfileAccordionComponent,
    ApplicationKnowledgeComponent,
    AiCapabilityFormComponent,
    GalaxyPopoverModule,

    AiAssistantPipesModule,
    CdkOverlayOrigin,
  ],
  providers: [AiAssistantFormV2Service, AiToolFormRegistryService],
  templateUrl: './ai-assistant-form-v2.component.html',
  styleUrl: './ai-assistant-form-v2.component.scss',
})
export class AiAssistantFormV2Component implements CanComponentDeactivate {
  private readonly accountGroupId$ = inject(ACCOUNT_GROUP_ID_TOKEN);
  private readonly accountGroupId = toSignal(this.accountGroupId$);
  private readonly partnerId$ = inject(PARTNER_ID_TOKEN);
  private readonly partnerId = toSignal(this.partnerId$);
  private readonly clipboard = inject(Clipboard);
  private readonly aiToolCurlService = inject(AiToolCurlService);

  private readonly aiAssistantService = inject(AiAssistantService);
  private readonly route = inject(ActivatedRoute);
  private readonly snackbarService = inject(SnackbarService);
  private readonly pageService = inject(PageService);
  private readonly aiIconService = inject(GalaxyAiIconService);
  private readonly router = inject(Router);
  private readonly aiAssistantFormV2Service = inject(AiAssistantFormV2Service);
  private readonly confirmationModal = inject(OpenConfirmationModalService);
  private readonly unsavedGuard = inject(UnsavedChangesGuard);
  private readonly toolFormRegistry = inject(AiToolFormRegistryService);
  private readonly featureFlagService = inject(FeatureFlagService);

  protected showAutonomyPopover = false;
  protected readonly autonomyLevels = AUTONOMY_LEVELS;
  readonly defaultAvatarIcon = DEFAULT_AVATAR_SVG_ICON;

  protected readonly isLangChainEnabled$ = this.partnerId$.pipe(
    switchMap((partnerId) =>
      this.featureFlagService.batchGetStatus(partnerId, '', [LANGCHAIN_FEATURE_FLAG]).pipe(
        map((resp) => resp[LANGCHAIN_FEATURE_FLAG]),
        catchError(() => of(false)),
      ),
    ),
    take(1),
  );
  protected readonly isLangChainEnabled = toSignal(this.isLangChainEnabled$);

  protected readonly assistantForm = toSignal(this.aiAssistantFormV2Service.form$);

  protected readonly aiAssistant = toSignal(this.aiAssistantFormV2Service.aiAssistant$);
  readonly isCustomAssistant = computed(() => {
    return (
      this.aiAssistant()?.assistant?.type === AssistantType.ASSISTANT_TYPE_CUSTOM ||
      this.aiAssistant()?.assistant?.type === undefined
    );
  });

  protected readonly activeSubForms = signal<ActiveSubForms>({});
  protected readonly activeToolForm = computed(() => this.activeSubForms().toolForm);
  protected readonly activeCapabilityForm = computed(() => this.activeSubForms().capabilityForm);

  protected closeToolForm() {
    this.activeSubForms.update((subForms) => ({
      ...subForms,
      toolForm: undefined,
    }));
  }

  protected closeCapabilityForm() {
    this.activeSubForms.set({});
  }

  protected readonly aiConnections = toSignal(this.aiAssistantFormV2Service.aiConnections$);
  protected readonly appId = computed(() => this.aiAssistantService.buildAppId(this.aiAssistant()?.assistant.id));

  protected readonly availableModels = toSignal(this.aiAssistantFormV2Service.availableModels);

  protected readonly loading = computed(() => {
    return this.assistantForm() === undefined;
  });

  protected readonly saving = signal(false);
  protected PopoverPositions = PopoverPositions;
  protected showPopover = false;

  protected readonly showBetaBadge = computed(() => {
    const assistantType = this.aiAssistant()?.assistant?.type;
    return assistantType && BETA_ASSISTANT_TYPES.includes(assistantType);
  });

  protected readonly assistantSupportsVoice = computed(() => {
    return this.aiAssistant()?.assistant?.type === AssistantType.ASSISTANT_TYPE_VOICE_RECEPTIONIST;
  });

  protected readonly formDirty = signal(false);

  protected readonly submitDisabled = computed(() => {
    const isFormDirty = this.formDirty();
    const isSaving = this.saving();
    return !isFormDirty || isSaving;
  });

  protected readonly showToolFormCancel = computed(() => {
    const toolForm = this.activeToolForm();
    const assistantForm = this.assistantForm();
    if (!toolForm || !assistantForm) return false;

    const matchedCapabilityForm = assistantForm.controls.capabilities.controls.find(
      (capabilityForm) => capabilityForm === toolForm.parentCapabilityForm,
    );
    if (!matchedCapabilityForm) return true; // New tool for non-existent capability

    return !matchedCapabilityForm.controls.tools.controls.find((tool) => {
      return tool === toolForm.form;
    });
  });

  protected readonly showCapabilityFormCancel = computed(() => {
    const capabilityForm = this.activeCapabilityForm();
    const assistantForm = this.assistantForm();
    if (!capabilityForm || !assistantForm) return false;

    // Show cancel if capability hasn't been put on the assistant yet
    return !assistantForm.controls.capabilities.controls.find((capability) => capability === capabilityForm);
  });

  private readonly destroyRef = inject(DestroyRef);
  private readonly injector = inject(Injector);

  constructor() {
    // Make sure the default AI icon is registered
    this.aiIconService.dummyInit();

    effect(() => {
      const form = this.assistantForm();
      if (!form) return;
      // @ts-expect-error temp
      window.foo = () => console.log('Dirty state map:', getDirtyStateMap(form));
    });

    effect(() => {
      const form = this.assistantForm();
      if (!form) return;
      setupFormDirtyTracking(form, this.destroyRef, this.injector, () => {
        this.formDirty.set(true);
        this.unsavedGuard.notifyStateChanged(true);
      });
    });
  }

  protected readonly isSalesCoach = computed(
    () => this.aiAssistant()?.assistant?.type === AssistantType.ASSISTANT_TYPE_SALES_COACH,
  );

  protected readonly defaultAssistantName = computed(() => {
    return DEFAULT_ASSISTANT_NAMES.get(this.aiAssistant()?.assistant?.type ?? AssistantType.ASSISTANT_TYPE_CUSTOM);
  });

  protected onImageChanged(imageUrl: string): void {
    this.assistantForm()?.controls.avatarUrl.setValue(imageUrl);
    this.assistantForm()?.controls.avatarUrl.markAsDirty();
  }

  protected updateSelectedSources(
    opts: { sources: KnowledgeSource[] | null; initialLoad?: boolean } | undefined,
  ): void {
    const { sources = null, initialLoad = true } = opts || {};
    const form = this.assistantForm();
    if (!form) {
      return;
    }
    form.controls.knowledge.fromKnowledgeSources(sources ?? [], initialLoad);
    if (!initialLoad) {
      this.formDirty.set(true);
    }
  }

  protected handleCapabilityRollback(capabilityToReplace: AiCapabilityForm) {
    return this.confirmationModal
      .openModal({
        type: 'warn',
        title: 'AI_ASSISTANT.SETTINGS.ROLLBACK_CAPABILITY_TITLE',
        message: 'AI_ASSISTANT.SETTINGS.ROLLBACK_CAPABILITY_CONFIRMATION',
        confirmButtonText: 'AI_ASSISTANT.SETTINGS.ROLLBACK',
      })
      .subscribe(async (confirmed: boolean) => {
        if (confirmed) {
          await this.rollbackCapability(capabilityToReplace);
        }
      });
  }

  protected handleToolFormDisplay(event: { parentCapabilityForm?: AiCapabilityForm; form: AiToolForm } | undefined) {
    const { parentCapabilityForm, form: toolForm } = event || {};

    if (!toolForm) {
      // Create a new tool form if none provided
      this.activeSubForms.update((subForms) => ({
        capabilityForm: subForms.capabilityForm ?? parentCapabilityForm,
        toolForm: {
          parentCapabilityForm: parentCapabilityForm,
          form: new AiToolForm({ metadata: { isNew: true, localId: uuidv4() } }),
        },
      }));
      return;
    }

    // Simply clone the form - we don't need to maintain form identity here
    // The registry is used when we need to ensure only one form exists across the app
    this.activeSubForms.update((subForms) => ({
      capabilityForm: subForms.capabilityForm ?? parentCapabilityForm,
      toolForm: {
        parentCapabilityForm: parentCapabilityForm,
        form: toolForm,
      },
    }));
  }

  protected handleToolFormClosed(_toolForm: AiToolForm | undefined) {
    this.activeSubForms.set({});
  }

  protected handleToolFormSubmit() {
    const submittedToolForm = this.activeToolForm();
    const assistantForm = this.assistantForm();
    if (!submittedToolForm || !assistantForm || submittedToolForm.form.disabled) {
      this.activeSubForms.update((subForms) => ({
        ...subForms,
        toolForm: undefined,
      }));
      return;
    }

    if (!submittedToolForm.form.valid) {
      submittedToolForm.form.markAllAsTouched();
      this.snackbarService.openErrorSnack('AI_ASSISTANT.FUNCTIONS.SNACKBAR.MISSING_FIELDS');
      return;
    }

    const parentCapabilityGoalId = submittedToolForm.parentCapabilityForm?.controls.goalId.getRawValue();
    const submittedToolId = submittedToolForm.form.controls.id.getRawValue();
    const matchedCapabilityForm = this.activeToolForm()?.parentCapabilityForm;

    // If matchedCapabilityForm has the tool already, update it (merge), otherwise add it
    if (matchedCapabilityForm) {
      const matchedTool = matchedCapabilityForm.controls.tools.controls.find((tool) => {
        if (!submittedToolId) {
          return tool.metadata.localId && tool.metadata.localId === submittedToolForm.form.metadata.localId;
        }
        return tool.controls.id.getRawValue() === submittedToolId;
      });
      if (!matchedTool) {
        matchedCapabilityForm.controls.tools.push(submittedToolForm.form);
      }
    } else {
      // Handle updating the tool if we don't know what capability it is on
      assistantForm.controls.capabilities.controls.forEach((capabilityForm) => {
        if (capabilityForm.controls.goalId.getRawValue() === parentCapabilityGoalId) {
          return;
        }
        const matchedTool = capabilityForm.controls.tools.controls.find((tool) => {
          if (submittedToolId) {
            return tool.controls.id.getRawValue() === submittedToolId;
          }
          return false;
        });
        if (!matchedTool) {
          capabilityForm.controls.tools.push(submittedToolForm.form);
        }
      });
    }

    this.activeSubForms.update((subForms) => ({
      ...subForms,
      toolForm: undefined,
    }));
  }

  protected handleCapabilityFormDisplay(capabilityForm: AiCapabilityForm | undefined) {
    if (!capabilityForm) {
      this.activeSubForms.set({});
      return;
    }

    // Pass the form directly - we'll work with the original form
    this.activeSubForms.set({ capabilityForm });
  }

  protected handleCapabilityFormSubmit() {
    const submittedCapabilityForm = this.activeCapabilityForm();
    const assistantForm = this.assistantForm();
    if (!submittedCapabilityForm || submittedCapabilityForm.disabled || !assistantForm) {
      this.activeSubForms.set({});
      return;
    }

    if (!submittedCapabilityForm.valid) {
      submittedCapabilityForm.markAllAsTouched();
      this.snackbarService.openErrorSnack('AI_ASSISTANT.SETTINGS.CAPABILITY_FORM_INVALID');
      return;
    }

    // Check if this is a new capability that hasn't been added to the capabilities array yet
    const existingCapability = assistantForm.controls.capabilities.controls.find(
      (capability) => capability === submittedCapabilityForm,
    );

    // If capability doesn't exist in the array yet (new capability), add it
    if (!existingCapability) {
      assistantForm.controls.capabilities.push(submittedCapabilityForm);
    }

    // Close the capability form view
    this.activeSubForms.set({});
  }

  protected async submit(): Promise<void> {
    try {
      this.saving.set(true);
      await this.handleSubmit();
      this.assistantForm()?.markAsPristine();
      this.formDirty.set(this.assistantForm()?.dirty ?? false);
    } catch (e) {
      console.error(e);
      this.snackbarService.openErrorSnack('AI_ASSISTANT.SETTINGS.ASSISTANT_UPDATE_ERROR');
    } finally {
      this.saving.set(false);
    }
  }

  private async handleSubmit(): Promise<void> {
    const form = this.assistantForm();
    if (!form) {
      return;
    }

    const localNamespace = getNamespace(this.accountGroupId(), this.partnerId());
    if (!localNamespace) {
      return;
    }

    // --
    // Tool Updates
    // --
    const toolUpdates: Promise<unknown>[] = [];
    const toolsToUpdate = form?.controls?.capabilities?.controls?.flatMap((capability) => {
      return capability.controls.tools.controls
        ?.filter((tool) => tool.dirty && !tool.disabled)
        .map((tool) => {
          return tool.toFunction(localNamespace);
        });
    });
    const uniqueToolsToUpdate = toolsToUpdate?.filter(
      (tool, index, self) => index === self.findIndex((t) => t.id === tool.id),
    );
    uniqueToolsToUpdate
      ?.filter((tool) => tool.id)
      .forEach((tool) => toolUpdates.push(this.aiAssistantService.upsertFunction(tool)));

    // --
    // Prompt Module Updates
    // --
    const promptModuleUpdates: Promise<unknown>[] = [];
    form?.controls?.capabilities?.controls?.forEach((capability) => {
      const promptModuleForms = capability.controls.promptModules;
      promptModuleForms.controls.forEach((promptModuleForm) => {
        if (!promptModuleForm.dirty || promptModuleForm.disabled) {
          return;
        }

        const promptModuleNamespace = promptModuleForm.controls.namespace?.getRawValue();
        const isCapabilityManaged = capability.controls.managed.getRawValue();
        if (promptModuleNamespace?.globalNamespace || isCapabilityManaged) {
          // If a prompt module has been edited and it is either (1) global or (2) on a global capability:
          // Then we modify the prompt module to trigger creation of a copy in the local namespace.
          promptModuleForm.controls.namespace.setValue(localNamespace);
          promptModuleForm.controls.managed.setValue(false);
          promptModuleForm.controls.id.setValue(null);
          // Mark the capability dirty to trigger a copy of it in the local namespace with a reference to the new prompt module we'll be creating.
          capability.markAsDirty();
        }

        const promptModuleId = promptModuleForm.controls.id.getRawValue() ?? undefined;
        const content = promptModuleForm.getContent();
        if (promptModuleId || content) {
          // If prompt module doesn't exist yet, and there is no content, we don't need to create a prompt module
          promptModuleUpdates.push(
            this.aiAssistantService
              .upsertPromptModule(
                promptModuleForm.toPromptModule(localNamespace, capability.controls.name.getRawValue()),
                content ?? '',
              )
              .then((promptModuleId) => {
                // If the prompt module was created, set the ID on the form so it's available when associating the prompt module with the capability
                if (promptModuleId && !promptModuleForm.controls.id.getRawValue()) {
                  promptModuleForm.controls.id.setValue(promptModuleId);
                }
                // Mark as pristine to avoid creating redundant versions of the prompt module on additionals submits
                promptModuleForm.markAsPristine();
              }),
          );
        }
      });
    });

    // --
    // Capability updates
    // --
    const capabilityUpdates: Promise<unknown>[] = [];
    {
      // Capabilities depend on tools and prompt modules
      await Promise.all(toolUpdates);
      await Promise.all(promptModuleUpdates);

      const capabilitiesToUpdate = form?.controls?.capabilities?.controls?.filter(
        (capability) => capability.dirty && capability.enabled && !capability.hasOnlyConfigurationChanges(),
      );

      capabilitiesToUpdate?.forEach((capability) => {
        const goal = capability.toGoal(localNamespace);
        capabilityUpdates.push(
          this.aiAssistantService.upsertCapability(goal).then((id) => {
            // If the goal was created, set the ID on the form so it's available when associating the goal with the assistant
            if (id && !capability.controls.goalId.getRawValue()) {
              capability.controls.goalId.setValue(id);
            }
          }),
        );
      });
    }

    // Assistant update depends on capability updates if any of the capabilities are new
    await Promise.all(capabilityUpdates);

    const assistant = form.toAssistant(localNamespace);
    assistant.namespace = localNamespace;
    await firstValueFrom(this.aiAssistantFormV2Service.upsertAssistant(assistant));

    try {
      if (form.controls.knowledge.dirty) {
        await this.aiAssistantFormV2Service.updateAssistantKnowledge(
          this.appId(),
          this.aiAssistant()?.assistant.name || '',
          form.controls.knowledge,
        );
      }
    } catch (e) {
      console.error(`Error updating assistant knowledge: ${e}`);
    }

    if (form.controls.connections.dirty) {
      await firstValueFrom(
        this.aiAssistantFormV2Service.updateConnections(
          assistant,
          this.aiConnections() || [],
          this.assistantForm()?.controls.connections.controls.map((connectionForm) => ({
            connectionId: connectionForm.metadata.id,
            enabled: connectionForm.get('connected')?.value ?? false,
          })) || [],
        ),
      );
    }

    form.markAsPristine();
    this.saving.set(false);
    this.snackbarService.openSuccessSnack('AI_ASSISTANT.SETTINGS.ASSISTANT_UPDATED');
    this.unsavedGuard.notifyStateChanged(false);
  }

  protected back(): void {
    this.pageService.navigateToPrevious(FALLBACK_BACK_URL, this.route);
  }

  viewRecordedMeetings() {
    const filters = [
      {
        fieldId: StandardIds.ActivityMeetingStatus,
        operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
        values: [{ string: 'Completed' }],
      },
      {
        fieldId: StandardIds.ActivitySourceName,
        operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
        values: [{ string: 'Meeting Analysis' }],
      },
    ];
    const filterParam = encodeFilters(filters);
    this.router.navigate(['/crm/activities'], {
      queryParams: { filter: filterParam },
    });
  }

  canDeactivate(): boolean {
    const form = this.assistantForm();
    return !(form && form.dirty);
  }

  @HostListener('window:beforeunload', ['$event'])
  onBeforeUnload(event: BeforeUnloadEvent): void {
    if (this.formDirty()) {
      event.preventDefault();
      event.stopPropagation();
      event.returnValue = false;
    }
  }

  protected async openDeleteAssistantConfirmationModal() {
    if (!this.isCustomAssistant()) {
      // Safety net to prevent deletion of non-custom assistants - this is not supported
      return;
    }
    const assistant = this.aiAssistant()?.assistant;
    if (!assistant) {
      return;
    }
    const deleteConfirmed = await firstValueFrom(
      this.confirmationModal.openModal({
        type: 'warn',
        title: 'AI_ASSISTANT.SHARED.DELETE_ASSISTANT_CONFIRMATION.TITLE',
        message: 'AI_ASSISTANT.SHARED.DELETE_ASSISTANT_CONFIRMATION.MESSAGE',
        confirmButtonText: 'AI_ASSISTANT.SHARED.DELETE_ASSISTANT_CONFIRMATION.CONFIRM',
        cancelButtonText: 'AI_ASSISTANT.SHARED.DELETE_ASSISTANT_CONFIRMATION.CANCEL',
        cancelOnEscapeKeyOrBackgroundClick: true,
      }),
    );
    if (!deleteConfirmed) {
      return;
    }

    try {
      await this.aiAssistantService.deleteAssistant({
        id: assistant.id,
        namespace: assistant.namespace,
      });
      this.snackbarService.openSuccessSnack('AI_ASSISTANT.SETTINGS.ASSISTANT_DELETED');
      this.back();
    } catch (e) {
      this.snackbarService.openSuccessSnack('AI_ASSISTANT.SETTINGS.ASSISTANT_DELETE_ERROR');
    }
  }

  private async rollbackCapability(capabilityToReplace: AiCapabilityForm) {
    try {
      const replacementGoalId = capabilityToReplace.controls.overrideOf.getRawValue()?.id;
      const replacementGoalNamespace = capabilityToReplace.controls.overrideOf.getRawValue()?.namespace;
      if (!replacementGoalId || !replacementGoalNamespace) {
        this.snackbarService.openErrorSnack('AI_ASSISTANT.SETTINGS.ROLLBACK_CAPABILITY_ERROR');
        return;
      }

      const replacementGoal = await firstValueFrom(
        this.aiAssistantService.getGoal(replacementGoalId, replacementGoalNamespace),
      );
      if (!replacementGoal) {
        this.snackbarService.openErrorSnack('AI_ASSISTANT.SETTINGS.ROLLBACK_CAPABILITY_ERROR');
        return;
      }

      const promptModuleContents = await this.aiAssistantService.getMultiPromptModuleVersions(
        replacementGoal.promptModules?.map(
          (promtModule) => new PromptModuleKey({ id: promtModule.id, namespace: promtModule.namespace }),
        ) || [],
      );

      const replacementForm = new AiCapabilityForm(
        { goal: replacementGoal },
        promptModuleContents,
        this.toolFormRegistry,
      );

      capabilityToReplace.patchValue(replacementForm.getRawValue());
      capabilityToReplace.markAsPristine();
      capabilityToReplace.controls.promptModules.markAsPristine();
      capabilityToReplace.controls.tools.markAsPristine();
    } catch (err) {
      console.error('Error rolling back capability', err);
      this.snackbarService.openErrorSnack('AI_ASSISTANT.SETTINGS.ROLLBACK_CAPABILITY_ERROR');
    }
  }

  toggleAutonomyPopover(): void {
    this.showAutonomyPopover = !this.showAutonomyPopover;
  }

  closeAutonomyPopover(): void {
    this.showAutonomyPopover = false;
  }

  protected copyCapability(): void {
    const capabilityForm = this.activeCapabilityForm();
    if (!capabilityForm) return;

    const formValue = capabilityForm.getRawValue();
    const funcs = capabilityForm.controls.tools.toFunctions(undefined, { excludeAuthHeaders: true });

    const functions = funcs?.map((tool) => {
      const curlCommand = this.aiToolCurlService.functionToCurlCommand(tool);
      return {
        name: tool.id,
        description: tool.description,
        curl: curlCommand,
      };
    });

    const capabilityName = formValue.name;
    const functionCount = functions?.length || 0;
    const preamble = `This is an AI assistant capability called "${capabilityName}".

## What is a Capability?
A capability is a modular component that gives an AI assistant specific skills and behaviors. It's part of a larger system where multiple capabilities work together to create a comprehensive AI assistant.

## Structure:
- **Prompt Section**: Instructions that define the AI's behavior, role, and decision-making process for this specific capability
- **Functions (${functionCount} total)**: ${functionCount === 0 ? 'No functions defined' : 'External APIs/tools the AI can call to perform actions beyond text generation'}

## Context:
- This capability is one module within a larger AI assistant prompt
- The prompt section will be combined with other capability prompts and system instructions
- Functions extend the AI's abilities by allowing it to interact with external services
- Each function includes a cURL command showing exactly how it would be executed`;

    const copiedCapability = {
      preamble: preamble,
      prompt: formValue.promptModules[0].instructions,
      functions: functions,
    };

    this.clipboard.copy(JSON.stringify(copiedCapability));
    this.snackbarService.openSuccessSnack('AI_ASSISTANT.SETTINGS.COPY_CAPABILITY_SUCCESS');
  }
}
