import { TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { of } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { AiAssistantFormV2Component } from './ai-assistant-form-v2.component';
import { AiAssistantService } from '../../../core/services/ai-assistant.service';
import {
  AssistantApiService,
  AssistantType,
  ConfigurableGoal,
  GoalType,
  Model,
  ModelVendor,
  Namespace,
  PromptModule,
  UpsertAssistantResponse,
  VendorModel,
  KeyValuePair,
} from '@vendasta/ai-assistants';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { PageService } from '@vendasta/galaxy/page/src/page.service';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  AI_DEFAULT_WORKFORCE_TOKEN,
  NAMESPACE_CONFIG_TOKEN,
  PARTNER_ID_TOKEN,
} from '../../../core/tokens';
import { AiAssistantI18nModule } from '../../../assets/i18n/ai-assistant-i18n.module';
import { LexiconModule } from '@galaxy/lexicon';
import { TranslateService } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { AiAssistant, ConnectionType } from '../../../core/interfaces/ai-assistant.interface';
import { ConversationApiService } from '@vendasta/conversation';
import {
  AiAssistantForm,
  AiCapabilityForm,
  AiConnectionsForm,
  AiToolForm,
  AiToolFormArray,
  AiToolHeaderForm,
  AiToolParameterForm,
} from '../../../core/forms';
import {
  ACCOUNT_GROUP_ID_TOKEN as KNOWLEDGE_ACCOUNT_GROUP_ID_TOKEN,
  AiKnowledgeService,
  BUSINESS_PROFILE_URL_TOKEN,
  MANAGE_KNOWLEDGE_URL_TOKEN,
  MARKET_ID_TOKEN,
  PARTNER_ID_TOKEN as KNOWLEDGE_PARTNER_ID_TOKEN,
  SHOW_BUSINESS_PROFILE_SOURCE_TOKEN,
  WEB_CHAT_WIDGET_EDIT_ROUTE_TOKEN,
} from '@galaxy/ai-knowledge';
import { UnsavedChangesGuard } from '../../../core/services/unsaved-changes.guard';
import { AiAssistantFormV2Service } from './ai-assistant-form-v2.service';
import { voiceFamilies } from '../../../core/services/ai-assistant-utils';
import { SmsDetailsService } from '../../../core/services/sms-details.service';
import { WhatsappDetailsService } from '../../../core/services/whatsapp-details.service';
import { PartnerServiceInterfaceToken } from '@galaxy/partner';
import { By } from '@angular/platform-browser';

jest.mock('../../../core/services/ai-assistant.service');
// jest.mock('@vendasta/ai-assistants');
jest.mock('@vendasta/galaxy/snackbar-service');
jest.mock('@vendasta/galaxy/page/src/page.service');

const ACCOUNT_GROUP_ID = 'AG-1234';
const PARTNER_ID = 'ABC';

// Create a test class that extends the component to access protected members
class TestAiAssistantFormV2Component extends AiAssistantFormV2Component {
  // Expose protected members for testing
  public getTestAssistantForm() {
    return this.assistantForm();
  }

  public testSubmit() {
    return this.submit();
  }

  public testOnImageChanged(url: string) {
    return this.onImageChanged(url);
  }

  public testBack() {
    return this.back();
  }

  public testSetActiveToolForm(data: { parentCapabilityForm?: AiCapabilityForm; form: AiToolForm }) {
    return this.activeSubForms.set({ toolForm: data });
  }

  public testHandleToolFormDisplay(event: { parentCapabilityForm?: any; form: any }) {
    return this.handleToolFormDisplay(event);
  }

  public testHandleToolFormSubmit() {
    return this.handleToolFormSubmit();
  }

  public testHandleCapabilityRollback(capabilityForm: AiCapabilityForm) {
    return this.handleCapabilityRollback(capabilityForm);
  }

  // Expose toolsToUpdate for testing
  public toolsToUpdate: any[] = [];
}

// Helper to get plain object from Namespace or plain object
function getNamespaceObj(ns: any) {
  if (ns && typeof ns.toApiJson === 'function') {
    return ns.toApiJson();
  }
  return ns;
}

describe('AiAssistantFormV2Component', () => {
  let component: TestAiAssistantFormV2Component;
  let aiAssistantService: jest.Mocked<AiAssistantService>;
  let assistantApiService: jest.Mocked<AssistantApiService>;
  let snackbarService: jest.Mocked<SnackbarService>;
  let conversationApiService: jest.Mocked<ConversationApiService>;
  let pageService: jest.Mocked<PageService>;
  let MockAiKnowledgeService: jest.Mocked<AiKnowledgeService>;
  let mockUnsavedChangesGuard: jest.Mocked<UnsavedChangesGuard>;
  let mockSmsDetailsService: jest.Mocked<SmsDetailsService>;
  let mockWhatsappDetailsService: jest.Mocked<WhatsappDetailsService>;

  const setupTestBed = async (
    overrides: {
      aiAssistantService?: Partial<AiAssistantService>;
      assistantApiService?: Partial<AssistantApiService>;
      snackbarService?: Partial<SnackbarService>;
      conversationApiService?: Partial<ConversationApiService>;
      pageService?: Partial<PageService>;
      aiKnowledgeService?: Partial<AiKnowledgeService>;
      unsavedChangesGuard?: Partial<UnsavedChangesGuard>;
    } = {},
    initialData: {
      assistant?: any;
      connections?: any[];
      capabilities?: ConfigurableGoal[];
      promptModuleContents?: Record<string, string>;
    } = {},
  ) => {
    const mockSMSConnection = {
      connection: {
        id: 'test-sms-connection-id',
        namespace: new Namespace({
          accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
        }),
        name: '',
        assistantKeys: [
          {
            id: 'test-assistant-id',
            namespace: new Namespace({
              accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
            }),
          },
        ],
        connectionType: 'SMS',
        connectionTypeName: 'SMS',
        supportedAssistantTypes: [2],
      },
    };

    const mockWebchatConnection = {
      connection: {
        id: 'test-webchat-connection-id',
        namespace: new Namespace({
          accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
        }),
        name: '',
        assistantKeys: [
          {
            id: 'test-assistant-id',
            namespace: new Namespace({
              accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
            }),
          },
        ],
        connectionType: 'WebchatWidget',
        connectionTypeName: 'Web Chat',
        supportedAssistantTypes: [2],
        isConnectionLocked: true,
      },
    };

    const mockAssistant = initialData.assistant ?? {
      assistant: {
        id: 'test-assistant-id',
        namespace: new Namespace({
          accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
        }),
        name: 'Test Assistant',
        type: 2,
        avatarUrl: 'test-assistant-avatar-url',
        configurableGoals: initialData.capabilities ?? [
          new ConfigurableGoal({
            goal: {
              id: 'test-capability',
              name: 'Test Capability',
              description: 'Test Description',
              namespace: new Namespace({
                accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
              }),
              type: GoalType.GOAL_TYPE_CUSTOM,
              functions: [],
              promptModules: [
                new PromptModule({
                  id: 'test-prompt-module-id',
                  namespace: new Namespace({
                    accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
                  }),
                  deployedVersion: '1',
                }),
              ],
            },
            configuration: [],
          }),
        ],
      },
    };

    const mockConnections = initialData.connections ?? [mockSMSConnection, mockWebchatConnection];

    aiAssistantService = {
      buildAssistantConfigurationUrl: jest.fn().mockReturnValue('/assistant/configuration'),
      getAssistant: jest.fn().mockReturnValue(of(mockAssistant)),
      hydrateAssistantWithDefaultInfo: jest.fn().mockReturnValue(mockAssistant),
      listConnectionsForAssistant: jest.fn().mockReturnValue(of(mockConnections)),
      upsertCapability: jest.fn().mockReturnValue(Promise.resolve('new-capability-id')),
      upsertPromptModule: jest.fn().mockReturnValue(Promise.resolve({ id: 'test-prompt-module-id' })),
      getMultiPromptModuleVersions: jest.fn().mockReturnValue(of([])),
      buildAppId: jest.fn().mockReturnValue('APP-AI-123'),
      $isDeepgramEnabled: jest.fn().mockReturnValue(true),
      $isElevenLabsEnabled: jest.fn().mockReturnValue(true),
      listAvailableModels: jest.fn().mockReturnValue(
        of([
          new Model({
            id: 'gpt-4',
            vendor: ModelVendor.VENDOR_OPENAI,
            name: 'GPT-4',
            recommended: true,
          }),
          new Model({
            id: 'gpt-3.5-turbo',
            vendor: ModelVendor.VENDOR_OPENAI,
            name: 'GPT-3.5 Turbo',
            recommended: false,
          }),
        ]),
      ),
      ...overrides.aiAssistantService,
    } as unknown as jest.Mocked<AiAssistantService>;

    assistantApiService = {
      upsertAssistant: jest.fn().mockReturnValue(
        of({
          assistant: {
            id: 'test-assistant-id',
            namespace: new Namespace({
              accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
            }),
            name: 'Test Assistant',
            type: 2,
            avatarUrl: 'test-assistant-avatar-url',
            configurableGoals: [],
            config: {},
            toApiJson: () => ({}),
          },
          toApiJson: () => ({}),
        } as unknown as UpsertAssistantResponse),
      ),
      setAssistantConnections: jest.fn().mockReturnValue(of({})),
      ...overrides.assistantApiService,
    } as unknown as jest.Mocked<AssistantApiService>;

    snackbarService = {
      openErrorSnack: jest.fn(),
      openSuccessSnack: jest.fn(),
      ...overrides.snackbarService,
    } as unknown as jest.Mocked<SnackbarService>;

    conversationApiService = {
      getMultiWidget: jest.fn().mockReturnValue(
        of({
          widgets: [],
        }),
      ),
      ...overrides.conversationApiService,
    } as unknown as jest.Mocked<ConversationApiService>;

    pageService = {
      navigateToPrevious: jest.fn(),
      ...overrides.pageService,
    } as unknown as jest.Mocked<PageService>;

    MockAiKnowledgeService = {
      businessProfileKSId: jest.fn().mockReturnValue('businessProfileKSId'),
      listAllKnowledgeSourcesForApp: jest.fn().mockResolvedValue([]),
      ...overrides.aiKnowledgeService,
    } as unknown as jest.Mocked<AiKnowledgeService>;

    mockUnsavedChangesGuard = {
      notifyStateChanged: jest.fn(),
      canDeactivate: jest.fn().mockReturnValue(true),
      ...overrides.unsavedChangesGuard,
    } as unknown as jest.Mocked<UnsavedChangesGuard>;

    mockSmsDetailsService = {
      phoneNumber$: of('+***********'),
    } as unknown as jest.Mocked<SmsDetailsService>;

    mockWhatsappDetailsService = {
      phoneNumber$: of('+***********'),
    } as unknown as jest.Mocked<WhatsappDetailsService>;

    await TestBed.configureTestingModule({
      imports: [
        AiAssistantI18nModule,
        HttpClientTestingModule,
        LexiconModule.forRoot(),
        TestAiAssistantFormV2Component,
      ],
      providers: [
        FormBuilder,
        { provide: PARTNER_ID_TOKEN, useValue: of(PARTNER_ID) },
        { provide: ACCOUNT_GROUP_ID_TOKEN, useValue: of(ACCOUNT_GROUP_ID) },
        { provide: AI_DEFAULT_WORKFORCE_TOKEN, useValue: of({}) },
        { provide: AiAssistantService, useValue: aiAssistantService },
        { provide: AssistantApiService, useValue: assistantApiService },
        { provide: SnackbarService, useValue: snackbarService },
        { provide: PageService, useValue: pageService },
        { provide: ConversationApiService, useValue: conversationApiService },
        { provide: UnsavedChangesGuard, useValue: mockUnsavedChangesGuard },
        { provide: SHOW_BUSINESS_PROFILE_SOURCE_TOKEN, useValue: of(true) },
        { provide: AiKnowledgeService, useValue: MockAiKnowledgeService },
        AiAssistantFormV2Service,
        {
          provide: ActivatedRoute,
          useValue: {
            paramMap: of({
              get: (key: string) => (key === 'assistantId' ? 'test-assistant-id' : null),
            }),
          },
        },
        { provide: TranslateService, useValue: { setDefaultLang: jest.fn(), use: jest.fn() } },
        { provide: SmsDetailsService, useValue: mockSmsDetailsService },
        { provide: WhatsappDetailsService, useValue: mockWhatsappDetailsService },
        {
          provide: NAMESPACE_CONFIG_TOKEN,
          useValue: of({
            namespace: new Namespace({
              accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
            }),
          }),
        },
        { provide: KNOWLEDGE_ACCOUNT_GROUP_ID_TOKEN, useValue: of(ACCOUNT_GROUP_ID) },
        { provide: KNOWLEDGE_PARTNER_ID_TOKEN, useValue: of(PARTNER_ID) },
        { provide: MARKET_ID_TOKEN, useValue: of('default') },
        { provide: MANAGE_KNOWLEDGE_URL_TOKEN, useValue: of('some-url') },
        { provide: BUSINESS_PROFILE_URL_TOKEN, useValue: of('some-url') },
        { provide: SHOW_BUSINESS_PROFILE_SOURCE_TOKEN, useValue: of(false) },
        { provide: WEB_CHAT_WIDGET_EDIT_ROUTE_TOKEN, useValue: of(['some-url']) },
        { provide: PartnerServiceInterfaceToken, useValue: { getPartnerId: () => of(PARTNER_ID) } },
      ],
    }).compileComponents();

    const fixture = TestBed.createComponent(TestAiAssistantFormV2Component);
    component = fixture.componentInstance;
    fixture.detectChanges();
    return { fixture };
  };

  const waitForFormToBePopulated = async (
    component: AiAssistantFormV2Component,
  ): Promise<AiAssistantForm | undefined> => {
    for (let i = 0; i < 20; i++) {
      // up to 2 seconds
      const form = component['assistantForm']();
      if (form) {
        // console.log('form populated after ', i * 100, 'ms');
        return form;
      }
      await new Promise((res) => setTimeout(res, 100));
    }
    return undefined;
  };

  it('should create', async () => {
    await setupTestBed();
    expect(component).toBeTruthy();
  });

  it('should initialize the form with default values', async () => {
    const initialAssistant = {
      assistant: {
        id: 'test-assistant-id',
        namespace: new Namespace({
          accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
        }),
        name: 'Test Assistant',
        type: 2,
        avatarUrl: 'test-assistant-avatar-url',
        configurableGoals: [],
      },
    };
    await setupTestBed({}, { assistant: initialAssistant });
    expect(aiAssistantService.getAssistant).toHaveBeenCalledWith(PARTNER_ID, ACCOUNT_GROUP_ID, 'test-assistant-id');
    const hydrateCall = aiAssistantService.hydrateAssistantWithDefaultInfo.mock.calls[0][0] as {
      assistant: { namespace: any };
      connections: any[];
    };
    // Debug log
    // eslint-disable-next-line no-console
    // console.log(
    //   'hydrateCall:',
    //   JSON.stringify(hydrateCall, (key, value) => (typeof value === 'function' ? '[Function]' : value), 2),
    // );
    // Check assistant namespace
    if (hydrateCall.assistant?.namespace === undefined) {
      throw new Error('hydrateCall.assistant.namespace is undefined');
    } else {
      expect(getNamespaceObj(hydrateCall.assistant.namespace)).toEqual({
        accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
      });
    }
    // Check connections namespaces
    if (!hydrateCall.connections?.[0]?.connection?.namespace) {
      throw new Error('hydrateCall.connections[0].connection.namespace is undefined');
    } else {
      expect(getNamespaceObj(hydrateCall.connections[0].connection.namespace)).toEqual({
        accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
      });
    }
    if (!hydrateCall.connections?.[1]?.connection?.namespace) {
      throw new Error('hydrateCall.connections[1].connection.namespace is undefined');
    } else {
      expect(getNamespaceObj(hydrateCall.connections[1].connection.namespace)).toEqual({
        accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
      });
    }
    // Check assistantKeys namespaces
    if (!hydrateCall.connections?.[0]?.connection?.assistantKeys?.[0]?.namespace) {
      throw new Error('hydrateCall.connections[0].connection.assistantKeys[0].namespace is undefined');
    } else {
      expect(getNamespaceObj(hydrateCall.connections[0].connection.assistantKeys[0].namespace)).toEqual({
        accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
      });
    }
    if (!hydrateCall.connections?.[1]?.connection?.assistantKeys?.[0]?.namespace) {
      throw new Error('hydrateCall.connections[1].connection.assistantKeys[0].namespace is undefined');
    } else {
      expect(getNamespaceObj(hydrateCall.connections[1].connection.assistantKeys[0].namespace)).toEqual({
        accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
      });
    }

    const form = component.getTestAssistantForm();
    expect(form?.getRawValue()).toEqual({
      id: 'test-assistant-id',
      name: 'Test Assistant',
      type: 2,
      namespace: new Namespace({
        accountGroupNamespace: {
          accountGroupId: 'AG-1234',
        },
      }),
      avatarUrl: 'test-assistant-avatar-url',
      capabilities: [
        {
          goalId: null,
          namespace: expect.any(Object),
          managed: false,
          name: 'Test Assistant Personality',
          description: null,
          type: GoalType.GOAL_TYPE_PERSONALITY,
          promptModules: [
            {
              id: null,
              instructions: null,
              managed: false,
              namespace: expect.any(Object),
            },
          ],
          tools: [],
          configuration: null,
          overrideOf: null,
        },
      ],
      knowledge: [],
      model: {
        model: expect.any(Object),
      },
      voiceConfig: {
        voiceFamily: voiceFamilies[4],
        modelConfig: {
          deepgramConfig: {
            voice: 'aura-asteria-en',
          },
          elevenLabsConfig: {
            voice: '9BWtsMINqrJLrRacOk9x',
          },
          openAIRealtimeConfig: {
            voice: 'alloy',
            turnDetection: {
              prefixPadding: 300,
              silenceDuration: 500,
              threshold: 0.75,
            },
          },
        },
        vendorModel: VendorModel.VENDOR_MODEL_OPEN_AI_REALTIME,
      },
      connections: expect.any(Array),
    });

    // Get the connections as AiConnectionsForm
    const connectionsForm = form?.get('connections') as AiConnectionsForm;
    expect(connectionsForm.controls.length).toBe(2);

    // Find connections by their metadata IDs
    const smsConnection = connectionsForm.controls.find((control) => control.metadata.id === 'test-sms-connection-id');
    const webchatConnection = connectionsForm.controls.find(
      (control) => control.metadata.id === 'test-webchat-connection-id',
    );

    // Check that connections exist
    expect(smsConnection).toBeTruthy();
    expect(webchatConnection).toBeTruthy();

    // Check connection values
    expect(smsConnection?.get('connected')?.value).toBe(true);
    expect(webchatConnection?.get('connected')?.value).toBe(true);

    // Check disabled states
    expect(smsConnection?.get('connected')?.disabled).toBeFalsy();
    expect(webchatConnection?.get('connected')?.disabled).toBeTruthy();
  });

  it('should submit the expected assistant data', async () => {
    const initialAssistant = {
      assistant: {
        id: 'test-assistant-id',
        namespace: new Namespace({
          accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
        }),
        name: 'Test Assistant',
        type: 2,
        avatarUrl: 'test-assistant-avatar-url',
        configurableGoals: [],
      },
    };
    await setupTestBed({}, { assistant: initialAssistant });
    const form = component.getTestAssistantForm();
    form?.controls['name'].setValue('Updated Assistant Name');
    form?.controls['avatarUrl'].setValue('updated-avatar-url');

    // Get the connections FormArray and set the connected value to false for the SMS connection
    const connectionsArray = form?.get('connections') as AiConnectionsForm;
    const smsConnectionForm = connectionsArray.controls.find(
      (control) => control.metadata.id === 'test-sms-connection-id',
    );
    smsConnectionForm?.get('connected')?.setValue(false);
    smsConnectionForm?.markAsDirty();

    form?.markAsDirty();
    form?.controls['capabilities']?.controls.forEach((capform) => {
      capform.markAsDirty();
      capform.controls.promptModules.controls.forEach((pmForm) => pmForm.markAsDirty());
    });

    // Debug: ensure form and connection are dirty before submit
    expect(form?.dirty).toBe(true);
    expect(smsConnectionForm?.dirty).toBe(true);

    await component.testSubmit();

    // Debug: log setAssistantConnections calls
    // eslint-disable-next-line no-console
    // console.log('setAssistantConnections calls:', assistantApiService.setAssistantConnections.mock.calls);

    expect(assistantApiService.upsertAssistant).toHaveBeenCalledWith({
      assistant: {
        id: 'test-assistant-id',
        name: 'Updated Assistant Name',
        namespace: expect.any(Object),
        avatarUrl: 'updated-avatar-url',
        type: AssistantType.ASSISTANT_TYPE_INBOX,
        configurableGoals: [
          expect.objectContaining({
            goal: expect.objectContaining({
              id: 'new-capability-id',
              namespace: expect.any(Object),
            }),
            configuration: undefined,
          }),
        ],
        config: {
          models: [],
        },
      },
      options: { applyDefaults: false },
    });

    // Check the actual contents of the setAssistantConnections call
    const setConnectionsCall = assistantApiService.setAssistantConnections.mock.calls[0][0];
    // Debug log
    // eslint-disable-next-line no-console
    console.log(
      'setConnectionsCall:',
      JSON.stringify(setConnectionsCall, (key, value) => (typeof value === 'function' ? '[Function]' : value), 2),
    );
    expect(setConnectionsCall.assistantKey?.id).toBe('test-assistant-id');
    if (setConnectionsCall.assistantKey?.namespace === undefined) {
      throw new Error('setConnectionsCall.assistantKey.namespace is undefined');
    } else {
      expect(getNamespaceObj(setConnectionsCall.assistantKey?.namespace)).toEqual({
        accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
      });
    }
    expect(setConnectionsCall.associationStates?.length).toBe(1);
    const assoc = setConnectionsCall.associationStates?.[0];
    expect(assoc?.connectionKey?.id).toBe('test-sms-connection-id');
    if (assoc?.connectionKey?.namespace === undefined) {
      throw new Error('assoc.connectionKey.namespace is undefined');
    } else {
      expect(getNamespaceObj(assoc?.connectionKey?.namespace)).toEqual({
        accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
      });
    }
    expect(assoc?.connectionKey?.connectionType).toBe(ConnectionType.SMS);
    expect(assoc?.isAssociated).toBe(false);

    expect(snackbarService.openSuccessSnack).toHaveBeenCalledWith('AI_ASSISTANT.SETTINGS.ASSISTANT_UPDATED');
    expect(mockUnsavedChangesGuard.notifyStateChanged).toHaveBeenCalledWith(false);
  });

  it('should display the autonomy info button when autonomyRating is set', async () => {
    const initialAssistant: AiAssistant = {
      assistant: {
        id: 'test-assistant-id',
        namespace: new Namespace({
          accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
        }),
        name: 'Test Assistant',
        type: AssistantType.ASSISTANT_TYPE_INBOX,
        avatarUrl: 'test-assistant-avatar-url',
        configurableGoals: [],
      },
      autonomyRating: 3,
    };

    const { fixture } = await setupTestBed({}, { assistant: initialAssistant });

    fixture.detectChanges();
    await fixture.whenStable();

    const infoButton = fixture.debugElement.query(By.css('.autonomy-info-button'));
    expect(infoButton).toBeTruthy();
  });

  it('should not display the autonomy info button when autonomyRating is not set', async () => {
    const initialAssistant: AiAssistant = {
      assistant: {
        id: 'test-assistant-id',
        namespace: new Namespace({
          accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
        }),
        name: 'Test Assistant',
        type: AssistantType.ASSISTANT_TYPE_INBOX,
        avatarUrl: 'test-assistant-avatar-url',
        configurableGoals: [],
      },
    };

    const { fixture } = await setupTestBed({}, { assistant: initialAssistant });

    fixture.detectChanges();
    await fixture.whenStable();

    const infoButton = fixture.debugElement.query(By.css('.autonomy-info-button'));
    expect(infoButton).toBeFalsy();
  });

  describe('onSubmit', () => {
    describe('assistant fields', () => {
      it('should call the upsertAssistant method with the correct arguments', async () => {
        const initialAssistant: AiAssistant = {
          assistant: {
            id: 'test-assistant-id',
            namespace: new Namespace({
              accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
            }),
            name: 'Test Assistant',
            type: AssistantType.ASSISTANT_TYPE_INBOX,
            avatarUrl: 'test-assistant-avatar-url',
            configurableGoals: [],
          },
        };
        await setupTestBed({}, { assistant: initialAssistant });
        const form = component.getTestAssistantForm();
        form?.controls['name'].setValue('Another Assistant Name');
        form?.controls['avatarUrl'].setValue('another-avatar-url');
        form?.markAsDirty();
        form?.controls['capabilities']?.controls.forEach((capform) => {
          capform.markAsDirty();
          capform.controls.promptModules.controls.forEach((pmForm) => pmForm.markAsDirty());
        });

        await component.testSubmit();

        expect(assistantApiService.upsertAssistant).toHaveBeenCalledWith({
          assistant: {
            id: 'test-assistant-id',
            name: 'Another Assistant Name',
            namespace: expect.any(Object),
            avatarUrl: 'another-avatar-url',
            type: AssistantType.ASSISTANT_TYPE_INBOX,
            configurableGoals: [
              expect.objectContaining({
                goal: expect.objectContaining({
                  id: 'new-capability-id',
                  namespace: expect.any(Object),
                }),
                configuration: undefined,
              }),
            ],
            config: {
              models: [],
            },
          },
          options: { applyDefaults: false },
        });

        // Additional check: verify the namespace is correct
        const callArg = assistantApiService.upsertAssistant.mock.calls[0][0];
        if (!callArg.assistant) throw new Error('assistant is undefined in upsertAssistant call');
        expect(getNamespaceObj(callArg.assistant.namespace)).toEqual({
          accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
        });
      });

      it('should call the upsertAssistant method with the correct arguments - voice assistant', async () => {
        const initialAssistant: AiAssistant = {
          assistant: {
            id: 'test-assistant-id',
            namespace: new Namespace({
              accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
            }),
            name: 'Test Assistant',
            type: AssistantType.ASSISTANT_TYPE_VOICE_RECEPTIONIST,
            avatarUrl: 'test-assistant-avatar-url',
            configurableGoals: [],
          },
        };
        await setupTestBed({}, { assistant: initialAssistant });
        const form = component.getTestAssistantForm();
        form?.controls['name'].setValue('Another Assistant Name');
        form?.controls['avatarUrl'].setValue('another-avatar-url');
        form?.markAsDirty();
        form?.controls['capabilities']?.controls.forEach((capform) => {
          capform.markAsDirty();
          capform.controls.promptModules.controls.forEach((pmForm) => pmForm.markAsDirty());
        });

        await component.testSubmit();

        expect(assistantApiService.upsertAssistant).toHaveBeenCalledWith(
          expect.objectContaining({
            assistant: expect.objectContaining({
              id: 'test-assistant-id',
              name: 'Another Assistant Name',
              namespace: expect.any(Object),
              avatarUrl: 'another-avatar-url',
              type: AssistantType.ASSISTANT_TYPE_VOICE_RECEPTIONIST,
              configurableGoals: [
                expect.objectContaining({
                  goal: expect.objectContaining({
                    id: 'new-capability-id',
                    namespace: expect.any(Object),
                  }),
                  configuration: undefined,
                }),
              ],
              config: expect.objectContaining({
                voiceConfig: expect.objectContaining({
                  modelConfig: expect.objectContaining({
                    openaiRealtimeConfig: expect.objectContaining({
                      voice: expect.any(String),
                    }),
                  }),
                  vendorModel: expect.any(Number),
                }),
              }),
            }),
            options: { applyDefaults: false },
          }),
        );

        // Additional check: verify the namespace is correct
        const callArg = assistantApiService.upsertAssistant.mock.calls[0][0];
        if (!callArg.assistant) throw new Error('assistant is undefined in upsertAssistant call');
        expect(getNamespaceObj(callArg.assistant.namespace)).toEqual({
          accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
        });
      });
    });

    describe('tools', () => {
      it('should call upsertFunction for each tool that is dirty', async () => {
        await setupTestBed();
        // Create mock tools
        const mockTool1 = new AiToolForm({
          func: {
            id: 'tool1',
            description: 'Test Tool 1',
            methodType: 'GET',
            url: 'https://test1.com',
            generatesAnswer: false,
          },
        });
        const mockTool2 = new AiToolForm({
          func: {
            id: 'tool2',
            description: 'Test Tool 2',
            methodType: 'POST',
            url: 'https://test2.com',
            generatesAnswer: true,
          },
        });
        const mockTool3 = new AiToolForm({
          func: {
            id: 'tool3',
            description: 'Test Tool 3',
            methodType: 'PUT',
            url: 'https://test3.com',
            generatesAnswer: false,
          },
        });

        // Mark some tools as dirty by modifying their values
        mockTool1.controls.description.setValue('Updated Test Tool 1');
        mockTool3.controls.description.setValue('Updated Test Tool 3');

        const namespace = { accountGroupNamespace: { accountGroupId: 'AG-1234' } };

        // Create a capability form with the tools
        const capabilityForm = new AiCapabilityForm({
          goal: {
            id: 'test-capability',
            name: 'Test Capability',
            description: 'Test Description',
            namespace: namespace,
            type: GoalType.GOAL_TYPE_CUSTOM,
            functions: [],
          },
          configuration: [],
        });

        // Add tools to capability form
        capabilityForm.controls.tools.push(mockTool1);
        capabilityForm.controls.tools.push(mockTool2);
        capabilityForm.controls.tools.push(mockTool3);

        // Add the capability form to the assistant form
        (component as any).assistantForm()?.controls.capabilities.push(capabilityForm);

        // Mock the aiAssistantService
        const mockUpsertFunction = jest.fn();
        aiAssistantService.upsertFunction = mockUpsertFunction;

        // Call testSubmit
        await component.testSubmit();

        // Verify upsertFunction was called only for dirty tools
        expect(mockUpsertFunction).toHaveBeenCalledTimes(2);
        expect(mockUpsertFunction).toHaveBeenCalledWith(mockTool1.toFunction(namespace));
        expect(mockUpsertFunction).toHaveBeenCalledWith(mockTool3.toFunction(namespace));
        expect(mockUpsertFunction).not.toHaveBeenCalledWith(mockTool2.toFunction(namespace));
      });

      it('should not call upsertFunction when tools exist but are not dirty', async () => {
        await setupTestBed();
        // Create mock tools (none will be dirty)
        const mockTool1 = new AiToolForm({
          func: {
            id: 'tool1',
            description: 'Test Tool 1',
            methodType: 'GET',
            url: 'https://test1.com',
            generatesAnswer: false,
          },
        });
        const mockTool2 = new AiToolForm({
          func: {
            id: 'tool2',
            description: 'Test Tool 2',
            methodType: 'POST',
            url: 'https://test2.com',
            generatesAnswer: true,
          },
        });
        const mockTool3 = new AiToolForm({
          func: {
            id: 'tool3',
            description: 'Test Tool 3',
            methodType: 'PUT',
            url: 'https://test3.com',
            generatesAnswer: false,
          },
        });

        const namespace = { accountGroupNamespace: { accountGroupId: 'AG-1234' } };

        // Create a capability form with the tools
        const capabilityForm = new AiCapabilityForm({
          goal: {
            id: 'test-capability',
            name: 'Test Capability',
            description: 'Test Description',
            namespace: namespace,
            type: GoalType.GOAL_TYPE_CUSTOM,
            functions: [],
          },
          configuration: [],
        });
        capabilityForm.controls.tools.push(mockTool1);
        capabilityForm.controls.tools.push(mockTool2);
        capabilityForm.controls.tools.push(mockTool3);

        // Add the capability form to the assistant form
        (component as any).assistantForm()?.controls.capabilities.push(capabilityForm);

        // Mock the aiAssistantService
        const mockUpsertFunction = jest.fn();
        aiAssistantService.upsertFunction = mockUpsertFunction;

        // Call testSubmit
        await component.testSubmit();

        // Verify upsertFunction was not called
        expect(mockUpsertFunction).not.toHaveBeenCalled();
      });

      it('should fork a global capability to a local capability when adding a pre-existing global tool to the global capability', async () => {
        // Prepare a global capability with no tools initially
        const globalNamespace = { globalNamespace: {} };
        const localNamespace = { accountGroupNamespace: { accountGroupId: 'AG-1234' } };
        const initialAssistant = {
          assistant: {
            id: 'test-assistant-id',
            namespace: new Namespace({
              accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
            }),
            name: 'Test Assistant',
            type: 2,
            avatarUrl: 'test-assistant-avatar-url',
            configurableGoals: [
              new ConfigurableGoal({
                goal: {
                  id: 'DefaultPersonality',
                  name: 'DefaultPersonality',
                  description: 'DefaultPersonality',
                  namespace: globalNamespace,
                  type: GoalType.GOAL_TYPE_PERSONALITY,
                  functions: [], // No tools initially
                  promptModules: [],
                },
              }),
              new ConfigurableGoal({
                goal: {
                  id: 'global-lead-capture',
                  name: 'Global Lead Capture',
                  description: 'Global lead capture capability',
                  namespace: globalNamespace,
                  type: GoalType.GOAL_TYPE_CUSTOM,
                  functions: [], // No tools initially
                  promptModules: [],
                },
                configuration: [],
              }),
            ],
          },
        };

        // Set up mocks
        const mockUpsertFunction = jest.fn();
        const mockUpsertCapability = jest.fn().mockReturnValue(Promise.resolve('new-local-capability-id'));

        const { fixture } = await setupTestBed(
          {
            aiAssistantService: {
              upsertFunction: mockUpsertFunction,
              upsertCapability: mockUpsertCapability,
              getMultiPromptModuleVersions: jest.fn().mockReturnValue(Promise.resolve({})),
            },
          },
          { assistant: initialAssistant },
        );

        // Wait for the assistantForm signal to be populated
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        const form = (await waitForFormToBePopulated(component))!;
        expect(form).toBeTruthy();
        fixture.detectChanges();
        await fixture.whenStable();

        const capabilityForm = form.controls.capabilities.at(1);

        // Verify initial state - should be global with no tools
        expect(capabilityForm.controls.namespace.getRawValue()).toEqual(globalNamespace);
        expect(capabilityForm.controls.goalId.getRawValue()).toBe('global-lead-capture');
        expect(capabilityForm.controls.tools.length).toBe(0);

        // Add a pre-existing tool to the global capability
        const existingTool = new AiToolForm({
          func: {
            id: 'existing-crm-tool-id',
            description: 'Existing CRM Tool',
            methodType: 'POST',
            url: 'https://crm.example.com/api/leads',
            generatesAnswer: false,
            namespace: globalNamespace,
          },
          metadata: { isNew: false },
        });
        capabilityForm.controls.tools.push(existingTool);

        // Mark the capability as dirty (adding a tool should trigger this)
        capabilityForm.markAsDirty();

        // Call testSubmit
        await component.testSubmit();

        // Verify upsertFunction was NOT called since the tool already exists
        expect(mockUpsertFunction).not.toHaveBeenCalled();

        // Verify upsertCapability was called with LOCAL namespace because tool was added
        // Called twice: once for personality capability, once for the modified global capability
        expect(mockUpsertCapability).toHaveBeenCalledTimes(1);

        // The modified global capability is called first (index 0)
        const capabilityCallArg = mockUpsertCapability.mock.calls[0][0];
        expect(capabilityCallArg.namespace).toEqual(localNamespace); // Should be LOCAL namespace
        expect(capabilityCallArg.id).toBeUndefined(); // Should be undefined for new goal
        expect(capabilityCallArg.name).toBe('Global Lead Capture');
        expect(capabilityCallArg.description).toBe('Global lead capture capability');
        expect(capabilityCallArg.overrideOf).toEqual({
          id: 'global-lead-capture',
          namespace: globalNamespace,
        });

        // Verify form state was updated to local namespace after submission
        expect(capabilityForm.controls.namespace.getRawValue()).toEqual(localNamespace);
        expect(capabilityForm.controls.goalId.getRawValue()).toBe('new-local-capability-id');
        expect(capabilityForm.controls.tools.length).toBe(1);
        expect(capabilityForm.controls.tools.length).toBe(1);
        expect(capabilityForm.controls.tools.at(0).controls.namespace.getRawValue()).toBe(globalNamespace);
      });

      it('should fork a global capability to a local capability when adding a pre-existing local tool to the global capability', async () => {
        // Prepare a global capability with no tools initially
        const globalNamespace = { globalNamespace: {} };
        const agNamespace = { accountGroupNamespace: { accountGroupId: 'AG-1234' } };
        const initialAssistant = {
          assistant: {
            id: 'test-assistant-id',
            namespace: new Namespace({
              accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
            }),
            name: 'Test Assistant',
            type: 2,
            avatarUrl: 'test-assistant-avatar-url',
            configurableGoals: [
              new ConfigurableGoal({
                goal: {
                  id: 'DefaultPersonality',
                  name: 'DefaultPersonality',
                  description: 'DefaultPersonality',
                  namespace: globalNamespace,
                  type: GoalType.GOAL_TYPE_PERSONALITY,
                  functions: [], // No tools initially
                  promptModules: [],
                },
              }),
              new ConfigurableGoal({
                goal: {
                  id: 'global-lead-capture',
                  name: 'Global Lead Capture',
                  description: 'Global lead capture capability',
                  namespace: globalNamespace,
                  type: GoalType.GOAL_TYPE_CUSTOM,
                  functions: [], // No tools initially
                  promptModules: [],
                },
                configuration: [],
              }),
            ],
          },
        };

        // Set up mocks
        const mockUpsertFunction = jest.fn();
        const mockUpsertCapability = jest.fn().mockReturnValue(Promise.resolve('new-local-capability-id'));

        const { fixture } = await setupTestBed(
          {
            aiAssistantService: {
              upsertFunction: mockUpsertFunction,
              upsertCapability: mockUpsertCapability,
              getMultiPromptModuleVersions: jest.fn().mockReturnValue(Promise.resolve({})),
            },
          },
          { assistant: initialAssistant },
        );

        // Wait for the assistantForm signal to be populated
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        const form = (await waitForFormToBePopulated(component))!;
        expect(form).toBeTruthy();
        fixture.detectChanges();
        await fixture.whenStable();

        const capabilityForm = form.controls.capabilities.at(1);

        // Verify initial state - should be global with no tools
        expect(capabilityForm.controls.namespace.getRawValue()).toEqual(globalNamespace);
        expect(capabilityForm.controls.goalId.getRawValue()).toBe('global-lead-capture');
        expect(capabilityForm.controls.tools.length).toBe(0);

        // Add a pre-existing tool to the global capability
        const existingTool = new AiToolForm({
          func: {
            id: 'existing-crm-tool-id',
            description: 'Existing CRM Tool',
            methodType: 'POST',
            url: 'https://crm.example.com/api/leads',
            generatesAnswer: false,
            namespace: agNamespace,
          },
          metadata: { isNew: false },
        });
        capabilityForm.controls.tools.push(existingTool);

        // Mark the capability as dirty (adding a tool should trigger this)
        capabilityForm.markAsDirty();

        // Call testSubmit
        await component.testSubmit();

        // Verify upsertFunction was NOT called since the tool already exists
        expect(mockUpsertFunction).not.toHaveBeenCalled();

        // Verify upsertCapability was called with LOCAL namespace because tool was added
        // Called twice: once for personality capability, once for the modified global capability
        expect(mockUpsertCapability).toHaveBeenCalledTimes(1);
        const capabilityCallArg = mockUpsertCapability.mock.calls[0][0]; // Use the first call (modified global capability)
        expect(capabilityCallArg.namespace).toEqual(agNamespace); // Should be LOCAL namespace
        expect(capabilityCallArg.id).toBeUndefined(); // Should be undefined for new goal
        expect(capabilityCallArg.name).toBe('Global Lead Capture');
        expect(capabilityCallArg.description).toBe('Global lead capture capability');
        expect(capabilityCallArg.overrideOf).toEqual({
          id: 'global-lead-capture',
          namespace: globalNamespace,
        });

        // Verify form state was updated to local namespace after submission
        expect(capabilityForm.controls.namespace.getRawValue()).toEqual(agNamespace);
        expect(capabilityForm.controls.goalId.getRawValue()).toBe('new-local-capability-id');
        expect(capabilityForm.controls.tools.length).toBe(1);
        expect(capabilityForm.controls.tools.length).toBe(1);
        expect(capabilityForm.controls.tools.at(0).controls.namespace.getRawValue()).toBe(agNamespace);
      });
    });

    describe('prompt modules', () => {
      it('upsertPromptModule should be called for each prompt that is modified', async () => {
        // Prepare prompt modules in the initial assistant state
        const namespace = { accountGroupNamespace: { accountGroupId: 'AG-1234' } };
        const initialAssistant = {
          assistant: {
            id: 'test-assistant-id',
            namespace: new Namespace({
              accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
            }),
            name: 'Test Assistant',
            type: 2,
            avatarUrl: 'test-assistant-avatar-url',
            configurableGoals: [
              new ConfigurableGoal({
                goal: {
                  id: 'test-capability',
                  name: 'Test Capability',
                  description: 'Test Description',
                  namespace: namespace,
                  type: GoalType.GOAL_TYPE_CUSTOM,
                  functions: [],
                  promptModules: [
                    new PromptModule({
                      id: 'prompt1',
                      namespace: namespace,
                      deployedVersion: '1',
                    }),
                    new PromptModule({
                      id: 'prompt2',
                      namespace: namespace,
                      deployedVersion: '1',
                    }),
                    new PromptModule({
                      id: 'prompt3',
                      namespace: namespace,
                      deployedVersion: '1',
                    }),
                  ],
                },
                configuration: [],
              }),
            ],
          },
        };

        // Set up the mock before component initialization
        const mockUpsertPromptModule = jest.fn().mockReturnValue(Promise.resolve({ id: 'test-prompt-module-id' }));
        const promptModuleContents = {
          prompt1: 'Original Prompt 1',
          prompt2: 'Original Prompt 2',
          prompt3: 'Original Prompt 3',
        };
        const { fixture } = await setupTestBed(
          {
            aiAssistantService: {
              upsertPromptModule: mockUpsertPromptModule,
              getMultiPromptModuleVersions: jest.fn().mockReturnValue(
                Promise.resolve({
                  prompt1: 'Original Prompt 1',
                  prompt2: 'Original Prompt 2',
                  prompt3: 'Original Prompt 3',
                }),
              ),
            },
          },
          { assistant: initialAssistant, promptModuleContents },
        );

        // Wait for the assistantForm signal to be populated
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        const form = (await waitForFormToBePopulated(component))!;
        expect(form).toBeTruthy();
        fixture.detectChanges();
        fixture.whenStable();

        const capabilityForm = form.controls.capabilities.at(0);
        const promptModules = capabilityForm.controls.promptModules;

        if (!promptModules) {
          throw new Error('Prompt modules array is undefined');
        }

        // Update the prompt modules to simulate user changes
        const prompt1 = promptModules.controls[0];
        const prompt2 = promptModules.controls.length > 1 ? promptModules.controls[1] : null;
        const prompt3 = promptModules.controls.length > 2 ? promptModules.controls[2] : null;

        prompt1.controls.instructions.setValue('Updated Prompt 1'); // should be dirty
        if (prompt2) {
          prompt2.controls.instructions.setValue('Original Prompt 2'); // should NOT be dirty
        }
        if (prompt3) {
          prompt3.controls.instructions.setValue('Updated Prompt 3'); // should be dirty
        }

        // Call testSubmit
        await component.testSubmit();

        // Verify upsertPromptModule was called only for dirty prompt modules
        const expectedCalls = prompt3 ? 2 : 1; // Depends on how many prompt modules exist
        expect(mockUpsertPromptModule).toHaveBeenCalledTimes(expectedCalls);
        expect(mockUpsertPromptModule).toHaveBeenCalledWith(
          {
            id: undefined,
            name: 'Test Assistant Personality',
            namespace: expect.any(Object),
            managed: false,
          },
          'Updated Prompt 1',
        );
        if (prompt3) {
          expect(mockUpsertPromptModule).toHaveBeenCalledWith(
            {
              id: undefined,
              name: 'Test Assistant Personality',
              namespace: expect.any(Object),
              managed: false,
            },
            'Updated Prompt 3',
          );
        }
        if (prompt2) {
          expect(mockUpsertPromptModule).not.toHaveBeenCalledWith(
            expect.objectContaining({ id: 'prompt2' }),
            expect.any(String),
          );
        }

        // Verify the form state after submission
        expect(promptModules.at(0)?.controls.instructions.dirty).toBe(false);
        if (promptModules.at(1)) {
          expect(promptModules.at(1)?.controls.instructions.dirty).toBe(false);
        }
        if (promptModules.at(2)) {
          expect(promptModules.at(2)?.controls.instructions.dirty).toBe(false);
        }
      });

      it('upsertPromptModule should not be called when no prompt modules have been modified', async () => {
        // Prepare prompt modules in the initial assistant state
        const namespace = { accountGroupNamespace: { accountGroupId: 'AG-1234' } };
        const initialAssistant = {
          assistant: {
            id: 'test-assistant-id',
            namespace: new Namespace({
              accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
            }),
            name: 'Test Assistant',
            type: 2,
            avatarUrl: 'test-assistant-avatar-url',
            configurableGoals: [
              new ConfigurableGoal({
                goal: {
                  id: 'test-capability',
                  name: 'Test Capability',
                  description: 'Test Description',
                  namespace: namespace,
                  type: GoalType.GOAL_TYPE_CUSTOM,
                  functions: [],
                  promptModules: [
                    new PromptModule({
                      id: 'prompt1',
                      namespace: namespace,
                      deployedVersion: '1',
                    }),
                    new PromptModule({
                      id: 'prompt2',
                      namespace: namespace,
                      deployedVersion: '1',
                    }),
                  ],
                },
                configuration: [],
              }),
            ],
          },
        };

        // Set up the mock before component initialization
        const mockUpsertPromptModule = jest.fn().mockReturnValue(Promise.resolve({ id: 'test-prompt-module-id' }));
        const promptModuleContents2 = {
          prompt1: 'Original Prompt 1',
          prompt2: 'Original Prompt 2',
        };
        await setupTestBed(
          {
            aiAssistantService: {
              upsertPromptModule: mockUpsertPromptModule,
              getMultiPromptModuleVersions: jest.fn().mockReturnValue(
                Promise.resolve({
                  prompt1: 'Original Prompt 1',
                  prompt2: 'Original Prompt 2',
                }),
              ),
            },
          },
          { assistant: initialAssistant, promptModuleContents: promptModuleContents2 },
        );

        // Wait for the assistantForm signal to be populated
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        const form = (await waitForFormToBePopulated(component))!;
        expect(form).toBeTruthy();

        const capabilityForm = form.controls.capabilities.at(0);
        const promptModules = capabilityForm.controls.promptModules;

        if (!promptModules) {
          throw new Error('Prompt modules array is undefined');
        }

        // Set value, but it matches the original contents (no modifications)
        const prompt1 = promptModules.controls[0];
        const prompt2 = promptModules.controls.length > 1 ? promptModules.controls[1] : null;
        prompt1.controls.instructions.setValue('Original Prompt 1');
        if (prompt2) {
          prompt2.controls.instructions.setValue('Original Prompt 2');
        }

        // Call testSubmit
        await component.testSubmit();

        // Verify upsertPromptModule was called once for the automatically created personality capability
        expect(mockUpsertPromptModule).toHaveBeenCalledTimes(1);
        expect(mockUpsertPromptModule).toHaveBeenCalledWith(
          {
            id: undefined,
            name: 'Test Assistant Personality',
            namespace: expect.any(Object),
            managed: false,
          },
          'Original Prompt 1',
        );

        // Verify the form state after submission
        expect(promptModules.at(0)?.controls.instructions.dirty).toBe(false);
        if (promptModules.at(1)) {
          expect(promptModules.at(1)?.controls.instructions.dirty).toBe(false);
        }
      });

      it('upsertPromptModule should not be called for the same changes twice if submit is clicked twice', async () => {
        // Prepare prompt modules in the initial assistant state
        const namespace = { accountGroupNamespace: { accountGroupId: 'AG-1234' } };
        const initialAssistant = {
          assistant: {
            id: 'test-assistant-id',
            namespace: new Namespace({
              accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
            }),
            name: 'Test Assistant',
            type: 2,
            avatarUrl: 'test-assistant-avatar-url',
            configurableGoals: [
              new ConfigurableGoal({
                goal: {
                  id: 'test-capability',
                  name: 'Test Capability',
                  description: 'Test Description',
                  namespace: namespace,
                  type: GoalType.GOAL_TYPE_CUSTOM,
                  functions: [],
                  promptModules: [
                    new PromptModule({
                      id: 'prompt1',
                      namespace: namespace,
                      deployedVersion: '1',
                    }),
                    new PromptModule({
                      id: 'prompt2',
                      namespace: namespace,
                      deployedVersion: '1',
                    }),
                  ],
                },
                configuration: [],
              }),
            ],
          },
        };

        // Set up the mock before component initialization
        const mockUpsertPromptModule = jest.fn().mockReturnValue(Promise.resolve({ id: 'test-prompt-module-id' }));
        const promptModuleContents = {
          prompt1: 'Original Prompt 1',
          prompt2: 'Original Prompt 2',
        };
        await setupTestBed(
          {
            aiAssistantService: {
              upsertPromptModule: mockUpsertPromptModule,
              getMultiPromptModuleVersions: jest.fn().mockReturnValue(
                Promise.resolve({
                  prompt1: 'Original Prompt 1',
                  prompt2: 'Original Prompt 2',
                }),
              ),
            },
          },
          { assistant: initialAssistant, promptModuleContents },
        );

        // Wait for the assistantForm signal to be populated
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        const form = (await waitForFormToBePopulated(component))!;
        expect(form).toBeTruthy();

        const capabilityForm = form.controls.capabilities.at(0);
        const promptModules = capabilityForm.controls.promptModules;
        if (!promptModules) {
          throw new Error('Prompt modules array is undefined');
        }

        // Update the prompt modules to simulate user changes
        const prompt1 = promptModules.controls[0];
        const prompt2 = promptModules.controls.length > 1 ? promptModules.controls[1] : null;
        prompt1.controls.instructions.setValue('Updated Prompt 1'); // should be dirty
        if (prompt2) {
          prompt2.controls.instructions.setValue('Original Prompt 2'); // should NOT be dirty
        }

        // First submit: should call upsertPromptModule for prompt1 only
        await component.testSubmit();
        expect(mockUpsertPromptModule).toHaveBeenCalledTimes(1);
        expect(mockUpsertPromptModule).toHaveBeenCalledWith(
          {
            id: undefined,
            name: 'Test Assistant Personality',
            namespace: expect.any(Object),
            managed: false,
          },
          'Updated Prompt 1',
        );
        expect(mockUpsertPromptModule).not.toHaveBeenCalledWith(
          expect.objectContaining({ id: 'prompt2' }),
          expect.any(String),
        );

        // Second submit: should NOT call upsertPromptModule again
        await component.testSubmit();
        expect(mockUpsertPromptModule).toHaveBeenCalledTimes(1);
      });

      it('should fork global prompt module and goal to local namespace when global prompt module is updated', async () => {
        // Prepare a global goal with a global prompt module
        const globalNamespace = { globalNamespace: {} };
        const localNamespace = { accountGroupNamespace: { accountGroupId: 'AG-1234' } };
        const initialAssistant = {
          assistant: {
            id: 'test-assistant-id',
            namespace: new Namespace({
              accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
            }),
            name: 'Test Assistant',
            type: 2,
            avatarUrl: 'test-assistant-avatar-url',
            configurableGoals: [
              new ConfigurableGoal({
                goal: {
                  id: 'DefaultPersonality',
                  name: 'DefaultPersonality',
                  description: 'DefaultPersonality',
                  namespace: globalNamespace,
                  type: GoalType.GOAL_TYPE_PERSONALITY,
                  functions: [], // No tools initially
                  promptModules: [],
                },
              }),
              new ConfigurableGoal({
                goal: {
                  id: 'lead-capture',
                  name: 'Lead Capture',
                  description: 'Lead Capture Goal Description',
                  namespace: globalNamespace,
                  type: GoalType.GOAL_TYPE_CUSTOM,
                  functions: [],
                  promptModules: [
                    new PromptModule({
                      id: 'global-prompt',
                      namespace: globalNamespace,
                      deployedVersion: '1',
                    }),
                  ],
                },
                configuration: [],
              }),
            ],
          },
        };

        // Set up mocks
        const mockUpsertPromptModule = jest.fn().mockReturnValue(Promise.resolve('new-local-prompt-id'));
        const mockUpsertCapability = jest.fn().mockReturnValue(Promise.resolve('new-local-capability-id'));
        const promptModuleContents = {
          'global-prompt': 'Original Prompt Content',
        };

        const { fixture } = await setupTestBed(
          {
            aiAssistantService: {
              upsertPromptModule: mockUpsertPromptModule,
              upsertCapability: mockUpsertCapability,
              getMultiPromptModuleVersions: jest.fn().mockReturnValue(
                Promise.resolve({
                  'global-prompt': 'Original Prompt Content',
                }),
              ),
            },
          },
          { assistant: initialAssistant, promptModuleContents },
        );

        // Wait for the assistantForm signal to be populated
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        const form = (await waitForFormToBePopulated(component))!;
        expect(form).toBeTruthy();
        fixture.detectChanges();
        await fixture.whenStable();

        const capabilityForm = form.controls.capabilities.at(1);
        const promptModules = capabilityForm.controls.promptModules;

        if (!promptModules) {
          throw new Error('Prompt modules array is undefined');
        }

        // Verify initial state - should be global
        expect(capabilityForm.controls.namespace.getRawValue()).toEqual(globalNamespace);
        expect(promptModules.controls[0].controls.namespace.getRawValue()).toEqual(globalNamespace);

        // Update the global prompt module to simulate user changes
        const globalPrompt = promptModules.controls[0];
        globalPrompt.controls.instructions.setValue('Updated Prompt Content');

        // Call testSubmit
        await component.testSubmit();

        // Verify upsertPromptModule was called with LOCAL namespace
        expect(mockUpsertPromptModule).toHaveBeenCalledTimes(1);
        expect(mockUpsertPromptModule).toHaveBeenCalledWith(
          {
            id: undefined, // Should be undefined for new prompt module
            name: 'Lead Capture',
            namespace: localNamespace, // Should be LOCAL namespace
            managed: false,
          },
          'Updated Prompt Content',
        );

        // Verify upsertCapability was called with LOCAL namespace
        expect(mockUpsertCapability).toHaveBeenCalledTimes(1);
        const capabilityCallArg = mockUpsertCapability.mock.calls[0][0]; // Use the first call (modified global capability)
        expect(capabilityCallArg.namespace).toEqual(localNamespace); // Should be LOCAL namespace
        expect(capabilityCallArg.id).toBeUndefined();
        expect(capabilityCallArg.name).toBe('Lead Capture');
        expect(capabilityCallArg.description).toBe('Lead Capture Goal Description');
        expect(capabilityCallArg.overrideOf).toEqual({
          id: 'lead-capture',
          namespace: globalNamespace,
        });

        // Verify form state was updated to local namespace after submission
        expect(capabilityForm.controls.namespace.getRawValue()).toEqual(localNamespace);
        expect(promptModules.controls[0].controls.namespace.getRawValue()).toEqual(localNamespace);
        expect(promptModules.controls[0].controls.id.getRawValue()).toBe('new-local-prompt-id');
        expect(capabilityForm.controls.goalId.getRawValue()).toBe('new-local-capability-id');
      });
    });

    describe('capabilities', () => {
      it('should fork a global capability to a local capability when modified', async () => {
        // Prepare a global capability
        const globalNamespace = { globalNamespace: {} };
        const localNamespace = { accountGroupNamespace: { accountGroupId: 'AG-1234' } };
        const initialAssistant = {
          assistant: {
            id: 'test-assistant-id',
            namespace: new Namespace({
              accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
            }),
            name: 'Test Assistant',
            type: 2,
            avatarUrl: 'test-assistant-avatar-url',
            configurableGoals: [
              new ConfigurableGoal({
                goal: {
                  id: 'global-sales-capability',
                  name: 'Global Sales Capability',
                  description: 'A global sales capability',
                  namespace: globalNamespace,
                  type: GoalType.GOAL_TYPE_CUSTOM,
                  functions: [],
                  promptModules: [],
                },
                configuration: [],
              }),
            ],
          },
        };

        // Set up mocks
        const mockUpsertCapability = jest.fn().mockReturnValue(Promise.resolve('new-local-capability-id'));

        const { fixture } = await setupTestBed(
          {
            aiAssistantService: {
              upsertCapability: mockUpsertCapability,
              getMultiPromptModuleVersions: jest.fn().mockReturnValue(Promise.resolve({})),
            },
          },
          { assistant: initialAssistant },
        );

        // Wait for the assistantForm signal to be populated
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        const form = (await waitForFormToBePopulated(component))!;
        expect(form).toBeTruthy();
        fixture.detectChanges();
        await fixture.whenStable();

        // The form now automatically creates a personality capability at index 0, so our sales capability is at index 1
        const capabilityForm = form.controls.capabilities.at(1);

        // Verify initial state - should be global
        expect(capabilityForm.controls.namespace.getRawValue()).toEqual(globalNamespace);
        expect(capabilityForm.controls.goalId.getRawValue()).toBe('global-sales-capability');

        // Modify the global capability directly (name and description)
        capabilityForm.controls.name.setValue('Updated Sales Capability');
        capabilityForm.controls.description.setValue('Updated sales capability description');
        // Mark the capability as dirty so it gets processed
        capabilityForm.markAsDirty();
        capabilityForm.controls.promptModules.controls.forEach((pmForm) => pmForm.markAsDirty());

        // Call testSubmit
        await component.testSubmit();

        // Verify upsertCapability was called with LOCAL namespace
        // Should be called at least once for the modified global capability (personality capability may or may not be called)
        expect(mockUpsertCapability).toHaveBeenCalledWith(
          expect.objectContaining({
            namespace: localNamespace,
            id: undefined, // Should be undefined for new capability (this is the input argument)
            name: 'Updated Sales Capability',
            description: 'Updated sales capability description',
            overrideOf: {
              id: 'global-sales-capability',
              namespace: globalNamespace,
            },
          }),
        );
        // Get the call for the modified global capability (find it by name)
        const capabilityCallArg = mockUpsertCapability.mock.calls.find(
          (call) => call[0].name === 'Updated Sales Capability',
        )?.[0];
        expect(capabilityCallArg.namespace).toEqual(localNamespace); // Should be LOCAL namespace
        expect(capabilityCallArg.id).toBeUndefined(); // Should be undefined for new goal (this is the input to upsertCapability)
        expect(capabilityCallArg.name).toBe('Updated Sales Capability');
        expect(capabilityCallArg.description).toBe('Updated sales capability description');
        expect(capabilityCallArg.overrideOf).toEqual({
          id: 'global-sales-capability',
          namespace: globalNamespace,
        });

        // Verify form state was updated to local namespace after submission
        expect(capabilityForm.controls.namespace.getRawValue()).toEqual(localNamespace);
        expect(capabilityForm.controls.goalId.getRawValue()).toBe('new-local-capability-id');
      });

      it('should NOT fork a global capability when only configuration is changed', async () => {
        // Prepare a global capability
        const globalNamespace = { globalNamespace: {} };
        const initialAssistant = {
          assistant: {
            id: 'test-assistant-id',
            namespace: new Namespace({
              accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
            }),
            name: 'Test Assistant',
            type: 2,
            avatarUrl: 'test-assistant-avatar-url',
            configurableGoals: [
              new ConfigurableGoal({
                goal: {
                  id: 'global-sales-capability',
                  name: 'Global Sales Capability',
                  description: 'A global sales capability',
                  namespace: globalNamespace,
                  type: GoalType.GOAL_TYPE_CUSTOM,
                  functions: [],
                  promptModules: [],
                },
                configuration: [new KeyValuePair({ key: 'foo', value: 'bar' })],
              }),
            ],
          },
        };

        // Set up mocks
        const mockUpsertCapability = jest.fn().mockReturnValue(Promise.resolve('should-not-be-called'));

        const { fixture } = await setupTestBed(
          {
            aiAssistantService: {
              upsertCapability: mockUpsertCapability,
              getMultiPromptModuleVersions: jest.fn().mockReturnValue(Promise.resolve({})),
            },
          },
          { assistant: initialAssistant },
        );

        // Wait for the assistantForm signal to be populated
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        const form = (await waitForFormToBePopulated(component))!;
        expect(form).toBeTruthy();
        fixture.detectChanges();
        await fixture.whenStable();

        // The form now automatically creates a personality capability at index 0, so our sales capability is at index 1
        const capabilityForm = form.controls.capabilities.at(1);

        // Verify initial state - should be global
        expect(capabilityForm.controls.namespace.getRawValue()).toEqual(globalNamespace);
        expect(capabilityForm.controls.goalId.getRawValue()).toBe('global-sales-capability');

        // Only update the configuration (not name/description/type)
        capabilityForm.controls.configuration.setValue([new KeyValuePair({ key: 'foo', value: 'baz' })]);
        capabilityForm.controls.configuration.markAsDirty();

        // Call testSubmit
        await component.testSubmit();

        // upsertCapability should not be called for the global capability since only configuration changed
        // (personality capability may or may not be called, but we don't expect the global capability to be forked)
        const salesCapabilityCalls = mockUpsertCapability.mock.calls.filter(
          (call) => call[0].name === 'Global Sales Capability' || call[0].overrideOf?.id === 'global-sales-capability',
        );
        expect(salesCapabilityCalls).toHaveLength(0);

        // The goalId and namespace should remain unchanged
        expect(capabilityForm.controls.namespace.getRawValue()).toEqual(globalNamespace);
        expect(capabilityForm.controls.goalId.getRawValue()).toBe('global-sales-capability');

        // upsertAssistant should be called with the updated configuration
        expect(assistantApiService.upsertAssistant).toHaveBeenCalled();
        const upsertArg = assistantApiService.upsertAssistant.mock.calls[0][0];
        expect(upsertArg).toBeDefined();
        expect(upsertArg?.assistant).toBeDefined();
        expect(upsertArg?.assistant?.configurableGoals).toBeDefined();
        // Find the correct goal in configurableGoals
        const updatedGoal = upsertArg?.assistant?.configurableGoals?.find(
          (g: any) => g.goal.id === 'global-sales-capability',
        );
        expect(updatedGoal).toBeDefined();
        expect(updatedGoal && updatedGoal.configuration).toEqual([
          expect.objectContaining({ key: 'foo', value: 'baz' }),
        ]);
      });

      it('should be able to rollback a capability that has overrideOf field defined', async () => {
        // Prepare a local capability that overrides a global capability
        const globalNamespace = { globalNamespace: {} };
        const localNamespace = { accountGroupNamespace: { accountGroupId: 'AG-1234' } };
        const initialAssistant = {
          assistant: {
            id: 'test-assistant-id',
            namespace: new Namespace({
              accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
            }),
            name: 'Test Assistant',
            type: 2,
            avatarUrl: 'test-assistant-avatar-url',
            configurableGoals: [
              new ConfigurableGoal({
                goal: {
                  id: 'local-override-capability',
                  name: 'Local Override Capability',
                  description: 'A local override of global capability',
                  namespace: localNamespace,
                  type: GoalType.GOAL_TYPE_CUSTOM,
                  functions: [],
                  promptModules: [],
                  overrideOf: {
                    id: 'global-sales-capability',
                    namespace: globalNamespace,
                  },
                },
                configuration: [],
              }),
            ],
          },
        };

        // Set up mocks for the rollback functionality
        const mockGetGoal = jest.fn().mockReturnValue(
          of({
            id: 'global-sales-capability',
            name: 'Global Sales Capability',
            description: 'Original global sales capability',
            namespace: globalNamespace,
            type: GoalType.GOAL_TYPE_CUSTOM,
            functions: [],
            promptModules: [],
          }),
        );

        const mockConfirmationModal = {
          openModal: jest.fn().mockReturnValue(of(true)),
        };

        const { fixture } = await setupTestBed(
          {
            aiAssistantService: {
              getGoal: mockGetGoal,
              getMultiPromptModuleVersions: jest.fn().mockReturnValue(Promise.resolve({})),
            },
          },
          { assistant: initialAssistant },
        );

        // Replace the confirmation modal with our mock
        (component as any).confirmationModal = mockConfirmationModal;

        // Wait for the assistantForm signal to be populated
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        const form = (await waitForFormToBePopulated(component))!;
        expect(form).toBeTruthy();
        fixture.detectChanges();
        await fixture.whenStable();

        // Find the capability with overrideOf field (it might not be at index 0 due to automatic personality capability creation)
        const capabilityForm =
          form.controls.capabilities.controls.find((cap) => cap.controls.overrideOf.getRawValue() !== null) ||
          form.controls.capabilities.at(0);

        // Verify initial state - should be local with overrideOf
        expect(capabilityForm.controls.namespace.getRawValue()).toEqual(expect.any(Object));
        expect(capabilityForm.controls.overrideOf.getRawValue()).toEqual(
          expect.objectContaining({
            id: 'global-sales-capability',
            namespace: globalNamespace,
          }),
        );

        // Modify the capability to make it dirty
        capabilityForm.controls.name.setValue('Modified Override Capability');
        capabilityForm.controls.description.setValue('Modified description');

        // Call the rollback method through the test class
        component.testHandleCapabilityRollback(capabilityForm);

        // Wait for the async operations to complete
        await fixture.whenStable();

        // Verify getGoal was called with the correct overrideOf values
        expect(mockGetGoal).toHaveBeenCalledWith('global-sales-capability', globalNamespace);

        // Verify the form was updated with the original global values
        expect(capabilityForm.controls.name.getRawValue()).toBe('Global Sales Capability');
        expect(capabilityForm.controls.description.getRawValue()).toBe('Original global sales capability');
        expect(capabilityForm.controls.namespace.getRawValue()).toEqual(globalNamespace);
        expect(capabilityForm.controls.goalId.getRawValue()).toBe('global-sales-capability');
        expect(capabilityForm.controls.overrideOf.getRawValue()).toBeNull();

        // Verify the form is no longer dirty
        expect(capabilityForm.dirty).toBe(false);
        expect(capabilityForm.controls.promptModules.dirty).toBe(false);
        expect(capabilityForm.controls.tools.dirty).toBe(false);
      });
    });
  });

  describe('when adding and editing tools', () => {
    const setupToolTestBed = async () => {
      const namespace = { accountGroupNamespace: { accountGroupId: 'AG-1234' } };
      const initialAssistant = {
        assistant: {
          id: 'test-assistant-id',
          namespace: new Namespace({
            accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
          }),
          name: 'Test Assistant',
          type: 2,
          avatarUrl: 'test-assistant-avatar-url',
          configurableGoals: [
            new ConfigurableGoal({
              goal: {
                id: 'test-capability',
                name: 'Test Capability',
                description: 'Test Description',
                namespace: namespace,
                type: GoalType.GOAL_TYPE_CUSTOM,
                functions: [],
                promptModules: [],
              },
              configuration: [],
            }),
          ],
        },
      };
      await setupTestBed({}, { assistant: initialAssistant });
      const form = component.getTestAssistantForm();
      expect(form).toBeTruthy();
      const capabilityForm = form!.controls.capabilities.at(0);
      const toolsArray = capabilityForm.controls.tools;
      return { form, capabilityForm, toolsArray, namespace, initialAssistant };
    };

    interface TestCase {
      name: string;
      setup: (
        component: TestAiAssistantFormV2Component,
        capabilityForm: AiCapabilityForm,
      ) => Promise<{
        toolForm: AiToolForm;
        updatedToolForm?: AiToolForm;
      }>;
      checkExpectations: (
        toolsArray: AiToolFormArray,
        toolData: { toolForm: AiToolForm; updatedToolForm?: AiToolForm },
      ) => void;
    }

    const testCases: TestCase[] = [
      {
        name: 'should be able to add a new tool to a capability',
        setup: async (component, capabilityForm) => {
          const toolForm = new AiToolForm({
            func: {
              id: 'test_tool',
              description: 'Test Tool',
              methodType: 'GET',
              url: 'https://example.com',
              generatesAnswer: false,
            },
            metadata: { isNew: true, localId: 'test-tool-id' },
          });
          component.testHandleToolFormDisplay({ parentCapabilityForm: capabilityForm, form: toolForm });
          component.testHandleToolFormSubmit();
          return { toolForm };
        },
        checkExpectations: (toolsArray, { toolForm }) => {
          expect(toolsArray.controls.length).toBe(1);
          expect(toolsArray.controls[0].metadata.localId).toEqual(toolForm.metadata.localId);
          expect(toolsArray.controls[0].controls.url.value).toEqual(toolForm.controls.url.value);
          expect(toolsArray.controls[0].controls.description.value).toEqual(toolForm.controls.description.value);
        },
      },
      {
        name: 'should not be able to add a tool without a valid name',
        setup: async (component, capabilityForm) => {
          const toolForm = new AiToolForm({
            func: {
              id: '',
              description: 'Test Tool',
              methodType: 'GET',
              url: 'https://example.com',
              generatesAnswer: false,
            },
            metadata: { isNew: true, localId: 'test-tool-id' },
          });
          component.testHandleToolFormDisplay({ parentCapabilityForm: capabilityForm, form: toolForm });
          component.testHandleToolFormSubmit();
          return { toolForm };
        },
        checkExpectations: (toolsArray) => {
          expect(toolsArray.controls.length).toBe(0);
        },
      },
      {
        name: 'should update form values when editing a new tool',
        setup: async (component, capabilityForm) => {
          // First create a new tool
          const toolForm = new AiToolForm({
            func: {
              id: 'test_tool',
              description: 'Test Tool',
              methodType: 'GET',
              url: 'https://example.com',
              generatesAnswer: false,
            },
            metadata: { isNew: true, localId: 'test-tool-id' },
          });
          component.testHandleToolFormDisplay({ parentCapabilityForm: capabilityForm, form: toolForm });
          component.testHandleToolFormSubmit();

          // Now the tool is in the capability. When editing, the form itself gets updated through UI interactions
          // We simulate this by directly updating the form that's already in the capability
          const toolInCapability = capabilityForm.controls.tools.at(0);
          toolInCapability.controls.url.setValue('https://example.com/updated');
          toolInCapability.controls.description.setValue('Updated Tool Description');
          toolInCapability.controls.method.setValue('POST');
          toolInCapability.controls.headers.clear();
          toolInCapability.controls.headers.push(new AiToolHeaderForm({ key: 'Authorization', value: 'Bearer token' }));
          toolInCapability.controls.parameters.clear();
          toolInCapability.controls.parameters.push(new AiToolParameterForm({ name: 'param1', type: 'string' }));

          // When submitting the edit, we pass the same tool form (which is already updated)
          component.testHandleToolFormDisplay({ parentCapabilityForm: capabilityForm, form: toolInCapability });
          component.testHandleToolFormSubmit();

          return { toolForm, updatedToolForm: toolInCapability };
        },
        checkExpectations: (toolsArray, { updatedToolForm }) => {
          expect(toolsArray.controls.length).toBe(1);
          const toolControl = toolsArray.controls[0];
          expect(toolControl.metadata.localId).toEqual(updatedToolForm?.metadata.localId);
          expect(toolControl.controls.url.value).toEqual(updatedToolForm?.controls.url.value);
          expect(toolControl.controls.description.value).toEqual(updatedToolForm?.controls.description.value);
        },
      },
      {
        name: 'should be able to add a pre-existing tool to a capability',
        setup: async (component, capabilityForm) => {
          const toolForm = new AiToolForm({
            func: {
              id: 'existing-tool-id',
              description: 'Test Tool',
              methodType: 'GET',
              url: 'https://example.com',
              generatesAnswer: false,
            },
            metadata: { isNew: false },
          });
          component.testHandleToolFormDisplay({ parentCapabilityForm: capabilityForm, form: toolForm });
          component.testHandleToolFormSubmit();
          return { toolForm };
        },
        checkExpectations: (toolsArray, { toolForm }) => {
          expect(toolsArray.controls.length).toBe(1);
          expect(toolsArray.controls[0].metadata.localId).toEqual(toolForm.metadata.localId);
          expect(toolsArray.controls[0].controls.url.value).toEqual(toolForm.controls.url.value);
          expect(toolsArray.controls[0].controls.description.value).toEqual(toolForm.controls.description.value);
        },
      },
      {
        name: 'should update form values when editing a pre-existing tool',
        setup: async (component, capabilityForm) => {
          // First add a pre-existing tool to the capability
          const toolForm = new AiToolForm({
            func: {
              id: 'existing-tool',
              description: 'Test Tool',
              methodType: 'GET',
              url: 'https://example.com',
              generatesAnswer: false,
            },
          });
          component.testHandleToolFormDisplay({ parentCapabilityForm: capabilityForm, form: toolForm });
          component.testHandleToolFormSubmit();

          // Now the tool is in the capability. When editing, the form itself gets updated through UI interactions
          // We simulate this by directly updating the form that's already in the capability
          const toolInCapability = capabilityForm.controls.tools.at(0);
          toolInCapability.controls.url.setValue('https://example.com/updated');
          toolInCapability.controls.description.setValue('Updated Tool Description');
          toolInCapability.controls.method.setValue('POST');
          toolInCapability.controls.headers.clear();
          toolInCapability.controls.headers.push(new AiToolHeaderForm({ key: 'Authorization', value: 'Bearer token' }));
          toolInCapability.controls.parameters.clear();
          toolInCapability.controls.parameters.push(new AiToolParameterForm({ name: 'param1', type: 'string' }));

          // When submitting the edit, we pass the same tool form (which is already updated)
          component.testHandleToolFormDisplay({ parentCapabilityForm: capabilityForm, form: toolInCapability });
          component.testHandleToolFormSubmit();

          return { toolForm, updatedToolForm: toolInCapability };
        },
        checkExpectations: (toolsArray, { updatedToolForm }) => {
          expect(toolsArray.controls.length).toBe(1);
          const toolControl = toolsArray.controls[0];
          expect(toolControl.controls.url.value).toEqual(updatedToolForm?.controls.url.value);
          expect(toolControl.controls.description.value).toEqual(updatedToolForm?.controls.description.value);
          expect(toolControl.controls.method.value).toEqual(updatedToolForm?.controls.method.value);
          expect(toolControl.controls.headers.length).toEqual(updatedToolForm?.controls.headers.length);
          expect(toolControl.controls.headers.at(0)?.controls.name.value).toEqual(
            updatedToolForm?.controls.headers.at(0)?.controls.name.value,
          );
          expect(toolControl.controls.headers.at(0)?.controls.value.value).toEqual(
            updatedToolForm?.controls.headers.at(0)?.controls.value.value,
          );
          expect(toolControl.controls.parameters.length).toEqual(updatedToolForm?.controls.parameters.length);
          expect(toolControl.controls.parameters.at(0)?.controls.name.value).toEqual(
            updatedToolForm?.controls.parameters.at(0)?.controls.name.value,
          );
          expect(toolControl.controls.parameters.at(0)?.controls.type.value).toEqual(
            updatedToolForm?.controls.parameters.at(0)?.controls.type.value,
          );
        },
      },
    ];

    testCases.forEach((testCase) => {
      it(testCase.name, async () => {
        const { capabilityForm, toolsArray } = await setupToolTestBed();
        const toolData = await testCase.setup(component, capabilityForm);
        testCase.checkExpectations(toolsArray, toolData);
      });
    });
  });

  it('should update the form when the image changes', async () => {
    await setupTestBed();
    component.testOnImageChanged('new-image-url');
    const form = component.getTestAssistantForm();
    expect(form?.get('avatarUrl')?.value).toBe('new-image-url');
    expect(form?.get('avatarUrl')?.dirty).toBe(true);
  });

  it('should navigate back when back() is called', async () => {
    await setupTestBed();
    component.testBack();
    expect(pageService.navigateToPrevious).toHaveBeenCalled();
  });
});
