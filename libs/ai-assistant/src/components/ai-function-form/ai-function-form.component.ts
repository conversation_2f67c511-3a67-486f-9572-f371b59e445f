import { Component, computed, effect, inject } from '@angular/core';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { TranslateModule } from '@ngx-translate/core';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatButton, MatIconButton } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatOption } from '@angular/material/autocomplete';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatSelect } from '@angular/material/select';
import { catchError, Observable, of, switchMap } from 'rxjs';
import { StickyFooterComponent } from '../sticky-footer/sticky-footer.component';
import { AiAssistantService } from '../../core/services/ai-assistant.service';
import { AIFunction } from '../../core/services/ai-assistant-utils';
import { FunctionParameterInterface, FunctionParameterParameterLocation, Namespace } from '@vendasta/ai-assistants';
import {
  FunctionHeaderInterface,
  FunctionInterface,
} from '@vendasta/ai-assistants/lib/_internal/interfaces/function.interface';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { CommonModule } from '@angular/common';
import { FunctionParameterComponent } from './function-parameter/function-parameter.component';
import { MatCard, MatCardContent } from '@angular/material/card';
import { ACCOUNT_GROUP_ID_TOKEN, PARTNER_ID_TOKEN } from '../../core/tokens';
import { ActivatedRoute } from '@angular/router';
import { PageService } from '@vendasta/galaxy/page/src/page.service';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { CurlImportDialogComponent, CurlImportResult } from './curl-import-dialog.component';
import { Clipboard } from '@angular/cdk/clipboard';
import { HttpErrorResponse } from '@angular/common/http';

export enum HttpMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH',
}

export enum ParameterType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  INTEGER = 'integer',
  OBJECT = 'object',
  ARRAY = 'array',
}

const FALLBACK_BACK_URL = '../../../assistants';

function httpUrlValidator(): ValidatorFn {
  const urlPattern =
    /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_+.~#?&/=]*)$/;
  return (control: AbstractControl): ValidationErrors | null => {
    if (!control.value) {
      return null;
    }
    return urlPattern.test(control.value) ? null : { invalidHttpUrl: true };
  };
}

@Component({
  selector: 'ai-ai-function-form',
  imports: [
    GalaxyPageModule,
    TranslateModule,
    ReactiveFormsModule,
    GalaxyFormFieldModule,
    MatSelect,
    MatOption,
    MatButton,
    MatIconModule,
    MatIconButton,
    MatMenuModule,
    MatInputModule,
    MatOption,
    StickyFooterComponent,
    CommonModule,
    FunctionParameterComponent,
    MatCard,
    MatCardContent,
    MatDialogModule,
  ],
  templateUrl: './ai-function-form.component.html',
  styleUrl: './ai-function-form.component.scss',
})
export class AiFunctionFormComponent {
  private fb = inject(FormBuilder);
  private aiAssistantService = inject(AiAssistantService);
  private readonly partnerId$ = inject(PARTNER_ID_TOKEN);
  private readonly accountGroupId$ = inject(ACCOUNT_GROUP_ID_TOKEN);
  private readonly snackbarService = inject(SnackbarService);
  private readonly pageService = inject(PageService);
  private readonly activeRoute = inject(ActivatedRoute);
  private readonly dialog = inject(MatDialog);
  private readonly clipboard = inject(Clipboard);

  private partnerID = toSignal(this.partnerId$);
  private accountGroupID = toSignal(this.accountGroupId$);
  private nameSpace = computed(() => {
    const accountGroupId = this.accountGroupID();
    const partnerId = this.partnerID();
    if (accountGroupId) {
      return new Namespace({
        accountGroupNamespace: {
          accountGroupId: accountGroupId,
        },
      });
    } else if (partnerId) {
      return new Namespace({
        partnerNamespace: {
          partnerId: partnerId,
        },
      });
    }
    return;
  });
  private aiFunction: Observable<FunctionInterface | null> = this.activeRoute.paramMap.pipe(
    switchMap((paramMap) => {
      const functionId = paramMap.get('functionId');
      if (functionId) {
        return this.aiAssistantService.getFunction(functionId).pipe(
          catchError((err) => {
            console.error(err);
            this.snackbarService.openErrorSnack('AI_ASSISTANT.FUNCTIONS.SNACKBAR.ERROR_FETCHING_FUNCTION');
            return of(null);
          }),
        );
      }
      return of(null);
    }),
  );
  protected readonly function = toSignal(this.aiFunction);

  httpMethodOptions = [
    { value: HttpMethod.GET, label: 'GET' },
    { value: HttpMethod.POST, label: 'POST' },
    { value: HttpMethod.PUT, label: 'PUT' },
    { value: HttpMethod.DELETE, label: 'DELETE' },
    { value: HttpMethod.PATCH, label: 'PATCH' },
  ];

  protected readonly ParameterType = ParameterType;
  protected readonly ParameterLocation = FunctionParameterParameterLocation;

  commonHeaderOptions = [
    { label: 'Content-Type: JSON', header: { key: 'Content-Type', value: 'application/json' } },
    { label: 'Accept: JSON', header: { key: 'Accept', value: 'application/json' } },
    { label: 'Authorization', header: { key: 'Authorization', value: 'YOUR_TOKEN_HERE' } },
  ];

  protected form: FormGroup = this.fb.group(
    {
      name: ['', [Validators.required, Validators.pattern(/^[a-zA-Z0-9_-]{1,64}$/), Validators.maxLength(64)]],
      description: ['', Validators.required],
      methodType: [HttpMethod.GET, Validators.required],
      url: ['', [Validators.required, httpUrlValidator()]],
      parameters: this.fb.array([]),
      headers: this.fb.array([]),
      generateResponse: [false],
    },
    { updateOn: 'change' },
  );

  get name() {
    return this.form.get('name');
  }

  get description() {
    return this.form.get('description');
  }

  get parameters() {
    return this.form.get('parameters') as FormArray<FormControl>;
  }

  get headers() {
    return this.form.get('headers') as FormArray;
  }

  get methodType() {
    return this.form.get('methodType');
  }

  get url() {
    return this.form.get('url');
  }

  get generateResponse() {
    return this.form.get('generateResponse');
  }

  isGenerateResponseFunction = toSignal(this.generateResponse?.valueChanges || of(false));

  toggleDisableMethodAndUrlEffect = effect(() => {
    const generateResponse = this.isGenerateResponseFunction();
    if (generateResponse) {
      this.methodType?.disable();
      this.url?.disable();
      this.headers.clear();
      const hasOutputParam = this.parameters.controls.some((control) => control.value.name === 'Output');

      if (!hasOutputParam) {
        this.parameters?.insert(
          0,
          this.createParameter({
            name: 'Output',
            type: ParameterType.STRING,
          }),
        );
      }
    } else {
      this.methodType?.enable();
      this.url?.enable();
      if (this.parameters.controls[0]?.value.name === 'Output') this.parameters?.removeAt(0);
    }
  });

  setUpFormEffect = effect(() => {
    const functionData = this.function();
    if (functionData !== null && functionData !== undefined) {
      this.parameters.clear();
      if (functionData.functionParameters && functionData.functionParameters.length > 0) {
        functionData.functionParameters.forEach(() => {
          this.addParameter({});
        });
      }

      this.headers.clear();
      if (functionData.headers && functionData.headers.length > 0) {
        functionData.headers.forEach(() => {
          this.addHeader({});
        });
      }

      this.form.patchValue({
        name: functionData.id,
        description: functionData.description,
        methodType: functionData.methodType,
        url: functionData.url,
        parameters: functionData.functionParameters,
        headers: functionData.headers,
        generateResponse: functionData.generatesAnswer,
      });

      // the name is the function id and is not editable
      this.name?.disable();
      this.form.markAsPristine();
    }
  });

  constructor() {
    this.toggleDisableMethodAndUrlEffect;
    this.setUpFormEffect;
  }

  createParameter(parameter: FunctionParameterInterface) {
    return this.fb.control({
      name: parameter?.name || '',
      type: parameter?.type || '',
      description: parameter?.description,
      location: parameter?.location || FunctionParameterParameterLocation.LOCATION_BODY,
      properties: parameter?.properties || [],
      items: parameter?.items,
    });
  }

  addParameter(parameter?: FunctionParameterInterface): void {
    this.parameters.push(this.createParameter(parameter ?? {}));
  }

  removeParameter(index: number): void {
    this.parameters.removeAt(index);
  }

  addHeader(header?: FunctionHeaderInterface): void {
    this.headers.push(
      this.fb.group({
        key: [header?.key || '', Validators.required],
        value: [header?.value || '', Validators.required],
      }),
    );
    this.form.markAsDirty();
  }

  removeHeader(index: number): void {
    this.headers.removeAt(index);
  }

  async submit(): Promise<void> {
    const AIFunction: AIFunction = {
      id: this.name?.value,
      namespace: this.nameSpace(),
      description: this.description?.value,
      methodType: this.methodType?.value,
      functionParameters: this.parameters.value,
      headers: this.headers.value,
      url: this.url?.value,
      generatesAnswer: this.generateResponse?.value,
      authStrategy: {
        unspecified: {},
      },
    };

    if (!this.form.valid) {
      Object.values(this.form.controls).forEach((control) => {
        control.markAsTouched();
      });
      this.headers.controls.forEach((headerGroup) => {
        if (headerGroup instanceof FormGroup) {
          Object.values(headerGroup.controls).forEach((control) => {
            control.markAsTouched();
          });
        }
      });
      this.snackbarService.openErrorSnack('AI_ASSISTANT.FUNCTIONS.SNACKBAR.MISSING_FIELDS');
      return;
    }
    try {
      await this.aiAssistantService.upsertFunction(AIFunction);
      this.snackbarService.openSuccessSnack('AI_ASSISTANT.FUNCTIONS.SNACKBAR.SUCCESS_SAVING_TOOL');
    } catch (error) {
      console.error(error);
      this.snackbarService.openErrorSnack(getSaveErrorMessage(error));
    } finally {
      if (this.function()) {
        this.form.markAsPristine();
      } else {
        this.pageService.navigateToPrevious(FALLBACK_BACK_URL, this.activeRoute);
      }
    }
  }

  cancel(): void {
    this.pageService.navigateToPrevious(FALLBACK_BACK_URL, this.activeRoute);
  }

  canSave(): boolean {
    return this.form.dirty;
  }

  protected readonly FormArray = FormArray;

  openCurlImportDialog(): void {
    const dialogRef = this.dialog.open(CurlImportDialogComponent, {
      width: '600px',
      disableClose: false,
      autoFocus: false,
    });
    dialogRef.afterClosed().subscribe((result: CurlImportResult) => {
      if (result) {
        this.applyImportedCurl(result);
      }
    });
  }

  private applyImportedCurl(result: CurlImportResult): void {
    // Patch method and URL
    this.form.patchValue({
      methodType: result.methodType,
      url: result.url,
    });

    this.parameters.clear();
    // Add body parameters
    if (result.bodyParams.length > 0) {
      result.bodyParams.forEach((param) => {
        const parameterDefinition: any = {
          name: param.name,
          type: param.type || ParameterType.STRING,
          location: FunctionParameterParameterLocation.LOCATION_BODY,
        };
        if (param.properties && param.type === ParameterType.OBJECT) {
          parameterDefinition.properties = param.properties.map((p: any) => this.convertNestedProperty(p));
        }
        if (param.type === ParameterType.ARRAY) {
          parameterDefinition.items = param.item
            ? this.convertNestedProperty(param.item)
            : {
                type: ParameterType.STRING,
                location: FunctionParameterParameterLocation.LOCATION_BODY,
              };
        }
        this.parameters.push(this.createParameter(parameterDefinition));
      });
    }

    // Add query parameters
    if (result.queryParams.length > 0) {
      result.queryParams.forEach((param) => {
        this.parameters.push(
          this.createParameter({
            name: param.name,
            type: ParameterType.STRING,
            location: FunctionParameterParameterLocation.LOCATION_QUERY_PARAM,
          }),
        );
      });
    }

    // Set headers
    this.headers.clear();
    if (result.headers.length > 0) {
      result.headers.forEach((header) => {
        this.addHeader(header);
      });
    }
    this.form.markAsDirty();
    this.snackbarService.openSuccessSnack('cURL command imported successfully');
  }

  copyCurlCommand(): void {
    let curlCommand = `curl -X ${this.methodType?.value} `;

    if (this.headers && this.headers.length > 0) {
      this.headers.value.forEach((header: any) => {
        if (header.key && header.value) {
          curlCommand += `-H '${header.key}: ${header.value}' `;
        }
      });
    }

    const requestBodyArgs: any = {};
    const urlQueryArgs: any = {};

    const getDefaultValue = (param: any): any => {
      switch (param.type) {
        case ParameterType.INTEGER:
          return 0;
        case ParameterType.NUMBER:
          return 0;
        case ParameterType.BOOLEAN:
          return false;
        case ParameterType.ARRAY:
          if (param.item) {
            return [getDefaultValue(param.item)];
          }
          return [];
        case ParameterType.OBJECT:
          if (param.properties && Array.isArray(param.properties)) {
            const obj: any = {};
            param.properties.forEach((prop: any) => {
              obj[prop.name] = getDefaultValue(prop);
            });
            return obj;
          }
          return {};
        case ParameterType.STRING:
        default:
          return '';
      }
    };

    if (this.parameters && this.parameters.length > 0) {
      this.parameters.value.forEach((param: any) => {
        if (param.location === FunctionParameterParameterLocation.LOCATION_QUERY_PARAM) {
          urlQueryArgs[param.name] = '';
        } else {
          requestBodyArgs[param.name] = getDefaultValue(param);
        }
      });
    }

    if (Object.keys(requestBodyArgs).length > 0) {
      curlCommand += `-d '${JSON.stringify(requestBodyArgs)}' `;
    }

    let url = this.url?.value || '';
    if (Object.keys(urlQueryArgs).length > 0) {
      const searchParams = new URLSearchParams(urlQueryArgs).toString();
      url += `?${searchParams}`;
    }
    curlCommand += `'${url}'`;

    this.clipboard.copy(curlCommand);
    this.snackbarService.openSuccessSnack('Copied cURL command to clipboard');
  }

  private convertNestedProperty(prop: any): FunctionParameterInterface {
    const base: FunctionParameterInterface = {
      name: prop.name || '',
      type: prop.type || ParameterType.STRING,
      location: FunctionParameterParameterLocation.LOCATION_BODY,
      description: '',
    };

    if (prop.type === ParameterType.OBJECT && prop.properties) {
      base.properties = prop.properties.map((p: any) => {
        return this.convertNestedProperty(p);
      });
    }

    if (prop.type === ParameterType.ARRAY) {
      if (prop.item) {
        // Get converted nested item
        const convertedItem = this.convertNestedProperty(prop.item);

        // Create a complete item
        base.items = {
          name: '', // Always provide an empty name for array items
          type: convertedItem.type,
          description: convertedItem.description || '',
          location: FunctionParameterParameterLocation.LOCATION_BODY, // Always BODY
          properties: convertedItem.properties || [],
          items: convertedItem.items, // For nested arrays
        };
      } else {
        base.items = {
          name: '',
          type: ParameterType.STRING,
          description: '',
          location: FunctionParameterParameterLocation.LOCATION_BODY,
          properties: [],
        };
      }
    }

    return base;
  }
}

function getSaveErrorMessage(error: HttpErrorResponse) {
  if (error?.error?.message?.indexOf('Too many nested object parameters') !== -1) {
    return 'AI_ASSISTANT.FUNCTIONS.SNACKBAR.TOO_MANY_NESTED_PARAMETERS';
  }
  return 'AI_ASSISTANT.FUNCTIONS.SNACKBAR.SAVE_ERROR';
}
