import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { combineLatest, map, Observable, ReplaySubject } from 'rxjs';
import { shareReplay, take, tap } from 'rxjs/operators';
import { BulkImportStepperService } from '../bulk-import-stepper.service';
import { CSVHeaderMappingInterface, FieldType } from '@vendasta/crm';
import { FieldSchemaInterface, ObjectType, CrmFormService, SystemFieldIds } from '@galaxy/crm/static';

export interface FieldSchemaGroup {
  label: string;
  schemaOptions: FieldSchemaInterface[];
}

@Injectable()
export class FieldMappingService {
  fieldMappings$: Observable<CSVHeaderMappingInterface[]>;
  importTypeValues$$ = new ReplaySubject<{ [key: number]: ObjectType }>(1);

  constructor(
    private readonly translate: TranslateService,
    private readonly formService: CrmFormService,
    private readonly stepperService: BulkImportStepperService,
  ) {
    this.fieldMappings$ = this.stepperService.csvMappings$;
    this.importTypeValues$$.next({});
  }

  getFieldSchemaMap$(objectType: ObjectType): Observable<Map<string, FieldSchemaInterface>> {
    return this.formService.listAllFormFields$(objectType).pipe(
      map((schemas) => {
        const schemaMap: Map<string, FieldSchemaInterface> = new Map();
        for (const schema of schemas) {
          schemaMap.set(schema.fieldId, schema);
        }
        return schemaMap;
      }),
    );
  }

  displayField(s: FieldSchemaInterface | undefined): boolean {
    if (s?.fieldId == SystemFieldIds.CompanyID || s?.fieldId == SystemFieldIds.ContactID) return true;
    return !!s && !s.readOnly;
  }

  listFieldGroups$(objectType: ObjectType): Observable<FieldSchemaGroup[]> {
    const fieldSchemaMap$ = this.getFieldSchemaMap$(objectType);
    const fieldGroups$ = this.formService.listAllObjectFormFieldGroups$(objectType);
    return combineLatest([fieldSchemaMap$, fieldGroups$]).pipe(
      map(([schemaMap, groups]) => {
        return groups.map((g) => ({
          label: this.translate.instant(g.description || 'BULK_IMPORT.MAP_FIELDS_STEP.OTHER_FIELDS'),
          schemaOptions: g.fieldIds.map((id) => schemaMap.get(id)).filter(this.displayField) as FieldSchemaInterface[],
        }));
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  private fieldAllowsMultipleMappings(fieldType: FieldType): boolean {
    const typesWithMultipleMappings = [FieldType.FIELD_TYPE_TAG];
    return typesWithMultipleMappings.includes(fieldType || FieldType.FIELD_TYPE_INVALID);
  }

  availableFieldGroups$(objectType: ObjectType): Observable<FieldSchemaGroup[]> {
    return combineLatest([this.listFieldGroups$(objectType), this.fieldMappings$]).pipe(
      map(([groups, mappings]) => {
        const fieldIDs = mappings.map((m) => m.fieldId);
        return groups
          .map((g) => {
            const fg = {
              label: g.label,
            } as FieldSchemaGroup;
            fg.schemaOptions = g.schemaOptions.filter((option) => {
              return !fieldIDs.includes(option.fieldId) || this.fieldAllowsMultipleMappings(option.fieldType);
            });
            return fg;
          })
          .filter((g) => g.schemaOptions.length > 0);
      }),
    );
  }

  addMapping(header: string, fieldId: string, objectType: string): void {
    this.stepperService.csvMappings$
      .pipe(
        take(1),
        map((mappings) => {
          const newMappings = Object.assign([], mappings); // If we don't do this, the stepper service won't detect the change
          newMappings.push({
            headerName: header,
            fieldId: fieldId,
            crmObjectType: objectType,
          } as CSVHeaderMappingInterface);
          return newMappings;
        }),
        tap((mappings) => this.stepperService.setCSVHeaderMappings(mappings)),
      )
      .subscribe();
  }

  removeMapping(header: string): void {
    this.stepperService.csvMappings$
      .pipe(
        map((mappings) => {
          return mappings.filter((mapping) => mapping.headerName !== header);
        }),
        tap((mappings) => this.stepperService.setCSVHeaderMappings(mappings)),
        take(1),
      )
      .subscribe();
  }

  getImportTypeValues() {
    return this.importTypeValues$$;
  }

  resetImportTypeValues() {
    this.importTypeValues$$.next({});
  }
}
