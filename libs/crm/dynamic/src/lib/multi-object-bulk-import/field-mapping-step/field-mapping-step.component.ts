import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  OnInit,
  AfterContentChecked,
  Output,
  ChangeDetectorRef,
  inject,
  DestroyRef,
} from '@angular/core';
import {
  ObjectType,
  TranslationModule,
  CrmObjectService,
  SystemFieldIds,
  StandardIds,
  StandardExternalIds,
  CrmFieldService,
} from '@galaxy/crm/static';
import { MatButtonModule } from '@angular/material/button';
import { BulkImportStepperService, CsvRow, PermissionType } from '../bulk-import-stepper.service';
import { combineLatest, forkJoin, Observable, of, ReplaySubject, shareReplay } from 'rxjs';
import { map, switchMap, tap } from 'rxjs/operators';
import { MatTableModule } from '@angular/material/table';
import { CrmFieldMappingRowComponent } from './field-mapping-row.component';
import { FieldMappingService, FieldSchemaGroup } from './field-mapping.service';
import {
  CSVHeaderMappingInterface,
  FieldSchemaInterface,
  FieldType,
  ValidateCrmObjectResponseInterface,
  ValidateCrmObjectResponseValidationErrorSeverity as Severity,
} from '@vendasta/crm';
import { MatFormFieldModule } from '@angular/material/form-field';
import { CrmDependencies, CrmInjectionToken } from '@galaxy/crm/static';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatOption } from '@angular/material/autocomplete';
import { MatSelectModule } from '@angular/material/select';
import { FieldValueInterface } from '@vendasta/crm/lib/_internal/interfaces/crm-objects.interface';
import { TranslateService } from '@ngx-translate/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatIcon } from '@angular/material/icon';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';

@Component({
  selector: 'crm-field-mapping-step',
  templateUrl: 'field-mapping-step.component.html',
  providers: [],
  styleUrls: ['field-mapping-step.component.scss'],
  imports: [
    CommonModule,
    TranslationModule,
    MatButtonModule,
    MatTableModule,
    MatFormFieldModule,
    CrmFieldMappingRowComponent,
    GalaxyFormFieldModule,
    MatSelectModule,
    MatOption,
    MatIcon,
    GalaxyTooltipModule,
  ],
})
export class CrmFieldMappingStepComponent implements OnInit, AfterContentChecked {
  private readonly config: CrmDependencies = inject(CrmInjectionToken);
  private readonly stepperService = inject(BulkImportStepperService);
  private readonly fieldMapping = inject(FieldMappingService);
  private readonly objectService = inject(CrmObjectService);
  private readonly translate = inject(TranslateService);
  private readonly cdref = inject(ChangeDetectorRef);
  protected readonly crmFieldService = inject(CrmFieldService);

  protected readonly displayedColumns = ['header', 'firstRow', 'importAs', 'mapTo'];
  protected importTypeValues: { [key: number]: ObjectType } = {};
  private importTypeValues$$!: ReplaySubject<{ [key: number]: ObjectType }>;

  dataSource$: Observable<MappingTableData[]> = this.stepperService.csvFirstRow$.pipe(
    map((firstRow) =>
      Object.entries(firstRow).map(([key, value]) => ({
        header: key,
        firstRow: value.value,
        isDuplicateHeaderName: value.isDuplicateHeaderName,
      })),
    ),
  );
  isPartnerCenter = this.config.appID === 'partner-center-client';
  instructionsText$ = combineLatest([
    this.translate.stream('BULK_IMPORT.MAP_FIELDS_STEP.INSTRUCTIONS'),
    this.translate.stream('BULK_IMPORT.MAP_FIELDS_STEP.PARTNER_INSTRUCTIONS'),
  ]).pipe(
    map(([instructions, partnerInstructions]) => {
      if (this.isPartnerCenter) {
        return `${instructions} ${partnerInstructions}`;
      }
      return instructions;
    }),
  );
  protected companyFieldMappingGroups$!: Observable<FieldSchemaGroup[]>;
  protected contactFieldMappingGroups$!: Observable<FieldSchemaGroup[]>;
  protected fieldMappingGroupsLoading$!: Observable<boolean>;
  private readonly fieldMappings$: Observable<CSVHeaderMappingInterface[]> = this.fieldMapping.fieldMappings$;
  protected mappingsErrors$!: Observable<string[]>;
  @Output() fieldMappingsErrors = new EventEmitter<string[]>();

  private readonly destroyRef = inject(DestroyRef);

  completeStep(): void {
    this.stepperService.completeStep('fieldMappingCompleted', true);
  }

  blockStep(): void {
    this.stepperService.completeStep('fieldMappingCompleted', false);
  }

  addValidationValue(
    mapping: CSVHeaderMappingInterface,
    firstRow: CsvRow,
    validationRow: FieldValueInterface[],
    fieldSchemaMap: Map<string, FieldSchemaInterface>,
  ) {
    if (!mapping.fieldId || !mapping.headerName || !firstRow[mapping.headerName]) {
      return;
    }
    const fieldType = fieldSchemaMap.get(mapping.fieldId)?.fieldType;
    const foundTagIndex = validationRow.findIndex((vRow) => vRow.fieldId === mapping.fieldId);
    if (!fieldType) {
      return;
    }

    const validationValue = this.getValidationValue(mapping, fieldType, firstRow[mapping.headerName].value);
    if (!validationValue) {
      if (fieldType == FieldType.FIELD_TYPE_TAG && foundTagIndex !== -1) {
        validationRow[foundTagIndex].stringValues?.values?.push(firstRow[mapping.headerName].value);
      }
      return;
    }
    validationRow.push(validationValue);
  }

  getValidationValue(
    mapping: CSVHeaderMappingInterface,
    fieldType: FieldType,
    firstRowValue: string,
  ): FieldValueInterface | null {
    switch (fieldType) {
      case FieldType.FIELD_TYPE_EMAIL:
      case FieldType.FIELD_TYPE_PHONE:
      case FieldType.FIELD_TYPE_STRING:
        return {
          fieldId: mapping.fieldId,
          stringValue: firstRowValue,
        };
      case FieldType.FIELD_TYPE_INTEGER:
        return {
          fieldId: mapping.fieldId,
          integerValue: parseInt(firstRowValue, 10),
        };
      case FieldType.FIELD_TYPE_DATE:
        return {
          fieldId: mapping.fieldId,
          dateValue: new Date(firstRowValue),
        };
      case FieldType.FIELD_TYPE_BOOLEAN:
        return {
          fieldId: mapping.fieldId,
          booleanValue: !!firstRowValue,
        };
      case FieldType.FIELD_TYPE_TAG:
        return {
          fieldId: mapping.fieldId,
          stringValues: {
            values: [firstRowValue],
          },
        };
      default:
        break;
    }
    return null;
  }

  getErrorMessages(validation: ValidateCrmObjectResponseInterface): string[] {
    const severeErrors = (validation.errors ?? []).filter(
      (e) => e.severity === Severity.VALIDATION_ERROR_SEVERITY_INVALID && !e.details?.uniquenessCheck,
    );
    return severeErrors.map((error) => this.translate.instant(error.message || ''));
  }

  ngOnInit(): void {
    // done so that importTypeValues is reset when parsing a new file
    this.importTypeValues$$ = this.fieldMapping.getImportTypeValues();
    this.importTypeValues$$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((values) => {
      this.importTypeValues = values;
    });

    this.contactFieldMappingGroups$ = this.stepperService.contactPermissions$.pipe(
      switchMap((canAccess) => {
        if (canAccess) {
          return this.fieldMapping.listFieldGroups$('Contact');
        } else {
          return of([]);
        }
      }),
      shareReplay(1),
    );
    this.companyFieldMappingGroups$ = this.stepperService.companyPermissions$.pipe(
      switchMap((canAccess) => {
        if (canAccess) {
          return this.fieldMapping.listFieldGroups$('Company');
        } else {
          return of([]);
        }
      }),
      shareReplay(1),
    );
    this.fieldMappingGroupsLoading$ = combineLatest([
      this.companyFieldMappingGroups$,
      this.contactFieldMappingGroups$,
    ]).pipe(
      map(([companyFieldMappingGroups, contactFieldMappingGroups]) => {
        return !companyFieldMappingGroups || !contactFieldMappingGroups;
      }),
    );

    this.mappingsErrors$ = combineLatest([
      this.stepperService.csvFirstRow$,
      this.stepperService.contactPermissions$,
      this.stepperService.companyPermissions$,
      this.fieldMappings$,
      this.importTypeValues$$,
    ]).pipe(
      switchMap(([firstRow, contactPermissions, companyPermissions, mappings, _]) => {
        const contactFieldSchemaMap$ = contactPermissions
          ? this.fieldMapping.getFieldSchemaMap$('Contact')
          : of(new Map<string, FieldSchemaInterface>());
        const companyFieldSchemaMap$ = companyPermissions
          ? this.fieldMapping.getFieldSchemaMap$('Company')
          : of(new Map<string, FieldSchemaInterface>());
        return forkJoin([of(firstRow), contactFieldSchemaMap$, companyFieldSchemaMap$, of(mappings)]);
      }),
      map(([firstRow, contactFieldSchemaMap, companyFieldSchemaMap, mappings]) => {
        const contactValidationRow: FieldValueInterface[] = [];
        const companyValidationRow: FieldValueInterface[] = [];
        for (const m of mappings) {
          switch (m.crmObjectType) {
            case 'Contact':
              this.addValidationValue(m, firstRow, contactValidationRow, contactFieldSchemaMap);
              break;
            case 'Company':
              this.addValidationValue(m, firstRow, companyValidationRow, companyFieldSchemaMap);
              break;
            default:
              break;
          }
        }
        return { contactValidationRow, companyValidationRow };
      }),
      switchMap(({ contactValidationRow, companyValidationRow }) => {
        const hasContactSelected = Object.values(this.importTypeValues).includes('Contact');
        const hasCompanySelected = Object.values(this.importTypeValues).includes('Company');

        // only get validation errors if there are fields to validate
        const contactValidation$ = hasContactSelected
          ? this.objectService.validateCrmObject('Contact', {
              fields: this.checkForContactIDs(contactValidationRow),
            })
          : of({ errors: [] });
        const companyValidation$ = hasCompanySelected
          ? this.objectService.validateCrmObject('Company', {
              fields: this.checkForCompanyIDs(companyValidationRow),
            })
          : of({ errors: [] });

        const NoImportTypesValidation$ =
          !hasContactSelected && !hasCompanySelected
            ? of('BULK_IMPORT.MAP_FIELDS_STEP.NO_IMPORT_TYPES_SELECTED')
            : of('');

        return combineLatest([contactValidation$, companyValidation$, NoImportTypesValidation$]);
      }),
      map(([contactValidation, companyValidation, NoImportTypesValidation]) => {
        // special case when no import types have been selected
        if (NoImportTypesValidation) {
          return [this.translate.instant(NoImportTypesValidation)];
        }
        const severeContactFieldMappingsErrors = this.getErrorMessages(contactValidation);
        const severeCompanyFieldMappingsErrors = this.getErrorMessages(companyValidation);
        return [...severeContactFieldMappingsErrors, ...severeCompanyFieldMappingsErrors];
      }),
      tap((errorMessages) => {
        // emit errors for tooltip in bulk-import-stepper
        this.fieldMappingsErrors.emit(errorMessages);
      }),
      shareReplay(1),
    );

    this.mappingsErrors$
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        tap((errorMessages) => {
          if (errorMessages.length) {
            this.blockStep();
          } else {
            this.completeStep();
          }
        }),
      )
      .subscribe();
  }

  ngAfterContentChecked() {
    this.cdref.detectChanges();
  }

  hasPermission$(permissionType: PermissionType) {
    return this.stepperService.hasPermission$(permissionType);
  }

  updateImportTypeValues(key: number, value: ObjectType) {
    this.importTypeValues[key] = value;
    this.importTypeValues$$.next(this.importTypeValues);
  }

  // Will check if ID is present, if it is and the name is not, set a non-empty value to skip the requirement validation
  checkForContactIDs(contactValidationRow: FieldValueInterface[]): FieldValueInterface[] {
    if (
      contactValidationRow.some((contactField) => contactField?.fieldId === SystemFieldIds.ContactID) &&
      !contactValidationRow.some(
        (contactField) => contactField?.fieldId === this.crmFieldService.getFieldId(StandardExternalIds.Email),
      )
    ) {
      return [
        ...contactValidationRow,
        {
          fieldId: this.crmFieldService.getFieldId(StandardExternalIds.Email),
          stringValue: 'NON_EMPTY_VALUE',
        },
      ];
    }
    return contactValidationRow;
  }

  // Will check if ID is present, if it is and the name is not, set a non-empty value to skip the requirement validation
  checkForCompanyIDs(companyValidationRow: FieldValueInterface[]): FieldValueInterface[] {
    if (
      companyValidationRow.some((contactField) => contactField?.fieldId === SystemFieldIds.CompanyID) &&
      !companyValidationRow.some((contactField) => contactField?.fieldId === StandardIds.CompanyName)
    ) {
      return [
        ...companyValidationRow,
        {
          fieldId: StandardIds.CompanyName,
          stringValue: 'NON_EMPTY_VALUE',
        },
      ];
    }
    return companyValidationRow;
  }
}

interface MappingTableData {
  header: string;
  firstRow?: string;
}
