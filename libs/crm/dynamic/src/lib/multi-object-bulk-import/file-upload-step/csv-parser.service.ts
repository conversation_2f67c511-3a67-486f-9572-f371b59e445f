import { Inject, Injectable } from '@angular/core';
import { Observable, switchMap, map, of, forkJoin } from 'rxjs';
import { CRMImporterApiService, CSVHeaderMapping } from '@vendasta/crm';
import { CrmDependencies, CrmInjectionToken } from '@galaxy/crm/static';
import { BulkImportStepperService, CsvRow } from '../bulk-import-stepper.service';

interface CSVInfo {
  rows: string;
  isEstimate: boolean;
  firstRow: CsvRow;
  rowMapping: CSVHeaderMapping[];
}

@Injectable()
export class CsvParserService {
  private namespace$ = this.config.namespace$;

  constructor(
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    private importer: CRMImporterApiService,
    private stepper: BulkImportStepperService,
  ) {}

  parseCSV(crmObjectType: string, filename: string): Observable<CSVInfo> {
    return this.namespace$.pipe(
      switchMap((namespace) =>
        this.importer.getMultiFileUploadData({
          namespace,
          crmObjectType,
          filename: [filename],
        }),
      ),
      switchMap((response) =>
        forkJoin([
          of(response),
          this.stepper.hasPermission$('contactPermissions'),
          this.stepper.hasPermission$('companyPermissions'),
        ]),
      ),
      map(([response, contactPermissions, companyPermissions]) => {
        const file = response.files && response.files[0];

        const headers = file?.headers || [];
        const firstRowEntries = headers.map(({ name, sampleData }) => [name, (sampleData && sampleData[0]) || '']);
        // uses the first occurrence of repeat headers
        const seenHeader = new Set();
        const duplicateHeader = new Set();
        const uniqueFirstRowEntries = firstRowEntries.filter(([key]) => {
          if (seenHeader.has(key)) {
            duplicateHeader.add(key);
            return false;
          } else {
            seenHeader.add(key);
            return true;
          }
        });
        const firstRow = Object.fromEntries(
          uniqueFirstRowEntries.map(([key, value]) => [
            key,
            { value: value, isDuplicateHeaderName: duplicateHeader.has(key) },
          ]),
        );

        const mappingSuggestions: CSVHeaderMapping[] = file?.mappingSuggestions || [];
        const uniqueMappingSuggestions = mappingSuggestions.filter((entry, index, self) => {
          if (!contactPermissions && entry.crmObjectType === 'Contact') {
            return false;
          }
          if (!companyPermissions && entry.crmObjectType === 'Company') {
            return false;
          }

          const duplicateIndex = self.findIndex(
            (e) => e.headerName === entry.headerName && e.crmObjectType === entry.crmObjectType,
          );
          return duplicateIndex === index;
        });

        return {
          rows: file.size.totalRows || '0',
          isEstimate: !!file?.size?.isEstimate,
          firstRow: firstRow,
          rowMapping: uniqueMappingSuggestions,
        };
      }),
    );
  }
}
