<img src="./images/Galaxy_Icon.png" alt="Galaxy framework icon" height="96px" />

# Contributing to the Galaxy

👍🎉 First off, thanks for taking the time to contribute! 🎉👍

The following is a set of guidelines for contributing to this monorepo as well as the Galaxy frontend framework.

## Links to important resources

[Galaxy frontend framework documentation lives here](https://galaxy.vendasta.com/)

We track issues on [Jira](https://vendasta.jira.com/jira/software/c/projects/GD/boards/552).

We communicate mainly on Slack in the [#galaxy-design-system](https://app.slack.com/client/T02AKE45B/C02GFL2JDB3) channel, that's a good place to reach out to us about issues or with questions. 

You'll want to familiarize yourself with our monorepo tooling's [generation cli](https://nx.dev/cli/generate).


## Environment details

Galaxy and its constituents run best on Node 12. Get setup to develop using the following steps:

1. `npm install` or if it's your first time here, `npm ci`
   1. Why the distinction? `npm ci` will get rid of any of your already compiled Angular Ivy modules so it isn't optimal to remove all of those just to update your deps. If you didn't change package.json, just do an npm install and then reset any changes to package-lock.json, e.g. by doing `git checkout -- package-lock.json`
2. That's pretty much it. See [Testing](#Testing) for how to start your app or test your library

## Testing

There are a few ways to test your code locally, depending on what you're doing!

- If you're testing an app (you're in the apps folder), run `npm start <app folder name>` to run it locally, here's an example:
  - `npm start mission-control-client`
- If you're testing a library (you're in the libs folder), it should be used in one of the apps and therefore can be tested that way. If it isn't used in any apps, you should still have some tests that can be run with `npm test`. If it doesn't have tests, it can still be tested using Galaxy's npm package testing capabilities, i.e. if you make a PR with an affected library you'll get a comment on your PR with a link to install the package in the app you're interested in.
- We use Jest as a test runner, so your favourite Jest test commands are available with `npm test`, e.g.
  - `npm test -- path-to-file`
  - `npm test <app name> -- path-to-file`
  - `npm test -- --runTestsByPath path-to-file`
  - etc.

## Adding features

Familiarize with [Angular CLI](https://angular.io/cli/generate) and [NX CLI](https://nx.dev/latest/angular/cli/generate)

### Examples of common used commands (disclaimer - subject to become stale, use the tool's docs to keep up todate).

#### To generate modules/components/services see nx --help
Example for help docs on generating component
`npx nx g component --help`

Example of new module in Sales Success Center
`npx nx g module pipeline/pipeline-table --project=sales-center-client`
Helpful commands: `--dryRun`

## Galaxy Design System
### How to request a component, enhancement, or report a bug
Check the [component process in Confluence](https://vendasta.jira.com/wiki/spaces/GLXY/pages/1799127182/Component+contribution+process)

Ask in Slack / GChat channel for #frontend or #galaxy-design-system.

Any and all enhancements to the frontend framework must go through design.

### Adding a component to the Galaxy frontend framework

There are a few steps involved to add a component to the framework, rig it up so it exports properly, and add a docs page, so we've abstracted that into a single npm command.
Running `npm run generate:new-galaxy-component --name=<COMPONENT_NAME>` will do all of this for you.

Note: you'll still need to add the right routing for docs. [More info](https://galaxy.vendasta.com/documentation/new-component)

### Adding a storybook to the Galaxy frontend framework

There are a few steps involved to add a new storybook to the library/app.
Check the storybook creation process [here](create-new-storybook.md)

### Opening a PR for the Galaxy frontend framework

When you're ready to open up a PR for the Galaxy frontend framework, we recommend the following:

- Add a description entailing the modifications/features you've added
- Add a screen capture of the feature that you've added/modified
- Tag @vendasta/guardians-of-the-galaxy on the PR

## Other monorepo apps or libs

Make a [Github issue](https://github.com/vendasta/galaxy/issues) and tag it appropriately.

## NPM Libraries

For libraries that are published to NPM, add the library name (from the package.json) to the NPM_LIBRARIES list in build-tools/library-checker.js.

## Style guide / coding conventions

### Git commit messages

We follow [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/#summary) commit messages. An example commit message looks something like this (elements in <> are defined below):

```
<type>(<scope>): <subject>
BLANK LINE
<body>
BLANK LINE
<footer>
```

#### Type

Must be one of the following:

- **build**: Changes that affect the build system or external dependencies (example scopes: gulp, broccoli, npm)
- **ci**: Changes to our CI configuration files and scripts (example scopes: Travis, Circle, BrowserStack, SauceLabs)
- **chore**: Things that need to be done, like updating package-lock, or bumping versions
- **docs**: Documentation only changes
- **feat**: A new feature
- **fix**: A bug fix
- **perf**: A code change that improves performance
- **refactor**: A code change that neither fixes a bug nor adds a feature
- **style**: Changes that do not affect the meaning of the code (white-space, formatting, missing semi-colons, etc)
- **test**: Adding missing tests or correcting existing tests

#### Scope

The scope should be the name of the package affected. Example scopes:

- **galaxy**: Relating to the Galaxy frontend framework
- **galaxy-exp**: Relating to the experimental pieces of the Galaxy frontend framework
- **mission-control**: Relating to Mission Control
- etc...

#### Subject

The subject contains a succinct description of the change:

- use the imperative, present tense: "change" not "changed" nor "changes"
  - Think of it like you're writing a command: "make the border a rich blue" rather than "made the border a rich blue"
- don't capitalize the first letter
- no dot (.) at the end

#### Body

Just as in the **subject**, use the imperative, present tense: "change" not "changed" nor "changes". The body should include the motivation for the change and contrast this with previous behavior.

#### Footer

The footer should contain any information about **Breaking Changes** and is also the place to reference GitHub issues that this commit **Closes**. This is also a great place to put your Jira or other tracking information.

**Breaking Changes** should start with the word `BREAKING CHANGE:` with a space or two newlines. The rest of the commit message is then used for this.

### Coding style

We use Prettier and Editorconfig to assist with this. See [.prettierrc](./prettierrc) and [.editorconfig](./editorconfig) for the conventions we use.

The easiest way to adhere is to use Prettier and Editor Config plugins in your editor of choice. These plugins will sync your editor settings with the respective files, and can format your files on save/at a time of your choosing. However, if you'd like to format all of your work at once, you can run `npm run format:write`. To just check your work without actually changing any files, use `npm run format:check` to see which files need correcting.

### Testing guidelines

Galaxy and Galaxy Experimental use [@ngneat/spectator](https://ngneat.github.io/spectator/) to test their code. This is a library that encourages testing your code the same way users use it. I.e. your users don't search your page for a certain id or class, they search it with their eyes by looking for elements that perform certain roles (for the example of headings or lists of data) or they look for labels in the case of form inputs. Spectator lets you write tests that are less Angular dependent and more dependent on end-user interaction. If the end-user interaction changes you can expect your tests will need to be updated, otherwise you can expect to be able to safely refactor your code without having to change tests.

We encourage the use of [@ngneat/spectator](https://ngneat.github.io/spectator/) and would love to help you get started with it.

We use \*.spec.ts for a naming convention for test files. These spec files should live next to the files they test.

## Recognition

If you successfully contribute to the Galaxy frontend framework, we will give you beer (or something). At the very least, your name and GitHub username will be in our release notes.

## Who is involved?

### Core contributors

Formerly [Wisakejak](https://github.com/orgs/vendasta/teams/wisakejak). Galaxy is internal open source, reviewed and maintained by [@vendasta/guardians-of-the-galaxy](https://github.com/orgs/vendasta/teams/guardians-of-the-galaxy/).

### Design

[Joel Kesler](https://github.com/joelkesler)
[Jenna Barth](https://github.com/jbarth-va)
