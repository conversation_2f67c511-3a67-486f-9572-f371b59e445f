@use 'design-tokens' as *;

.aieo-nav-tabs {
  display: flex;
  gap: $spacing-2;
  margin-bottom: $spacing-4;
  padding: 0 $spacing-3;
  border-bottom: 1px solid $border-color;

  .nav-tab {
    padding: $spacing-2 $spacing-4;
    border-radius: $default-border-radius $default-border-radius 0 0;
    border: 1px solid transparent;
    border-bottom: none;
    background: transparent;
    color: $secondary-text-color;
    transition: all 0.2s ease;

    mat-icon {
      margin-right: $spacing-2;
    }

    &:hover {
      background: $secondary-background-color;
      color: $primary-text-color;
    }

    &.active {
      background: $card-background-color;
      color: $primary-color;
      border-color: $border-color;
      border-bottom-color: $card-background-color;
      margin-bottom: -1px;
    }
  }
}

.success-message-container {
  margin-bottom: $spacing-4;
  max-width: 600px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  flex: 1;
}

.success-message-card {
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
  border-radius: $default-border-radius * 3;
  padding: $spacing-5;
  box-shadow: 0 $spacing-3 $spacing-5 rgba(76, 175, 80, 0.3);
  border: 2px solid #4caf50;
  display: flex;
  align-items: center;
  gap: $spacing-4;
  width: 100%;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 2s infinite;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    text-align: center;
    padding: $spacing-4;
  }
}

.success-icon {
  flex-shrink: 0;
  
  mat-icon {
    font-size: $spacing-5;
    width: $spacing-5;
    height: $spacing-5;
    color: white;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  }

  @media (max-width: 768px) {
    mat-icon {
      font-size: $spacing-5;
      width: $spacing-5;
      height: $spacing-5;
    }
  }
}

.success-content {
  flex: 1;
  color: white;
}

.success-title {
  @include text-preset-2;
  font-weight: 700;
  margin: 0 0 $spacing-2 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  line-height: 1.2;

  @media (max-width: 768px) {
    @include text-preset-3;
  }
}

.success-text {
  @include text-preset-4;
  margin: 0 0 $spacing-3 0;
  opacity: 0.95;
  line-height: 1.5;
  font-weight: 500;

  @media (max-width: 768px) {
    @include text-preset-4;
  }
}

.success-actions {
  display: flex;
  gap: $spacing-2;
  align-items: center;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    justify-content: center;
  }
}

.success-action-button {
  background: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  font-weight: 600;
  text-transform: none;
  border-radius: $default-border-radius * 2;
  transition: all 0.2s ease-in-out;

  &:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
}

.dismiss-button {
  color: rgba(255, 255, 255, 0.8) !important;
  font-weight: 500;
  text-transform: none;
  transition: all 0.2s ease-in-out;

  &:hover {
    color: white !important;
    background: rgba(255, 255, 255, 0.1) !important;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.input-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  padding: $spacing-4;
  background: linear-gradient(135deg, $primary-background-color 0%, $secondary-background-color 100%);
}

.input-header {
  text-align: center;
  margin-bottom: $spacing-5;
  max-width: 600px;
  transition: all 0.3s ease-in-out;

  &.hidden {
    opacity: 0;
    transform: translateY(-20px);
    pointer-events: none;
    visibility: hidden;
    height: 0;
    margin: 0;
    overflow: hidden;
  }
}

.input-title {
  @include text-preset-1;
  color: $primary-text-color;
  margin-bottom: $spacing-3;
  line-height: 1.2;

  @media (max-width: 768px) {
    @include text-preset-2;
  }

  @media (max-width: 480px) {
    @include text-preset-3;
  }
}

.input-subtitle {
  @include text-preset-4;
  color: $secondary-text-color;
  margin: 0;
  line-height: 1.6;

  @media (max-width: 768px) {
    @include text-preset-4;
  }
}

.input-form-container {
  width: 100%;
  max-width: 500px;
  margin-bottom: $spacing-4;
  transition: all 0.3s ease-in-out;

  &.hidden {
    opacity: 0;
    transform: translateY(20px);
    pointer-events: none;
    visibility: hidden;
    height: 0;
    margin: 0;
    overflow: hidden;
  }
}

.input-card {
  border-radius: $default-border-radius * 3;
  box-shadow: 0 $spacing-2 $spacing-5 $shadow-color;
  border: none;
  background: $card-background-color;
  overflow: hidden;
}

.audit-form {
  padding: $spacing-4;

  @media (max-width: 480px) {
    padding: $spacing-3;
  }
}

.form-field {
  margin-bottom: $spacing-3;
}

.full-width {
  width: 100%;
}

.form-input {
  @include text-preset-4;
}

::ng-deep .glxy-form-field {
  .input-wrapper {
    border-radius: $default-border-radius * 2;
    border-color: $border-color;

    &:focus-within {
      border-color: $primary-color;
      box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
    }
  }

  .label-and-hint label {
    color: $secondary-text-color;
    font-weight: 500;
  }

  input {
    color: $primary-text-color;
    padding: $spacing-2 0;
  }

  &.glxy-form-field-invalid .input-wrapper:not(:focus-within) {
    border-color: $error-border-color;
  }
}

::ng-deep .glxy-error {
  color: $error-text-color;
  @include text-preset-5;
  margin-top: $spacing-1;
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: $spacing-4;
}

.submit-button {
  min-width: 120px;
  height: $spacing-5;
  @include text-preset-4;
  font-weight: 600;
  border-radius: $default-border-radius * 2;
  text-transform: none;
  letter-spacing: 0.025em;
  transition: all 0.2s ease-in-out;

  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 $spacing-1 $spacing-3 rgba($primary-color, 0.3);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &.processing {
    background: linear-gradient(45deg, $primary-color, $blue);
    animation: pulse 1.5s ease-in-out infinite;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba($primary-color, 0.7);
  }
  70% {
    box-shadow: 0 0 0 $spacing-2 rgba($primary-color, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba($primary-color, 0);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .input-container {
    padding: $spacing-3;
  }

  .input-header {
    margin-bottom: $spacing-4;
  }

  .audit-form {
    padding: $spacing-3;
  }

  .form-field {
    margin-bottom: $spacing-2;
  }
}

@media (max-width: 480px) {
  .input-container {
    padding: $spacing-1;
  }

  .input-header {
    margin-bottom: $spacing-3;
  }

  .audit-form {
    padding: $spacing-3;
  }

  .submit-button {
    width: 100%;
  }

  .input-header .input-title {
    @include text-preset-3;
  }

  .input-header .input-subtitle {
    @include text-preset-4;
  }
}
