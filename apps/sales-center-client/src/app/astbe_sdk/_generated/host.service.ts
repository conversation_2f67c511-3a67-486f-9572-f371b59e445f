import { Injectable } from '@angular/core';
import { EnvironmentService, Environment } from '@galaxy/core';

@Injectable()
export class HostService {
  private _host: string;
  private _httpsHost: string;

  constructor(private readonly environmentService: EnvironmentService) {}

  host(): string {
    if (this._host) {
      return this._host;
    }

    switch (this.environmentService.getEnvironment()) {
      case Environment.LOCAL:
        this._host = 'astbe-api.vendasta-local.com';
        break;
      case Environment.TEST:
        this._host = '';
        break;
      case Environment.DEMO:
        this._host = 'astbe-api-demo.apigateway.co';
        break;
      case Environment.PROD:
        this._host = 'astbe-api-prod.apigateway.co';
        break;
    }
    return this._host;
  }

  httpsHost(): string {
    if (this._httpsHost) {
      return this._httpsHost;
    }

    switch (this.environmentService.getEnvironment()) {
      case Environment.LOCAL:
        this._httpsHost = 'astbe.vendasta-local.com';
        break;
      case Environment.TEST:
        this._httpsHost = '';
        break;
      case Environment.DEMO:
        this._httpsHost = 'astbe-demo.apigateway.co';
        break;
      case Environment.PROD:
        this._httpsHost = 'astbe-prod.apigateway.co';
        break;
    }
    return this._httpsHost;
  }

  hostWithScheme(): string {
    const scheme = this.environmentService.getEnvironment() === Environment.LOCAL ? 'http://' : 'https://';
    return scheme + this.host();
  }

  httpsHostWithScheme(): string {
    const scheme = this.environmentService.getEnvironment() === Environment.LOCAL ? 'http://' : 'https://';
    return scheme + this.httpsHost();
  }
}
