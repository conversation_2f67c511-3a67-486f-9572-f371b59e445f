// *********************************
// Code generated by sdkgen
// DO NOT EDIT!.
//
// API Service.
// *********************************
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map, share } from 'rxjs/operators';
import { HostService } from '../_generated/host.service';
import {
  CallRequestInterface,
  CallResponseInterface,
  CreateConfigRequestInterface,
  CreateConfigResponseInterface,
  GetConfigRequestInterface,
  GetConfigResponseInterface,
  GetRecordingUrlRequestInterface,
  GetRecordingUrlResponseInterface,
  LineStatusRequestInterface,
  LineStatusResponseInterface,
  LiveStatusRequestInterface,
  LiveStatusResponseInterface,
} from './interfaces/index';
import {
  CallRequest,
  CallResponse,
  CreateConfigRequest,
  CreateConfigResponse,
  GetConfigRequest,
  GetConfigResponse,
  GetRecordingUrlRequest,
  GetRecordingUrlResponse,
  LineStatusRequest,
  LineStatusResponse,
  LiveStatusRequest,
  LiveStatusResponse,
} from './objects/index';

@Injectable()
export class AsteriskApiService {
  constructor(private readonly http: HttpClient, private readonly hostService: HostService) {}

  private apiOptions(): { headers: HttpHeaders; withCredentials: boolean } {
    return {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      }),
      withCredentials: true,
    };
  }

  call(r: CallRequest | CallRequestInterface): Observable<CallResponse> {
    const request = (<CallRequest>r).toApiJson ? <CallRequest>r : new CallRequest(r);
    return this.http
      .post<CallResponseInterface>(
        this.hostService.hostWithScheme() + '/astbe.v1.Asterisk/Call',
        request.toApiJson(),
        this.apiOptions(),
      )
      .pipe(
        map((resp) => CallResponse.fromProto(resp)),
        share(),
      );
  }
  lineStatus(r: LineStatusRequest | LineStatusRequestInterface): Observable<LineStatusResponse> {
    const request = (<LineStatusRequest>r).toApiJson ? <LineStatusRequest>r : new LineStatusRequest(r);
    return this.http
      .post<LineStatusResponseInterface>(
        this.hostService.hostWithScheme() + '/astbe.v1.Asterisk/LineStatus',
        request.toApiJson(),
        this.apiOptions(),
      )
      .pipe(
        map((resp) => LineStatusResponse.fromProto(resp)),
        share(),
      );
  }
  liveStatus(r: LiveStatusRequest | LiveStatusRequestInterface): Observable<LiveStatusResponse> {
    const request = (<LiveStatusRequest>r).toApiJson ? <LiveStatusRequest>r : new LiveStatusRequest(r);
    return this.http
      .post<LiveStatusResponseInterface>(
        this.hostService.hostWithScheme() + '/astbe.v1.Asterisk/LiveStatus',
        request.toApiJson(),
        this.apiOptions(),
      )
      .pipe(
        map((resp) => LiveStatusResponse.fromProto(resp)),
        share(),
      );
  }
  getConfig(r: GetConfigRequest | GetConfigRequestInterface): Observable<GetConfigResponse> {
    const request = (<GetConfigRequest>r).toApiJson ? <GetConfigRequest>r : new GetConfigRequest(r);
    return this.http
      .post<GetConfigResponseInterface>(
        this.hostService.hostWithScheme() + '/astbe.v1.Asterisk/GetConfig',
        request.toApiJson(),
        this.apiOptions(),
      )
      .pipe(
        map((resp) => GetConfigResponse.fromProto(resp)),
        share(),
      );
  }
  createConfig(r: CreateConfigRequest | CreateConfigRequestInterface): Observable<CreateConfigResponse> {
    const request = (<CreateConfigRequest>r).toApiJson ? <CreateConfigRequest>r : new CreateConfigRequest(r);
    return this.http
      .post<CreateConfigResponseInterface>(
        this.hostService.hostWithScheme() + '/astbe.v1.Asterisk/CreateConfig',
        request.toApiJson(),
        this.apiOptions(),
      )
      .pipe(
        map((resp) => CreateConfigResponse.fromProto(resp)),
        share(),
      );
  }
  getRecordingUrl(r: GetRecordingUrlRequest | GetRecordingUrlRequestInterface): Observable<GetRecordingUrlResponse> {
    const request = (<GetRecordingUrlRequest>r).toApiJson ? <GetRecordingUrlRequest>r : new GetRecordingUrlRequest(r);
    return this.http
      .post<GetRecordingUrlResponseInterface>(
        this.hostService.hostWithScheme() + '/astbe.v1.Asterisk/GetRecordingUrl',
        request.toApiJson(),
        this.apiOptions(),
      )
      .pipe(
        map((resp) => GetRecordingUrlResponse.fromProto(resp)),
        share(),
      );
  }
}
