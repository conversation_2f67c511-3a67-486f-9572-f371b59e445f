// *********************************
// Code generated by sdkgen
// DO NOT EDIT!.
//
// Objects.
// *********************************
/* tslint:disable */
import * as i from '../interfaces/index';

import * as e from '../enums/index';

export function enumStringToValue<E>(enumRef: any, value: string): E {
  if (typeof value === 'number') {
    return value;
  }
  return enumRef[value];
}

export class Queue implements i.QueueInterface {
  name: string;
  number: number;
  agents: number;
  callsAnswered: number;
  callsMissed: number;

  static fromProto(proto: any): Queue {
    let m = new Queue();
    m = Object.assign(m, proto);
    if (proto.number) {
      m.number = parseInt(proto.number, 10);
    }
    if (proto.agents) {
      m.agents = parseInt(proto.agents, 10);
    }
    if (proto.callsAnswered) {
      m.callsAnswered = parseInt(proto.callsAnswered, 10);
    }
    if (proto.callsMissed) {
      m.callsMissed = parseInt(proto.callsMissed, 10);
    }
    return m;
  }

  constructor(kwargs?: i.QueueInterface) {
    if (!kwargs) {
      return;
    }
    Object.assign(this, kwargs);
  }

  toApiJson(): object {
    if (
      typeof this.name === 'undefined' &&
      typeof this.number === 'undefined' &&
      typeof this.agents === 'undefined' &&
      typeof this.callsAnswered === 'undefined' &&
      typeof this.callsMissed === 'undefined'
    ) {
      return {};
    }

    const toReturn: {
      [key: string]: any;
    } = {};

    if (typeof this.name !== 'undefined') {
      toReturn['name'] = this.name;
    }
    if (typeof this.number !== 'undefined') {
      toReturn['number'] = this.number;
    }
    if (typeof this.agents !== 'undefined') {
      toReturn['agents'] = this.agents;
    }
    if (typeof this.callsAnswered !== 'undefined') {
      toReturn['callsAnswered'] = this.callsAnswered;
    }
    if (typeof this.callsMissed !== 'undefined') {
      toReturn['callsMissed'] = this.callsMissed;
    }
    return toReturn;
  }
}

export class Extension implements i.ExtensionInterface {
  extension: number;
  user: string;
  status: e.LineStatus;

  static fromProto(proto: any): Extension {
    let m = new Extension();
    m = Object.assign(m, proto);
    if (proto.extension) {
      m.extension = parseInt(proto.extension, 10);
    }
    if (proto.status) {
      m.status = enumStringToValue<e.LineStatus>(e.LineStatus, proto.status);
    }
    return m;
  }

  constructor(kwargs?: i.ExtensionInterface) {
    if (!kwargs) {
      return;
    }
    Object.assign(this, kwargs);
  }

  toApiJson(): object {
    if (
      typeof this.extension === 'undefined' &&
      typeof this.user === 'undefined' &&
      typeof this.status === 'undefined'
    ) {
      return {};
    }

    const toReturn: {
      [key: string]: any;
    } = {};

    if (typeof this.extension !== 'undefined') {
      toReturn['extension'] = this.extension;
    }
    if (typeof this.user !== 'undefined') {
      toReturn['user'] = this.user;
    }
    if (typeof this.status !== 'undefined') {
      toReturn['status'] = this.status;
    }
    return toReturn;
  }
}

export class LiveStatusRequest implements i.LiveStatusRequestInterface {
  extension: number;

  static fromProto(proto: any): LiveStatusRequest {
    let m = new LiveStatusRequest();
    m = Object.assign(m, proto);
    if (proto.extension) {
      m.extension = parseInt(proto.extension, 10);
    }
    return m;
  }

  constructor(kwargs?: i.LiveStatusRequestInterface) {
    if (!kwargs) {
      return;
    }
    Object.assign(this, kwargs);
  }

  toApiJson(): object {
    if (typeof this.extension === 'undefined') {
      return {};
    }
    const toReturn: {
      [key: string]: any;
    } = {};
    if (typeof this.extension !== 'undefined') {
      toReturn['extension'] = this.extension;
    }
    return toReturn;
  }
}

export class LiveStatusResponse implements i.LiveStatusResponseInterface {
  token: string;

  static fromProto(proto: any): LiveStatusResponse {
    let m = new LiveStatusResponse();
    m = Object.assign(m, proto);
    return m;
  }

  constructor(kwargs?: i.LiveStatusResponseInterface) {
    if (!kwargs) {
      return;
    }
    Object.assign(this, kwargs);
  }

  toApiJson(): object {
    if (typeof this.token === 'undefined') {
      return {};
    }

    const toReturn: {
      [key: string]: any;
    } = {};

    if (typeof this.token !== 'undefined') {
      toReturn['token'] = this.token;
    }
    return toReturn;
  }
}

export class LineStatusRequest implements i.LineStatusRequestInterface {
  extension: number;

  static fromProto(proto: any): LineStatusRequest {
    let m = new LineStatusRequest();
    m = Object.assign(m, proto);
    if (proto.extension) {
      m.extension = parseInt(proto.extension, 10);
    }
    return m;
  }

  constructor(kwargs?: i.LineStatusRequestInterface) {
    if (!kwargs) {
      return;
    }
    Object.assign(this, kwargs);
  }

  toApiJson(): object {
    if (typeof this.extension === 'undefined') {
      return {};
    }

    const toReturn: {
      [key: string]: any;
    } = {};

    if (typeof this.extension !== 'undefined') {
      toReturn['extension'] = this.extension;
    }
    return toReturn;
  }
}

export class LineStatusResponse implements i.LineStatusResponseInterface {
  id: number;
  extension: Extension;
  queuestatus: e.MemberStatus;

  static fromProto(proto: any): LineStatusResponse {
    let m = new LineStatusResponse();
    m = Object.assign(m, proto);
    if (proto.id) {
      m.id = parseInt(proto.id, 10);
    }
    if (proto.extension) {
      m.extension = Extension.fromProto(proto.extension);
    }
    if (proto.queuestatus) {
      m.queuestatus = enumStringToValue<e.MemberStatus>(e.MemberStatus, proto.queuestatus);
    }
    return m;
  }

  constructor(kwargs?: i.LineStatusResponseInterface) {
    if (!kwargs) {
      return;
    }
    Object.assign(this, kwargs);
  }

  toApiJson(): object {
    if (
      typeof this.id === 'undefined' &&
      typeof this.extension === 'undefined' &&
      typeof this.queuestatus === 'undefined'
    ) {
      return {};
    }

    const toReturn: {
      [key: string]: any;
    } = {};

    if (typeof this.id !== 'undefined') {
      toReturn['id'] = this.id;
    }
    if (typeof this.extension !== 'undefined' && this.extension !== null) {
      toReturn['extension'] = 'toApiJson' in this.extension ? (this.extension as any).toApiJson() : this.extension;
    }
    if (typeof this.queuestatus !== 'undefined') {
      toReturn['queuestatus'] = this.queuestatus;
    }
    return toReturn;
  }
}

export class CallRequest implements i.CallRequestInterface {
  number: string;
  extension: string;
  ring: boolean;

  static fromProto(proto: any): CallRequest {
    let m = new CallRequest();
    m = Object.assign(m, proto);
    return m;
  }

  constructor(kwargs?: i.CallRequestInterface) {
    if (!kwargs) {
      return;
    }
    Object.assign(this, kwargs);
  }

  toApiJson(): object {
    if (
      typeof this.number === 'undefined' &&
      typeof this.extension === 'undefined' &&
      typeof this.ring === 'undefined'
    ) {
      return {};
    }

    const toReturn: {
      [key: string]: any;
    } = {};

    if (typeof this.number !== 'undefined') {
      toReturn['number'] = this.number;
    }
    if (typeof this.extension !== 'undefined') {
      toReturn['extension'] = this.extension;
    }
    if (typeof this.ring !== 'undefined') {
      toReturn['ring'] = this.ring;
    }
    return toReturn;
  }
}

export class CallResponse implements i.CallResponseInterface {
  status: string;

  static fromProto(proto: any): CallResponse {
    let m = new CallResponse();
    m = Object.assign(m, proto);
    return m;
  }

  constructor(kwargs?: i.CallResponseInterface) {
    if (!kwargs) {
      return;
    }
    Object.assign(this, kwargs);
  }

  toApiJson(): object {
    if (typeof this.status === 'undefined') {
      return {};
    }

    const toReturn: {
      [key: string]: any;
    } = {};

    if (typeof this.status !== 'undefined') {
      toReturn['status'] = this.status;
    }
    return toReturn;
  }
}

export class GetConfigRequest implements i.GetConfigRequestInterface {
  pid: string;

  static fromProto(proto: any): GetConfigRequest {
    let m = new GetConfigRequest();
    m = Object.assign(m, proto);
    return m;
  }

  constructor(kwargs?: i.GetConfigRequestInterface) {
    if (!kwargs) {
      return;
    }
    Object.assign(this, kwargs);
  }

  toApiJson(): object {
    if (typeof this.pid === 'undefined') {
      return {};
    }

    const toReturn: {
      [key: string]: any;
    } = {};

    if (typeof this.pid !== 'undefined') {
      toReturn['pid'] = this.pid;
    }
    return toReturn;
  }
}

export class CreateConfigRequest implements i.CreateConfigRequestInterface {
  pid: string;
  config: PrivateConfig;

  static fromProto(proto: any): CreateConfigRequest {
    let m = new CreateConfigRequest();
    m = Object.assign(m, proto);
    if (proto.config) {
      m.config = PrivateConfig.fromProto(proto.config);
    }
    return m;
  }

  constructor(kwargs?: i.CreateConfigRequestInterface) {
    if (!kwargs) {
      return;
    }
    Object.assign(this, kwargs);
  }

  toApiJson(): object {
    if (typeof this.pid === 'undefined' && typeof this.config === 'undefined') {
      return {};
    }

    const toReturn: {
      [key: string]: any;
    } = {};

    if (typeof this.pid !== 'undefined') {
      toReturn['pid'] = this.pid;
    }
    if (typeof this.config !== 'undefined' && this.config !== null) {
      toReturn['config'] = 'toApiJson' in this.config ? (this.config as any).toApiJson() : this.config;
    }
    return toReturn;
  }
}

export class CreateConfigResponse implements i.CreateConfigResponseInterface {
  pid: string;
  config: PublicConfig;

  static fromProto(proto: any): CreateConfigResponse {
    let m = new CreateConfigResponse();
    m = Object.assign(m, proto);
    if (proto.config) {
      m.config = PublicConfig.fromProto(proto.config);
    }
    return m;
  }

  constructor(kwargs?: i.CreateConfigResponseInterface) {
    if (!kwargs) {
      return;
    }
    Object.assign(this, kwargs);
  }

  toApiJson(): object {
    if (typeof this.pid === 'undefined' && typeof this.config === 'undefined') {
      return {};
    }

    const toReturn: {
      [key: string]: any;
    } = {};

    if (typeof this.pid !== 'undefined') {
      toReturn['pid'] = this.pid;
    }
    if (typeof this.config !== 'undefined' && this.config !== null) {
      toReturn['config'] = 'toApiJson' in this.config ? (this.config as any).toApiJson() : this.config;
    }
    return toReturn;
  }
}

export class GetConfigResponse implements i.GetConfigResponseInterface {
  pid: string;
  config: PublicConfig;

  static fromProto(proto: any): GetConfigResponse {
    let m = new GetConfigResponse();
    m = Object.assign(m, proto);
    if (proto.config) {
      m.config = PublicConfig.fromProto(proto.config);
    }
    return m;
  }

  constructor(kwargs?: i.GetConfigResponseInterface) {
    if (!kwargs) {
      return;
    }
    Object.assign(this, kwargs);
  }

  toApiJson(): object {
    if (typeof this.pid === 'undefined' && typeof this.config === 'undefined') {
      return {};
    }

    const toReturn: {
      [key: string]: any;
    } = {};

    if (typeof this.pid !== 'undefined') {
      toReturn['pid'] = this.pid;
    }
    if (typeof this.config !== 'undefined' && this.config !== null) {
      toReturn['config'] = 'toApiJson' in this.config ? (this.config as any).toApiJson() : this.config;
    }
    return toReturn;
  }
}

export class PublicConfig implements i.PublicConfigInterface {
  name: string;
  createddate: Date;
  asteriskip: string;
  amiuser: string;
  zendeskintegration: boolean;
  activequeues: string[];

  static fromProto(proto: any): PublicConfig {
    let m = new PublicConfig();
    m = Object.assign(m, proto);
    if (proto.createddate) {
      m.createddate = new Date(proto.createddate);
    }
    return m;
  }

  constructor(kwargs?: i.PublicConfigInterface) {
    if (!kwargs) {
      return;
    }
    Object.assign(this, kwargs);
  }

  toApiJson(): object {
    if (
      typeof this.name === 'undefined' &&
      typeof this.createddate === 'undefined' &&
      typeof this.asteriskip === 'undefined' &&
      typeof this.amiuser === 'undefined' &&
      typeof this.zendeskintegration === 'undefined' &&
      typeof this.activequeues === 'undefined'
    ) {
      return {};
    }

    const toReturn: {
      [key: string]: any;
    } = {};

    if (typeof this.name !== 'undefined') {
      toReturn['name'] = this.name;
    }
    if (typeof this.createddate !== 'undefined' && this.createddate !== null) {
      toReturn['createddate'] =
        'toApiJson' in this.createddate ? (this.createddate as any).toApiJson() : this.createddate;
    }
    if (typeof this.asteriskip !== 'undefined') {
      toReturn['asteriskip'] = this.asteriskip;
    }
    if (typeof this.amiuser !== 'undefined') {
      toReturn['amiuser'] = this.amiuser;
    }
    if (typeof this.zendeskintegration !== 'undefined') {
      toReturn['zendeskintegration'] = this.zendeskintegration;
    }
    if (typeof this.activequeues !== 'undefined') {
      toReturn['activequeues'] = this.activequeues;
    }
    return toReturn;
  }
}

export class PrivateConfig implements i.PrivateConfigInterface {
  public: PublicConfig;
  amikey: string;

  static fromProto(proto: any): PrivateConfig {
    let m = new PrivateConfig();
    m = Object.assign(m, proto);
    if (proto.public) {
      m.public = PublicConfig.fromProto(proto.public);
    }
    return m;
  }

  constructor(kwargs?: i.PrivateConfigInterface) {
    if (!kwargs) {
      return;
    }
    Object.assign(this, kwargs);
  }

  toApiJson(): object {
    if (typeof this.public === 'undefined' && typeof this.amikey === 'undefined') {
      return {};
    }

    const toReturn: {
      [key: string]: any;
    } = {};

    if (typeof this.public !== 'undefined' && this.public !== null) {
      toReturn['public'] = 'toApiJson' in this.public ? (this.public as any).toApiJson() : this.public;
    }
    if (typeof this.amikey !== 'undefined') {
      toReturn['amikey'] = this.amikey;
    }
    return toReturn;
  }
}

export class GetRecordingUrlRequest implements i.GetRecordingUrlRequestInterface {
  id: string;

  static fromProto(proto: any): GetRecordingUrlRequest {
    let m = new GetRecordingUrlRequest();
    m = Object.assign(m, proto);
    return m;
  }

  constructor(kwargs?: i.GetRecordingUrlRequestInterface) {
    if (!kwargs) {
      return;
    }
    Object.assign(this, kwargs);
  }

  toApiJson(): object {
    if (typeof this.id === 'undefined') {
      return {};
    }

    const toReturn: {
      [key: string]: any;
    } = {};

    if (typeof this.id !== 'undefined') {
      toReturn['id'] = this.id;
    }
    return toReturn;
  }
}

export class GetRecordingUrlResponse implements i.GetRecordingUrlResponseInterface {
  url: string;
  expires: Date;

  static fromProto(proto: any): GetRecordingUrlResponse {
    let m = new GetRecordingUrlResponse();
    m = Object.assign(m, proto);
    if (proto.expires) {
      m.expires = new Date(proto.expires);
    }
    return m;
  }

  constructor(kwargs?: i.GetRecordingUrlResponseInterface) {
    if (!kwargs) {
      return;
    }
    Object.assign(this, kwargs);
  }

  toApiJson(): object {
    if (typeof this.url === 'undefined' && typeof this.expires === 'undefined') {
      return {};
    }

    const toReturn: {
      [key: string]: any;
    } = {};

    if (typeof this.url !== 'undefined') {
      toReturn['url'] = this.url;
    }
    if (typeof this.expires !== 'undefined' && this.expires !== null) {
      toReturn['expires'] = 'toApiJson' in this.expires ? (this.expires as any).toApiJson() : this.expires;
    }
    return toReturn;
  }
}
