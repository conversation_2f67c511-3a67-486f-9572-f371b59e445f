// *********************************
// Code generated by sdkgen
// DO NOT EDIT!.
//
// Interfaces.
// *********************************

import * as e from '../enums/index';

export interface QueueInterface {
  name?: string;
  number?: number;
  agents?: number;
  callsAnswered?: number;
  callsMissed?: number;
}

export interface ExtensionInterface {
  extension?: number;
  user?: string;
  status?: e.LineStatus;
}

export interface LiveStatusRequestInterface {
  extension?: number;
}

export interface LiveStatusResponseInterface {
  token?: string;
}

export interface LineStatusRequestInterface {
  extension?: number;
}

export interface LineStatusResponseInterface {
  id?: number;
  extension?: ExtensionInterface;
  queuestatus?: e.MemberStatus;
}

export interface CallRequestInterface {
  number?: string;
  extension?: string;
  ring?: boolean;
}

export interface CallResponseInterface {
  status?: string;
}

export interface GetConfigRequestInterface {
  pid?: string;
}

export interface CreateConfigRequestInterface {
  pid?: string;
  config?: PrivateConfigInterface;
}

export interface CreateConfigResponseInterface {
  pid?: string;
  config?: PublicConfigInterface;
}

export interface GetConfigResponseInterface {
  pid?: string;
  config?: PublicConfigInterface;
}

export interface PublicConfigInterface {
  name?: string;
  createddate?: Date;
  asteriskip?: string;
  amiuser?: string;
  zendeskintegration?: boolean;
  activequeues?: string[];
}

export interface PrivateConfigInterface {
  public?: PublicConfigInterface;
  amikey?: string;
}

export interface GetRecordingUrlRequestInterface {
  id?: string;
}

export interface GetRecordingUrlResponseInterface {
  url?: string;
  expires?: Date;
}
