import { Injectable } from '@angular/core';
import { AsteriskApiService, CallRequest } from './_internal';
import { Observable, ReplaySubject } from 'rxjs';
import { filter, map, withLatestFrom, switchMap } from 'rxjs/operators';
import { LoggedInUserInfoService } from '../logged-in-user-info';

export interface CallStatus {
  status: string;
}

export interface CallQueueRequest {
  number: string;
  ring: boolean;
}

export const pruneNumber = (rawNumber: string): string => (rawNumber ? rawNumber.replace(/[^0-9]/g, '') : '');

@Injectable()
export class AsteriskService {
  private readonly request$$: ReplaySubject<CallQueueRequest> = new ReplaySubject(1);
  readonly callStatus$: Observable<CallStatus>;

  constructor(private readonly api: AsteriskApiService, private readonly currentUser: LoggedInUserInfoService) {
    this.callStatus$ = this.request$$.pipe(
      withLatestFrom(this.currentUser.phoneExtension$.pipe(filter((ext) => ext !== null && ext !== undefined))),
      switchMap(([request, extension]) => {
        const req = new CallRequest({
          number: request.number,
          extension: extension,
          ring: request.ring,
        });
        return this.api.call(req).pipe(map((r) => ({ status: r.status })));
      }),
    );

    this.callStatus$.subscribe();
  }

  public call(number: string, extension: string, ring: boolean): Observable<CallStatus> {
    const req = new CallRequest({
      number: pruneNumber(number),
      extension: extension,
      ring: ring,
    });
    return this.api.call(req).pipe(
      map((r) => {
        return { status: r.status };
      }),
    );
  }

  public queueCall(number: string, ring: boolean): void {
    const qReq: CallQueueRequest = { number: pruneNumber(number), ring: ring };
    this.request$$.next(qReq);
  }
}
