import { schedule } from '@vendasta/rx-utils';
import { Observable } from 'rxjs';
import { TestScheduler } from 'rxjs/testing';
import { LoggedInUserInfoService } from '../logged-in-user-info';
import { CallResponse } from './_internal/objects';
import { AsteriskService, pruneNumber } from './asterisk.service';

let sched: TestScheduler;

class MockLoggedInUserInfoService {
  happy: boolean;
  extMarbles: string;
  extValues: { [marble: string]: string };

  constructor(happy = true, marbles = '-x', extensionValues = { x: '' }) {
    this.extMarbles = marbles;
    this.extValues = extensionValues;
    this.happy = happy;
  }
  get phoneExtension$(): Observable<string> {
    if (this.happy) {
      return sched.createColdObservable<string>(this.extMarbles, this.extValues);
    } else {
      return sched.createColdObservable('-#', undefined, new Error('failed'));
    }
  }
}

describe('AsteriskService', () => {
  let apiServiceSpy: any;

  beforeEach(() => {
    sched = new TestScheduler((a, b) => expect(a).toEqual(b));
    apiServiceSpy = {
      call: jest.fn(),
    };
  });
  afterEach(() => {
    sched.flush();
  });

  it('should not call api service if no call requests come in', () => {
    const loggedInUserSrv = new MockLoggedInUserInfoService();

    new AsteriskService(apiServiceSpy, loggedInUserSrv as unknown as LoggedInUserInfoService);

    expect(apiServiceSpy.call).toHaveBeenCalledTimes(0);
  });

  describe('queueCall', () => {
    beforeEach(() => {
      apiServiceSpy = {
        call: jest.fn(),
      };
    });

    afterEach(() => {
      sched.flush();
    });

    it('should not call api service if a request happens before logged in user has loaded', (done) => {
      sched = new TestScheduler((a, b) => expect(a).toEqual(b));
      const loggedInUserSrv = new MockLoggedInUserInfoService(true, '-x', { x: '1234' });
      apiServiceSpy.call = jest.fn(() => {
        const resp = new CallResponse({ status: 'this is a status' });
        return sched.createColdObservable('--x', { x: resp });
      });

      const service = new AsteriskService(apiServiceSpy, loggedInUserSrv as any);
      schedule(sched, '|', () => service.queueCall('1233456543', false));

      schedule(sched, '----|', () => {
        expect(apiServiceSpy.call).toHaveBeenCalledTimes(0);
        done();
      });
      sched.flush();
    });

    it('should not call api service if there is no extension', (done) => {
      sched = new TestScheduler((a, b) => expect(a).toEqual(b));
      const loggedInUserSrv = new MockLoggedInUserInfoService(true, 'x', { x: null as never });

      const service = new AsteriskService(apiServiceSpy, loggedInUserSrv as any);
      schedule(sched, '-|', () => service.queueCall('1233456543', false));

      schedule(sched, '----|', () => {
        expect(apiServiceSpy.call).toHaveBeenCalledTimes(0);
        done();
      });
      sched.flush();
    });

    it('should call api service if a request happens after logged in user has loaded', (done) => {
      const loggedInUserSrv = new MockLoggedInUserInfoService(true, '-x', { x: '1234' });
      apiServiceSpy.call = jest.fn(() => {
        const resp = new CallResponse({ status: 'this is a status' });
        return sched.createColdObservable('--x', { x: resp });
      });

      const service = new AsteriskService(apiServiceSpy, loggedInUserSrv as any);
      schedule(sched, '----|', () => service.queueCall('1233456543', false));

      schedule(sched, '------|', () => {
        expect(apiServiceSpy.call).toHaveBeenCalledTimes(1);
        done();
      });
      sched.flush();
    });

    it('should call api service if a request happens with an unformated number after logged in user has loaded', (done) => {
      const loggedInUserSrv = new MockLoggedInUserInfoService(true, '-x', { x: '1234' });
      apiServiceSpy.call = jest.fn(() => {
        const resp = new CallResponse({ status: 'this is a status' });
        return sched.createColdObservable('--x', { x: resp });
      });

      const service = new AsteriskService(apiServiceSpy, loggedInUserSrv as any);
      schedule(sched, '----|', () => service.queueCall('******-456-7543', false));

      const expectedCallParams = {
        number: '12334567543',
        extension: '1234',
        ring: false,
      };
      schedule(sched, '------|', () => {
        expect(apiServiceSpy.call).toHaveBeenCalledTimes(1);
        expect(apiServiceSpy.call).toHaveBeenCalledWith(expectedCallParams);
        done();
      });
      sched.flush();
    });

    test('Should do the real api call and get call status back when we do the queueCall', () => {
      const loggedInUserSrv = new MockLoggedInUserInfoService(true, '-x', { x: '1234' });
      apiServiceSpy.call = jest.fn(() => {
        const resp = new CallResponse({ status: 'this is a status' });
        return sched.createColdObservable('--x', { x: resp });
      });

      const service = new AsteriskService(apiServiceSpy, loggedInUserSrv as any);
      schedule(sched, '----|', () => service.queueCall('******-456-7543', false));

      sched.expectObservable(service.callStatus$).toBe('------x', { x: { status: 'this is a status' } });
    });
  });

  describe('pruneNumber', () => {
    beforeEach(() => {
      sched = new TestScheduler((a, b) => expect(a).toEqual(b));
    });

    afterEach(() => {
      sched.flush();
    });

    it('should return number string start with a 0, if the phone number start with 0', () => {
      const recievedNumber = pruneNumber('0111231231234');
      expect(recievedNumber).toBe('0111231231234');
    });

    it('should correctly remove all the extra character and space of an input phone number', () => {
      const recievedNumber = pruneNumber('+011 (*************');
      expect(recievedNumber).toBe('0111231231234');
    });

    it('should return empty string, if nothin input', () => {
      const recievedNumber = pruneNumber(null as never);
      expect(recievedNumber).toBe('');
    });
  });
});
