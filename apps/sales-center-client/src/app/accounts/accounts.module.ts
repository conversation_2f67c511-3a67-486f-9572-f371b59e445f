import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { LexiconModule } from '@galaxy/lexicon';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyInputModule } from '@vendasta/galaxy/input';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { CommonPipesModule, VaBadgeModule, VaStencilsModule } from '@vendasta/uikit';
import baseTranslation from '../../assets/i18n/en_devel.json';
import { WEBLATE_COMPONENT_NAME } from '../constants';
import { NavigationModule } from '../navigation/navigation.module';
import { AccountsRouting } from './accounts.routing';
import { AccountSearchComponent, GenerateAccountGroupUrl } from './search/account-search.component';
import { AccountSearchService } from './search/account-search.service';

@NgModule({
  declarations: [AccountSearchComponent, GenerateAccountGroupUrl],
  imports: [
    CommonModule,
    AccountsRouting,
    NavigationModule,
    GalaxyInputModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    MatTableModule,
    GalaxyPageModule,
    MatIconModule,
    VaStencilsModule,
    CommonPipesModule,
    TranslateModule,
    LexiconModule.forChild({
      componentName: WEBLATE_COMPONENT_NAME,
      baseTranslation: baseTranslation,
    }),
    VaBadgeModule,
  ],
  providers: [AccountSearchService],
})
export class AccountsModule {}
