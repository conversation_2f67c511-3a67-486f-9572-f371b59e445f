import { Account, AddonActivation, ListAddonActivationsResponse, ListResponse } from '@vendasta/accounts/legacy';
import { Observable } from 'rxjs';
import { TestScheduler } from 'rxjs/testing';
import { AccountsServiceInterface } from './../interfaces/accounts-interface';

export class AccountsServiceMock implements AccountsServiceInterface {
  constructor(private readonly scheduler: TestScheduler) {}

  list(): Observable<ListResponse> {
    const accounts = [
      new Account({
        partnerId: 'ABC',
        businessId: 'AG-123',
        activation: new Date(0),
        trial: false,
      }),
    ];
    const response = new ListResponse({
      accounts: accounts,
      nextCursor: '',
      hasMore: false,
      totalResults: 1,
    });
    return this.scheduler.createColdObservable('-c|', {
      c: response,
    });
  }

  listAddonActivations(): Observable<ListAddonActivationsResponse> {
    const addonActivations = [
      new AddonActivation({
        businessId: 'AG-123',
        appId: 'testapp',
        addonId: 'A-123',
        activationId: 'AC-123',
        activated: new Date(0),
        deactivated: new Date(0),
        orderFormSubmissionId: 'OFS-123',
      }),
    ];
    const response = new ListAddonActivationsResponse({
      activations: addonActivations,
    });

    return this.scheduler.createColdObservable('-c|', {
      c: response,
    });
  }
}

export class AccountsServiceErrorMock implements AccountsServiceInterface {
  constructor(private readonly scheduler: TestScheduler) {}

  list(): Observable<ListResponse> {
    return this.scheduler.createColdObservable('-#', {}, new Error('Server error'));
  }

  listAddonActivations(): Observable<ListAddonActivationsResponse> {
    return this.scheduler.createColdObservable('-#', {}, new Error('Server error'));
  }
}
