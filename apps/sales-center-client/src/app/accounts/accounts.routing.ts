import { RouterModule, Routes } from '@angular/router';
import { NgModule } from '@angular/core';
import { AccountSearchComponent } from './search/account-search.component';
import { Features } from '../features';
import { FeatureFlagGuard, FeatureFlagGuardData } from '../core/feature-flag.guard';

export const BUSINESS_SEARCH_URL = '/business/search';

const routes: Routes = [
  {
    path: 'search',
    component: AccountSearchComponent,
    data: { feature: Features.NewSearch } as FeatureFlagGuardData,
    canActivate: [FeatureFlagGuard],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AccountsRouting {}
