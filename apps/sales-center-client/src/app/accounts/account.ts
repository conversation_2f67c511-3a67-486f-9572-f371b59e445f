import { Account as InternalAccount, AccountInterface } from '@vendasta/accounts/legacy';

export class Account extends InternalAccount {
  constructor(data?: AccountInterface) {
    super(data);
  }

  // getAppId for the different product types, handling custom and legacy product ids
  public getAppId(): string {
    if (this.productId === 'MP') {
      return this.appId;
    } else if (this.productId === 'CP') {
      return this.accountId;
    } else {
      return this.productId;
    }
  }
}
