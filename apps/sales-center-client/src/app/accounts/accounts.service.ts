import { Injectable, Inject } from '@angular/core';
import { from as observableFrom, Observable } from 'rxjs';
import { Account } from './account';
import { AddonActivation } from './addon-activation';
import { AccountsServiceInterface } from './interfaces/accounts-interface';
import { ListAddonActivationsResponse } from '@vendasta/accounts/legacy';
import { map, mergeMap, take, reduce } from 'rxjs/operators';

@Injectable()
export class AccountsService {
  constructor(@Inject('AccountsServiceInterface') private readonly accountsService: AccountsServiceInterface) {}

  public getAccounts(partnerId: string, businessId: string): Observable<Account[]> {
    return this.accountsService
      .list(businessId, partnerId, null, 100)
      .pipe(map((response) => (response.accounts || []).map((a) => new Account(a))));
  }

  public listAddonActivations(businessId: string, appId: string): Observable<AddonActivation[]> {
    return this.accountsService.listAddonActivations(businessId, appId).pipe(map((response) => response.activations));
  }

  public listAllAddonActivations(businessId: string, appIds: string[]): Observable<AddonActivation[]> {
    return observableFrom(appIds).pipe(
      map((appId) => this.accountsService.listAddonActivations(businessId, appId)),
      mergeMap((innerObs) => innerObs),
      take(appIds.length),
      reduce(
        (acc: AddonActivation[], val: ListAddonActivationsResponse) =>
          acc.concat(val.activations ? val.activations : []),
        [],
      ),
    );
  }
}
