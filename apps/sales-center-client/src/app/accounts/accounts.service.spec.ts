import { AddonActivation, ListResponse } from '@vendasta/accounts/legacy';
import { Observable, of } from 'rxjs';
import { TestScheduler } from 'rxjs/testing';
import { Account } from './account';
import { AccountsService } from './accounts.service';
import { AccountsServiceErrorMock, AccountsServiceMock } from './mocks/accounts-service.mock';

let scheduler: TestScheduler;

describe('AccountsService', () => {
  beforeEach(() => {
    scheduler = new TestScheduler((a, b) => expect(a).toEqual(b));
  });
  afterEach(() => scheduler.flush());

  it('should return an account', () => {
    const accountsServiceMock = new AccountsServiceMock(scheduler);
    const accountsService = new AccountsService(accountsServiceMock);
    const expected = [
      new Account({
        partnerId: 'ABC',
        businessId: 'AG-123',
        activation: new Date(0),
        trial: false,
      }),
    ];
    scheduler.expectObservable(accountsService.getAccounts('ABC', 'AG-123')).toBe('-a|', {
      a: expected,
    });
  });

  it('should return an addon activation object', () => {
    const accountsServiceMock = new AccountsServiceMock(scheduler);
    const accountsService = new AccountsService(accountsServiceMock);
    const expected = [
      new AddonActivation({
        businessId: 'AG-123',
        appId: 'testapp',
        addonId: 'A-123',
        activationId: 'AC-123',
        activated: new Date(0),
        deactivated: new Date(0),
        orderFormSubmissionId: 'OFS-123',
      }),
    ];

    scheduler.expectObservable(accountsService.listAddonActivations('AG-123', 'testapp')).toBe('-a|', {
      a: expected,
    });
  });

  it('should return a list of addon activation objects', () => {
    const accountsServiceMock = new AccountsServiceMock(scheduler);
    const accountsService = new AccountsService(accountsServiceMock);
    const addonActivation1 = new AddonActivation({
      businessId: 'AG-123',
      appId: 'testapp',
      addonId: 'A-123',
      activationId: 'AC-123',
      activated: new Date(0),
      deactivated: new Date(0),
      orderFormSubmissionId: 'OFS-123',
    });
    const addonActivation2 = new AddonActivation({
      businessId: 'AG-123',
      appId: 'testapp',
      addonId: 'A-123',
      activationId: 'AC-123',
      activated: new Date(0),
      deactivated: new Date(0),
      orderFormSubmissionId: 'OFS-123',
    });
    const expected = [addonActivation1, addonActivation2];

    scheduler
      .expectObservable(accountsService.listAllAddonActivations('AG-123', ['testapp1', 'testapp2']))
      .toBe('-(a|)', {
        a: expected,
      });
  });

  it('should throw an error if the getAccounts throws an error', () => {
    const accountsServiceErrorMock = new AccountsServiceErrorMock(scheduler);
    const accountsService = new AccountsService(accountsServiceErrorMock);
    scheduler.expectObservable(accountsService.getAccounts('ABC', 'AG-999')).toBe('-#', {}, new Error('Server error'));
  });

  it('should throw an error if the listAddonActications throws an error', () => {
    const accountsServiceErrorMock = new AccountsServiceErrorMock(scheduler);
    const accountsService = new AccountsService(accountsServiceErrorMock);
    scheduler
      .expectObservable(accountsService.listAddonActivations('AG-123', 'testapps'))
      .toBe('-#', {}, new Error('Server error'));
  });

  it('should throw an error if the listAllAddonActivation throws an error', () => {
    const accountsServiceErrorMock = new AccountsServiceErrorMock(scheduler);
    const accountsService = new AccountsService(accountsServiceErrorMock);
    scheduler
      .expectObservable(accountsService.listAllAddonActivations('AG-123', ['testapp3', 'testapp4']))
      .toBe('-#', {}, new Error('Server error'));
  });

  it('should return an empty list if the response is empty', () => {
    class NoAccountsMock extends AccountsServiceMock {
      list(): Observable<ListResponse> {
        return of({} as ListResponse);
      }
    }
    const accountsService = new AccountsService(new NoAccountsMock(scheduler));
    scheduler.expectObservable(accountsService.getAccounts('pid', 'bid')).toBe('(x|)', { x: [] });
  });
});
