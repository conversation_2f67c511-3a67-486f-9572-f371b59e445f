import { Inject, Injectable } from '@angular/core';
import { AccountGroup, AccountGroupApiService, ProjectionFilter } from '@galaxy/account-group';
import { BusinessSearchResult, BusinessService } from '@vendasta/sales-v2';
import { BehaviorSubject, Observable, ReplaySubject, firstValueFrom } from 'rxjs';
import { USER_INFO_TOKEN } from '../../core/feature-flag.service';
import { LoggedInUserInfo } from '../../logged-in-user-info';

@Injectable()
export class AccountSearchService {
  private readonly loading$$ = new BehaviorSubject<boolean>(false);
  readonly loading$ = this.loading$$.asObservable();

  private readonly lastEnteredEmail$$ = new ReplaySubject<string>(1);
  readonly lastEnteredEmail$ = this.lastEnteredEmail$$.asObservable();

  constructor(
    @Inject(USER_INFO_TOKEN)
    private readonly userInfo$: Observable<
      Pick<LoggedInUserInfo, 'partnerId' | 'marketId' | 'unifiedUserId' | 'hasAccessToAllAccountsInMarket'>
    >,
    private readonly businessService: BusinessService,
    private readonly accountGroupApiService: AccountGroupApiService,
  ) {}

  searchByEmail(email: string): Promise<AccountGroup[]> {
    this.beginLoading();
    if (email !== '') {
      this.lastEnteredEmail$$.next(email);
      return this.doSearch(email)
        .catch(() => {
          return <BusinessSearchResult[]>[];
        })
        .then((businessResults) => {
          if (!!businessResults && businessResults.length > 0) {
            const AGIDs = businessResults.map((bs) => bs.accountGroupId);
            return this.getInformationForAccounts(AGIDs);
          } else {
            this.doneLoading();
            return [];
          }
        });
    }
  }

  private beginLoading(): void {
    this.loading$$.next(true);
  }

  private doneLoading(): void {
    window.setTimeout(() => this.loading$$.next(false), 350);
  }

  private async getInformationForAccounts(accountGroups: string[]): Promise<AccountGroup[]> {
    const projectionFilter = new ProjectionFilter({ napData: true });
    const result = this.accountGroupApiService.getMulti(accountGroups, projectionFilter);
    this.doneLoading();
    return firstValueFrom(result);
  }

  private async doSearch(emailQuery: string): Promise<BusinessSearchResult[]> {
    const userInfo = await firstValueFrom(this.userInfo$);
    const assignedUserId = userInfo.hasAccessToAllAccountsInMarket ? '' : userInfo.unifiedUserId;
    const result = this.businessService.searchBusinessesByContactEmail(
      userInfo.partnerId,
      userInfo.marketId,
      emailQuery,
      assignedUserId,
    );
    return firstValueFrom(result);
  }
}
