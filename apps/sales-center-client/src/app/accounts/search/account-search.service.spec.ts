import { AccountGroup, AccountGroupApiService } from '@galaxy/account-group';
import { BusinessService } from '@vendasta/sales-v2';
import { BusinessSearchResult } from '@vendasta/sales-v2/lib/business.service';
import { EMPTY, Observable, of } from 'rxjs';
import { LoggedInUserInfo } from '../../logged-in-user-info';
import { AccountSearchService } from './account-search.service';

const testAccountGroups: AccountGroup[] = [
  <AccountGroup>{
    accountGroupId: 'AG-1234',
    napData: {
      workNumber: ['+1 234 567-8901'],
      country: 'US',
      zip: '90210',
      address: '404 Dang Street',
      state: 'NY',
      companyName: 'Business Not Found',
    },
  },
  <AccountGroup>{
    accountGroupId: 'AG-1235',
    napData: {
      workNumber: ['+1 234 567-1245'],
      country: 'US',
      zip: '90210',
      address: '404 Shoot Street',
      state: 'MI',
      companyName: 'Shoot st biz',
    },
  },
];

const testBusinessResults: BusinessSearchResult[] = [
  <BusinessSearchResult>{
    accountGroupId: 'AG-1234',
    updated: new Date(),
  },
  <BusinessSearchResult>{
    accountGroupId: 'AG-1235',
    updated: new Date(),
  },
];

const testEmail1 = '<EMAIL>';
const testEmail2 = '<EMAIL>';
const testEmail3 = '<EMAIL>';

class MockBusinessService {
  searchBusinessesByContactEmail(
    partner: string,
    market: string,
    contactEmailQuery: string,
  ): Observable<BusinessSearchResult[]> {
    if (contactEmailQuery === testEmail1) {
      return of([testBusinessResults[0]]);
    }

    if (contactEmailQuery === testEmail2) {
      return of([testBusinessResults[1]]);
    }

    return EMPTY;
  }
}

class MockAccountGroupApiService {
  getMulti(accountGroupIds: string[]): Observable<AccountGroup[]> {
    const agsToReturn = [];
    for (const ag of accountGroupIds) {
      agsToReturn.push(...testAccountGroups.filter((testAg) => testAg.accountGroupId === ag));
    }
    return agsToReturn !== [] ? of(agsToReturn) : EMPTY;
  }
}

describe('AccountSearchService', () => {
  // tslint:disable-next-line:prefer-const
  let service: AccountSearchService;

  beforeEach(() => {
    const busService = new MockBusinessService();
    const acctService = new MockAccountGroupApiService();
    service = new AccountSearchService(
      of({
        partnerId: 'TEST',
        marketId: 'test-market',
      } as LoggedInUserInfo),
      busService as BusinessService,
      acctService as AccountGroupApiService,
    );
  });

  it('should return Account Groups if the email is matched to an account', (done) => {
    const results = service.searchByEmail(testEmail1);

    expect(results).toBeTruthy();
    results.then((r) => {
      expect(r).toEqual([testAccountGroups[0]]);
      done();
    });
  });

  it('should return nothing if the email is not matched to any accounts', (done) => {
    const results = service.searchByEmail(testEmail3);

    results.then((r) => {
      expect(r).toEqual([]);
      done();
    });
  });
});
