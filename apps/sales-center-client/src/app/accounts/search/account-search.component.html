<glxy-page [pagePadding]="false" class="page">
  <glxy-page-toolbar>
    <glxy-page-nav>
      <glxy-page-nav-button
        previousPageTitle="{{ 'MANAGE_ACCOUNTS.TITLE' | translate }}"
        previousPageUrl="/"
      ></glxy-page-nav-button>
    </glxy-page-nav>

    <glxy-page-title>
      {{ 'MANAGE_ACCOUNTS.ACCOUNT_SEARCH' | translate }}
      <va-badge class="early-access" color="blue-solid">
        {{ 'COMMON.EARLY_ACCESS' | translate }}
      </va-badge>
    </glxy-page-title>
  </glxy-page-toolbar>

  <form [formGroup]="emailForm" autocomplete="off">
    <mat-form-field class="search-box" appearance="outline">
      <mat-label>
        {{ 'MANAGE_ACCOUNTS.SEARCH_BY_EMAIL' | translate }}
      </mat-label>
      <input #emailInput matInput formControlName="email" type="email" />
      <mat-error *ngIf="emailForm.touched && emailForm.invalid">
        {{ 'MANAGE_ACCOUNTS.ENTER_VALID_EMAIL' | translate }}
      </mat-error>
    </mat-form-field>
  </form>

  <table mat-table [dataSource]="searchResults$ | async">
    <ng-container matColumnDef="lastUpdated">
      <th mat-header-cell *matHeaderCellDef>
        {{ 'MANAGE_ACCOUNTS.UPDATED' | translate }}
      </th>
      <td mat-cell *matCellDef="let result">{{ result.updated }}</td>
    </ng-container>
    <ng-container matColumnDef="business">
      <th mat-header-cell *matHeaderCellDef>
        {{ 'COMMON.LABELS.ACCOUNT' | translate }}
      </th>
      <td mat-cell *matCellDef="let result">
        <a [routerLink]="result.accountGroupId | generateAccountGroupUrl" [queryParams]="{ origin: 'accounts-search' }">
          {{ result.napData.companyName }}
        </a>
      </td>
    </ng-container>
    <ng-container matColumnDef="address">
      <th mat-header-cell *matHeaderCellDef>
        {{ 'SALES_ORDERS.COMMON_FIELDS.BUSINESS_ADDRESS' | translate }}
      </th>
      <td mat-cell *matCellDef="let result">
        {{ result.napData.address }},
        <br />
        {{ result.napData.city }}, {{ result.napData.state }},
        {{ result.napData.zip }}
      </td>
    </ng-container>

    <ng-container matColumnDef="phoneNumber">
      <th mat-header-cell *matHeaderCellDef>
        {{ 'SALES_ORDERS.COMMON_FIELDS.BUSINESS_PHONE_NUMBER' | translate }}
      </th>
      <td mat-cell *matCellDef="let result">
        {{ result?.napData?.workNumber || '' | phone : result.napData.country }}
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
  </table>
  <ng-container *ngIf="(loading$ | async) === true; then loadingState"></ng-container>
  <ng-container
    *ngIf="(hasSearchResults$ | async) === false && (loading$ | async) === false; then emptyState"
  ></ng-container>
  <ng-container
    *ngIf="
      (searchResults$ | async) && (searchResults$ | async).length === 0 && (loading$ | async) === false;
      then noAccountsState
    "
  ></ng-container>

  <ng-template #notAvailable>
    <p class="coming-soon">Coming soon</p>
  </ng-template>
  <ng-template #emptyState>
    <div class="empty-state">
      <mat-icon>help_outline</mat-icon>
      <h4>
        {{ 'MANAGE_ACCOUNTS.EMAIL_ADDRESS_HINT' | translate }}
      </h4>
    </div>
  </ng-template>
  <ng-template #noAccountsState>
    <div class="empty-state">
      <mat-icon>help_outline</mat-icon>
      <h4>
        {{ 'MANAGE_ACCOUNTS.EMAIL_NOT_FOUND' | translate }}
      </h4>
    </div>
  </ng-template>
  <ng-template #loadingState>
    <uikit-list-stencil [showHeader]="false"></uikit-list-stencil>
  </ng-template>
</glxy-page>
