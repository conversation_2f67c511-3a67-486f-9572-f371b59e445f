import { Compo<PERSON>, On<PERSON><PERSON>roy, OnInit, <PERSON>pe, PipeTransform } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { AccountGroup } from '@galaxy/account-group';
import { SubscriptionList } from '@vendasta/rx-utils';
import { Observable, ReplaySubject, firstValueFrom } from 'rxjs';
import { debounceTime, distinctUntilChanged, map } from 'rxjs/operators';
import { AccountSearchService } from './account-search.service';

const DELAY_BEFORE_PERFORMING_WORK = 500;

@Component({
  selector: 'app-account-search',
  templateUrl: './account-search.component.html',
  styleUrls: ['./account-search.component.scss'],
  standalone: false,
})
export class AccountSearchComponent implements OnInit, OnDestroy {
  emailForm: UntypedFormGroup;
  readonly lastEnteredEmail$: Observable<string>;

  private readonly searchResults$$ = new ReplaySubject<AccountGroup[]>(1);
  readonly searchResults$ = this.searchResults$$.asObservable();
  readonly hasSearchResults$: Observable<boolean> = this.searchResults$.pipe(map((s) => Boolean(s)));
  readonly loading$: Observable<boolean>;
  private readonly subscriptions = SubscriptionList.new();

  readonly date = Date.now();

  displayedColumns: string[] = ['business', 'address', 'phoneNumber'];

  constructor(
    private readonly formBuilder: UntypedFormBuilder,
    private readonly accountSearchService: AccountSearchService,
  ) {
    this.loading$ = this.accountSearchService.loading$;
    this.lastEnteredEmail$ = this.accountSearchService.lastEnteredEmail$;
  }

  private validateAndPerformAccountSearch(email: string): void {
    if (this.emailForm.valid) {
      this.doSearch(email);
    } else {
      this.searchResults$$.next(null);
    }
  }

  private async setInitialStateOfSearchBar(): Promise<void> {
    const lastSearchTerm = await firstValueFrom(this.accountSearchService.lastEnteredEmail$);
    this.emailForm.get('email').setValue(lastSearchTerm);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private async doSearch(email: string, assignedUserId?: string): Promise<void> {
    this.accountSearchService.searchByEmail(email).then((r) => this.searchResults$$.next(r));
  }

  ngOnInit(): void {
    this.emailForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.minLength(3)]],
    });
    const emailSearchBarChanges$ = this.emailForm.valueChanges.pipe(
      debounceTime(DELAY_BEFORE_PERFORMING_WORK),
      distinctUntilChanged(),
    );
    this.subscriptions.add(emailSearchBarChanges$, (formChanges) =>
      this.validateAndPerformAccountSearch(formChanges.email),
    );

    this.setInitialStateOfSearchBar();
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }
}

@Pipe({
  name: 'generateAccountGroupUrl',
  standalone: false,
})
export class GenerateAccountGroupUrl implements PipeTransform {
  transform(accountGroupId: string): string {
    return `/info/${accountGroupId}`;
  }
}
