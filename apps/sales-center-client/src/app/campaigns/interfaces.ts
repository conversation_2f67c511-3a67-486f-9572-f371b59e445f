import { WorkStates } from '@vendasta/rx-utils/work-state';

export interface StatusChangeEvent {
  recipientCampaignId: string;
  active: boolean;
}

export type CampaignStatus = 'active' | 'stopped' | 'completed' | 'active-pending'; // Active-Pending is a client-side status only.

export interface RecipientCampaign {
  recipient_campaign_id: string;
  campaign_id: string;
  status: CampaignStatus;
}

export type StepType = 'snapshot-creation' | 'email';

export interface ScheduleStep {
  step_type: StepType;
  seconds_after_last_email: number;
  name: string;
  template_id: string;
}

export interface Campaign {
  name: string;
  status: CampaignStatus;
  campaign_id: string;
  campaign_schedule: ScheduleStep[];
}

export interface UiCampaign {
  name: string;
  recipientCampaignId: string;
  statusState: WorkStates<CampaignStatus>;
}

export interface UiRecipientCampaign {
  recipientCampaignId: string;
  campaignId: string;
  statusState: WorkStates<CampaignStatus>;
}
