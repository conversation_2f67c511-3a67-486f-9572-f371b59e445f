import { Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';
import { SubscriptionList } from '@vendasta/rx-utils';
import { ScheduleStep } from '@vendasta/sales';
import { MarketingCampaign } from '@vendasta/sales-v2';
import { BehaviorSubject, ReplaySubject, firstValueFrom } from 'rxjs';
import { tap } from 'rxjs/operators';
import { CampaignDialogService } from '../campaign-dialog.service';
import { CampaignSidePanelService } from '../campaign-side-panel/campaign-side-panel.service';
@Component({
  selector: 'app-campaign-previews',
  templateUrl: './campaign-previews.component.html',
  styleUrls: ['./campaign-previews.component.scss'],
  standalone: false,
})
export class CampaignPreviewsComponent implements OnInit, OnChanges, OnDestroy {
  @Input() campaign: MarketingCampaign;
  @Input() accountGroupID: string;
  @Input() userID: string;

  private readonly previewTemplate$$ = new ReplaySubject<{ step: ScheduleStep; template: any }>(1);
  readonly previewTemplate$ = this.previewTemplate$$.asObservable().pipe(tap(() => this.previewLoading$$.next(false)));

  private readonly previewLoading$$ = new BehaviorSubject<boolean>(false);
  readonly previewLoading$ = this.previewLoading$$.asObservable();

  private readonly currentCampaign$$ = new BehaviorSubject<{
    campaign: MarketingCampaign;
    step: number;
    daysSince: number;
  }>(null);
  readonly currentCampaign$ = this.currentCampaign$$.asObservable();

  subscriptions = SubscriptionList.new();

  constructor(private readonly dialogService: CampaignDialogService) {}

  ngOnInit(): void {
    this.currentCampaign$$.next({ campaign: this.campaign, step: 0, daysSince: 0 });

    this.subscriptions.add(
      this.dialogService.isPreviewLoading$(this.campaign.campaignId, this.campaign.campaignSchedule[0].templateId),
      (previewLoading) => {
        this.previewLoading$$.next(previewLoading);
      },
    );

    this.loadCampaignStepPreview(this.campaign, 0, this.accountGroupID);
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }

  ngOnChanges(changes: SimpleChanges): void {
    let campaign = this.currentCampaign$$.getValue()?.campaign;
    if (changes.campaign?.currentValue) {
      this.currentCampaign$$.next({
        campaign: changes.campaign.currentValue,
        step: 0,
        daysSince: 0,
      });
      campaign = changes.campaign.currentValue;
    }

    this.subscriptions.add(
      this.dialogService.isPreviewLoading$(campaign.campaignId, campaign.campaignSchedule[0].templateId),
      (previewLoading) => {
        this.previewLoading$$.next(previewLoading);
      },
    );
    this.loadCampaignStepPreview(campaign, 0, this.accountGroupID);
  }

  changeStep(direction: string, currentStep: number, totalSteps: number, campaign: MarketingCampaign): void {
    this.previewLoading$$.next(true);
    const index = currentStep;
    if (direction === 'left' && currentStep !== 0) {
      this.loadCampaignStepPreview(campaign, currentStep - 1, this.accountGroupID);
      this.currentCampaign$$.next({
        campaign: campaign,
        step: currentStep - 1,
        daysSince: CampaignSidePanelService.calculateDaysSince(this.campaign, index - 1),
      });
    } else if (direction === 'right' && currentStep + 1 < totalSteps) {
      this.loadCampaignStepPreview(campaign, currentStep + 1, this.accountGroupID);
      this.currentCampaign$$.next({
        campaign: campaign,
        step: currentStep + 1,
        daysSince: CampaignSidePanelService.calculateDaysSince(this.campaign, index + 1),
      });
    }
  }

  async loadCampaignStepPreview(campaign: MarketingCampaign, step: number, accountGroupID: string): Promise<void> {
    if (this.campaign.campaignSchedule[step].stepType === 'email') {
      const preview = await firstValueFrom(
        this.dialogService.preview$(
          this.campaign.campaignId,
          this.campaign.campaignSchedule[step].templateId,
          accountGroupID,
          this.userID,
        ),
      );

      this.previewTemplate$$.next({ step: this.campaign.campaignSchedule[step], template: preview });
      this.previewLoading$$.next(false);
    } else {
      this.previewTemplate$$.next({ step: this.campaign.campaignSchedule[step], template: null });
      this.previewLoading$$.next(false);
    }
  }
}
