@use 'design-tokens' as *;

:host ::ng-deep {
  store-list-stencil {
    mat-card.list-container.mat-mdc-card {
      border: none;
    }
  }
}

.current-step-info {
  text-align: center;
}

.preview-stepper {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 24px;
  position: sticky;
  top: 0;
  background: $lightest-gray;
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.subject-line {
  margin-bottom: 8px;
  margin-top: 16px;
}

.snapshot-creation-preview {
  padding: 16px 64px;
  align-content: center;
  margin-top: 8px;
  text-align: center;
  color: $dark-gray;

  span {
    font-size: 25px;
  }

  mat-icon {
    height: 75px;
    width: 75px;
    font-size: 75px;
    color: $gray;
  }
}
