<ng-container *ngIf="currentCampaign$ | async as campaignData">
  <div class="preview-stepper">
    <button
      mat-icon-button
      class="stepper"
      [disabled]="campaignData.step === 0"
      (click)="
        changeStep('left', campaignData.step, campaignData.campaign.campaignSchedule.length, campaignData.campaign)
      "
    >
      <mat-icon>chevron_left</mat-icon>
    </button>
    <div class="current-step-info">
      <div>{{ campaignData.campaign.name }}</div>
      <div>
        {{ campaignData.campaign.campaignSchedule[campaignData.step].name }}
      </div>
      <div>
        {{
          'CAMPAIGNS.EVENT_STEP_INFO'
            | translate
              : {
                  firstCount: campaignData.step + 1,
                  totalCount: campaignData.campaign.campaignSchedule.length,
                  currentDay: campaignData.daysSince
                }
        }}
      </div>
    </div>
    <button
      mat-icon-button
      class="stepper"
      [disabled]="campaignData.step + 1 >= campaignData.campaign.campaignSchedule.length"
      (click)="
        changeStep('right', campaignData.step, campaignData.campaign.campaignSchedule.length, campaignData.campaign)
      "
    >
      <mat-icon>chevron_right</mat-icon>
    </button>
  </div>
  <div *ngIf="(previewLoading$ | async) === false; else previewLoading">
    <ng-container *ngIf="previewTemplate$ | async as previewData">
      <ng-container *ngIf="previewData.step.stepType === 'email'">
        <div class="subject-line">
          <h4>{{ 'CAMPAIGNS.SUBJECT_LINE' | translate }}</h4>
          <div class="subject-line-text">
            {{ previewData.template.subject }}
          </div>
        </div>
        <div>
          <h4>{{ 'CAMPAIGNS.EMAIL_CONTENT' | translate }}</h4>
          <app-email-preview [templateHtml]="previewData.template.html" [step]="previewData.step"></app-email-preview>
        </div>
      </ng-container>
      <ng-container *ngIf="previewData.step.stepType === 'snapshot-creation'">
        <div class="snapshot-creation-preview">
          <mat-icon>note_add</mat-icon>
          <br />
          <span>{{ 'CAMPAIGNS.SNAPSHOT_CREATION' | translate }}</span>
          <hr />
          <p>
            {{ 'CAMPAIGNS.CREATE_OR_REFRESH_SNAPSHOT_REPORT' | whitelabelTranslate | async }}
          </p>
        </div>
      </ng-container>
    </ng-container>
  </div>
</ng-container>

<ng-template #previewLoading>
  <store-list-stencil [showHeader]="true" rowHeight="50px" [numRows]="5"></store-list-stencil>
</ng-template>
