import { Component, Input, ViewEncapsulation } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { CampaignStep } from '../campaign-dialog.service';
import { ScheduleStep } from '@vendasta/sales-v2';

@Component({
  selector: 'app-email-preview',
  templateUrl: './email-preview.component.html',
  styleUrls: ['./email-preview.component.scss'],
  standalone: false,
})
export class EmailPreviewComponent {
  isEmailPreview: boolean;

  @Input() templateHtml: string;
  @Input() set step(step: CampaignStep | ScheduleStep) {
    this.isEmailPreview = step.stepType === 'email';
  }
}

// This component is required to avoid css styles from email templates bleeding into the rest of the app
@Component({
  selector: 'app-template-preview',
  template: ` <div [innerHTML]="safeTemplate"></div> `,
  encapsulation: ViewEncapsulation.ShadowDom,
  standalone: false,
})
export class TemplatePreviewComponent {
  safeTemplate: SafeHtml;

  constructor(private readonly sanitizer: DomSanitizer) {}
  @Input() set templateHtml(html: string) {
    this.safeTemplate = html !== null ? this.sanitizer.bypassSecurityTrustHtml(html) : '';
  }
}
