import { MarketingCampaign } from '@vendasta/sales';
import { EMPTY, Observable, of } from 'rxjs';
import { TestScheduler } from 'rxjs/testing';
import { Campaign, CampaignDialogService } from './campaign-dialog.service';
import { CampaignPreviewResponse, CampaignService } from './vbc-campaign-apis';

let sched: TestScheduler;

const mockCampaign = [
  {
    campaignSchedule: [
      {
        stepType: 'email',
        templateId: '10x-email-1',
        secondsAfterLastEmail: 0,
        name: 'Snapshot Report',
      },
      {
        stepType: 'email',
        templateId: '10x-email-2',
        secondsAfterLastEmail: 604800,
        name: 'Reputation Management',
      },
      {
        stepType: 'email',
        templateId: '10x-email-3',
        secondsAfterLastEmail: 604800,
        name: 'Listings',
      },
      {
        stepType: 'email',
        templateId: 'default-social',
        secondsAfterLastEmail: 604800,
        name: 'Social',
      },
    ],
    name: 'Local Marketing Snapshot',
    campaignId: 'CAMPAIGN-f523078b2551432a9ae207c2e8803c24',
  },
] as MarketingCampaign[];

const templateMock = {
  html: `<some html>`,
  name: 'template name',
  subject: 'template subject',
};

class CampaignsMock implements CampaignService {
  constructor(
    private readonly getMultiResponse: Observable<MarketingCampaign[]>,
    private readonly getPreviewResponse: Observable<CampaignPreviewResponse>,
  ) {}

  getMultiCampaigns(): Observable<MarketingCampaign[]> {
    return this.getMultiResponse;
  }

  listCampaigns = () => EMPTY;

  setStatus = () => EMPTY;

  getCampaignPreview(): Observable<CampaignPreviewResponse> {
    return this.getPreviewResponse;
  }
}

describe('CampaignDialogService', () => {
  let service: CampaignDialogService;

  beforeEach(() => (sched = new TestScheduler((a, b) => expect(a).toEqual(b))));
  afterEach(() => sched.flush());

  describe('loadCampaign', () => {
    beforeEach(() => {
      const campaigns = new CampaignsMock(of(mockCampaign), EMPTY);
      service = new CampaignDialogService(campaigns, sched);
    });

    it('should load a campaign from the response', () => {
      const campaignId = 'CAMPAIGN-f523078b2551432a9ae207c2e8803c24';
      const businessId = 'AG-123';
      const expected = <Campaign>{
        campaignId: campaignId,
        name: 'Local Marketing Snapshot',
        steps: [
          {
            stepType: 'email',
            stepName: 'Snapshot Report',
            templateId: '10x-email-1',
            secondsAfterLastEmail: 0,
          },
          {
            stepType: 'email',
            stepName: 'Reputation Management',
            templateId: '10x-email-2',
            secondsAfterLastEmail: 604800,
          },
          {
            stepType: 'email',
            stepName: 'Listings',
            templateId: '10x-email-3',
            secondsAfterLastEmail: 604800,
          },
          {
            stepType: 'email',
            stepName: 'Social',
            templateId: 'default-social',
            secondsAfterLastEmail: 604800,
          },
        ],
      };

      sched.expectObservable(service.campaign$(businessId, campaignId)).toBe('-x', {
        x: expected,
      });
    });
  });

  describe('loadPreview', () => {
    beforeEach(() => {
      const campaigns = new CampaignsMock(EMPTY, of(templateMock));
      service = new CampaignDialogService(campaigns, sched);
    });

    it('should load the template from the response', () => {
      sched.expectObservable(service.preview$('campaignId', 'template-id', '', 'userId')).toBe('-x', {
        x: {
          html: `<some html>`,
          name: 'template name',
          subject: 'template subject',
        },
      });
    });
  });
});
