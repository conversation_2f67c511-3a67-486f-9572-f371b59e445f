import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { EmailTemplateService } from '@vendasta/campaigns';
import { CampaignSDKService, MarketingCampaign } from '@vendasta/sales';
import { combineLatest, Observable } from 'rxjs';
import { map, switchMap, take } from 'rxjs/operators';
import { LoggedInUserInfoService } from '../logged-in-user-info';
import { Campaign, CampaignStatus, RecipientCampaign } from './interfaces';

export const CAMPAIGN_SERVICE_TOKEN = 'CAMPAIGN_SERVICE';

type CampaignStatusInput = 'active' | 'paused';

export interface ListCampaignsResponse {
  recipient_campaigns: RecipientCampaign[];
}

export interface GetMultiCampaignsResponse {
  campaigns: Campaign[];
}

export interface SetStatusResponse {
  recipientCampaignId: string;
  status: CampaignStatus;
}

export interface CampaignPreviewResponse {
  html: string;
  name: string;
  subject: string;
}

export interface CampaignService {
  listCampaigns(businessId: string): Observable<ListCampaignsResponse>;

  getMultiCampaigns(businessId: string, campaignIds: string[]): Observable<MarketingCampaign[]>;

  setStatus(recipientCampaignId: string, active: boolean): Observable<SetStatusResponse>;

  getCampaignPreview(templateId: string, accountGroupID: string, userID: string): Observable<CampaignPreviewResponse>;
}

@Injectable()
export class VbcCampaignApis implements CampaignService {
  constructor(
    private readonly http: HttpClient,
    private readonly user: LoggedInUserInfoService,
    private readonly campaignService: CampaignSDKService,
    private readonly emailTemplateService: EmailTemplateService,
  ) {}

  listCampaigns(businessId: string): Observable<ListCampaignsResponse> {
    return this.user.vbcHost$.pipe(
      take(1),
      switchMap((vbcHost) => this.doListCampaigns(vbcHost, businessId)),
    );
  }

  getMultiCampaigns(businessId: string, campaignIds: string[]): Observable<MarketingCampaign[]> {
    return combineLatest([this.user.partnerId$, this.user.marketId$]).pipe(
      take(1),
      switchMap(([partnerId, marketId]) => {
        return this.campaignService.getMultiCampaigns(partnerId, marketId, businessId, campaignIds);
      }),
    );
  }

  setStatus(recipientCampaignId: string, active: boolean): Observable<SetStatusResponse> {
    const status: CampaignStatusInput = active ? 'active' : 'paused';
    return this.user.vbcHost$.pipe(
      take(1),
      switchMap((vbcHost) => this.doSetStatus(vbcHost, recipientCampaignId, status)),
    );
  }

  getCampaignPreview(templateId: string, accountGroupID: string, userID: string): Observable<CampaignPreviewResponse> {
    return this.user.partnerIdAndMarketId$.pipe(
      switchMap((partnerMarket) => {
        return this.emailTemplateService.previewEmailTemplate(
          templateId,
          accountGroupID,
          partnerMarket.partnerId,
          partnerMarket.marketId,
          '',
          userID,
          null,
        );
      }),
      map((resp) => {
        return {
          html: resp.htmlBody,
          name: resp.name,
          subject: resp.subject,
        };
      }),
    );
  }

  private doListCampaigns(vbcHost: string, businessId: string): Observable<ListCampaignsResponse> {
    const url = `${vbcHost}/api/v1/business/campaign/list/`;
    const body = {
      accountGroupId: businessId,
    };
    return this.http
      .post(url, body, { withCredentials: true })
      .pipe(map((response) => response['data'] as ListCampaignsResponse));
  }

  private doSetStatus(
    vbcHost: string,
    recipientCampaignId: string,
    status: CampaignStatusInput,
  ): Observable<SetStatusResponse> {
    const url = `${vbcHost}/api/v1/recipient-campaign/status/update/`;
    const body = {
      recipientCampaignId: recipientCampaignId,
      status: status,
    };
    return this.http
      .post(url, body, { withCredentials: true })
      .pipe(map((response) => response['data'] as SetStatusResponse));
  }
}
