import { Inject, Injectable, Optional } from '@angular/core';
import { Observable, SchedulerLike } from 'rxjs';
import { map } from 'rxjs/operators';
import { ObservableWorkStateMap } from '@vendasta/rx-utils/work-state';
import { doWorkIfInitial } from '../rx-utils/state-maps';
import { CAMPAIGN_SERVICE_TOKEN, CampaignService } from './vbc-campaign-apis';
import { MarketingCampaign } from '@vendasta/sales';

export interface CampaignStep {
  stepType: 'snapshot-creation' | 'email';
  secondsAfterLastEmail: number;
  stepName: string;
  templateId: string;
}

export interface Campaign {
  name: string;
  campaignId: string;
  steps: CampaignStep[];
}

export interface CampaignPreview {
  html: string;
  name: string;
  subject: string;
}

@Injectable()
export class CampaignDialogService {
  private readonly campaignWSM = new ObservableWorkStateMap<string, Campaign>();
  private readonly previewWSM = new ObservableWorkStateMap<string, CampaignPreview>();

  constructor(
    @Inject(CAMPAIGN_SERVICE_TOKEN) private readonly campaignApis: CampaignService,
    @Inject('scheduler') @Optional() private readonly scheduler?: SchedulerLike,
  ) {
    this.scheduler = this.scheduler || undefined;
  }

  static rawCampaignToCampaign(response: MarketingCampaign[]): Campaign {
    const rawCampaign = response[0];
    return {
      campaignId: rawCampaign.campaignId,
      name: rawCampaign.name,
      steps: rawCampaign.campaignSchedule?.map((schedule) => {
        return <CampaignStep>{
          stepName: schedule.name,
          stepType: schedule.stepType,
          secondsAfterLastEmail: schedule.secondsAfterLastEmail,
          templateId: schedule.templateId,
        };
      }),
    };
  }

  campaign$(businessId: string, campaignId: string): Observable<Campaign> {
    const work = () =>
      this.campaignApis
        .getMultiCampaigns(businessId, [campaignId])
        .pipe(map((response) => CampaignDialogService.rawCampaignToCampaign(response)));

    return doWorkIfInitial(campaignId, this.campaignWSM, work, this.scheduler);
  }

  preview$(
    campaignId: string,
    templateId: string,
    accountGroupID: string,
    userId: string,
  ): Observable<CampaignPreview> {
    const work = () => this.campaignApis.getCampaignPreview(templateId, accountGroupID, userId);
    return doWorkIfInitial(campaignId + templateId + accountGroupID + userId, this.previewWSM, work, this.scheduler);
  }

  isPreviewLoading$(campaignId: string, templateId: string): Observable<boolean> {
    return this.previewWSM.isLoading$(campaignId + templateId);
  }

  isPreviewSuccess$(campaignId: string, templateId: string): Observable<boolean> {
    return this.previewWSM.isSuccess$(campaignId + templateId);
  }
}
