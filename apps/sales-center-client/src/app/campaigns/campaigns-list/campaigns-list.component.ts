import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { StatusChangeEvent, UiCampaign } from '../interfaces';

@Component({
  selector: 'app-campaigns-list',
  templateUrl: './campaigns-list.component.html',
  styleUrls: ['./campaigns-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class CampaignsListComponent {
  @Input()
  campaigns: UiCampaign[];

  @Output() statusChanged = new EventEmitter<StatusChangeEvent>();

  pause(recipientCampaignId: string): void {
    this.statusChanged.emit(<StatusChangeEvent>{
      recipientCampaignId: recipientCampaignId,
      active: false,
    });
  }

  resume(recipientCampaignId: string): void {
    this.statusChanged.emit(<StatusChangeEvent>{
      recipientCampaignId: recipientCampaignId,
      active: true,
    });
  }
}
