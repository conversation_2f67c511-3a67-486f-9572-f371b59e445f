<ng-container
  *ngIf="{
    status: status$ | async,
    loading: isLoading$ | async,
    isFailed: isFailed$ | async
  } as results"
>
  <button
    *ngIf="results.isFailed !== true"
    mat-icon-button
    [disabled]="results.status === 'completed' || results.status === 'active-pending'"
    color="primary"
    [matMenuTriggerFor]="actions"
  >
    <mat-icon>more_vert</mat-icon>
  </button>
  <button
    *ngIf="results.isFailed === true"
    mat-icon-button
    matTooltip="{{ 'CAMPAIGNS.FAILED_STATUS_UPDATE' | translate }}"
    [matMenuTriggerFor]="actions"
  >
    <mat-icon color="warn">error</mat-icon>
  </button>
</ng-container>

<mat-menu #actions="matMenu">
  <ng-container [ngSwitch]="status$ | async">
    <ng-container *ngSwitchCase="'active'">
      <button mat-menu-item (click)="pause()">
        {{ 'COMMON.ACTION_LABELS.PAUSE' | translate }}
      </button>
    </ng-container>
    <ng-container *ngSwitchCase="'stopped'">
      <button mat-menu-item (click)="resume()">
        {{ 'COMMON.ACTION_LABELS.RESUME' | translate }}
      </button>
    </ng-container>
  </ng-container>
</mat-menu>
