<mat-list>
  <mat-list-item *ngFor="let campaign of recipientCampaigns">
    <div class="campaign">
      <div mat-list-text class="campaign-name-stencil stencil-shimmer"></div>
      <app-status-icon [status]="campaign.statusState.workResults$ | async"></app-status-icon>
      <app-status-toggle
        [statusState]="campaign.statusState"
        (paused)="pause(campaign.recipientCampaignId)"
        (resumed)="resume(campaign.recipientCampaignId)"
      ></app-status-toggle>
    </div>
  </mat-list-item>
</mat-list>
