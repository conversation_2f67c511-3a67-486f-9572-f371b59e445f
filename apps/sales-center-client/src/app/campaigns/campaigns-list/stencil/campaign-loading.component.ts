import { Component, EventEmitter, Input, Output } from '@angular/core';
import { StatusChangeEvent, UiRecipientCampaign } from '../../interfaces';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-campaign-loading',
  templateUrl: './campaign-loading.component.html',
  styleUrls: ['./campaign-loading.component.scss'],
  standalone: false,
})
export class CampaignLoadingComponent {
  constructor(private readonly translate: TranslateService) {}
  @Input() recipientCampaigns: UiRecipientCampaign[];
  @Input() placeholderText: string = this.translate.instant('CAMPAIGNS.LOADING_PLACEHOLDER');

  @Output() statusChanged = new EventEmitter<StatusChangeEvent>();

  pause(recipientCampaignId: string): void {
    this.statusChanged.emit(<StatusChangeEvent>{
      recipientCampaignId: recipientCampaignId,
      active: false,
    });
  }

  resume(recipientCampaignId: string): void {
    this.statusChanged.emit(<StatusChangeEvent>{
      recipientCampaignId: recipientCampaignId,
      active: true,
    });
  }
}
