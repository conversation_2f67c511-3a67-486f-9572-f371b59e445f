<mat-list>
  <mat-list-item *ngFor="let campaign of campaigns">
    <div matListItemLine>
      <div class="campaign-name">
        <div class="title-status">
          <span class="title" [matTooltip]="campaign.name">{{ campaign.name }}</span>
          <app-status-icon [status]="campaign.statusState.workResults$ | async"></app-status-icon>
        </div>
        <div>
          <app-status-toggle
            [statusState]="campaign.statusState"
            (paused)="pause(campaign.recipientCampaignId)"
            (resumed)="resume(campaign.recipientCampaignId)"
          ></app-status-toggle>
        </div>
      </div>
    </div>
  </mat-list-item>
</mat-list>
