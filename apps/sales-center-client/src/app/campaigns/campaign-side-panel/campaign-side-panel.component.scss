@use 'design-tokens' as *;

.header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-content: center;
  border-bottom: 1px solid $light-gray;
  margin-bottom: 8px;

  .title {
    margin-left: 8px;
  }
}

.header-actions {
  display: flex;
  align-items: center;

  button {
    margin: 8px;
  }
}

.content {
  padding: 8px;
  margin: 8px;
}

.preview {
  padding: 8px;
}

.form-actions {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  margin-top: 8px;

  .spacing {
    margin-right: 8px;
  }
}

.input {
  width: 100%;
}

@keyframes stencil-shimmer {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 100% 0;
  }
}

.stencil-shimmer {
  background: linear-gradient(-80deg, #c1bebe 10%, #f0f0f0 45%, #f8f8f8 70%, #c1bebe 85%);
  background-size: 500% 100%;
  height: 51.31px;
  border-radius: 5px;
  -webkit-animation: stencil-shimmer 1.5s ease-in 0s infinite;
  animation: stencil-shimmer 1.5s ease-in 0s infinite;
}

.shimmer-outline {
  margin: 4px 0 22px 0;
  border: 1px solid rgb(215, 215, 215);
  border-radius: 5px;
  height: 51.31px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 16px 6px 0 9.5px;
  color: rgba(0, 0, 0, 0.6);

  .shimmer-drop-down {
    margin-top: -4px;
    color: rgba(0, 0, 0, 0.5);
  }
}

@keyframes stencil-fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.stencil-fade {
  animation: stencil-fadein 2s;
}

.loading-overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(1px) opacity(100%);

  .loading-overlay-internal {
    margin: 0;
    position: absolute;
    border-radius: 8px;
    padding: 16px;
    background: $lightest-gray;
    top: 50%;
    left: 50%;
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    text-align: center;
  }

  .spinner {
    width: 100%;
    margin: auto auto 8px;
  }
}

.campaign-form {
  display: flex;
  flex-direction: column;
}
