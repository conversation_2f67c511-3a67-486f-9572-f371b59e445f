<sales-ui-slide-out-panel-header
  [title]="'CAMPAIGNS.BEGIN_EMAIL_CAMPAIGN' | translate"
  icon="email"
></sales-ui-slide-out-panel-header>
<div class="content">
  <h3 class="header-content">
    <ng-container *ngIf="(businessNameLoading$ | async) === false; else loadingName">
      {{ businessName$ | async }}
    </ng-container>
  </h3>
  <form [formGroup]="formGroup" #ngForm="ngForm" class="campaign-form" (ngSubmit)="onSubmit(ngForm)">
    <mat-form-field appearance="outline" *ngIf="(campaignsLoading$ | async) === false; else loadingCampaigns">
      <mat-label>
        {{ 'CAMPAIGNS.CAMPAIGN' | translate }}
      </mat-label>

      <ng-container *ngIf="campaigns$ | async as campaigns">
        <mat-select
          [required]="true"
          formControlName="campaign_select"
          class="input"
          (selectionChange)="setSelectedCampaign($event.value, campaigns)"
        >
          <glxy-input
            placeholder="ACCOUNT_DETAILS.CAMPAIGNS.SEARCH"
            trailingIcon="search"
            [formControl]="searchBarControl"
          ></glxy-input>
          <ng-container *ngFor="let option of campaignOptions$ | async">
            <mat-option [value]="option.value">{{ option.label }}</mat-option>
          </ng-container>
        </mat-select>
      </ng-container>
    </mat-form-field>

    <mat-form-field appearance="outline" *ngIf="(contactsLoading$ | async) === false; else loadingContacts">
      <mat-label>{{ 'CONTACTS.CONTACT' | translate }}</mat-label>
      <mat-select
        *ngIf="contactOptions$ | async as contacts"
        [required]="true"
        formControlName="recipient"
        class="input"
        (selectionChange)="setSelectedRecipient($event.value)"
      >
        <ng-container *ngFor="let contact of contacts">
          <mat-option [value]="contact.contactEmail">
            {{ contact.contactEmail }}
            <span *ngIf="(contact | contactFullName) !== ''">({{ contact | contactFullName }})</span>
          </mat-option>
        </ng-container>
        <mat-option value="ADD_CONTACT">{{ 'CONTACTS.CREATE_CONTACT' | translate }}</mat-option>
      </mat-select>
    </mat-form-field>

    <ng-container *ngIf="(addingNewContact$ | async) === true">
      <mat-form-field appearance="outline">
        <mat-label>
          {{ 'CAMPAIGNS.RECIPIENT_FIRST_NAME' | translate }}
        </mat-label>
        <input matInput [required]="true" formControlName="receiver_first_name" class="input" />
        <mat-error *ngIf="formGroup.controls['receiver_first_name'].invalid">
          {{ 'FORMS.FIRST_NAME_REQUIRED' | translate }}
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>{{ 'CAMPAIGNS.RECIPIENT_LAST_NAME' | translate }}</mat-label>
        <input matInput [required]="true" formControlName="receiver_last_name" class="input" />
        <mat-error *ngIf="formGroup.controls['receiver_last_name'].invalid">
          {{ 'FORMS.LAST_NAME_REQUIRED' | translate }}
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>{{ 'CAMPAIGNS.RECIPIENT_EMAIL' | translate }}</mat-label>
        <input matInput type="email" [required]="true" formControlName="receiver_email" class="input" />
        <mat-error *ngIf="formGroup.controls['receiver_email'].invalid">
          {{ 'FORMS.EMAIL_REQUIRED_WHEN_CREATING_USER' | translate }}
        </mat-error>
      </mat-form-field>
    </ng-container>

    <div class="form-actions">
      <button mat-stroked-button class="spacing" type="button" (click)="closeSidePanel()">
        {{ 'COMMON.ACTION_LABELS.CANCEL' | translate }}
      </button>
      <button mat-flat-button color="primary" type="submit" [disabled]="formGroup.invalid">
        {{ 'CAMPAIGNS.BEGIN_CAMPAIGN' | translate }}
      </button>
    </div>
  </form>
</div>

<div class="preview" *ngIf="selectedCampaign$ | async">
  <h2>{{ 'CAMPAIGNS.CAMPAIGN_PREVIEW' | translate }}</h2>
  <app-campaign-previews
    [accountGroupID]="accountGroupID"
    [campaign]="selectedCampaign$ | async"
    [userID]="selectedContact$$ | async"
  ></app-campaign-previews>
</div>

<div class="loading-overlay" *ngIf="(isCreatingNewContact$ | async) === true">
  <div class="loading-overlay-internal">
    <mat-spinner class="spinner" diameter="24"></mat-spinner>
    <span>{{ 'ACCOUNT_DETAILS.VERIFY.CREATING_NEW_CONTACT' | translate }}</span>
  </div>
</div>

<div class="loading-overlay" *ngIf="(isAddingContactToCampaign$ | async) === true">
  <div class="loading-overlay-internal border-gradient">
    <mat-spinner class="spinner" diameter="24"></mat-spinner>
    <span>{{ 'CAMPAIGNS.ADDING_CONTACT_TO_CAMPAIGN' | translate }}</span>
  </div>
</div>

<ng-template #loadingCampaigns>
  <div class="shimmer-outline stencil-shimmer">
    <span>{{ 'CAMPAIGNS.CAMPAIGN' | translate }} *</span>
    <mat-icon class="shimmer-drop-down">arrow_drop_down</mat-icon>
  </div>
</ng-template>

<ng-template #loadingContacts>
  <div class="shimmer-outline stencil-shimmer">
    <span>{{ 'CONTACTS.CONTACT' | translate }} *</span>
    <mat-icon class="shimmer-drop-down">arrow_drop_down</mat-icon>
  </div>
</ng-template>

<ng-template #loadingName>
  <mat-spinner mode="indeterminate" diameter="16"></mat-spinner>
</ng-template>
