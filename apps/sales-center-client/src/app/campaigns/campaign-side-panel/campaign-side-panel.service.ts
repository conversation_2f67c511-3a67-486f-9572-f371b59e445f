import { Inject, Injectable } from '@angular/core';
import { AccountGroup, AccountGroupService, ProjectionFilter } from '@galaxy/account-group';
import { TranslateService } from '@ngx-translate/core';
import { CampaignService, CampaignStepType, Statuses } from '@vendasta/campaigns';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import {
  CampaignContact,
  CampaignSDKService,
  ContactCreateAttributesInterface,
  MarketingCampaign,
  ScheduleStep,
} from '@vendasta/sales-v2';
import { BehaviorSubject, Observable, ReplaySubject, firstValueFrom, of } from 'rxjs';
import { catchError, map, shareReplay, startWith, switchMap } from 'rxjs/operators';
import { AllContactFields } from '../../common/contacts/contact-field.enum';
import { ContactShadow } from '../../common/contacts/contact-v2';
import { ContactsV2Service } from '../../common/contacts/contacts-v2.service';
import { AddToCampaignError } from '../../common/errors/sales-v2-sdk/sales-v2-campaign-errors/errors';
import { SalesV2CampaignErrorParserService } from '../../common/errors/sales-v2-sdk/sales-v2-campaign-errors/sales-v2-campaign-error-parser.service';
import { USER_INFO_TOKEN } from '../../core/feature-flag.service';
import { LoggedInUserInfo } from '../../logged-in-user-info';

export interface AccountGroupProvider {
  get(agid: string, pf: ProjectionFilter): Observable<AccountGroup>;
}

@Injectable()
export class CampaignSidePanelService {
  public readonly campaigns$: Observable<MarketingCampaign[]>;

  private readonly contactsLoading$$ = new BehaviorSubject<boolean>(true);
  public readonly contactsLoading$ = this.contactsLoading$$.asObservable();

  private readonly contacts$$ = new ReplaySubject<ContactShadow[]>(1);
  public readonly contacts$ = this.contacts$$.asObservable().pipe(startWith([]));

  private readonly creatingNewContact$$ = new BehaviorSubject<boolean>(false);
  public readonly creatingNewContact$ = this.creatingNewContact$$.asObservable();

  private readonly addingContactToCampaign$$ = new BehaviorSubject<boolean>(false);
  public readonly addingContactToCampaign$ = this.addingContactToCampaign$$.asObservable();

  private readonly campaignSuccessfullyStarted$$ = new BehaviorSubject<boolean>(false);
  public readonly campaignSuccessfullyStarted$ = this.campaignSuccessfullyStarted$$.asObservable();

  private _accountGroupId: string;

  set accountGroupId(agid: string) {
    this._accountGroupId = agid;
    this.getContactsForAccount(agid);
  }

  static calculateDaysSince(campaign: MarketingCampaign, stepIndex: number): number {
    const stepsSinceLastEmail = campaign.campaignSchedule.slice(0, stepIndex + 1);
    const days = stepsSinceLastEmail
      .map((step) => this.secondsToDays(step.secondsAfterLastEmail || 0))
      .reduce((prev, curr) => prev + curr);
    return days + 1;
  }

  static secondsToDays(seconds: number): number {
    return seconds / 86400;
  }

  constructor(
    private readonly campaignSDKService: CampaignSDKService,
    private readonly campaignService: CampaignService,
    private readonly contactsService: ContactsV2Service,
    @Inject(USER_INFO_TOKEN) private readonly userInfo$: Observable<LoggedInUserInfo>,
    private readonly alertService: SnackbarService,
    private readonly errorParser: SalesV2CampaignErrorParserService,
    private readonly translate: TranslateService,
    @Inject(AccountGroupService) private readonly accountGroupService: AccountGroupProvider,
  ) {
    this.campaigns$ = userInfo$.pipe(
      switchMap((userInfo) => {
        return campaignService.list(
          userInfo.partnerId,
          undefined,
          [Statuses.STATUSES_PUBLISHED, Statuses.STATUSES_ACTIVE],
          undefined,
          {
            pageSize: 100,
          },
        );
      }),
      map((v) =>
        v.map(
          (data) =>
            ({
              campaignId: data.campaignId,
              name: data.name,
              campaignSchedule: data.campaignSchedule.map(
                (s) =>
                  ({
                    stepType: this.convertStepType(s.stepType),
                    secondsAfterLastEmail: s.secondsAfterLastEmail,
                    name: s.name,
                    templateId: s.templateId,
                  }) as ScheduleStep,
              ),
              campaignStatus: this.convertStatus(data.status),
            }) as MarketingCampaign,
        ),
      ),
      shareReplay(1),
    );
  }

  private getContactsForAccount(accountGroupID: string): void {
    this.contactsLoading$$.next(true);
    const contacts$ = this.contactsService.list$(accountGroupID, ...AllContactFields);

    firstValueFrom(contacts$)
      .then((contacts) => this.contacts$$.next(contacts))
      .finally(() => this.contactsLoading$$.next(false));
  }

  private createContact(
    accountGroupID: string,
    firstName: string,
    lastName: string,
    email: string,
  ): Observable<boolean> {
    this.creatingNewContact$$.next(true);
    const attr: ContactCreateAttributesInterface = {
      firstName: firstName,
      lastName: lastName,
      email: email,
    };

    return this.contactsService.create$(accountGroupID, attr).pipe(
      map(Boolean),
      catchError(() => of(false)),
    );
  }

  createContactAndStartCampaign(
    accountGroupId: string,
    firstName: string,
    lastName: string,
    contactEmail: string,
    campaignId: string,
  ): void {
    this.createContact(accountGroupId, firstName, lastName, contactEmail).subscribe((successful) => {
      this.creatingNewContact$$.next(false);
      if (successful === true) {
        this.startCampaign(campaignId, accountGroupId, contactEmail);
      } else {
        this.alertService.openErrorSnack('Failed to create a new contact');
      }
    });
  }

  async startCampaign(campaignId: string, accountGroupId: string, contactEmail: string): Promise<void> {
    this.addingContactToCampaign$$.next(true);
    const userInfo = await firstValueFrom(this.userInfo$);

    const contact = <CampaignContact>{
      emailAddress: contactEmail,
      accountGroupId: accountGroupId,
    };

    const pf = new ProjectionFilter({ accountGroupExternalIdentifiers: true });
    const agInfo = await firstValueFrom(this.accountGroupService.get(this._accountGroupId, pf));
    await firstValueFrom(
      this.campaignSDKService.addContactToCampaign(
        agInfo.externalIdentifiers.partnerId,
        agInfo.externalIdentifiers.marketId,
        campaignId,
        userInfo.unifiedUserId,
        contact,
      ),
    )
      .then((result) => {
        this.addingContactToCampaign$$.next(false);
        this.campaignSuccessfullyStarted$$.next(true);
        this.alertService.openSuccessSnack('Successfully added contact to campaign');
        console.log(result);
      })
      .catch((err) => {
        const errMsg = this.getErrorMessage(err);
        this.addingContactToCampaign$$.next(false);
        this.alertService.openErrorSnack(errMsg);
      });
  }

  private getErrorMessage(errObject: any): string {
    const error = this.errorParser.determineAddToCampaignErrorType(JSON.stringify(errObject));
    switch (error.errorType) {
      case AddToCampaignError.MAILING_INFO_NOT_CONFIGURED:
        return this.translate.instant('CAMPAIGNS.ADD_TO_CAMPAIGN_ERRORS.MISSING_CONFIGURATION');
      case AddToCampaignError.SNAPSHOT_MISSING:
        return this.translate.instant('CAMPAIGNS.ADD_TO_CAMPAIGN_ERRORS.SNAPSHOT_MISSING');
      case AddToCampaignError.CANNOT_ACCESS_PRODUCT:
        return this.translate.instant('CAMPAIGNS.ADD_TO_CAMPAIGN_ERRORS.PRODUCT_NEEDS_ACTIVATING', {
          productName: error.productName,
        });
      case AddToCampaignError.UNKNOWN:
        return this.translate.instant('CAMPAIGNS.ADD_TO_CAMPAIGN_ERRORS.UNKNOWN');
      default:
        return this.translate.instant('CAMPAIGNS.ADD_TO_CAMPAIGN_ERRORS.UNKNOWN');
    }
  }

  private assertNever(value: never): never {
    throw new Error(`Unhandled value: ${value}`);
  }

  private convertStepType(stepType: CampaignStepType) {
    switch (stepType) {
      case CampaignStepType.CAMPAIGN_STEP_TYPE_EMAIL:
        return 'email';
      case CampaignStepType.CAMPAIGN_STEP_TYPE_SNAPSHOT_CREATION:
        return 'snapshot-creation';
      case CampaignStepType.CAMPAIGN_STEP_TYPE_SMS:
        return 'sms';
      case CampaignStepType.CAMPAIGN_STEP_TYPE_UNSPECIFIED:
        return null;
      case undefined:
        return null;
      default:
        // If you see a compile error here, it likely means you're missing a case.
        this.assertNever(stepType);
    }
  }

  private convertStatus(status: Statuses) {
    switch (status) {
      case Statuses.STATUSES_ARCHIVED:
        return 'archived';
      case Statuses.STATUSES_PUBLISHED:
        return 'published';
      case Statuses.STATUSES_DRAFT:
        return 'draft';
      case Statuses.STATUSES_ACTIVE:
        return 'active';
      case Statuses.STATUSES_UNSPECIFIED:
        return null;
      default:
        // If you see a compile error here, it likely means you're missing a case.
        this.assertNever(status);
    }
  }
}
