import { CampaignSidePanelService } from './campaign-side-panel.service';
import { MarketingCampaign, ScheduleStep } from '@vendasta/sales-v2';

describe('Campaign Side Panel Service', () => {
  it('should calculate days since correctly', () => {
    const campaign = <MarketingCampaign>{
      name: 'camp',
      campaignStatus: 'published',
      campaignId: 'camp-123',
      campaignSchedule: [
        <ScheduleStep>{
          stepType: 'email',
          secondsAfterLastEmail: 86400,
          name: 'email-step',
          templateId: '',
        },
      ],
    };
    const daysSince = CampaignSidePanelService.calculateDaysSince(campaign, 0);
    const shouldBeDaysSince = 2;
    expect(daysSince).toBe(shouldBeDaysSince);
  });

  it('should calculate seconds to days correctly', () => {
    const result = CampaignSidePanelService.secondsToDays(86400);
    expect(result).toEqual(1);
  });
});
