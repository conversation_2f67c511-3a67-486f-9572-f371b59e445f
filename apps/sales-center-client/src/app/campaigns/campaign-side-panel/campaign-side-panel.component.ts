import { Component, Inject, Input, OnChanges, OnDestroy, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import {
  FormControl,
  FormGroupDirective,
  NgForm,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { AccountGroupService, ProjectionFilter } from '@galaxy/account-group';
import { TranslateService } from '@ngx-translate/core';
import { CampaignService, Statuses } from '@vendasta/campaigns';
import { SelectInputOption } from '@vendasta/galaxy/input';
import { SubscriptionList } from '@vendasta/rx-utils';
import { MarketingCampaign } from '@vendasta/sales';
import { LoggedInUserInfo } from '@vendasta/sales-common';
import { BehaviorSubject, Observable, ReplaySubject, combineLatest, firstValueFrom } from 'rxjs';
import { debounceTime, finalize, map, startWith, switchMap, tap } from 'rxjs/operators';
import { SlideOutPanelService } from '../../common';
import { ContactShadow } from '../../common/contacts';
import { USER_INFO_TOKEN } from '../../core/feature-flag.service';
import { CampaignSidePanelService } from './campaign-side-panel.service';

const ADD_CONTACT = 'ADD_CONTACT';

@Component({
  selector: 'app-campaign-side-panel',
  templateUrl: './campaign-side-panel.component.html',
  styleUrls: ['./campaign-side-panel.component.scss'],
  standalone: false,
})
export class CampaignSidePanelComponent implements OnDestroy, OnChanges, OnInit {
  @Input() accountGroupID: string;

  @ViewChild('ngForm') form: NgForm;
  formGroup: UntypedFormGroup;

  private readonly subscriptions = SubscriptionList.new();

  readonly campaigns$: Observable<MarketingCampaign[]>;
  campaignOptions$: Observable<SelectInputOption[]>;
  readonly campaignsLoading$: Observable<boolean>;
  selectedContact$$ = new BehaviorSubject<string>(null);
  private contacts$: Observable<ContactShadow[]>;
  contactsLoading$: Observable<boolean>;
  contactOptions$: Observable<ContactShadow[]>;

  private readonly addingNewContact$$ = new BehaviorSubject<boolean>(false);
  readonly addingNewContact$ = this.addingNewContact$$.asObservable();

  private readonly businessName$$ = new ReplaySubject<string>(1);
  readonly businessName$ = this.businessName$$.asObservable();

  private readonly businessNameLoading$$ = new BehaviorSubject<boolean>(false);
  readonly businessNameLoading$ = this.businessNameLoading$$.asObservable();

  isCreatingNewContact$: Observable<boolean>;
  isAddingContactToCampaign$: Observable<boolean>;

  selectedCampaign$ = new ReplaySubject<MarketingCampaign>(1);

  searchBarControl: FormControl = new FormControl();

  constructor(
    private readonly slideOutPanelService: SlideOutPanelService,
    private readonly campaignSidePanelService: CampaignSidePanelService,
    private readonly translate: TranslateService,
    private readonly accountGroupService: AccountGroupService,
    private readonly campaignSDKService: CampaignService,
    @Inject(USER_INFO_TOKEN) private readonly userInfo$: Observable<LoggedInUserInfo>,
  ) {
    this.campaigns$ = this.campaignSidePanelService.campaigns$;
    this.isCreatingNewContact$ = this.campaignSidePanelService.creatingNewContact$;
    this.isAddingContactToCampaign$ = this.campaignSidePanelService.addingContactToCampaign$;

    this.subscriptions.add(this.campaignSidePanelService.campaignSuccessfullyStarted$, (success) => {
      if (success === true) {
        this.resetForm();
        this.closeSidePanel();
      }
    });

    this.campaignsLoading$ = this.campaigns$.pipe(map(() => false));

    this.formGroup = new UntypedFormGroup({
      campaign_select: new UntypedFormControl('', Validators.required),
      recipient: new UntypedFormControl('', Validators.required),
    });
  }

  ngOnInit(): void {
    this.campaignOptions$ = combineLatest([
      this.userInfo$,
      this.searchBarControl.valueChanges.pipe(startWith(''), debounceTime(200)),
    ]).pipe(
      switchMap(([userInfo, searchTerm]) => {
        return this.campaignSDKService.list(
          userInfo.partnerId,
          undefined,
          [Statuses.STATUSES_PUBLISHED, Statuses.STATUSES_ACTIVE],
          searchTerm,
          {
            pageSize: 100,
          },
        );
      }),
      map((campaigns = []) => {
        return campaigns.map((c) => {
          return {
            value: c.campaignId,
            label: c.name,
          };
        });
      }),
    );
  }
  ngOnChanges(changes: SimpleChanges): void {
    this.setUpAccountGroup(changes.accountGroupID.currentValue);
  }

  setUpAccountGroup(accountGroupId: string): void {
    this.campaignSidePanelService.accountGroupId = accountGroupId;
    this.getBusinessName(accountGroupId);
    this.contacts$ = this.campaignSidePanelService.contacts$;
    this.contactOptions$ = this.contacts$.pipe(map((contacts) => contacts.filter((c) => !!c.contactEmail)));

    this.contactsLoading$ = this.campaignSidePanelService.contactsLoading$;
  }

  async getBusinessName(accountGroupId: string): Promise<void> {
    this.businessNameLoading$$.next(true);
    const businessInfo = await firstValueFrom(
      this.accountGroupService.get(accountGroupId, new ProjectionFilter({ napData: true })),
    );
    this.businessName$$.next(businessInfo.napData.companyName);
    this.businessNameLoading$$.next(false);
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }

  setSelectedRecipient(value: string): void {
    if (value === ADD_CONTACT) {
      this.addingNewContact$$.next(true);
      this.formGroup.addControl('receiver_first_name', new UntypedFormControl('', Validators.required));
      this.formGroup.addControl('receiver_last_name', new UntypedFormControl('', Validators.required));
      this.formGroup.addControl('receiver_email', new UntypedFormControl('', [Validators.required, Validators.email]));
    } else {
      this.contacts$
        .pipe(
          tap((contacts) => {
            const selectedContact = contacts.find((c) => c.contactEmail === value);
            if (selectedContact) {
              this.selectedContact$$.next(selectedContact.userId);
            }
          }),
          finalize(() => {
            this.addingNewContact$$.next(false);
            this.formGroup.removeControl('receiver_first_name');
            this.formGroup.removeControl('receiver_last_name');
            this.formGroup.removeControl('receiver_email');
          }),
        )
        .subscribe();
    }
  }

  setSelectedCampaign(selectedCampaignId: string, campaigns: MarketingCampaign[]): void {
    const selectedCampaign = campaigns.find((c) => c.campaignId === selectedCampaignId);
    this.selectedCampaign$.next(selectedCampaign);
  }

  resetForm(): void {
    this.selectedCampaign$.next(null);
    this.formGroup.reset();
    this.form.resetForm();
  }

  closeSidePanel(): void {
    this.resetForm();
    this.slideOutPanelService.closeSlideOut();
  }

  onSubmit(event: FormGroupDirective): void {
    this.startCampaign(event);
  }

  async startCampaign(event: FormGroupDirective): Promise<void> {
    const values = event.form.value;
    const isAddingNewUser = await firstValueFrom(this.addingNewContact$$);

    if (isAddingNewUser === true) {
      this.campaignSidePanelService.createContactAndStartCampaign(
        this.accountGroupID,
        values.receiver_first_name,
        values.receiver_last_name,
        values.receiver_email,
        values.campaign_select,
      );
    } else {
      this.campaignSidePanelService.startCampaign(values.campaign_select, this.accountGroupID, values.recipient);
    }
  }
}
