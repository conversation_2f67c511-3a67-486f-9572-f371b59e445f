import { CampaignsListComponent } from './campaigns-list/campaigns-list.component';
import { StatusIconComponent } from './campaigns-list/status-icon/status-icon.component';
import { StatusToggleComponent } from './campaigns-list/status-toggle/status-toggle.component';
import { CampaignSidePanelComponent } from './campaign-side-panel/campaign-side-panel.component';
import { EmailPreviewComponent, TemplatePreviewComponent } from './email-preview/email-preview.component';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { StencilsModule } from '@vendasta/store';
import { CampaignLoadingComponent } from './campaigns-list/stencil/campaign-loading.component';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { VFormModule } from '@vendasta/vform';
import { MatTooltipModule } from '@angular/material/tooltip';
import { VaBadgeModule } from '@vendasta/uikit';
import { TranslateModule } from '@ngx-translate/core';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { GalaxyInputModule } from '@vendasta/galaxy/input';
import { MatCardModule } from '@angular/material/card';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatOptionModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { WhitelabelTranslationModule } from '@galaxy/snapshot';
import { CAMPAIGN_SERVICE_TOKEN, VbcCampaignApis } from './vbc-campaign-apis';
import { CampaignDialogService } from './campaign-dialog.service';
import { CampaignSidePanelService } from './campaign-side-panel/campaign-side-panel.service';
import { AsyncUiModule } from '@vendasta/uikit';
import { ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { CampaignPreviewsComponent } from './campaign-previews/campaign-previews.component';
import { SalesV2CampaignErrorsModule } from '../common/errors/sales-v2-sdk/sales-v2-campaign-errors/sales-v2-campaign-errors.module';
import { SlideOutPanelModule } from '@vendasta/sales-ui';
import { ContactFormsModule } from '../common/contacts';
import { ContactPipeModule } from '../common/contacts/contact-pipe/contact-pipe.module';

@NgModule({
  declarations: [
    CampaignsListComponent,
    CampaignLoadingComponent,
    StatusIconComponent,
    EmailPreviewComponent,
    TemplatePreviewComponent,
    StatusToggleComponent,
    CampaignSidePanelComponent,
    CampaignPreviewsComponent,
  ],
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatListModule,
    MatMenuModule,
    VFormModule,
    MatTooltipModule,
    StencilsModule,
    VaBadgeModule,
    AsyncUiModule,
    TranslateModule,
    WhitelabelTranslationModule,
    MatCheckboxModule,
    GalaxyInputModule,
    MatFormFieldModule,
    MatOptionModule,
    MatCardModule,
    MatSelectModule,
    ReactiveFormsModule,
    MatInputModule,
    MatProgressSpinnerModule,
    SalesV2CampaignErrorsModule,
    SlideOutPanelModule,
    ContactFormsModule,
    ContactPipeModule,
  ],
  exports: [CampaignsListComponent, CampaignLoadingComponent, CampaignSidePanelComponent],
  providers: [
    { provide: CAMPAIGN_SERVICE_TOKEN, useClass: VbcCampaignApis },
    CampaignDialogService,
    CampaignSidePanelService,
  ],
})
export class CampaignsModule {}
