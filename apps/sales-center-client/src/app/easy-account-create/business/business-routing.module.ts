import { RouterModule, Routes } from '@angular/router';
import { NgModule } from '@angular/core';
import { BusinessSearchPageComponent } from './business-search-page/business-search-page.component';
import { EasyAccountCreatePageComponent } from './easy-account-create/easy-account-create-page.component';

const routes: Routes = [
  { path: 'create', component: EasyAccountCreatePageComponent },
  { path: 'search', component: BusinessSearchPageComponent },
  {
    path: 'verify',
    loadChildren: () => import('./business-verify/business-verify.module').then((m) => m.BusinessVerifyModule),
  },
];

@NgModule({
  declarations: [],
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  providers: [],
})
export class BusinessRoutingModule {}
