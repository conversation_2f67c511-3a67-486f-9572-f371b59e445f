import { Inject, Injectable } from '@angular/core';
import { combineLatest, Observable } from 'rxjs';
import { USER_PARTNER_MARKET_TOKEN } from '../../../core/feature-flag.service';
import { Configuration, PARTNER_MICROSERVICE_TOKEN, PartnerService } from '../../../partner';
import { SnapshotCountSdkService, SnapshotCountService } from '@vendasta/snapshot';
import { PartnerMarket } from '../../../logged-in-user-info/logged-in-user-info.service';
import { map, shareReplay, switchMap } from 'rxjs/operators';
import { SALESPERSON_ID_TOKEN } from '../../../common/providers';

export const SNAPSHOT_LIMIT_SERVICE_TOKEN = 'SNAPSHOT_LIMIT_SERVICE_TOKEN';

// TODO: find a more suitable module for this
@Injectable()
export class SnapshotCountSDKWrapperService {
  private readonly snapshotCount$: Observable<number>;
  private readonly config$: Observable<Configuration>;
  readonly hasExceededSnapshotLimit$: Observable<boolean>;

  constructor(
    @Inject(SALESPERSON_ID_TOKEN) private readonly salespersonId$: Observable<string>,
    @Inject(USER_PARTNER_MARKET_TOKEN) readonly partnerMarket$: Observable<PartnerMarket>,
    @Inject(PARTNER_MICROSERVICE_TOKEN) readonly partner: PartnerService,
    @Inject(SnapshotCountSdkService) private readonly snapshotCountService: SnapshotCountService,
  ) {
    this.snapshotCount$ = combineLatest([this.salespersonId$, this.partnerMarket$]).pipe(
      switchMap(([spId, pm]) => this.snapshotCountService.getSnapshotCount(pm.partnerId, pm.marketId, spId)),
      map((r) => r.count || 0),
    );

    this.config$ = this.partnerMarket$.pipe(
      switchMap((pm) => this.partner.getConfiguration$(pm.partnerId, pm.marketId)),
    );
    this.hasExceededSnapshotLimit$ = combineLatest([this.config$, this.snapshotCount$]).pipe(
      map(([config, snapshotCount]) => {
        const limit = config.snapshotsPerSalesperson || 0;
        if (config.enableSnapshotLimit) {
          return snapshotCount >= limit;
        }
        return false;
      }),
      shareReplay(),
    );
  }
}
