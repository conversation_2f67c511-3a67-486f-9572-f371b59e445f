import { Injectable } from '@angular/core';
import { Salesperson } from '@galaxy/types';
import { Salesperson as EACSalesperson, SalespersonService } from '@vendasta/businesses/easy-account-create';
import { ReplaySubject } from 'rxjs';
import { take, tap } from 'rxjs/operators';
import { SalespeopleService } from '../../../salespeople/salespeople.service';

@Injectable()
export class SalespersonServiceProvider implements SalespersonService {
  latestSalespeople$$ = new ReplaySubject<EACSalesperson[]>();

  constructor(private readonly salespeopleService: SalespeopleService) {}

  loadAllSalespeople(): void {
    this.salespeopleService
      .loadSalespeople()
      .pipe(
        take(1),
        tap((salespeople) => this.latestSalespeople$$.next(salespeople.map(toEACSalesPerson))),
      )
      .subscribe();
  }
}

function toEACSalesPerson(responseSP: Salesperson): EACSalesperson {
  const names = (responseSP.fullName || '').split(' ');
  return new EACSalesperson({
    firstName: names[0],
    lastName: names.slice(-1)[0],
    partnerId: responseSP.partnerId,
    marketId: responseSP.marketId,
    salesPersonId: responseSP.id,
    email: responseSP.email,
    phoneNumber: responseSP.phoneNumbers ? responseSP.phoneNumbers[0] : '',
    photoUrl: responseSP.pictureServingUrl,
  });
}

export const SALESPERSON_SERVICE_TOKEN = 'SALESPERSON_SERVICE_TOKEN';

export const SalespersonProvider = {
  provide: SALESPERSON_SERVICE_TOKEN,
  useExisting: SalespersonServiceProvider,
  deps: [SalespeopleService],
};
