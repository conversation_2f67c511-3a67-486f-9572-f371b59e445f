import { Salesperson } from '@galaxy/types';
import { schedule } from '@vendasta/rx-utils';
import { TestScheduler } from 'rxjs/testing';
import { SalespersonServiceProvider } from './salesperson.provider';

export let sched: TestScheduler;

class MockSalesPersonDep {
  mockSalespeople: Salesperson[];

  constructor(mockSalespeople: Salesperson[] = []) {
    this.mockSalespeople = mockSalespeople;
  }

  loadSalespeople = () => sched.createColdObservable('--x', { x: this.mockSalespeople });
}

describe('SalespersonServiceProvider', () => {
  beforeEach(() => (sched = new TestScheduler((a, b) => expect(a).toEqual(b))));
  afterEach(() => sched.flush());

  describe('latestSalespeople$$', () => {
    test('loads empty list', () => {
      const salespersonSrvDep = new MockSalesPersonDep();
      const service = new SalespersonServiceProvider(salespersonSrvDep as any);

      schedule(sched, '|', () => service.loadAllSalespeople());
      const actual$$ = service.latestSalespeople$$;

      const expected = [];
      sched.expectObservable(actual$$).toBe('--x', { x: expected });
    });

    test('converts @src/app/salespeople/Salesperson into @vendasta/businesses/easy-account-create/Salesperson', () => {
      const salespersonSrvDep = new MockSalesPersonDep([
        {
          fullName: 'John Snow',
          id: 'SP-123',
          pictureServingUrl: '',
        } as Salesperson,
      ]);
      const service = new SalespersonServiceProvider(salespersonSrvDep as any);

      schedule(sched, '|', () => service.loadAllSalespeople());
      const actual$$ = service.latestSalespeople$$;

      const expected = [
        { salesPersonId: 'SP-123', firstName: 'John', lastName: 'Snow', photoUrl: '', phoneNumber: '' },
      ];
      sched.expectObservable(actual$$).toBe('--x', { x: expected });
    });
  });
});
