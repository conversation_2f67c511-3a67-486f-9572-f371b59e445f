// TODO: this file should not have any sales-center-client specific imports
import { AccessService, Feature } from '@vendasta/businesses/easy-account-create';
import { Observable } from 'rxjs';
import { SSCAccessService } from '../../../access';

// Warning: this has to be the same value as the host app token.
// TODO: inject this in a more conventional way
export const ACCESS_ALL_MARKET_TOKEN = `ACCESS_ALL_MARKET_TOKEN`;

export const accessFactory = (accessService: SSCAccessService): AccessService => {
  return {
    hasAccessToFeature(feature: Feature): Observable<boolean> {
      return accessService.hasAccessToFeature(feature);
    },
  };
};
