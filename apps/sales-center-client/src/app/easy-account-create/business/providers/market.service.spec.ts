import { TestScheduler } from 'rxjs/testing';
import { MarketService } from './market.service';

export let sched: TestScheduler;

class PartnerWrapperMock {
  getMarketName$ = () => {
    return sched.createColdObservable('x', { x: 'test' });
  };
}

class LoggedInUserInfoMock {
  getPartnerIdAndMarketId$ = () => sched.createColdObservable('-x', { x: { partnerId: 'VUNI', marketId: 'demomkt' } });
}

describe('MarketService', () => {
  beforeEach(() => (sched = new TestScheduler((a, b) => expect(a).toEqual(b))));
  afterEach(() => sched.flush());

  describe('markets', () => {
    test('loads a list of markets', () => {
      const partnerMock = new PartnerWrapperMock();
      const userInfo = new LoggedInUserInfoMock();
      const marketService = new MarketService(userInfo as any, partnerMock as any);
      const actualMarkets = marketService.markets;
      const expectedMarket = [{ name: 'test', partner_id: 'VUNI', market_id: 'demomkt' }];

      sched.expectObservable(actualMarkets).toBe('-x', { x: expectedMarket });
    });
  });
});
