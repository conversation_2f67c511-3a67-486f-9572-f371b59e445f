import { Inject, Injectable } from '@angular/core';
import { Market, MarketsService } from '@vendasta/businesses/easy-account-create';
import { Observable } from 'rxjs';
import { map, switchMap, withLatestFrom } from 'rxjs/operators';
import { LoggedInUserInfoService } from '../../../logged-in-user-info';
import { PARTNER_MICROSERVICE_TOKEN, PartnerService } from '../../../partner';

@Injectable()
export class MarketService implements MarketsService {
  markets: Observable<Market[]>;
  currentMarket$: Observable<Market>;
  constructor(
    private readonly loggedInUserInfoService: LoggedInUserInfoService,
    @Inject(PARTNER_MICROSERVICE_TOKEN) readonly partner: PartnerService,
  ) {
    const partnerMarket$ = this.loggedInUserInfoService.getPartnerIdAndMarketId$();
    const marketName$ = partnerMarket$.pipe(
      switchMap((params) => this.partner.getMarketName$(params.partnerId, params.marketId)),
    );

    const marketData$: Observable<Market> = marketName$.pipe(
      withLatestFrom(partnerMarket$),
      map(([marketName, partnerMarket]) => ({
        name: marketName,
        market_id: partnerMarket.marketId,
        partner_id: partnerMarket.partnerId,
      })),
    );
    this.markets = marketData$.pipe(map((market) => [market]));
    this.currentMarket$ = marketData$;
  }
}
