import { GetSnapshotCountCreatedBySalespersonIDResponseInterface, SnapshotCountService } from '@vendasta/snapshot';
import { Observable, firstValueFrom, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { PartnerMarket } from '../../../logged-in-user-info/logged-in-user-info.service';
import { Configuration, PartnerService } from '../../../partner';
import { SnapshotCountSDKWrapperService } from './snapshot-limit.service';

function makeConfig(limit: number): Configuration {
  return {
    defaultDisplayCurrency: 'XYZ',
    enableSnapshotLimit: true,
    snapshotsPerSalesperson: limit,
  };
}

function makeCountResponse(count: number): GetSnapshotCountCreatedBySalespersonIDResponseInterface {
  return {
    count: count,
  };
}

function makeServices(
  partnerConfigWithLimit: Configuration,
  partnerMarket: Observable<PartnerMarket>,
  currentCount: GetSnapshotCountCreatedBySalespersonIDResponseInterface,
): any {
  const ps: PartnerService = {
    getConfiguration$: () => of(partnerConfigWithLimit),
    getMarketName$: () => partnerMarket.pipe(map((pm) => pm.marketId)),
  };
  const sc: SnapshotCountService = {
    getSnapshotCount: () => of(currentCount),
  };
  return { ps, sc };
}

describe('SnapshotCountSDKWrapperService', () => {
  const spID = of('SP-123');
  const partnerMarket = of({
    partnerId: 'PID',
    marketId: 'MID',
  } as PartnerMarket);
  describe('hasExceededSnapshotLimit$', () => {
    it('should emit true if the limit is zero and the number created so far is zero', async () => {
      const partnerConfigWithLimit = makeConfig(0);
      const currentCount = makeCountResponse(0);

      const { ps, sc } = makeServices(partnerConfigWithLimit, partnerMarket, currentCount);
      const s = new SnapshotCountSDKWrapperService(spID, partnerMarket, ps, sc);

      expect.assertions(1);
      await firstValueFrom(s.hasExceededSnapshotLimit$)
        .then((result) => expect(result).toBeTruthy())
        .catch((err) => fail(err));
    });
    it('should emit true if the limit is zero and the number created so far is missing from response', async () => {
      const partnerConfigWithLimit = makeConfig(0);
      const currentCount = makeCountResponse(undefined);

      const { ps, sc } = makeServices(partnerConfigWithLimit, partnerMarket, currentCount);
      const s = new SnapshotCountSDKWrapperService(spID, partnerMarket, ps, sc);

      expect.assertions(1);
      await firstValueFrom(s.hasExceededSnapshotLimit$)
        .then((result) => expect(result).toBeTruthy())
        .catch((err) => fail(err));
    });
    it('should emit true if the limit is missing from the response', async () => {
      const partnerConfigWithLimit = makeConfig(undefined);
      const currentCount = makeCountResponse(1);

      const { ps, sc } = makeServices(partnerConfigWithLimit, partnerMarket, currentCount);
      const s = new SnapshotCountSDKWrapperService(spID, partnerMarket, ps, sc);

      expect.assertions(1);
      await firstValueFrom(s.hasExceededSnapshotLimit$)
        .then((result) => expect(result).toBeTruthy())
        .catch((err) => fail(err));
    });
    it('should emit false if the limit is greater than created count', async () => {
      const partnerConfigWithLimit = makeConfig(1);
      const currentCount = makeCountResponse(0);

      const { ps, sc } = makeServices(partnerConfigWithLimit, partnerMarket, currentCount);
      const s = new SnapshotCountSDKWrapperService(spID, partnerMarket, ps, sc);

      expect.assertions(1);
      await firstValueFrom(s.hasExceededSnapshotLimit$)
        .then((result) => expect(result).toBeFalsy())
        .catch((err) => fail(err));
    });
    it('should emit true if the limit is smaller than created count', async () => {
      const partnerConfigWithLimit = makeConfig(1);
      const currentCount = makeCountResponse(2);

      const { ps, sc } = makeServices(partnerConfigWithLimit, partnerMarket, currentCount);
      const s = new SnapshotCountSDKWrapperService(spID, partnerMarket, ps, sc);

      expect.assertions(1);
      await firstValueFrom(s.hasExceededSnapshotLimit$)
        .then((result) => expect(result).toBeTruthy())
        .catch((err) => fail(err));
    });
    it('should emit true if the limit is equal to created count', async () => {
      const partnerConfigWithLimit = makeConfig(1);
      const currentCount = makeCountResponse(1);

      const { ps, sc } = makeServices(partnerConfigWithLimit, partnerMarket, currentCount);
      const s = new SnapshotCountSDKWrapperService(spID, partnerMarket, ps, sc);

      expect.assertions(1);
      await firstValueFrom(s.hasExceededSnapshotLimit$)
        .then((result) => expect(result).toBeTruthy())
        .catch((err) => fail(err));
    });
  });
});
