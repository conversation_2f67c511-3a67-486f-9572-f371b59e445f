import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BusinessVerifyComponent } from './business-verify.component';
import { NavigationModule } from '../../../navigation/navigation.module';
import { BusinessQuickActionsModule } from './business-quick-actions';
import { BusinessLoadingBannerModule } from '../../../common/business-loading-banner';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { TranslateModule } from '@ngx-translate/core';
import { RouterModule } from '@angular/router';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { BusinessVerifyRoutingModule } from './business-verify-routing.module';

@NgModule({
  declarations: [BusinessVerifyComponent],
  imports: [
    CommonModule,
    NavigationModule,
    BusinessQuickActionsModule,
    BusinessLoadingBannerModule,
    MatButtonModule,
    MatProgressBarModule,
    RouterModule,
    GalaxyPageModule,
    TranslateModule,
    BusinessVerifyRoutingModule,
  ],
})
export class BusinessVerifyModule {}
