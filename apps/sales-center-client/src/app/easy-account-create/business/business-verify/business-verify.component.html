<glxy-page [isLoading]="waitingForBusiness$ | async">
  <glxy-page-toolbar>
    <glxy-page-title>
      <!-- leaving this here as I don't have time to pull out the business name -->
      <!-- This should turn into using the `<glxy-page-nav-button>` and `<glxy-page-title>` in the futute -->
      <app-nav-breadcrumbs [breadCrumbs]="breadcrumbs$ | async"></app-nav-breadcrumbs>
    </glxy-page-title>
    <glxy-page-actions>
      <ng-container *ngIf="enableGoToBusiness$ | async; then enabledLink; else disabledLink"></ng-container>
    </glxy-page-actions>
  </glxy-page-toolbar>

  <app-business-quick-actions
    [accountGroupId]="accountGroupId$ | async"
    [enableGoToBusiness]="enableGoToBusiness$ | async"
    [pipeline]="pipeline$ | async"
  ></app-business-quick-actions>

  <!--templates-->
  <ng-template #disabledLink>
    <button [disabled]="true" mat-raised-button color="primary">
      {{ 'ACCOUNT_DETAILS.GO_TO_NEW_BUSINESS' | translate }}
    </button>
  </ng-template>

  <ng-template #enabledLink>
    <a [routerLink]="accountInfoUrl">
      <button mat-raised-button color="primary" (click)="trackGoToNewBusiness()">
        {{ 'ACCOUNT_DETAILS.GO_TO_NEW_BUSINESS' | translate }}
      </button>
    </a>
  </ng-template>
</glxy-page>
