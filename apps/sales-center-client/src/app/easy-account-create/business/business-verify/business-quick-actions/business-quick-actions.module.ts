import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BusinessQuickActionsComponent } from './business-quick-actions.component';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ContactFormsModule } from '../../../../common/contacts';
import { VaBadgeModule, VImgModule } from '@vendasta/uikit';
import { SalesOpportunitiesModule } from '../../../../sales-opportunities/sales-opportunities.module';
import { TranslateModule } from '@ngx-translate/core';
import { MatExpansionModule } from '@angular/material/expansion';
import { VideoWidgetModule } from '../../../../common/video-widget/video-widget.module';
import { WhitelabelTranslationModule } from '@galaxy/snapshot';

@NgModule({
  declarations: [BusinessQuickActionsComponent],
  exports: [BusinessQuickActionsComponent],
  imports: [
    CommonModule,
    VImgModule,
    ContactFormsModule,
    MatIconModule,
    MatButtonModule,
    MatExpansionModule,
    VaBadgeModule,
    TranslateModule,
    SalesOpportunitiesModule,
    MatTooltipModule,
    VideoWidgetModule,
    WhitelabelTranslationModule,
  ],
})
export class BusinessQuickActionsModule {}
