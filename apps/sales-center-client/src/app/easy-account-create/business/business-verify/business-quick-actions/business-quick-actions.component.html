<mat-expansion-panel
  id="business-verify"
  [expanded]="panelIsOpen"
  (opened)="markPanelOpened()"
  (closed)="markPanelClosed()"
>
  <mat-expansion-panel-header class="va-card__subtitle" collapsedHeight="54px" expandedHeight="54px">
    <div class="panel-header-wrapper">
      <div class="panel-header-text">
        <ng-container *ngIf="enableGoToBusiness; else notReady">
          {{ 'ACCOUNT_DETAILS.BUSINESS_IS_READY' | translate }}
        </ng-container>
        <ng-template #notReady>
          {{ 'ACCOUNT_DETAILS.BUILDING_BUSINESS' | translate }}
        </ng-template>
      </div>
    </div>
  </mat-expansion-panel-header>

  <section class="business-verify-container">
    <!-- Generated a Snapshot Report -->
    <div class="business-verify-item">
      <div class="business-verify-title">
        {{ 'ACCOUNT_DETAILS.VERIFY.GENERATE_SNAPSHOT_REPORT' | whitelabelTranslate | async }}
      </div>

      <div class="business-verify-content">
        <ng-container>
          <div class="business-verify--description">
            {{ 'ACCOUNT_DETAILS.VERIFY.GENERATE_SNAPSHOT_DESCRIPTION' | whitelabelTranslate | async }}
          </div>
          <div>
            <a (click)="openVideoWidget('snapshot')">
              <uikit-v-img src="assets/thumbnails/snapshot_report_thumbnail.png"></uikit-v-img>
              <div class="image-overlay">
                <mat-icon class="play-icon">play_circle_filled</mat-icon>
              </div>
            </a>
          </div>
        </ng-container>

        <ng-container>
          <div
            class="business-verify-button"
            matTooltip="{{ 'SNAPSHOT_REPORT.DISABLE_CREATE_SNAPSHOT' | whitelabelTranslate | async }}"
            [matTooltipDisabled]="(exceedLimit$ | async) === false"
          >
            <button
              [disabled]="disableCreateSnapshot$ | async"
              mat-raised-button
              color="primary"
              (click)="createSnapshotCTA()"
            >
              <ng-container *ngIf="(snapshotCreated$ | async) === false; else snapShotGenerated">
                {{ 'ACCOUNT_DETAILS.VERIFY.GENERATE_SNAPSHOT_REPORT' | whitelabelTranslate | async }}
              </ng-container>
              <ng-template #snapShotGenerated>
                {{ 'ACCOUNT_DETAILS.VERIFY.SNAPSHOT_GENERATING' | whitelabelTranslate | async }}
              </ng-template>
            </button>
          </div>
        </ng-container>
      </div>
    </div>

    <!-- Add contacts -->
    <div class="business-verify-item">
      <div class="business-verify-title">
        {{ 'ACCOUNT_DETAILS.VERIFY.ADD_CONTACTS' | translate }}
      </div>

      <div class="business-verify-content">
        <ng-container>
          <div class="business-verify--description">
            {{ 'ACCOUNT_DETAILS.VERIFY.ADD_CONTACTS_DESCRIPTION' | translate }}
          </div>
          <div>
            <a (click)="openVideoWidget('contacts')">
              <uikit-v-img src="assets/thumbnails/contacts_thumbnail.png"></uikit-v-img>
              <div class="image-overlay">
                <mat-icon class="play-icon">play_circle_filled</mat-icon>
              </div>
            </a>
          </div>
        </ng-container>

        <ng-container>
          <div class="business-verify-button">
            <button
              [disabled]="!enableGoToBusiness"
              mat-raised-button
              color="primary"
              (click)="addContactToAccountCTA()"
            >
              {{ 'ACCOUNT_DETAILS.VERIFY.ADD_CONTACTS' | translate }}
            </button>
          </div>
        </ng-container>
      </div>
    </div>

    <!-- Create an opportunity -->
    <div class="business-verify-item">
      <div class="business-verify-title">
        {{ 'ACCOUNT_DETAILS.VERIFY.CREATE_OPPORTUNITY' | translate }}
      </div>

      <div class="business-verify-content">
        <ng-container>
          <div class="business-verify--description">
            {{ 'ACCOUNT_DETAILS.VERIFY.CREATE_OPPORTUNITY_DESCRIPTION' | translate }}
          </div>
          <div>
            <a (click)="openVideoWidget('opportunity')">
              <uikit-v-img src="assets/thumbnails/opportunities_thumbnail.png"></uikit-v-img>
              <div class="image-overlay">
                <mat-icon class="play-icon">play_circle_filled</mat-icon>
              </div>
            </a>
          </div>
        </ng-container>

        <ng-container>
          <div class="business-verify-button">
            <button mat-raised-button color="primary" (click)="createOpportunityCTA()">
              {{ 'ACCOUNT_DETAILS.VERIFY.CREATE_OPPORTUNITY' | translate }}
            </button>
          </div>
        </ng-container>
      </div>
    </div>
  </section>
</mat-expansion-panel>

<div class="messages" *ngIf="mostRecentMessage$ | async as mostRecentMessage">
  <va-badge color="warn">Error: {{ mostRecentMessage }}</va-badge>
</div>
