@use 'design-tokens' as *;

$width-lg: 1280px;
$width-sm: 680px;

.call-to-actions {
  border: $green solid 1px;
  border-radius: 5px;
  padding: 20px;
  font-size: 14px; // PCC is using 14px

  .action-box {
    border: $blue solid 1px;
    border-radius: 5px;
    background-color: $light-blue;
    text-align: center;
    height: 100%;
    &.snapshot {
      background: $light-gray;
      &.enabled {
        background: $light-blue;
      }
    }
  }

  .create-another-account {
    display: flex;
    .top-text {
      margin: auto;
    }
  }

  .action-icon {
    padding-top: 10px;
    margin-bottom: 22px;
    color: $blue;
  }
  .money-icon {
    margin-bottom: 10px;
    height: 36px;
    width: 36px;
    fill: $blue;
  }

  .top-text {
    font-weight: bolder;
  }

  .bottom-text {
    color: $gray;
    margin: 0 16px;
  }

  .action-button {
    color: $blue;
    padding: 10px 0;
  }
}

.messages {
  margin: 8px;
}

:host #business-verify.mat-expansion-panel ::ng-deep {
  .mat-expansion-panel-header {
    padding: 0 24px 0 16px;
  }
  .mat-expansion-indicator {
    margin-top: -4px;
  }
  .mat-expansion-panel-body {
    padding: 0;
    border-top: 1px solid $border-color;
  }
}

.business-verify-container {
  display: flex;
  align-content: stretch;
  padding: 0 16px;
}

.business-verify-item {
  flex: 1 1 25%;
  position: relative;

  display: flex;
  flex-direction: column;

  overflow: hidden;

  &:not(:first-child) {
    // Add the border between sections
    @media only screen and (min-width: $width-lg) {
      border-left: 1px solid $border-color;
      padding-left: 20px;
      margin-left: 20px;
    }
  }
}

.business-verify-button {
  padding-top: 16px;
  text-align: center;
}

.business-verify-title {
  padding-top: 16px;
  position: relative;
  font:
    400 15px/24px Roboto,
    'Helvetica Neue',
    sans-serif;
  flex-grow: 0;
  flex-shrink: 0;
}

.business-verify-content {
  padding: 16px 0 16px 0;
  flex-grow: 3;
  flex-shrink: 3;
  max-width: 400px;
}

.business-verify--description {
  line-height: 1.5;
  width: 100%;
  white-space: pre-wrap;
  height: 50px;
}

:host #business-verify.mat-expansion-panel ::ng-deep {
  // Tweak the styling of the expansion panel to
  // fit with SSC
  .mat-expansion-panel-header {
    padding: 0 24px 0 16px;
  }
  .mat-expansion-indicator {
    margin-top: -4px;
  }
  .mat-expansion-panel-body {
    padding: 0;
    border-top: 1px solid $border-color;
  }
}

// Main Business verify Container
.mat-expansion-panel {
  border: 1px solid $border-color;
  box-shadow: none;
}

.mat-expansion-panel-header {
  margin: unset;
  user-select: none;
}

.panel-header-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  padding-right: 20px;
}

.bottom-settings {
  border-top: 1px solid $border-color;
  padding: 8px 16px;
  text-align: right;
}

.panel-header-text {
  flex-grow: 2;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.panel-summary {
  padding: 0 14px;
  font-size: 14px;
  color: $secondary-font-color;
}

@media only screen and (max-width: $width-sm) {
  .business-verify-container {
    display: block;
  }
  .business-verify-item {
    padding: 10px 16px 10px;
  }
  .business-verify-item:nth-child(odd) {
    border-right: none;
  }
  .business-verify-title-action {
    opacity: 1;
    transition: none;
  }

  .business-verify-item {
    border-top: 1px solid $border-color;
  }
}

.image-overlay {
  position: relative;
  top: -120px;
  opacity: 0.5;
  text-align: center;
  .play-icon {
    font-size: 64px;
  }
}
