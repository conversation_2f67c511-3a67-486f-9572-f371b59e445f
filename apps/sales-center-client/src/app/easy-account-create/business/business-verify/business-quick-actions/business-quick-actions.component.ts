import { Component, Inject, Input, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';

import { MatDialog } from '@angular/material/dialog';
import {
  CreateSalesOpportunityComponent,
  OpportunityCreateDialogData,
} from '../../../../sales-opportunities/create/create-sales-opportunities.component';
import {
  CreateRefreshDialogData,
  SnapshotAction,
  SnapshotCheckoutComponent,
  WhitelabelTranslationService,
} from '@galaxy/snapshot';
import { map } from 'rxjs/operators';
import { SubscriptionList } from '@vendasta/rx-utils';
import { BehaviorSubject, combineLatest, Observable } from 'rxjs';
import { Pipeline } from '@vendasta/sales-ui';
import { SNAPSHOT_LIMIT_SERVICE_TOKEN, SnapshotCountSDKWrapperService } from '../../providers/snapshot-limit.service';
import { AddContactComponent } from '../../../../common/contacts';
import { CdkTextareaAutosize } from '@angular/cdk/text-field';
import { BreakpointObserver } from '@angular/cdk/layout';
import { VideoWidgetComponent, VideoWidgetData } from '../../../../common/video-widget/video-widget.component';
import { EACUsageTrackingService, UsageTracking } from '@vendasta/businesses/easy-account-create';

@Component({
  selector: 'app-business-quick-actions',
  templateUrl: './business-quick-actions.component.html',
  styleUrls: ['./business-quick-actions.component.scss'],
  standalone: false,
})
export class BusinessQuickActionsComponent implements OnInit, OnDestroy {
  @Input() accountGroupId: string;
  private readonly mostRecentMessage$$ = new BehaviorSubject<string>(null);
  readonly mostRecentMessage$: Observable<string> = null;
  @Input() pipeline: Pipeline;
  @Input() enableGoToBusiness: boolean;
  private readonly subscriptions = SubscriptionList.new();
  private readonly snapshotCreated$$ = new BehaviorSubject<boolean>(false);
  readonly snapshotCreated$: Observable<boolean> = this.snapshotCreated$$.asObservable();
  readonly exceedLimit$: Observable<boolean>;
  readonly disableCreateSnapshot$: Observable<boolean>;
  panelIsOpen: boolean;

  @ViewChild('autosize') autosize: CdkTextareaAutosize;

  constructor(
    private readonly dialog: MatDialog,
    private readonly whitelabelTranslate: WhitelabelTranslationService,
    public breakpointObserver: BreakpointObserver,
    @Inject(SNAPSHOT_LIMIT_SERVICE_TOKEN) readonly snapshotLimitService: SnapshotCountSDKWrapperService,
    private readonly eacTrackingService: EACUsageTrackingService,
  ) {
    this.mostRecentMessage$ = this.mostRecentMessage$$.asObservable();
    this.exceedLimit$ = this.snapshotLimitService.hasExceededSnapshotLimit$;
    this.disableCreateSnapshot$ = combineLatest([this.snapshotCreated$, this.exceedLimit$]).pipe(
      map(([created, exceedLimit]) => created || exceedLimit),
    );
  }

  ngOnInit(): void {
    this.initPanelState();
  }

  createSnapshotCTA(): void {
    this.eacTrackingService.track(UsageTracking.GenerateSnapshotCTA);

    const dialogData: CreateRefreshDialogData = {
      accountGroupId: this.accountGroupId,
      accountGroupName: null,
      action: SnapshotAction.Create,
      snapshotOriginDetails: 'business-quick-actions',
    };
    const ref = this.dialog.open(SnapshotCheckoutComponent, { width: '480px', data: dialogData });
    this.subscriptions.add(ref.afterClosed(), (successful) => {
      if (successful === true) {
        this.snapshotCreated$$.next(true);
        this.clearErrorMessage();
      } else if (successful === false) {
        this.setErrorMessage(this.whitelabelTranslate.instant('SNAPSHOT_REPORT.UNABLE_TO_REFRESH'));
      }
    });
  }

  addContactToAccountCTA(): void {
    this.eacTrackingService.track(UsageTracking.AddContactsCTA);

    this.clearErrorMessage();
    const dialogRef = this.dialog.open(AddContactComponent, {
      width: '500px',
      maxWidth: '100vw',
      data: { accountGroupId: this.accountGroupId },
    });
    this.subscriptions.add(dialogRef.componentInstance.errorMessage, (error) => this.setErrorMessage(error));
  }

  createOpportunityCTA(): void {
    this.eacTrackingService.track(UsageTracking.CreateOpportunityCTA);

    this.clearErrorMessage();
    const dialogData: OpportunityCreateDialogData = {
      accountGroupId: this.accountGroupId,
      navigationData: {
        navigateOnSuccess: false,
      },
    };
    const dialogRef = this.dialog.open(CreateSalesOpportunityComponent, { data: dialogData });
    this.subscriptions.add(dialogRef.componentInstance.errorMessage, (result) => this.setErrorMessage(result));
  }

  clearErrorMessage(): void {
    this.mostRecentMessage$$.next(null);
  }

  setErrorMessage(error: string): void {
    this.mostRecentMessage$$.next(error);
  }

  openVideoWidget(title: string): void {
    this.eacTrackingService.track(UsageTracking.OverviewVideoClick);

    let videoUrl, videoTitle;
    switch (title) {
      case 'snapshot':
        videoUrl = `https://fast.wistia.net/embed/iframe/kv9d0xxt3v?videoFoam=true`;
        videoTitle = 'How to run a Snapshot Report Video';
        break;
      case 'contacts':
        videoUrl = `https://fast.wistia.net/embed/iframe/ze88emi4ag?videoFoam=true`;
        videoTitle = 'Adding contacts Video';
        break;
      case 'opportunity':
        videoUrl = `https://fast.wistia.net/embed/iframe/7cykkizi29?videoFoam=true`;
        videoTitle = 'Managing opportunities and pipelines Video';
        break;
    }

    const videoWidgetData: VideoWidgetData = {
      videoTitle: videoTitle,
      videoURL: videoUrl,
    };

    this.dialog.open(VideoWidgetComponent, { width: '940px', height: '550px', data: videoWidgetData });
  }

  markPanelOpened(): void {
    localStorage.setItem('businessVerifyPanelState', 'true');
    this.panelIsOpen = true;
  }

  markPanelClosed(): void {
    localStorage.setItem('businessVerifyPanelState', 'false');
    this.panelIsOpen = false;
  }

  initPanelState(): void {
    const isSmallScreen = this.breakpointObserver.isMatched('(max-width: 680px)');
    const storedPanelState = localStorage.getItem('businessVerifyPanelState');
    // convert storedPanelState string to bool:
    const storedPanelBool = storedPanelState === 'true';

    if (storedPanelState) {
      // If they have interacted with the panel, restore expanded state
      this.panelIsOpen = storedPanelBool;
    } else if (isSmallScreen) {
      // collapse the onboarding panel if mobile
      this.panelIsOpen = false;
    } else {
      // show onboarding panel on tablet and desktop by default
      this.panelIsOpen = true;
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }
}
