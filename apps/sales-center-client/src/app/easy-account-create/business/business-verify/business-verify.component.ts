import { Component, Inject, OnDestroy } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { combineLatest, interval, Observable, of, ReplaySubject, RetryConfig, throwError } from 'rxjs';
import { BreadCrumb } from '../../../navigation';
import { filter, map, mergeMap, retry, startWith, switchMap, tap } from 'rxjs/operators';
import { ActivatedRoute } from '@angular/router';
import { Pipeline, USER_PIPELINE_TOKEN } from '@vendasta/sales-ui';
import { SubscriptionList } from '@vendasta/rx-utils';
import { AjaxBusinessApiService, BusinessApiService } from '../../../business';
import { EACUsageTrackingService, UsageTracking } from '@vendasta/businesses/easy-account-create';
import { ACCOUNT_INFO } from '../../../urls';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-business-verify',
  templateUrl: './business-verify.component.html',
  styleUrls: ['./business-verify.component.scss'],
  standalone: false,
})
export class BusinessVerifyComponent implements OnDestroy {
  breadcrumbs$: Observable<BreadCrumb[]>;
  accountGroupId$: Observable<string>;
  private readonly businessIsReady$$ = new ReplaySubject<boolean>(1);
  enableGoToBusiness$: Observable<boolean> = this.businessIsReady$$.asObservable();
  waitingForBusiness$: Observable<boolean> = this.businessIsReady$$.asObservable().pipe(
    map((v) => !v),
    startWith(true),
  );
  private readonly subscriptions = SubscriptionList.new();

  // This is only for navigating to account info page.
  // This is safe to use in navigateToBusiness() since
  // the button is disabled until the business exists.
  public accountInfoUrl: string;

  constructor(
    private readonly i18n: TranslateService,
    private readonly routes: ActivatedRoute,
    @Inject(USER_PIPELINE_TOKEN) readonly pipeline$: Observable<Pipeline>,
    @Inject(AjaxBusinessApiService) private readonly businessApi: BusinessApiService,
    private readonly eacTrackingService: EACUsageTrackingService,
  ) {
    this.accountGroupId$ = this.routes.params.pipe(
      map((params) => params?.accountGroupId),
      tap((id) => (this.accountInfoUrl = ACCOUNT_INFO(id))),
    );

    this.breadcrumbs$ = combineLatest([
      this.i18n.stream(['MANAGE_ACCOUNTS.TITLE', 'EASY_ACCOUNT_CREATE.VERIFY']),
      this.accountGroupId$,
    ]).pipe(
      map(([translation, accountGroupId]): BreadCrumb[] => [
        { link: '/', queryParams: { agid: accountGroupId }, label: translation['MANAGE_ACCOUNTS.TITLE'] },
        { label: translation['EASY_ACCOUNT_CREATE.VERIFY'] },
      ]),
    );

    const nonEmptyId$ = this.accountGroupId$.pipe(filter((id) => !!id));
    const notifier$ = interval(5000);

    nonEmptyId$
      .pipe(
        takeUntilDestroyed(),
        switchMap((id) =>
          this.businessApi.getBusiness(id).pipe(
            map((resp) => Boolean(resp.accountGroupId)),
            mergeMap((hasId) =>
              !hasId ? throwError(() => new Error('AccountGroupId missing from response')) : of(hasId),
            ),
            retry(<RetryConfig>{ count: 25, delay: () => notifier$ }),
          ),
        ),
      )
      .subscribe({
        next: (_) => this.businessIsReady$$.next(true),
        error: () => {
          this.businessIsReady$$.next(false);
        },
      });
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }

  trackGoToNewBusiness(): void {
    this.eacTrackingService.track(UsageTracking.GoToNewBusiness);
  }
}
