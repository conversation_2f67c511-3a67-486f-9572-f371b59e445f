// TODO: extract shareable NgModules for business-create (easy-account-create) modules
// Note these modules should not depend on any sales-center-client specific imports
import { CommonModule } from '@angular/common';
import { Injector, NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';
import {
  ACCESS_SERVICE_TOKEN,
  DEFAULT_SALES_ASSIGNEE_ID_TOKEN,
  MARKET_SERVICE_TOKEN as EAC_MARKET_SERVICE_TOKEN,
  EasyAccountCreateModule,
} from '@galaxy/easy-account-create';
import { LexiconModule } from '@galaxy/lexicon';
import { WhitelabelService } from '@galaxy/partner';
import { TranslateModule } from '@ngx-translate/core';
import { AddressFormModule, FeatureFlagService } from '@vendasta/businesses';
import { DuplicateAccountsModule } from '@vendasta/businesses/duplicate-accounts';
import {
  BusinessSearchModule,
  ConfigurationService,
  DISPLAY_SIMPLE_SEARCH,
  IncludedProductsService,
  InferenceApiHost,
  PARTNER_ID_TOKEN,
  accessDisplaySimpleSearchFactory,
} from '@vendasta/businesses/easy-account-create';
import { CountryStateService, CountryStateServiceInterfaceToken } from '@vendasta/country-state';
import { VaFormsModule } from '@vendasta/forms';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { TaxonomyService, TaxonomyServiceInterfaceToken } from '@vendasta/taxonomy';
import { UIKitModule } from '@vendasta/uikit';
import baseTranslation from '../../../assets/i18n/en_devel.json';
import { SSCAccessService } from '../../access';
import { TaxonomyWrapperService } from '../../business-category';
import { CountryStateService as SalesCountryStateService } from '../../common/country-state.service';
import { AccessToAllMarketProvider, SALESPERSON_ID_TOKEN } from '../../common/providers';
import { WEBLATE_COMPONENT_NAME } from '../../constants';
import { PartnerCenterHostService } from '../../core';
import { LoggedInUserInfoService } from '../../logged-in-user-info';
import { NavigationModule } from '../../navigation/navigation.module';
import { SalespeopleService } from '../../salespeople/salespeople.service';
import { BusinessRoutingModule } from './business-routing.module';
import { BusinessSearchPageComponent } from './business-search-page/business-search-page.component';
import { BusinessVerifyModule } from './business-verify/business-verify.module';
import { EasyAccountCreatePageComponent } from './easy-account-create/easy-account-create-page.component';
import { EasyAccountCreateComponent, MARKET_SERVICE_TOKEN } from './easy-account-create/easy-account-create.component';
import { MarketService } from './providers/market.service';
import { accessFactory } from './providers/provider';
import { SalespersonProvider, SalespersonServiceProvider } from './providers/salesperson.provider';

const MAT_MODULES = [
  MatCardModule,
  MatInputModule,
  MatIconModule,
  MatButtonModule,
  MatAutocompleteModule,
  MatExpansionModule,
  MatCheckboxModule,
  MatTooltipModule,
  MatListModule,
];

const SALESPERSON_PROVIDER_DEPS = [SalespeopleService, SalespersonServiceProvider];

@NgModule({
  imports: [
    CommonModule,
    VaFormsModule,
    ReactiveFormsModule,
    UIKitModule,
    RouterModule,
    BusinessRoutingModule,
    MAT_MODULES,
    NavigationModule,
    DuplicateAccountsModule,
    BusinessVerifyModule,
    AddressFormModule,
    EasyAccountCreateModule,
    BusinessSearchModule,
    TranslateModule,
    GalaxyPageModule,
    LexiconModule.forChild({
      componentName: WEBLATE_COMPONENT_NAME,
      baseTranslation: baseTranslation,
    }),
  ],
  declarations: [EasyAccountCreateComponent, BusinessSearchPageComponent, EasyAccountCreatePageComponent],
  providers: [
    SALESPERSON_PROVIDER_DEPS,
    WhitelabelService,
    PartnerCenterHostService,
    SSCAccessService,
    InferenceApiHost,
    ConfigurationService,
    CountryStateService,
    SalesCountryStateService,
    FeatureFlagService,
    { provide: CountryStateServiceInterfaceToken, useClass: SalesCountryStateService },
    TaxonomyService,
    TaxonomyWrapperService,
    { provide: TaxonomyServiceInterfaceToken, useClass: TaxonomyWrapperService },
    { provide: MARKET_SERVICE_TOKEN, useClass: MarketService },
    { provide: EAC_MARKET_SERVICE_TOKEN, useClass: MarketService },
    SalespersonProvider,
    { provide: ACCESS_SERVICE_TOKEN, useFactory: accessFactory, deps: [SSCAccessService] },
    AccessToAllMarketProvider,
    { provide: DEFAULT_SALES_ASSIGNEE_ID_TOKEN, useExisting: SALESPERSON_ID_TOKEN },
    IncludedProductsService,
    {
      provide: PARTNER_ID_TOKEN,
      useFactory: (loggedInUser: LoggedInUserInfoService) => {
        return loggedInUser.getPartnerId();
      },
      deps: [LoggedInUserInfoService],
    },
    {
      provide: DISPLAY_SIMPLE_SEARCH,
      useFactory: accessDisplaySimpleSearchFactory,
      deps: [FeatureFlagService, Injector],
    },
  ],
})
export class BusinessModule {}
