import { DOCUMENT } from '@angular/common';
import { AfterViewInit, Component, inject, Inject, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AccountCreatedEvent } from '@galaxy/easy-account-create';
import { TranslateService } from '@ngx-translate/core';
import { DuplicateAccountsWarningComponent } from '@vendasta/businesses/duplicate-accounts';
import { combineLatest, EMPTY, Observable } from 'rxjs';
import { delay, map, retryWhen, switchMap, take } from 'rxjs/operators';
import { BUSINESS_SEARCH_URL } from '../../../accounts/accounts.routing';
import { MARKET_ID_TOKEN, PARTNER_ID_TOKEN, SALESPERSON_ID_TOKEN } from '../../../common/providers';
import { BreadCrumb } from '../../../navigation';
import { SalesActivityService } from '../../../sales-activity';
import { ACCOUNT_INFO } from '../../../urls';

@Component({
  styleUrls: ['./easy-account-create.component.scss'],
  templateUrl: './easy-account-create-page.component.html',
  standalone: false,
})
export class EasyAccountCreatePageComponent implements OnInit, AfterViewInit {
  marketId: string;

  getAccountDetailsUrl = ACCOUNT_INFO;

  get businessProfile(): any {
    return this._businessProfile;
  }

  set businessProfile(value) {
    this.marketId = value && value.marketId ? value.marketId.market_id : null;
    this._businessProfile = value;
  }

  private _businessProfile: any;
  breadcrumbs$: Observable<BreadCrumb[]>;

  @ViewChild('duplicateAccountsList', { static: false }) duplicateAccountsList: DuplicateAccountsWarningComponent;
  duplicatesWarning$: Observable<string>;
  document = inject(DOCUMENT);

  constructor(
    @Inject(PARTNER_ID_TOKEN) readonly partnerId$: Observable<string>,
    @Inject(MARKET_ID_TOKEN) readonly marketIds$: Observable<string[]>,
    @Inject(SALESPERSON_ID_TOKEN) readonly salespersonId$: Observable<string>,
    private readonly activityService: SalesActivityService,
    private readonly i18n: TranslateService,
    private readonly route: ActivatedRoute,
  ) {}

  ngOnInit(): void {
    const translations$ = this.i18n.stream([
      'MANAGE_ACCOUNTS.TITLE',
      'EASY_ACCOUNT_CREATE.SEARCH_FOR_BUSINESS',
      'EASY_ACCOUNT_CREATE.CREATE_ACCOUNT',
    ]);
    this.breadcrumbs$ = combineLatest([translations$]).pipe(
      map(([translations]) => {
        return <BreadCrumb[]>[
          { link: '/', label: translations['MANAGE_ACCOUNTS.TITLE'] },
          { link: BUSINESS_SEARCH_URL, label: translations['EASY_ACCOUNT_CREATE.SEARCH_FOR_BUSINESS'] },
          { label: translations['EASY_ACCOUNT_CREATE.CREATE_ACCOUNT'] },
        ];
      }),
    );
  }

  addAccountCreatedActivity(event: AccountCreatedEvent): void {
    this.salespersonId$
      .pipe(
        take(1),
        switchMap((loggedInSalespersonId) => {
          const action = 'account-created';
          if (loggedInSalespersonId === event.salespersonId) {
            return this.activityService.createActivity(event.accountGroupId, action, action);
          }
          return EMPTY;
        }),
        retryWhen((err) => err.pipe(delay(5000), take(10))),
      )
      .subscribe(
        () => {
          return;
        },
        () => {
          console.error('Failed to add account created activity');
        },
      );
  }

  ngAfterViewInit(): void {
    this.duplicatesWarning$ = this.duplicateAccountsList.potentialDuplicates$.pipe(
      map((potentialDuplicates) => {
        if (potentialDuplicates?.totalResults > 0) {
          return `${potentialDuplicates.totalResults} similiar account${
            potentialDuplicates.totalResults > 1 ? 's' : ''
          } found`;
        }
        return null;
      }),
    );
  }

  scrollIntoView(id: string): void {
    this.document.getElementById(id)?.scrollIntoView({ behavior: 'smooth' });
  }
}
