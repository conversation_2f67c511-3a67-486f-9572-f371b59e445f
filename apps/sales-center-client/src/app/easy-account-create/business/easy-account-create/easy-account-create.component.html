<eac-easy-account-create
  appHoverText="Go to training page"
  [enableMarketSelection]="false"
  [adminPanelSalespersonMode]="adminSalespersonMode$ | async"
  [partnerId]="partnerId"
  [placeId]="placeId$ | async"
  [competitorIds]="competitorIds$ | async"
  [showCost]="false"
  (appClicked)="handleAppClicked($event)"
  (businessProfileChange)="handleProfileChange($event)"
  (created)="handleCreated($event)"
  (createFailed)="handleCreateFailed()"
>
  <ng-content select=".pre-footer" class="pre-footer"></ng-content>
  <ng-content select=".footer" class="footer"></ng-content>
</eac-easy-account-create>
