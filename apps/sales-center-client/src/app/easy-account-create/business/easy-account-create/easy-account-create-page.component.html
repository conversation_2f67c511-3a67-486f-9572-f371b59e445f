<glxy-page>
  <glxy-page-toolbar>
    <glxy-page-title>
      <!-- leaving this here as I don't have time to pull out the business name -->
      <!-- This should turn into using the `<glxy-page-nav-button>` and `<glxy-page-title>` in the futute -->
      <app-nav-breadcrumbs [breadCrumbs]="breadcrumbs$ | async"></app-nav-breadcrumbs>
    </glxy-page-title>
  </glxy-page-toolbar>

  <div class="content">
    <app-easy-account-create
      *ngIf="partnerId$ | async as partnerId"
      [partnerId]="partnerId"
      (accountCreated)="addAccountCreatedActivity($event)"
      (businessProfileChange)="businessProfile = $event"
    >
      <business-duplicate-accounts-warning
        #duplicateAccountsList
        id="duplicate-accounts-list"
        class="pre-footer"
        [partnerId]="partnerId$ | async"
        [marketId]="marketId"
        [businessProfile]="businessProfile"
        [accountDetailsUrl]="getAccountDetailsUrl"
      ></business-duplicate-accounts-warning>
      <div class="footer" *ngIf="duplicatesWarning$ | async as warning">
        <button mat-stroked-button color="warn" (click)="scrollIntoView('duplicate-accounts-list')">
          <mat-icon>warning</mat-icon>
          &nbsp;{{ warning }}
        </button>
      </div>
    </app-easy-account-create>
  </div>
</glxy-page>
