# Easy Account Create 2.0

## Business Create (Easy Account Create)
This component is duplicated in `sales-center-client` and `partner-center-client` but contains sales center specific logic.

### Future development
Ideally after changes made
1. the entire file should be copy pasted into `partner-center-client`
1. but removing any code labeled as sales center specific
1. and adding back any partner center specific files.

Any changes should be either labeled as Sales Center specific or copied over to `partner-center-client`.

TODO:
- [x] move all injected dependencies into shared library.
- [x] create a page component for each center that is owned by that center. Would consist of breadcrumbs. The page component and its module may own the route.
- [ ] find the reusable pieces and migrate them to shared library under business-create.
- [ ] rename easy-account-create to business-create and move the component into shared library and wrap the component and all its dependencies in a `BusinessCreateModule`
