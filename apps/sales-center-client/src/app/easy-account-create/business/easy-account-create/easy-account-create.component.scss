// EAC styles come from the lib. These are SSC-specific additions / overrides.

@use 'design-tokens' as *;

:host ::ng-deep {
  // after introducing glxy-page, the "create account" footer would always stay visible at the bottom regardless of viewport
  // this wrapper allows the footer to latch onto it so that it can behave properly
  .content {
    position: relative;
    height: 100%;
    overflow-y: auto;
    overflow-x: visible;
  }

  .create-spinner {
    circle {
      stroke: $white;
    }
  }
}
