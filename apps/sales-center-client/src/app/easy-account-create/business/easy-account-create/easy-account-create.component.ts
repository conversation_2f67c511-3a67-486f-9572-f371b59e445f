import { ChangeDetectionStrategy, Component, EventEmitter, Inject, Input, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import {
  AccessService,
  EasyAccountCreateService,
  Feature,
  InferenceApiService,
  SalespersonService,
} from '@vendasta/businesses/easy-account-create';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Observable, combineLatest } from 'rxjs';
import { map, startWith } from 'rxjs/operators';

import { ACCESS_SERVICE_TOKEN, AccountCreatedEvent, AppId, SalespersonSelectorMode } from '@galaxy/easy-account-create';
import { USER_INFO_TOKEN } from '../../../core/feature-flag.service';
import { LoggedInUserInfo } from '../../../logged-in-user-info';
import { BUSINESS_VERIFY, TRAINING_PRODUCT } from '../../../urls';
import { SALESPERSON_SERVICE_TOKEN } from '../providers/salesperson.provider';

export const MARKET_SERVICE_TOKEN = 'MARKET_SERVICE_TOKEN';

@Component({
  selector: 'app-easy-account-create',
  templateUrl: './easy-account-create.component.html',
  styleUrls: ['./easy-account-create.component.scss'],
  providers: [InferenceApiService, EasyAccountCreateService],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class EasyAccountCreateComponent implements OnInit {
  @Input() partnerId: string;

  @Output() accountCreated = new EventEmitter<AccountCreatedEvent>();
  @Output() businessProfileChange: EventEmitter<any> = new EventEmitter();

  readonly adminSalespersonMode$: Observable<SalespersonSelectorMode>;
  readonly placeId$: Observable<string>;
  readonly competitorIds$: Observable<string[]>;

  constructor(
    private readonly router: Router,
    private readonly activatedRoute: ActivatedRoute,
    @Inject(SALESPERSON_SERVICE_TOKEN) private readonly salespersonService: SalespersonService,
    @Inject(ACCESS_SERVICE_TOKEN) private readonly accessService: AccessService,
    private readonly alertService: SnackbarService,
    @Inject(USER_INFO_TOKEN) private readonly loggedInUser$: Observable<LoggedInUserInfo>,
  ) {
    this.placeId$ = this.activatedRoute.queryParamMap.pipe(map((m) => m.get('place_id')));
    this.competitorIds$ = this.activatedRoute.queryParamMap.pipe(
      map((pm) => {
        const competitorPlaceIds = pm.get('competitor_place_ids');
        if (competitorPlaceIds) {
          return competitorPlaceIds.split(',');
        }
        return [];
      }),
    );
    this.adminSalespersonMode$ = combineLatest([
      this.accessService.hasAccessToFeature(Feature.markets),
      this.loggedInUser$,
      this.salespersonService.latestSalespeople$$.asObservable().pipe(startWith([])),
    ]).pipe(
      map(([hasMarketAccess, loggedInUser]) => {
        if (!loggedInUser.hasAccessToAllAccountsInMarket) {
          return 'hidden';
        }
        if (hasMarketAccess) {
          return 'read-write';
        }
        return 'read-only';
      }),
    );
  }

  ngOnInit(): void {
    this.salespersonService.loadAllSalespeople();
  }

  handleProfileChange($event: any): void {
    this.businessProfileChange.emit($event);
  }

  handleAppClicked(appId: AppId): void {
    this.router
      .navigateByUrl(TRAINING_PRODUCT(appId)) // TODO: Open in new tab
      .catch((err) => {
        console.error(err);
        this.alertService.openErrorSnack('Could not open training page');
      });
  }

  handleCreated($event: AccountCreatedEvent): void {
    this.router.navigateByUrl(BUSINESS_VERIFY($event.accountGroupId));
    this.accountCreated.emit($event);
  }

  handleCreateFailed(): void {
    this.alertService.openErrorSnack('Failed to create account. Ensure required fields are filled out.');
  }
}
