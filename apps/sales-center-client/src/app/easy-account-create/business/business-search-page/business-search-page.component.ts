import { Component } from '@angular/core';
import { combineLatest, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { BreadCrumb } from '../../../navigation';
import { TranslateService } from '@ngx-translate/core';
import { ActivatedRoute } from '@angular/router';
import { BUSINESS_SEARCH_URL } from '../../../accounts/accounts.routing';

@Component({
  selector: 'app-business-search-page',
  templateUrl: './business-search-page.component.html',
  styleUrls: ['./business-search-page.component.scss'],
  standalone: false,
})
export class BusinessSearchPageComponent {
  breadcrumbs$: Observable<BreadCrumb[]>;

  constructor(
    private readonly i18n: TranslateService,
    private readonly route: ActivatedRoute,
  ) {
    const translations$ = this.i18n.stream([
      'MANAGE_ACCOUNTS.TITLE',
      'EASY_ACCOUNT_CREATE.SEARCH_FOR_BUSINESS',
      'EASY_ACCOUNT_CREATE.CREATE_ACCOUNT',
    ]);
    this.breadcrumbs$ = combineLatest([translations$]).pipe(
      map(([translations]) => {
        return <BreadCrumb[]>[
          { link: '/', label: translations['MANAGE_ACCOUNTS.TITLE'] },
          { link: BUSINESS_SEARCH_URL, label: translations['EASY_ACCOUNT_CREATE.SEARCH_FOR_BUSINESS'] },
          { label: translations['EASY_ACCOUNT_CREATE.CREATE_ACCOUNT'] },
        ];
      }),
    );
  }
}
