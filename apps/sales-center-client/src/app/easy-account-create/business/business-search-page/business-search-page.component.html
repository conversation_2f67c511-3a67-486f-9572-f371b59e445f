<glxy-page>
  <glxy-page-toolbar>
    <glxy-page-title>
      <!-- leaving this here as I don't have time to pull out the business name -->
      <!-- This should turn into using the `<glxy-page-nav-button>` and `<glxy-page-title>` in the futute -->
      <app-nav-breadcrumbs [breadCrumbs]="breadcrumbs$ | async"></app-nav-breadcrumbs>
    </glxy-page-title>
  </glxy-page-toolbar>

  <h1 class="page-title">
    {{ 'EASY_ACCOUNT_CREATE.CREATE_BUSINESS_ACCOUNT' | translate }}
  </h1>
  <business-search></business-search>
</glxy-page>
