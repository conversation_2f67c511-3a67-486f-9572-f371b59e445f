import { Directive, HostListener, Input } from '@angular/core';
import { AsteriskService } from '../../astbe_sdk';
import { CallNowInfo } from './interface';
import { FeatureFlagService } from '../../core';
import { Features } from '../../features';
import { Observable, firstValueFrom } from 'rxjs';
import { shareReplay } from 'rxjs/operators';

@Directive({
  selector: '[appClickToCallNow]',
  standalone: false,
})
export class CallNowDirective {
  private _callNowInfo: CallNowInfo;

  @Input() set callNowInfo(callNowInfo: CallNowInfo) {
    this._callNowInfo = <CallNowInfo>{
      phoneNumber: callNowInfo.phoneNumber ? callNowInfo.phoneNumber.replace(/[^0-9]/g, '') : '',
      extension: callNowInfo.extension ? callNowInfo.extension : null,
    };
  }

  phoneServiceEnabled$: Observable<boolean>;

  constructor(
    private readonly asteriskService: AsteriskService,
    private readonly featureFlagService: FeatureFlagService,
  ) {
    // TODO: Move this check into a central phone service so it only gets called once
    this.phoneServiceEnabled$ = this.featureFlagService.featureFlagEnabled$(Features.PhoneService).pipe(shareReplay(1));
  }

  private async makeAsteriskCall(): Promise<void> {
    const extension = this._callNowInfo?.extension?.toString() || '';
    await firstValueFrom(this.asteriskService.call(this._callNowInfo.phoneNumber, extension, false));
  }

  private makePhoneCallFromBrowser(): void {
    const phoneNumber = this._callNowInfo.phoneNumber;
    const extension = this._callNowInfo.extension ? `;ext=${this._callNowInfo.extension}` : '';

    const phoneNumberWithExtension = phoneNumber + extension;

    window.open(`tel:${phoneNumberWithExtension}`, '_self');
  }

  @HostListener('click', ['$event'])
  async makeCall(): Promise<void> {
    const useAsteriskService = await firstValueFrom(this.phoneServiceEnabled$);
    useAsteriskService ? await this.makeAsteriskCall() : this.makePhoneCallFromBrowser();
  }
}
