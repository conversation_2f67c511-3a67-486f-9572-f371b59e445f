import { Observable } from 'rxjs';
import { startWith } from 'rxjs/operators';
import { CallNowDirective } from './call-now.directive';
import { CallNowInfo } from './interface';

class MockAsteriskService {}

class MockFeatureFlagService {
  featureFlagEnabled$(): Observable<boolean> {
    return new Observable<boolean>().pipe(startWith(false));
  }
}

describe('CallNowDirective', () => {
  it('should remove all non-numeric chracters from telephone string from callNowInfo', async () => {
    jest.spyOn(global, 'open').mockImplementation(() => window);

    const testCallNowInfo: CallNowInfo = {
      phoneNumber: '****************',
      extension: 127,
    };

    const directive = new CallNowDirective(MockAsteriskService as any, new MockFeatureFlagService() as any);

    directive.callNowInfo = testCallNowInfo;
    await directive.makeCall();

    expect(global.open).toHaveBeenCalledWith('tel:13065820666;ext=127', '_self');
  });
});
