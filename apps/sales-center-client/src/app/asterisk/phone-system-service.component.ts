import { Location } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Environment, EnvironmentService } from '@galaxy/core';
import { Salesperson } from '@galaxy/types';
import { $WebSocket, WebSocketSendMode } from 'angular2-websocket/angular2-websocket';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { HostService } from '../astbe_sdk/_generated';
import { AsteriskApiService, GetRecordingUrlResponse, LiveStatusResponse } from '../astbe_sdk/_internal';
import { SalespeopleService } from '../salespeople/salespeople.service';
import { PhoneMessage } from './asterisk';

/* tslint:disable */ @Injectable()
export class PhoneSystemService {
  // private phoneQueues$$: BehaviorSubject<PhoneQueue[]> = new BehaviorSubject(null);
  // private extension$$: BehaviorSubject<PhoneExtension> = new BehaviorSubject(null);
  private worker: SharedWorker;
  private ws: $WebSocket;
  private agid: string;
  private zenTicketID: string;
  private currentSalesPerson$: Observable<Salesperson>;
  private token$: BehaviorSubject<LiveStatusResponse> = new BehaviorSubject(null);

  constructor(
    private http: HttpClient,
    private hostService: HostService,
    private router: Router,
    private location: Location,
    private salesPeopleService: SalespeopleService,
    private environmentService: EnvironmentService,
    private api: AsteriskApiService, //        private iam: IAMService
  ) {
    if (typeof window['SharedWorker'] === 'undefined') {
      console.error('Some features are not supported by this browser');
      return;
    }
    // create shared web worker
    this.agid = '';
    let hostname = window.location.hostname;
    if (hostname === 'localhost') {
      hostname = hostname + ':' + window.location.port;
    }
    this.zenTicketID = '';
    // this.refreshAuthToken();

    this.worker = new SharedWorker(
      window.location.protocol + '//' + hostname + '/static/js/webworker_phonesystem.js',
      'phone_worker',
    );
    // register eventListener
    this.worker.port.onmessage = (event: MessageEvent) => {
      if (event.data['agid']) {
        window.open(`${window.location.protocol}//${window.location.hostname}/info/${event.data.agid}`, '_blank');
      }
      if (event.data['ZenTicketID']) {
        if (this.environmentService.getEnvironment() === Environment.PROD) {
          window.open(
            `${window.location.protocol}//support.vendasta.com/agent/tickets/${event.data.ZenTicketID}`,
            '_blank',
          );
        } else {
          window.open(
            `${window.location.protocol}//vendasta1425053146.zendesk.com/agent/tickets/${event.data.ZenTicketID}`,
            '_blank',
          );
        }
      }
    };
    window.setInterval(
      function (worker) {
        worker.postMessage('ping');
      },
      5000,
      this.worker.port,
    );

    const url = this.location.path(false);
    this.currentSalesPerson$ = !url.includes('forgot-password')
      ? this.salesPeopleService.getCurrentSalesPerson()
      : of(null);
    this.currentSalesPerson$.subscribe((salesPerson) => {
      if (!salesPerson) {
        return;
      }
      if (!salesPerson.phoneNumbers) {
        return;
      }
      if (salesPerson.phoneNumbers.length === 0) {
        return;
      }
      const extension = parseInt(salesPerson.phoneNumbers[0].split(',')[1], 10);
      const dat = {
        extension: extension,
      };
      if (salesPerson.partnerId === 'VMF' && dat.extension > 0) {
        const apiService = new AsteriskApiService(this.http, this.hostService);
        apiService.liveStatus(dat).subscribe((response) => this.token$.next(response));
        this.token$.subscribe((response) => {
          if (response == null) {
            if (this.ws) {
              this.ws.close(true);
            }
            return;
          }
          const token = response.token;
          const socketUrl = 'wss://' + this.hostService.httpsHost() + '/ws?token=' + token;
          // const socketUrl = 'ws://localhost:11001/ws?token=fred';
          this.ws = new $WebSocket(socketUrl, null, { reconnectIfNotNormalClose: false });
          this.ws.getDataStream().subscribe({
            next: (msg) => {
              this.handleEvent(JSON.parse(msg.data) as PhoneMessage);
            },
            error: () => {
              this.token$.next(null);
            },
            complete: () => {
              this.token$.next(null);
            },
          });
          window.setInterval(
            function (ws) {
              ws.send('ping', WebSocketSendMode.Direct);
            },
            25000,
            this.ws,
          );
        });
      }
    });
  }

  // public refreshAuthToken(): void {
  //   this.iam.getToken().subscribe(
  //     token => {
  //       const claims = this.decodeJWT(token);
  //       if (claims) {
  //         // Refresh 30 seconds before expiry
  //         const timeout = new Date(claims.exp * 1000).getTime() - new Date().getTime() - 30000;
  //         window.setTimeout(this.refreshAuthToken.bind(this), timeout);
  //       }
  //       // this.authToken$$.next(token);
  //     }
  //     // err => this.authToken$$.next('')
  //   );
  // }
  //
  // private decodeJWT(jwt: string): JWT | null {
  //   let claims;
  //   try {
  //     claims = JTWdecode(jwt);
  //   } catch (err) {
  //     console.log('CLAIMS FAILED: ', err);
  //   }
  //   return claims;
  // }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public sendMessage(message: string): void {
    this.worker.port.postMessage('sending');
  }

  public getRecordingUrl(key: string): Observable<GetRecordingUrlResponse> {
    const apiService = new AsteriskApiService(this.http, this.hostService);
    const dat = {
      id: key,
    };
    return apiService.getRecordingUrl(dat);
  }

  // public get phoneQueues$(): Observable<any> {
  //     return this.phoneQueues$$.pipe(shareReplay(1));
  // }
  //
  // public get extensions$(): Observable<any> {
  //     return this.extension$$.pipe(shareReplay(1));
  // }

  private handleEvent(data) {
    // format the data here before pushing
    if (data['Event'] === 'NewConnectedLine') {
      if (data['Agid']) {
        this.agid = data.Agid;
      }
      if (data['ZenTicketID']) {
        this.zenTicketID = data.ZenTicketID;
      }
    }
    if (data.Action) {
      const data2 = data;
      if (data2.Action === 'OpenWindow') {
        if (this.agid.length > 0 || this.zenTicketID.length > 0) {
          // check pid ?
          this.worker.port.postMessage({ action: 'OpenWindow', agid: this.agid, ZenTicketID: this.zenTicketID });
        }
      } else if (data2.Action === 'ClearLine') {
        this.agid = '';
        this.zenTicketID = '';
      }
    }
  }
}
