import { CommonModule as VendastaCommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { VaTableModule } from '@vendasta/uikit';
import { HostService } from '../astbe_sdk/_generated';
import { AsteriskApiService } from '../astbe_sdk/_internal';
import { SalesToolHostService } from '../core';
import { SalespeopleService } from '../salespeople/salespeople.service';
import { CallNowDirective } from './directives/call-now.directive';
import { PhoneSystemService } from './phone-system-service.component';

@NgModule({
  imports: [VendastaCommonModule, VaTableModule, MatCardModule],
  providers: [AsteriskApiService, PhoneSystemService, HostService, SalespeopleService, SalesToolHostService],
  exports: [CallNowDirective],
  declarations: [CallNowDirective],
})
export class AsteriskModule {}
