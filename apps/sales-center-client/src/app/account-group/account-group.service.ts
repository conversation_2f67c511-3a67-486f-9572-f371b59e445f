import { ObservableWorkStateMap } from '@vendasta/rx-utils/work-state';
import { AccountGroup, AccountGroupApiService, ProjectionFilter } from '@galaxy/account-group';
import { Observable, of, SchedulerLike } from 'rxjs';
import { doWorkIfInitial } from '../rx-utils/state-maps';
import { Injectable } from '@angular/core';
import { AccountGroupLocation } from '@galaxy/account-group';
import { catchError } from 'rxjs/operators';

export const LOAD_FAILED_ACCOUNT: AccountGroup = <AccountGroup>{
  accountGroupId: '',
  napData: <AccountGroupLocation>{
    companyName: 'PIPELINE.ACCOUNT_LOAD_FAILED',
  },
};

export interface AccountGroupService {
  readonly agsState: ObservableWorkStateMap<string, AccountGroup[]>;

  getMulti(
    accountGroupIds: string[],
    projectionFilter: ProjectionFilter,
    scheduler?: SchedulerLike,
  ): Observable<AccountGroup[]>;
}

@Injectable({ providedIn: 'root' })
export class AccountGroupStoreService implements AccountGroupService {
  readonly agsState = new ObservableWorkStateMap<string, AccountGroup[]>();

  constructor(private readonly accountGroupSdk: AccountGroupApiService) {}

  public getMulti(
    accountGroupIds: string[],
    projectionFilter: ProjectionFilter,
    scheduler?: SchedulerLike,
  ): Observable<AccountGroup[]> {
    const agIdsString = accountGroupIds.join('-');
    const id = `${agIdsString}-${JSON.stringify(projectionFilter)}`;

    return doWorkIfInitial(
      id,
      this.agsState,
      () => {
        return this.accountGroupSdk.getMulti(accountGroupIds, projectionFilter).pipe(
          catchError((err) => {
            if (err.status === 403) {
              return this.filterOutFailedAGIDsAndTryAgain(err, accountGroupIds, projectionFilter);
            } else {
              throw err;
            }
          }),
        );
      },
      scheduler,
    );
  }

  private getUniqueAccountGroupIDs(accountGroupIds: string[]): string[] {
    return accountGroupIds.filter(function (item, pos): boolean {
      return accountGroupIds.indexOf(item) === pos;
    });
  }

  private filterOutFailedAGIDsAndTryAgain(
    err: any,
    accountGroupIds: string[],
    projectionFilter: ProjectionFilter,
  ): Observable<AccountGroup[]> {
    const failedAgIds: string[] = [];
    // Note: this uses internal error details which are subject to change without notice.
    for (const AGID of err.error.details[0].failures) {
      failedAgIds.push(AGID.identifiers.account_group_id.values[0]);
    }

    const uniqueRequestAGIDs = this.getUniqueAccountGroupIDs(accountGroupIds);
    const uniqueFailedAGIDs = this.getUniqueAccountGroupIDs(failedAgIds);

    const accessibleAGIDs: string[] = [];
    for (const AGID of uniqueRequestAGIDs) {
      const foundAGID = uniqueFailedAGIDs.find((failedAgId) => {
        return AGID === failedAgId;
      });
      if (foundAGID === undefined) {
        accessibleAGIDs.push(AGID);
      }
    }

    if (accessibleAGIDs.length >= 1) {
      return this.accountGroupSdk.getMulti(accessibleAGIDs, projectionFilter);
    } else {
      return of([]);
    }
  }
}
