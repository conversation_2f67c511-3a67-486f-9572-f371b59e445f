import { Clipboard } from '@angular/cdk/clipboard';
import { BreakpointObserver } from '@angular/cdk/layout';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  Inject,
  OnDestroy,
  OnInit,
  Optional,
  ViewChild,
} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, NavigationBehaviorOptions, Router } from '@angular/router';
import { MarketingInfo, SocialURLs } from '@galaxy/account-group';
import { DocumentBuilderService } from '@galaxy/document-builder/core';
import { GetPartnerAccountGroupMappingResponseInterface, PartnerAccountGroupService } from '@galaxy/partner';
import { WhitelabelTranslationService } from '@galaxy/snapshot';
import { TranslateService } from '@ngx-translate/core';
import { Option } from '@vendasta/forms';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { SubscriptionList } from '@vendasta/rx-utils';
import { Opportunity as MicroserviceOpportunity } from '@vendasta/sales-opportunities';
import {
  Opportunity,
  Pipeline,
  SidePanelState,
  SidePanelStateData,
  SidePanelStateService,
  SlideOutPanelService,
  USER_PIPELINE_TOKEN,
} from '@vendasta/sales-ui';
import { EMPTY, Observable, ReplaySubject, combineLatest, of } from 'rxjs';
import { catchError, map, shareReplay, startWith, switchMap, take } from 'rxjs/operators';
import { AccessChecker, Feature, SSCAccessService } from '../access';
import { AccountTagsApiService } from '../account-tags/account-tags.api.service';
import { BANNER_TYPE } from '../banner';
import {
  CreateOpportunityData,
  CreateOpportunityFormComponent,
  DEFAULT_DESTINATION_URL_BUILDER,
} from '../common/sales-opportunities/create-opportunity-form/create-opportunity-form.component';
import { FeatureFlagService } from '../core';
import { USER_INFO_TOKEN } from '../core/feature-flag.service';
import { Features, VIEW_CAMPAIGNS_TOKEN } from '../features';
import { LoggedInUserInfo, LoggedInUserInfoService } from '../logged-in-user-info';
import { BreadCrumb } from '../navigation';
import { Configuration, PARTNER_CONFIG_TOKEN } from '../partner';
import {
  ACCESS_ASSOCIATED_PARTNER_INFO_TOKEN,
  CAN_ACCESS_IMPORTED_USER_ACTIONS_TOKEN,
  DEFAULT_ACCOUNTS_ORDERS_LAYOUT_TOKEN,
} from '../partner/partner-overrides';
import { SalesActivityComponent } from '../sales-activity/sales-activity.component';
import { PIN_STORE_TOKEN } from '../uikit-staging/pin/providers';
import { ACCOUNT_INFO_EDIT, PIPELINE_BOARD, PROPOSALS_ROOT } from '../urls';
import { AccountDetailsService } from './account-details.service';
import { AccountInfoPinStore } from './account-info-pin-store.service';
import { AccountInfoStoreService, ViewBusiness } from './account-info-store.service';
import { ArchiveBusinessDialogComponent } from './archive-business-dialog.component';
import { RetryMarketNameEvent } from './general-info/general-info-card.component';
import { NotesComponent, SubmitNotesEvent } from './notes/notes.component';
import { SalesOpportunityCardComponent } from './sales-opportunity/sales-opportunity-card.component';
import { CustomerIdentifiers } from './sub-title/customer-id/customer-id.service';

const GMAIL_EXTENSION_PARAM = 'ssc-gmail-extension';

@Component({
  selector: 'app-account-info',
  templateUrl: './account-info.component.html',
  styleUrls: ['./account-info.component.scss'],
  providers: [AccountInfoPinStore, { provide: PIN_STORE_TOKEN, useExisting: AccountInfoPinStore }],
  standalone: false,
})
export class AccountInfoComponent implements OnInit, OnDestroy, AfterViewInit {
  readonly opportunities$: Observable<Opportunity[]>;
  readonly businessInfo$: Observable<ViewBusiness>;
  readonly businessName$: Observable<string>;
  readonly marketName$: Observable<string>;
  readonly marketNameLoadFailed$: Observable<boolean>;
  readonly marketNameLoading$: Observable<boolean>;
  readonly hotness$: Observable<number>;
  readonly notes$: Observable<string>;
  readonly isBusinessLoadFailed$: Observable<boolean>;
  readonly isBusinessInfoLoading$: Observable<boolean>;
  readonly businessId$: Observable<string>;
  readonly partnerId$: Observable<string>;
  readonly marketId$: Observable<string>;
  readonly salesPersonId$: Observable<string>;
  readonly isNotesSaving$: Observable<boolean>;
  readonly enablePageActions$: Observable<boolean>;
  readonly hasAccessToOrders$: Observable<boolean>;
  readonly canCreateOrders$: Observable<boolean>;
  readonly hasAccessToOpportunities$: Observable<boolean>;
  readonly hasAccessToCustomFields$: Observable<boolean>;
  readonly businessIsArchived$: Observable<boolean>;
  readonly archivingBusiness$: Observable<boolean>;
  readonly customFieldFeature$: Observable<boolean>;
  readonly auxiliaryDataFeature$: Observable<boolean>;
  readonly customFieldMigration$: Observable<boolean>;
  readonly showOldCustomFieldCard$: Observable<boolean>;
  readonly inboundLeadAttributionFeature$: Observable<boolean>;
  readonly currency$: Observable<string> = this.configuration$.pipe(map((c) => c.defaultDisplayCurrency));
  readonly accessToPipeline$: Observable<boolean>;
  readonly archiveBannerType: string = BANNER_TYPE.info;
  readonly breadcrumbs$: Observable<BreadCrumb[]>;
  readonly socialMediaUrls$: Observable<SocialURLs>;
  readonly countryCode$: Observable<string>;
  readonly businessTags$: Observable<Option[]>;
  readonly customerIdentifiers$: Observable<CustomerIdentifiers>;
  readonly snapshotName$: Observable<string> = this.configuration$.pipe(
    map((c) => c.snapshotName || this.whitelabelTranslate.instant('SNAPSHOT_REPORT.SNAPSHOT_REPORT')),
  );
  readonly lifecycleFeatureEnabled$: Observable<boolean>;

  private readonly requests$$ = new ReplaySubject<null>();
  private readonly subscriptions = SubscriptionList.new();
  readonly createOpportunityData$: Observable<CreateOpportunityData>;

  readonly sidePanelState$: Observable<SidePanelStateData<any>>;

  readonly associatedPartnerId$: Observable<string>;

  readonly proposalUrl = `/${PROPOSALS_ROOT}`;
  @ViewChild('notes') private readonly notesEditor: NotesComponent;
  @ViewChild('opportunities') private readonly opportunitiesCard: SalesOpportunityCardComponent;
  @ViewChild('salesActivities') private readonly activityStream: SalesActivityComponent;
  @ViewChild('createOppFormOnAccountDetails') createOppForm: CreateOpportunityFormComponent;

  readonly marketingInfo$: Observable<MarketingInfo>;
  readonly businessInfoEditUrl$: Observable<string>;

  readonly proposalBuilderDisabled$ = this.documentBuilderService.isProposalBuilderDisabled();

  readonly SidePanelState = SidePanelState;

  largeDisplay = false;

  orderUrlCallback = (orderId: string) =>
    this.businessId$.pipe(map((businessId) => `/sales-orders/${businessId}/order/${orderId}`));

  fulfillmentFormUrlCallback = (orderId: string) =>
    this.businessId$.pipe(map((businessId) => `/sales-orders/${businessId}/work-order/${orderId}`));

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly store: AccountInfoStoreService,
    private readonly currentUser: LoggedInUserInfoService,
    private readonly dialog: MatDialog,
    private readonly archiveService: AccountDetailsService,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    // tslint:disable-next-line:no-unused-variable
    private readonly featureService: FeatureFlagService,
    private readonly sidePanelService: SidePanelStateService,
    private readonly slideOutPanelService: SlideOutPanelService,
    @Inject(SSCAccessService) private readonly access: AccessChecker,
    @Inject(PARTNER_CONFIG_TOKEN) public readonly configuration$: Observable<Configuration>,
    @Inject(VIEW_CAMPAIGNS_TOKEN) public readonly canViewCampaigns$: Observable<boolean>,
    @Optional() private readonly alerts: SnackbarService,
    private readonly infoCardService: AccountTagsApiService,
    private readonly translate: TranslateService,
    private readonly whitelabelTranslate: WhitelabelTranslationService,
    @Inject(USER_PIPELINE_TOKEN) readonly pipeline$: Observable<Pipeline>,
    @Inject(CAN_ACCESS_IMPORTED_USER_ACTIONS_TOKEN) readonly canAccessImportedUserActions$: Observable<boolean>,
    @Inject(DEFAULT_ACCOUNTS_ORDERS_LAYOUT_TOKEN) public readonly isDefaultAccountsOrdersLayout$: Observable<boolean>,
    @Inject(ACCESS_ASSOCIATED_PARTNER_INFO_TOKEN) public readonly accessAssociatedPartnerInfo$: Observable<boolean>,
    private readonly breakpoints: BreakpointObserver,
    private readonly changeDetector: ChangeDetectorRef,
    private readonly partnerAccountGroupService: PartnerAccountGroupService,
    private readonly pageTitleService: Title,
    @Inject(USER_INFO_TOKEN) readonly userInfo$: Observable<LoggedInUserInfo>,
    private readonly documentBuilderService: DocumentBuilderService,
    private clipboard: Clipboard,
  ) {
    this.accessToPipeline$ = this.access.hasAccessToFeature(Feature.pipeline);
    this.businessId$ = this.getAccountGroupIdFromUrl();
    this.isBusinessInfoLoading$ = this.businessId$.pipe(switchMap((id) => store.isLoading$(id)));
    this.sidePanelState$ = this.sidePanelService.sidePanelState$;

    this.socialMediaUrls$ = this.businessId$.pipe(switchMap((id) => store.fetchSocialMediaUrls(id)));

    this.isBusinessLoadFailed$ = this.businessId$.pipe(
      switchMap((id) => store.isFailed$(id)),
      shareReplay(1),
    );
    this.customFieldFeature$ = featureService.featureFlagEnabled$(Features.CustomFields);

    this.inboundLeadAttributionFeature$ = featureService.featureFlagEnabled$(Features.InboundLeadAttribution);
    this.inboundLeadAttributionFeature$.pipe(take(1)).subscribe();
    this.enablePageActions$ = this.businessId$.pipe(map((id: string): boolean => Boolean(id)));
    this.hasAccessToOrders$ = this.access.hasAccessToFeature(Feature.salesOrders);
    this.canCreateOrders$ = combineLatest([
      this.hasAccessToOrders$,
      this.featureService.featureFlagEnabled$(Features.AdminsCreateOrders),
      this.userInfo$,
    ]).pipe(
      map(([hasAccessToOrders, featureFlagEnabled, userInfo]) => {
        if (userInfo.isImpersonation) {
          return hasAccessToOrders && featureFlagEnabled;
        }
        return hasAccessToOrders;
      }),
    );
    this.hasAccessToOpportunities$ = this.access.hasAccessToFeature(Feature.pipeline);
    this.hasAccessToCustomFields$ = this.access.hasAccessToFeature(Feature.customFields);

    this.businessInfo$ = this.businessId$.pipe(
      switchMap((id) => store.businessInfo$(id)),
      shareReplay(1),
    );

    this.businessIsArchived$ = this.businessInfo$.pipe(
      switchMap((info) => {
        return this.archiveService.archiveBusinessResult$(info.businessId).pipe(startWith(info.isArchived));
      }),
    );

    this.archivingBusiness$ = this.businessId$.pipe(switchMap((id) => this.archiveService.archivingBusiness$(id)));

    this.businessName$ = this.businessInfo$.pipe(map((info) => info.name));

    this.subscriptions.add(this.businessName$, (name) => this.pageTitleService.setTitle(name));
    this.breadcrumbs$ = this.businessName$.pipe(
      switchMap((name) => {
        return this.buildBreadcrumbs(this.activatedRoute.queryParams.pipe(map((params) => params?.origin)), name);
      }),
    );
    this.marketName$ = this.businessInfo$.pipe(
      switchMap((info) => this.store.fetchMarketName$(info.partnerId, info.marketId)),
    );
    this.marketNameLoading$ = this.businessInfo$.pipe(
      switchMap((info) => this.store.isMarketNameLoading$(info.partnerId, info.marketId)),
    );
    this.marketNameLoadFailed$ = this.businessInfo$.pipe(
      switchMap((info) => this.store.isMarketNameLoadFailed$(info.partnerId, info.marketId)),
    );

    this.hotness$ = this.businessInfo$.pipe(map((info) => info.hotness));

    this.notes$ = this.businessInfo$.pipe(
      switchMap((info) => {
        return this.store.notes$(info.businessId).pipe(startWith(info.notes));
      }),
    );
    this.isNotesSaving$ = this.businessId$.pipe(switchMap((bizId) => this.store.isNotesSaving$(bizId)));
    const notesStatus$ = this.businessId$.pipe(switchMap((bizId) => this.store.isNotesSaveSuccess$(bizId)));
    this.subscriptions.add(notesStatus$, (success) => {
      if (!success) {
        this.alerts.openErrorSnack('There was a problem updating notes');
      }
    });

    this.subscriptions.add(
      combineLatest([this.currentUser.loggedInUserInfo$, this.businessInfo$]),
      ([info, business]) => {
        if (info.salespersonId === business.assigneeId) {
          this.store.updateIsRead(business.businessId, true);
        }
      },
    );

    this.createOpportunityData$ = this.businessInfo$.pipe(
      map((bizInfo) => {
        const oppData: CreateOpportunityData = {
          accountGroupId: bizInfo.accountGroupId,
          accountGroupName: bizInfo.name,
          navigationData: {
            navigateOnSuccess: true,
            destURLBuilderOnSuccess: DEFAULT_DESTINATION_URL_BUILDER,
          },
        };
        return oppData;
      }),
    );

    const isReadStatus$ = this.businessId$.pipe(switchMap((bizId) => this.store.isUpdateReadSuccess$(bizId)));

    this.subscriptions.add(isReadStatus$, (success) => {
      if (!success) {
        this.alerts.openErrorSnack('There was a problem updating notes');
      }
    });

    this.countryCode$ = this.businessInfo$.pipe(map((info) => info.countryCode));

    this.partnerId$ = this.businessInfo$.pipe(map((biz) => biz.partnerId));
    this.marketId$ = this.businessInfo$.pipe(map((biz) => biz.marketId));
    this.salesPersonId$ = this.currentUser.salesPersonId$;

    this.showOldCustomFieldCard$ = combineLatest([this.customFieldFeature$, this.customFieldMigration$]).pipe(
      map(([customFieldFeature, customFieldMigration]) => {
        return customFieldFeature && !customFieldMigration;
      }),
    );

    this.businessTags$ = this.businessId$.pipe(
      switchMap((id) => this.infoCardService.getAccountTags(id)),
      map((respTags) => respTags.tags.map((tag) => ({ name: tag, value: tag }) as Option)),
      catchError(() => {
        this.alerts.openErrorSnack('Cannot load business tags');
        return EMPTY;
      }),
    );

    this.setUpBusinessFetching(this.businessId$, store);

    this.customerIdentifiers$ = this.businessId$.pipe(
      switchMap((businessid) => this.store.fetchCustomerIdentifiers(businessid)),
    );

    this.fetchOriginFromUrlAndTrack();

    this.associatedPartnerId$ = this.getAssociatedPartnerID();

    this.marketingInfo$ = this.businessId$.pipe(switchMap((businessid) => this.store.fetchMarketingInfo$(businessid)));
    this.businessInfoEditUrl$ = this.businessId$.pipe(map((businessId) => ACCOUNT_INFO_EDIT(businessId)));
  }

  getAssociatedPartnerID(): Observable<string> {
    return combineLatest([this.accessAssociatedPartnerInfo$, this.businessId$]).pipe(
      switchMap(([hasAccess, businessId]) => {
        if (!hasAccess) {
          return of('');
        }
        return this.partnerAccountGroupService.getPartnerAccountGroupMapping('', businessId);
      }),
      map((pag: GetPartnerAccountGroupMappingResponseInterface) => {
        if (!pag) {
          return '';
        }
        return pag.partnerId;
      }),
      catchError((err) => {
        if (err.status === 404) {
          return of('');
        }
        this.alerts.openErrorSnack('ACCOUNT_DETAILS.BUSINESS_PRIORITIES.ERROR_GETTING_PARTNER_ID');
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  fetchOriginFromUrlAndTrack(): void {
    this.subscriptions.add(this.route.queryParams, (queryParams) => {
      if (!!queryParams.origin && queryParams.origin === GMAIL_EXTENSION_PARAM) {
        // Removes param from query so that it isn't tracked a second time if the user refreshes the page
        this.router.navigate([], {
          queryParams: {
            origin: null,
          },
          queryParamsHandling: 'merge',
        });
      }
    });
  }

  ngOnInit(): void {
    this.subscriptions.add(
      this.breakpoints
        .observe([
          '(min-width: 1024px)', // We use the layout breakpoints from UIKit
        ])
        .pipe(
          map((v) => {
            for (const i of Object.keys(v.breakpoints)) {
              if (v.breakpoints[i]) {
                return true;
              }
            }
            return false;
          }),
        ),
      (v) => {
        this.largeDisplay = v;
        this.changeDetector.detectChanges();
      },
    );

    this.refreshBusiness();
  }

  ngAfterViewInit(): void {
    this.subscriptions.add(this.getAccountGroupIdFromUrl(), (id) => {
      if (this.createOppForm) {
        this.createOppForm.triggerExternalSalesUIBusinessIdDep(id);
      }
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
    this.slideOutPanelService.closeSlideOut();
  }

  refreshBusiness(): void {
    this.requests$$.next(null);
  }

  saveNotes(event: SubmitNotesEvent): void {
    this.store.updateNotes(event.notes, event.businessId);
  }

  directToAccountInfoEdit(businessId: string): void {
    this.router.navigateByUrl(ACCOUNT_INFO_EDIT(businessId), <NavigationBehaviorOptions>{ skipLocationChange: true });
  }

  public addSalesActivityAction(): void {
    this.activityStream.enterCreateMode();
  }

  openOpportunityCreateForm(): void {
    this.sidePanelService.switchState(this.SidePanelState.opportunityCreate);
    this.slideOutPanelService.openSlideOut();
    this.scrollToTop();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  handleSavedOpportunity(o: MicroserviceOpportunity): void {
    this.slideOutPanelService.closeSlideOut();
  }

  private scrollToTop(): void {
    document.getElementById('top-of-sidepanel').scrollIntoView({ behavior: 'smooth' });
  }

  public startEmailCampaign(): void {
    this.sidePanelService.switchState(this.SidePanelState.campaignCreate);
    this.slideOutPanelService.openSlideOut();
  }

  public createOpportunityAction(): void {
    this.sidePanelService.switchState(this.SidePanelState.opportunityCreate);
    this.slideOutPanelService.openSlideOut();
  }

  public selectManualOperationAction(): void {
    this.sidePanelService.switchState(this.SidePanelState.manualAutomation);
    this.slideOutPanelService.openSlideOut();
  }

  public archiveAccountAction(businessId: string): void {
    this.dialog.closeAll();
    this.dialog.open(ArchiveBusinessDialogComponent, { data: { accountGroupId: businessId } });
  }

  public unarchiveAccountAction(businessId: string): void {
    this.archiveService.archiveBusiness(businessId, '', false);
  }

  async retryMarketName($event: RetryMarketNameEvent): Promise<void> {
    this.store.fetchMarketName$($event.partnerId, $event.marketId);
  }

  private setUpBusinessFetching(businessId$: Observable<string>, store: AccountInfoStoreService): void {
    combineLatest([this.requests$$, businessId$]).subscribe(([, bizId]) => store.fetchBusinessInfo(bizId));
  }

  private getAccountGroupIdFromUrl(): Observable<string> {
    return this.activatedRoute.params.pipe(map((params) => params?.accountGroupId));
  }

  scrollToContactCard(): void {
    const scrollOptions = <ScrollIntoViewOptions>{
      block: 'center',
      inline: 'center',
      behavior: 'smooth',
    };
    document.getElementById('contacts-card-ref').scrollIntoView(scrollOptions);
  }

  createOrder(): void {
    this.businessId$.pipe(take(1)).subscribe((businessId) => {
      this.router.navigate(['order-management', businessId, 'create']);
    });
  }

  orderClicked(orderId: string): void {
    this.businessId$.pipe(take(1)).subscribe((businessId) => {
      this.router.navigate(['sales-orders', businessId, 'order', orderId]);
    });
  }

  public buildBreadcrumbs(origin$: Observable<string>, businessName: string): Observable<BreadCrumb[]> {
    const pipelineOptionTitle$ = this.translate.stream('NAVIGATION.SIDEBAR.PIPELINE_OPTION_TITLE');
    const manageAccountsTitle$ = this.translate.stream('MANAGE_ACCOUNTS.TITLE');
    const accountSearchTitle$ = this.translate.stream('MANAGE_ACCOUNTS.ACCOUNT_SEARCH');

    return combineLatest([origin$, pipelineOptionTitle$, manageAccountsTitle$, accountSearchTitle$]).pipe(
      map(([origin, pipelineTitle, manageAccountsTitle, accountSearchTitle]) => {
        switch (origin) {
          case 'pipeline':
            return [{ label: pipelineTitle, link: '/pipeline' }, { label: businessName }];
          case 'pipeline-board':
            return [{ label: pipelineTitle, link: `/${PIPELINE_BOARD}` }, { label: businessName }];
          case 'accounts-search':
            return [{ label: accountSearchTitle, link: '/accounts/search' }, { label: businessName }];
          default:
            return [{ label: manageAccountsTitle, link: '/' }, { label: businessName }];
        }
      }),
    );
  }

  buildOrdersPageForBusinessQuery(businessId: string): Record<string, string> {
    return {
      'sales-orders': encodeURIComponent(JSON.stringify({ businessId })),
    };
  }

  copyBusinessName(): void {
    this.businessName$.pipe(take(1)).subscribe((name) => {
      this.clipboard.copy(name);
      this.alerts.openSuccessSnack('COMMON.COPIED_TO_CLIPBOARD');
    });
  }
}
