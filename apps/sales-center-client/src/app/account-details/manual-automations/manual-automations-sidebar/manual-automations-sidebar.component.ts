import { Component, Input, OnInit } from '@angular/core';
import { SlideOutPanelService } from '@vendasta/sales-ui';
import { ViewBusiness } from '../../account-info-store.service';
import { Context, EntityType } from '@vendasta/automata';

@Component({
  selector: 'app-manual-automations-sidebar',
  templateUrl: './manual-automations-sidebar.component.html',
  styleUrls: ['./manual-automations-sidebar.component.scss'],
  standalone: false,
})
export class ManualAutomationsSidebarComponent implements OnInit {
  @Input() business: ViewBusiness;
  entities = new Map<EntityType, string[]>();
  partnerContext = Context.AUTOMATION_CONTEXT_PARTNER;

  constructor(private readonly slideOutPanelService: SlideOutPanelService) {}

  ngOnInit() {
    this.entities.set(EntityType.ENTITY_TYPE_ACCOUNT_GROUP, [this.business.businessId]);
  }

  closeSideNav(): void {
    this.slideOutPanelService.closeSlideOut();
  }
}
