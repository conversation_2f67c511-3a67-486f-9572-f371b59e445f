<div class="start-automation-sidebar">
  <div class="container">
    <div class="row">
      <div class="col col-xs-10 panel-title">
        <h3>{{ 'MANUAL_AUTOMATIONS.START_AUTOMATION_TITLE' | translate }}</h3>
      </div>
      <div class="col col-xs-2 right-align">
        <button tabindex="-1" mat-button (click)="closeSideNav()">
          <mat-icon>close</mat-icon>
        </button>
      </div>
    </div>
  </div>
  <div class="start-container">
    <automata-start-manual-automation-form
      [companyName]="business.name"
      [namespace]="business.partnerId"
      [entities]="entities"
      [hideSettingsLink]="true"
      [onlySalespersonAutomations]="true"
      [hasAllMarketAccess]="true"
      [context]="partnerContext"
      (cancel)="closeSideNav()"
      (success)="closeSideNav()"
    ></automata-start-manual-automation-form>
  </div>
</div>
