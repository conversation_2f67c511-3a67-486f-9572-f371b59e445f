import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'app-manual-automations-card',
  templateUrl: './manual-automations-card.component.html',
  styleUrls: ['./manual-automations-card.component.scss'],
  standalone: false,
})
export class ManualAutomationsCardCardComponent {
  @Input() partnerId: string;
  @Input() businessId: string;
  @Input() marketId: string;

  @Output() startAutomation: EventEmitter<void> = new EventEmitter<void>();

  constructor(public dialog: MatDialog) {}

  startClicked(): void {
    this.startAutomation.emit();
  }
}
