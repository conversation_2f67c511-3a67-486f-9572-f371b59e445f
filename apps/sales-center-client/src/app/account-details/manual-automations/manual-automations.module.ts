import { MatCardModule } from '@angular/material/card';

import { MatDividerModule } from '@angular/material/divider';

import { NgModule } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import { ManualAutomationsCardCardComponent } from './manual-automations-card-card.component';
import { CommonModule } from '@angular/common';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { ManualAutomationsSidebarComponent } from './manual-automations-sidebar/manual-automations-sidebar.component';
import { StartManualAutomationModule, LatestAutomationsModule } from '@galaxy/automata/shared';
@NgModule({
  imports: [
    MatCardModule,
    MatDividerModule,
    CommonModule,
    GalaxyEmptyStateModule,
    MatIconModule,
    MatButtonModule,
    TranslateModule,
    StartManualAutomationModule,
    LatestAutomationsModule,
  ],
  declarations: [ManualAutomationsCardCardComponent, ManualAutomationsSidebarComponent],
  exports: [ManualAutomationsCardCardComponent, ManualAutomationsSidebarComponent],
})
export class ManualAutomationsModule {}
