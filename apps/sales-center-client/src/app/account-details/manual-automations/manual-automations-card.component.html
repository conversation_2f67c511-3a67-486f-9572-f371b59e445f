<mat-card appearance="outlined">
  <mat-card-header class="mat-card-header-with-action">
    <mat-card-title>
      <span>{{ 'MANUAL_AUTOMATIONS.TITLE' | translate }}</span>
      <ng-content select="[afterTitle]"></ng-content>
    </mat-card-title>
    <button mat-icon-button color="primary" (click)="startClicked()">
      <mat-icon>play_arrow</mat-icon>
    </button>
  </mat-card-header>

  <div content>
    <automata-latest-automations
      [partnerId]="partnerId"
      [marketId]="marketId"
      [businessId]="businessId"
    ></automata-latest-automations>
  </div>
</mat-card>
