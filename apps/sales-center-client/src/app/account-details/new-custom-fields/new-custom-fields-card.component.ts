import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, Input, NgModule, OnDestroy, ViewChild } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import {
  AuxiliaryDataFieldsComponent,
  AuxiliaryDataFieldsModule,
  AuxiliaryDataModule,
  SHOW_ALL_FIELDS,
} from '@vendasta/auxiliary-data-components';
import { BehaviorSubject, Observable, Subject, fromEvent } from 'rxjs';
import { map, take, takeUntil } from 'rxjs/operators';
import { NewCustomFieldsDialogComponent, NewCustomFieldsDialogModule } from './new-custom-fields-dialog.component';

const DEFAULT_NUMBER_OF_FIELDS_TO_SHOW = 10;

@Component({
  selector: 'app-new-custom-fields-card',
  template: `
    <mat-card appearance="outlined">
      <mat-card-header class="mat-card-header-with-action">
        <mat-card-title>
          <span>{{ 'CUSTOM_FIELDS.TITLE' | translate }}</span>
          <ng-content select="[afterTitle]"></ng-content>
        </mat-card-title>
        <button *ngIf="displayEditPencil$$ | async" mat-icon-button color="primary" (click)="openCustomFieldsDialog()">
          <mat-icon>edit</mat-icon>
        </button>
      </mat-card-header>

      <mat-card-content class="custom-fields-list">
        <aux-auxiliary-fields
          #fieldsComponent
          [partnerId]="partnerId"
          [objectType]="'business'"
          [objectId]="businessId"
          [numberOfFieldsToShow]="numberOfFieldsToDisplay$ | async"
          [readOnly]="true"
          [reload$$]="reloadComponent$$"
          [emptyStateIcon]="'format_list_bulleted'"
          [emptyStateTitle]="''"
          [emptyStateText]="'CUSTOM_FIELDS.EMPTY_CUSTOM_FIELDS_DESCRIPTION'"
        ></aux-auxiliary-fields>
      </mat-card-content>

      <ng-container *ngIf="(displayAllCustomFields$ | async) === false">
        <mat-card-actions align="end">
          <button mat-button color="primary" (click)="showAllCustomFields()">
            {{ 'COMMON.ACTION_LABELS.VIEW_ALL' | translate }}
          </button>
        </mat-card-actions>
      </ng-container>
    </mat-card>
  `,
  styles: [':host mat-card-title { display: flex; align-items: center; }'],
  standalone: false,
})
export class NewCustomFieldsCardComponent implements AfterViewInit, OnDestroy {
  @ViewChild('fieldsComponent') private fieldsComponent: AuxiliaryDataFieldsComponent;

  @Input() businessId: string;
  @Input() partnerId: string;

  private readonly displayAllCustomFields$$: BehaviorSubject<boolean> = new BehaviorSubject(false);
  displayAllCustomFields$: Observable<boolean> = this.displayAllCustomFields$$.asObservable();

  displayEditPencil$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);

  numberOfFieldsToDisplay$ = this.displayAllCustomFields$.pipe(
    map((displayAllCustomFields) => (displayAllCustomFields ? SHOW_ALL_FIELDS : DEFAULT_NUMBER_OF_FIELDS_TO_SHOW)),
  );

  reloadComponent$$ = new Subject<void>();
  keydown$ = fromEvent(window, 'keydown');
  destroyed$$ = new Subject<void>();

  constructor(private readonly dialog: MatDialog) {}

  openCustomFieldsDialog(): void {
    const data = {
      businessId: this.businessId,
      partnerId: this.partnerId,
    };
    const dialogRef = this.dialog.open(NewCustomFieldsDialogComponent, { width: '900px', data });
    dialogRef
      .afterClosed()
      .pipe(take(1))
      .subscribe((saved) => {
        if (saved) {
          this.reloadComponent$$.next();
        }
      });
  }

  showAllCustomFields(): void {
    this.displayAllCustomFields$$.next(true);
  }

  showOmniSearch(event: any): void {
    const tag = event.target.tagName.toLowerCase();
    if (
      event.key === 'c' &&
      tag !== 'input' &&
      tag !== 'textarea' &&
      tag !== 'span' &&
      !event.ctrlKey &&
      !event.metaKey &&
      !event.shiftKey &&
      !event.altKey
    ) {
      if (!this.dialog.openDialogs || !this.dialog.openDialogs.length) {
        this.openCustomFieldsDialog();
      }
    }
  }

  ngAfterViewInit(): void {
    this.fieldsComponent.fieldHandlers$.pipe(take(1)).subscribe((fields) => {
      if (!fields.length) {
        this.displayAllCustomFields$$.next(true);
        this.displayEditPencil$$.next(false);
      }
    });
    this.keydown$.pipe(takeUntil(this.destroyed$$)).subscribe((event) => this.showOmniSearch(event));
  }

  ngOnDestroy(): void {
    this.destroyed$$.next();
  }
}

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatDialogModule,
    AuxiliaryDataModule,
    AuxiliaryDataFieldsModule,
    NewCustomFieldsDialogModule,
  ],

  // AIM / SCAM patterns - only a single thing!
  declarations: [NewCustomFieldsCardComponent],
  exports: [NewCustomFieldsCardComponent],
})
export class NewCustomFieldsCardModule {}
