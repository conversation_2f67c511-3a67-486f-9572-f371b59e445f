import { CommonModule } from '@angular/common';
import { Component, Inject, NgModule, OnInit, ViewChild } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AuxiliaryDataModule, AuxiliaryDataTableComponent } from '@vendasta/auxiliary-data-components';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { BehaviorSubject } from 'rxjs';
import { take } from 'rxjs/operators';

const UPDATE_SUCCESS_TRANSLATION_KEY = 'CUSTOM_FIELDS.UPDATE_SUCCESS_MESSAGE';
const UPDATE_FAIL_TRANSLATION_KEY = 'CUSTOM_FIELDS.UPDATE_UNSUCCESSFUL_MESSAGE';

export interface CustomFieldDialogData {
  businessId: string;
  partnerId: string;
}

@Component({
  selector: 'app-new-custom-fields-dialog',
  template: `
    <mat-dialog-content>
      <aux-auxiliary-data-table
        #auxDataTable
        [partnerId]="partnerId"
        [objectType]="'business'"
        [objectId]="businessId"
      ></aux-auxiliary-data-table>
    </mat-dialog-content>

    <mat-dialog-actions align="end">
      <button mat-button matDialogClose color="primary" [disabled]="saving$$ | async">
        {{ 'COMMON.ACTION_LABELS.CANCEL' | translate }}
      </button>
      <ng-container *ngIf="saving$$ | async; else submitButton">
        <mat-spinner class="button-spinner" [diameter]="25" [strokeWidth]="3"></mat-spinner>
      </ng-container>
      <ng-template #submitButton>
        <button
          mat-raised-button
          color="primary"
          [disabled]="auxiliaryDataTable?.formGroup.pristine"
          (click)="onSave()"
        >
          {{ 'COMMON.ACTION_LABELS.SAVE' | translate }}
        </button>
      </ng-template>
    </mat-dialog-actions>
  `,
  styles: [],
  standalone: false,
})
export class NewCustomFieldsDialogComponent implements OnInit {
  @ViewChild('auxDataTable') auxiliaryDataTable: AuxiliaryDataTableComponent;

  businessId: string;
  partnerId: string;
  saving$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  constructor(
    public dialogRef: MatDialogRef<NewCustomFieldsDialogComponent>,
    @Inject(MAT_DIALOG_DATA) private readonly data: CustomFieldDialogData,
    private readonly translate: TranslateService,
    private readonly alerts: SnackbarService,
  ) {}

  onSave(): void {
    this.saving$$.next(true);
    this.auxiliaryDataTable
      ?.saveChanges()
      .pipe(take(1))
      .subscribe(
        (saved) => {
          if (saved) {
            this.alerts.openSuccessSnack(UPDATE_SUCCESS_TRANSLATION_KEY);
            this.dialogRef.close(true);
          } else {
            this.alerts.openErrorSnack(UPDATE_FAIL_TRANSLATION_KEY);
          }
          this.saving$$.next(false);
        },
        () => {
          this.alerts.openErrorSnack(UPDATE_FAIL_TRANSLATION_KEY);
          this.saving$$.next(false);
        },
      );
  }

  ngOnInit(): void {
    this.partnerId = this.data.partnerId;
    this.businessId = this.data.businessId;
  }
}

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    MatProgressSpinnerModule,
    MatIconModule,
    MatButtonModule,
    MatDialogModule,
    AuxiliaryDataModule,
  ],

  // AIM / SCAM patterns - only a single thing!
  declarations: [NewCustomFieldsDialogComponent],
  exports: [NewCustomFieldsDialogComponent],
})
export class NewCustomFieldsDialogModule {}
