import { Component, Input } from '@angular/core';
import { AccountGroupService, MarketingInfo, MarketingInfoOperation, UpdateOperations } from '@galaxy/account-group';
import { TranslateService } from '@ngx-translate/core';
import { LifecycleStage } from '@vendasta/account-group';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { BehaviorSubject, ReplaySubject, firstValueFrom } from 'rxjs';

@Component({
  selector: 'app-account-lifecycle, [app-account-lifecycle]',
  templateUrl: './account-lifecycle.component.html',
  styleUrls: ['./account-lifecycle.component.scss'],
  standalone: false,
})
export class AccountLifecycleComponent {
  readonly lifecycleStage = LifecycleStage;

  private readonly marketingInfo$$ = new ReplaySubject<MarketingInfo>(1);

  private readonly loading$$ = new BehaviorSubject<boolean>(false);
  readonly loading$ = this.loading$$.asObservable();

  private readonly lifeCycleStage$$ = new ReplaySubject<LifecycleStage>(1);
  readonly lifeCycleStage$ = this.lifeCycleStage$$.asObservable();

  @Input() accountGroupId: string;
  @Input() set marketingInfo(marketingInfo: MarketingInfo) {
    marketingInfo?.lifecycleStage
      ? this.lifeCycleStage$$.next(marketingInfo.lifecycleStage as number as LifecycleStage)
      : this.lifeCycleStage$$.next(LifecycleStage.LIFECYCLE_STAGE_UNSET);

    this.marketingInfo$$.next(marketingInfo);
  }

  constructor(
    private readonly translate: TranslateService,
    private readonly accountGroupService: AccountGroupService,
    private readonly alertService: SnackbarService,
  ) {}

  async updateLifeCycleStage(stage: LifecycleStage): Promise<void> {
    this.loading$$.next(true);

    const updateOperation = await firstValueFrom(this.marketingInfo$$).then((marketingInfo) =>
      this.formUpdateOperation(marketingInfo, stage),
    );

    firstValueFrom(this.accountGroupService.bulkUpdate(this.accountGroupId, updateOperation))
      .then(
        () => this.lifeCycleStage$$.next(stage),
        (err) => {
          console.error(err);
          this.alertService.openErrorSnack('COMMON.LIFECYCLE_STAGE_UPDATE_ERROR');
        },
      )
      .finally(() => this.loading$$.next(false));
  }

  private formUpdateOperation(marketingInfo: MarketingInfo, stage: LifecycleStage): UpdateOperations {
    const protoLifecycleStage = <LifecycleStage>(<unknown>stage);
    const marketingInfoOperation =
      typeof marketingInfo === 'undefined'
        ? new MarketingInfoOperation({ lifecycleStage: protoLifecycleStage })
        : new MarketingInfoOperation({
            conversionPoint: marketingInfo.conversionPoint,
            marketingClassification: marketingInfo.marketingClassification,
            lifecycleStage: protoLifecycleStage,
          });
    const UpdateOp = new UpdateOperations();
    UpdateOp.addUpdateOperation(marketingInfoOperation);
    return UpdateOp;
  }
}
