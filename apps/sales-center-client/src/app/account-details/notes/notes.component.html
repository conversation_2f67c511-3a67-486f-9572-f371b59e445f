<mat-card appearance="outlined">
  <mat-card-header class="mat-card-header-with-action">
    <mat-card-title>
      <span>{{ 'ACCOUNT_DETAILS.NOTES.TITLE' | translate }}</span>
      <ng-content select="[afterTitle]"></ng-content>
    </mat-card-title>
    <button mat-icon-button color="primary" *ngIf="(editMode$ | async) === false" (click)="openEditor()">
      <mat-icon>edit</mat-icon>
    </button>
  </mat-card-header>
  <ng-container *ngIf="editMode$ | async; then editor; else readonly"></ng-container>
  <ng-template #editor>
    <mat-card-content>
      <mat-form-field appearance="outline" class="fade-in">
        <textarea
          rows="25"
          matInput
          #textArea
          appVoiceTextArea
          [htmlTextAreaElement]="textArea"
          [formControl]="notesControl"
          (ngModelChange)="captureNotesChange($event)"
          (change)="save()"
        ></textarea>
      </mat-form-field>
    </mat-card-content>
    <mat-card-actions align="end">
      <button class="button-with-spinner" mat-raised-button color="primary" [disabled]="saving" (click)="save()">
        <mat-spinner *ngIf="saving; else saveBtnText" [diameter]="20"></mat-spinner>
        <ng-template #saveBtnText>
          {{ 'COMMON.ACTION_LABELS.SAVE' | translate }}
        </ng-template>
      </button>
    </mat-card-actions>
  </ng-template>
  <ng-template #readonly>
    <mat-card-content>
      <div class="notes-content">
        <!-- Must not break this line or extra space will be added -->
        <div class="fade-in" *ngIf="notes?.length > 0" [innerHtml]="notes | linky"></div>
      </div>
      <uikit-empty-state *ngIf="!!notes === false" iconName="assignment">
        <span state-description>
          {{ 'ACCOUNT_DETAILS.NOTES.EMPTY_NOTES_DESCRIPTION' | translate }}
        </span>
      </uikit-empty-state>
    </mat-card-content>
  </ng-template>
</mat-card>
