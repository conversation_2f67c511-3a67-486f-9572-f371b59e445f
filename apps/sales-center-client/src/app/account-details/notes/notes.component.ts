import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { SubscriptionList } from '@vendasta/rx-utils';
import { BehaviorSubject, Observable, combineLatest, interval, timer } from 'rxjs';
import { first, withLatestFrom } from 'rxjs/operators';

export interface SubmitNotesEvent {
  notes: string;
  businessId: string;
}

/**
 * Editor for updating notes on an account.
 *
 * This is NOT intended to be a standalone feature component.
 * It is a sub-component of the account info page.
 */
@Component({
  selector: 'app-notes-card',
  templateUrl: './notes.component.html',
  styleUrls: ['./notes.component.scss'],
  standalone: false,
})
export class NotesComponent implements OnInit, OnDestroy {
  private readonly changedNotes$$ = new BehaviorSubject<string>('');
  changedNotes$: Observable<string> = this.changedNotes$$.asObservable();
  private readonly editMode$$ = new BehaviorSubject<boolean>(false);
  readonly editMode$: Observable<boolean> = this.editMode$$.asObservable();
  private readonly subscriptions = SubscriptionList.new();

  @Input() notes: string;
  @Input() businessId: string;
  @Input() saving: boolean;

  @Output() submitEvent = new EventEmitter<SubmitNotesEvent>();

  notesControl = new UntypedFormControl('');

  ngOnInit(): void {
    this.changedNotes$$.next(this.notes);
    this.notesControl.setValue(this.notes);
    this.subscriptions.add(
      interval(10000).pipe(withLatestFrom(this.changedNotes$, this.editMode$)),
      ([, changes, editMode]) => {
        if (changes !== this.notes && editMode) {
          this.notesControl.setValue(changes);
          this.submitEvent.emit({
            notes: changes,
            businessId: this.businessId,
          });
        }
      },
    );
  }

  captureNotesChange(change: string): void {
    this.changedNotes$$.next(change);
  }

  openEditor(): void {
    this.editMode$$.next(true);
    const closeEditorTime = 5 * 60 * 1000;
    this.subscriptions.add(timer(closeEditorTime), () => {
      this.save();
    });
  }

  save(): void {
    this.subscriptions.add(combineLatest([this.changedNotes$.pipe(first()), this.editMode$]), ([changes, editMode]) => {
      if (editMode) {
        this.notesControl.setValue(changes);
        this.submitEvent.emit({
          notes: changes,
          businessId: this.businessId,
        });
      }
    });
    this.editMode$$.next(false);
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }
}
