import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Component, Inject, OnInit } from '@angular/core';
import { AccountDetailsService } from './account-details.service';
import { TranslateService } from '@ngx-translate/core';

export interface ArchiveDialogData {
  accountGroupId: string;
}

@Component({
  selector: 'app-archive-business-dialog',
  template: `
    <h2 mat-dialog-title>{{ 'ARCHIVE_ACCOUNT_MODAL.TITLE' | translate }}</h2>
    <mat-dialog-content>
      <p>{{ 'ARCHIVE_ACCOUNT_MODAL.REASON_FOR_ARCHIVING' | translate }}:</p>
      <mat-form-field class="text-area">
        <textarea matInput matTextareaAutosize [(value)]="reason"></textarea>
      </mat-form-field>
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button mat-button color="primary" (click)="cancel()">{{ 'COMMON.ACTION_LABELS.CANCEL' | translate }}</button>
      <button mat-raised-button color="primary" (click)="savedHandler()">
        {{ 'ARCHIVE_ACCOUNT_MODAL.ARCHIVE' | translate }}
      </button>
    </mat-dialog-actions>
  `,
  styleUrls: ['archive-business-dialog.component.scss'],
  standalone: false,
})
export class ArchiveBusinessDialogComponent implements OnInit {
  accountGroupId: string;
  reason = '';

  constructor(
    public dialogRef: MatDialogRef<ArchiveBusinessDialogComponent>,
    private readonly accountDetailsService: AccountDetailsService,
    private readonly translate: TranslateService,
    @Inject(MAT_DIALOG_DATA) private readonly data: ArchiveDialogData,
  ) {}

  ngOnInit(): void {
    this.accountGroupId = this.data.accountGroupId;
    this.dialogRef.updateSize('600px');
  }

  savedHandler(): void {
    this.accountDetailsService.archiveBusiness(this.accountGroupId, this.reason);
    this.dialogRef.close();
  }

  cancel(): void {
    this.dialogRef.close();
  }
}
