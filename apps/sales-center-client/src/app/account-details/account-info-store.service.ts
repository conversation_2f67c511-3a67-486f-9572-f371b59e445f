import { Inject, Injectable } from '@angular/core';
import {
  AccountGroup,
  AccountGroupApiService,
  MarketingInfo,
  ProjectionFilter,
  SocialURLs,
} from '@galaxy/account-group';
import { ManualStartWorkStateMap, ObservableWorkStateMap } from '@vendasta/rx-utils/work-state';
import { Observable, of } from 'rxjs';
import { catchError, filter, map, share } from 'rxjs/operators';
import { AjaxBusinessApiService, BasicBusinessInfo, BusinessApiService, BusinessResponse } from '../business';
import { TaxonomyService, TaxonomyWrapperService } from '../business-category';
import { PartnerSdkWrapperService, PartnerService } from '../partner';
import { CustomerIdentifiers } from './sub-title/customer-id/customer-id.service';

export interface ViewBusiness extends BasicBusinessInfo {
  businessId: string;
  primaryNumber: string;
  phoneNumber: string;
}

export interface BusinessExternalIdentifiers {
  customerId: string;
}

@Injectable()
export class AccountInfoStoreService {
  private readonly loadState = new ObservableWorkStateMap<string, BusinessResponse>();
  private readonly notesState = new ManualStartWorkStateMap<string, string>();
  private readonly isReadState = new ManualStartWorkStateMap<string, boolean>();
  private readonly marketNamesState = new ObservableWorkStateMap<string, string>();
  private readonly marketingInfoState = new ObservableWorkStateMap<string, MarketingInfo>();

  constructor(
    @Inject(AjaxBusinessApiService) private readonly businessApi: BusinessApiService,
    @Inject(TaxonomyWrapperService) private readonly categories: TaxonomyService,
    @Inject(PartnerSdkWrapperService) private readonly partner: PartnerService,
    @Inject(AccountGroupApiService) private readonly accountGroupApi,
  ) {}

  public fetchSocialMediaUrls(accountGroupId: string): Observable<SocialURLs> {
    return this.accountGroupApi.get(accountGroupId, new ProjectionFilter({ socialUrls: true })).pipe(
      filter(Boolean),
      map((ag: AccountGroup) => ag.socialUrls),
    );
  }

  public fetchCustomerIdentifiers(accountGroupId: string): Observable<CustomerIdentifiers> {
    return this.accountGroupApi
      .get(accountGroupId, new ProjectionFilter({ accountGroupExternalIdentifiers: true }))
      .pipe(
        map(
          (ag: AccountGroup) =>
            <CustomerIdentifiers>{
              businessId: accountGroupId,
              partnerId: ag.externalIdentifiers?.partnerId,
              customerId: ag.externalIdentifiers?.customerIdentifier,
              taxIds: ag.externalIdentifiers?.taxIds,
              vCategoryIds: ag.externalIdentifiers?.vCategoryIds,
            },
        ),
      );
  }

  public fetchBusinessInfo(id: string): Observable<ViewBusiness> {
    this.loadState.startWork(id, this.businessApi.getBusiness(id));
    return this.businessInfo$(id);
  }

  businessInfo$(id: string): Observable<ViewBusiness> {
    return this.loadState.getWorkResults$(id).pipe(
      map((res) =>
        Object.assign(res, {
          primaryNumber: res.primaryNumber,
          phoneNumber: res.phoneNumbers[0],
          businessId: id,
          assigneeId: res.assignee_id,
          additionalAssigneeIds: res.additional_assignee_ids,
        }),
      ),
      catchError(() => {
        return of({}) as Observable<ViewBusiness>;
      }),
      share(),
    );
  }

  isFailed$(id: string): Observable<boolean> {
    return this.loadState.isSuccess$(id).pipe(map((v) => !v));
  }

  isLoading$(id: string): Observable<boolean> {
    return this.loadState.isLoading$(id);
  }

  updateNotes(notes: string, businessId: string): void {
    this.notesState.startWork(businessId, (success, fail) => {
      this.businessApi.updateNotes(businessId, notes).subscribe(() => {
        success(notes);
      }, fail);
    });
  }

  updateIsRead(accountGroupId: string, isRead: boolean): void {
    this.isReadState.startWork(accountGroupId, (success, fail) => {
      this.businessApi.updateIsRead(accountGroupId, isRead).subscribe(() => {
        success(isRead);
      }, fail);
    });
  }

  notes$(bizId: string): Observable<string> {
    return this.notesState.getWorkResults$(bizId);
  }

  isNotesSaving$(bizId: string): Observable<boolean> {
    return this.notesState.isLoading$(bizId);
  }

  isNotesSaveSuccess$(bizId: string): Observable<boolean> {
    return this.notesState.isSuccess$(bizId);
  }

  isUpdateReadSuccess$(accountGroupId: string): Observable<boolean> {
    return this.isReadState.isSuccess$(accountGroupId);
  }

  fetchMarketName$(partnerId: string, marketId: string): Observable<string> {
    const workId = this.buildMarketNameWorkId(partnerId, marketId);
    const work = this.partner.getMarketName$(partnerId, marketId);
    this.marketNamesState.startWork(workId, work, marketId);
    return this.marketNamesState.getWorkResults$(workId);
  }

  isMarketNameLoading$(partnerId: string, marketId: string): Observable<boolean> {
    const workId = this.buildMarketNameWorkId(partnerId, marketId);
    return this.marketNamesState.isLoading$(workId);
  }

  isMarketNameLoadFailed$(partnerId: string, marketId: string): Observable<boolean> {
    const workId = this.buildMarketNameWorkId(partnerId, marketId);
    return this.marketNamesState.isSuccess$(workId).pipe(map((v) => !v));
  }

  private buildMarketNameWorkId(partnerId: string, marketId: string): string {
    return partnerId + marketId;
  }

  fetchMarketingInfo$(accountGroupId: string): Observable<MarketingInfo> {
    const workId = accountGroupId;
    const work = this.accountGroupApi
      .get(accountGroupId, new ProjectionFilter({ marketingInfo: true }))
      .pipe(map((res: AccountGroup) => res?.marketingInfo));
    this.marketingInfoState.startWork(workId, work, undefined);
    return this.marketingInfoState.getWorkResults$(workId);
  }
}
