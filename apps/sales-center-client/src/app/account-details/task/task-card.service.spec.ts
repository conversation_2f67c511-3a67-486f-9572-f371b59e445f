import { Result } from '@vendasta/sales';
import { Persona, SearchTaskResponseInterface, Status, Task } from '@vendasta/task';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { TestScheduler } from 'rxjs/testing';
import { TaskCardService } from './task-card.service';
import { SalesTasksSDKMock } from './task-card.service.spec.mocks';
import { mockTaskSearchResponse, mockTasks } from './task.service.mock';

let sched: TestScheduler;

const anything = (<any>expect).anything;

class MockTaskSdk {
  happy: boolean;
  hasResults: boolean;
  errorSequence: boolean[] = [false];

  constructor(happy = true, hasResults = true) {
    this.happy = happy;
    this.hasResults = hasResults;
  }

  addMultipleAssignees(): Observable<number> {
    if (this.happy) {
      return sched.createColdObservable('-x', { x: 1000 });
    } else {
      return sched.createColdObservable('-#', null, Error('oh no, an error!'));
    }
  }

  removeMultipleAssignees(): Observable<number> {
    if (this.happy) {
      return sched.createColdObservable('-x', { x: 1000 });
    } else {
      return sched.createColdObservable('-#', null, Error('oh no, an error!'));
    }
  }

  search(): Observable<SearchTaskResponseInterface> {
    if (this.hasResults) {
      return sched.createColdObservable('-x', { x: mockTaskSearchResponse }) as Observable<any>;
    } else {
      return sched.createColdObservable('-x', { x: { nextCursor: '25' } });
    }
  }

  setStatus(): Observable<number> {
    if (this.happy) {
      return sched.createColdObservable('-x', { x: 1000 });
    } else {
      return sched.createColdObservable('-#', null, Error('oh no, an error!'));
    }
  }

  setTitle(): Observable<number> {
    if (this.happy) {
      return sched.createColdObservable('-x', { x: 1000 });
    } else {
      return sched.createColdObservable('-#', null, Error('oh no, an error!'));
    }
  }

  setDueDate(): Observable<number> {
    if (this.happy) {
      return sched.createColdObservable('-x', { x: 1000 });
    } else {
      return sched.createColdObservable('-#', null, Error('oh no, an error!'));
    }
  }

  generate(): Observable<number> {
    const marbles = this.errorSequence.map((e) => (e ? '---#' : '---x')).join('');
    return sched.createColdObservable(marbles, { x: 1 }, new Error('api error'));
  }
}

class MockTranslateService {
  instant(): string | any {
    return {};
  }
}

describe('taskCardService', () => {
  let service: TaskCardService;
  let taskSdkMock: MockTaskSdk;
  let salesTasksSdkMock: SalesTasksSDKMock;
  const mockAppService = {
    pushNewMessage: jest.fn(),
  };

  beforeEach(() => {
    sched = new TestScheduler((a, b) => expect(a).toEqual(b));
    taskSdkMock = new MockTaskSdk();
    salesTasksSdkMock = new SalesTasksSDKMock();
  });
  afterEach(() => {
    sched.flush();
  });

  describe('taskEvents$$', () => {
    it('should initially be empty upon construction', () => {
      taskSdkMock = new MockTaskSdk();
      service = new TaskCardService(taskSdkMock as any, salesTasksSdkMock, null, null);
      sched.expectObservable(service.taskEvents$$).toBe('', {});
    });
  });

  describe('loadTasks', () => {
    it('should add a loadTasks event to taskEvents$$', () => {
      taskSdkMock = new MockTaskSdk(true);
      service = new TaskCardService(taskSdkMock as any, salesTasksSdkMock, null, null);

      service.loadTasks('AG-GX6GDC7K55', 'VUNI');
      sched.expectObservable(service.taskEvents$$).toBe('-x', { x: { eventType: 'loadTasks', tasks: mockTasks } });
    });

    it('should load the tasks for the current accountGroup', () => {
      taskSdkMock = new MockTaskSdk();
      service = new TaskCardService(taskSdkMock as any, salesTasksSdkMock, null, null);

      service.loadTasks('AG-GX6GDC7K55', 'VUNI');
      sched.expectObservable(service.tasks$).toBe('-x', { x: mockTasks });
    });
    it('should load the all status type tasks for the accountGroup', () => {
      taskSdkMock = {
        errorSequence: [],
        happy: false,
        hasResults: false,
        addMultipleAssignees(): Observable<number> {
          return undefined;
        },
        generate(): Observable<number> {
          return undefined;
        },
        removeMultipleAssignees(): Observable<number> {
          return undefined;
        },
        setDueDate(): Observable<number> {
          return undefined;
        },
        setStatus(): Observable<number> {
          return undefined;
        },
        setTitle(): Observable<number> {
          return undefined;
        },
        search: jest.fn(() => of({})),
      };
      service = new TaskCardService(taskSdkMock as any, salesTasksSdkMock, null, null);
      service.loadTasks('AG-GX6GDC7K55', 'VUNI');
      expect(taskSdkMock.search).toHaveBeenCalledWith({
        access: [Persona.salesPerson],
        namespace: 'partner/VUNI/account-group/AG-GX6GDC7K55',
        pageSize: 100,
        status: [Status.InProgress, Status.Completed, Status.Open, Status.WaitingOnCustomer],
      });
    });

    it('should not load anything on empty response', () => {
      taskSdkMock = new MockTaskSdk(true, false);
      service = new TaskCardService(taskSdkMock as any, salesTasksSdkMock, null, null);

      service.loadTasks('AG-GX6GDC7K55', 'VUNI');
      sched.expectObservable(service.tasks$).toBe('-x', { x: [] });
    });
  });

  describe('changeTaskStatus', () => {
    it('should return true if succeeded in changing status', () => {
      taskSdkMock = new MockTaskSdk();
      service = new TaskCardService(taskSdkMock as any, salesTasksSdkMock, null, null);

      const result$ = service.changeTaskStatus('TK-123', 'partner/VUNI/account-group/AG-123', Status.Completed);
      sched.expectObservable(result$).toBe('-x', { x: true });
    });
    it('should return false if it throws an error', () => {
      taskSdkMock = new MockTaskSdk(false);
      service = new TaskCardService(taskSdkMock as any, salesTasksSdkMock, null, null);

      const result$ = service.changeTaskStatus('TK-123', 'partner/VUNI/account-group/AG-123', Status.Completed);
      sched.expectObservable(result$).toBe('-(x|)', { x: false });
    });
    it('should update the task in the tasks$ stream', () => {
      taskSdkMock = new MockTaskSdk();
      service = new TaskCardService(taskSdkMock as any, salesTasksSdkMock, null, null);
      service.loadTasks('AG-GX6GDC7K55', 'VUNI');
      service
        .changeTaskStatus(
          'TK-6708fdb58f8c46eeab1eeaeeeba0a6ed',
          'partner/VUNI/account-group/AG-GX6GDC7K55',
          Status.InProgress,
        )
        .subscribe();

      const statuses$ = service.tasks$.pipe(map((tasks) => tasks.map((task) => task.status)));
      sched.expectObservable(statuses$).toBe('-(xy)', {
        x: [Status.Completed, Status.InProgress],
        y: [Status.InProgress, Status.InProgress],
      });
    });
  });

  describe('editTask', () => {
    beforeEach(() => {
      taskSdkMock = new MockTaskSdk();
      service = new TaskCardService(taskSdkMock as any, salesTasksSdkMock, null, null);
      service.loadTasks('AG-123', 'PARTNER');
    });

    it('should edit the date of the task', () => {
      const now = new Date();
      sched.schedule(
        () =>
          service.editTask(
            'TK-e934332a747f4ca9bc5ecd6cd740bb2e',
            'partner/VUNI/account-group/AG-123',
            now,
            '',
            null,
            null,
          ),
        20,
      );
      const dates$ = service.tasks$.pipe(map((tasks: Task[]) => tasks.map((task) => task.dueDate)));
      sched.expectObservable(dates$).toBe('-x-y', {
        x: anything(),
        y: [new Date('2018-10-31T06:00:00.000Z'), now],
      });
    });

    it('should edit the name of the task if a new name is passed in', () => {
      const newTitle = 'A new Title😺';
      sched.schedule(
        () =>
          service.editTask(
            'TK-e934332a747f4ca9bc5ecd6cd740bb2e',
            'partner/VUNI/account-group/AG-123',
            null,
            newTitle,
            null,
            null,
          ),
        20,
      );
      const title$ = service.tasks$.pipe(map((tasks: Task[]) => tasks.map((task) => task.title)));
      sched.expectObservable(title$).toBe('-x-y', {
        x: anything(),
        y: ['This is another task', newTitle],
      });
    });

    it('should not edit the date of the task if we are just updating the title', () => {
      const newTitle = 'A new Title😺';
      sched.schedule(
        () =>
          service.editTask(
            'TK-e934332a747f4ca9bc5ecd6cd740bb2e',
            'partner/VUNI/account-group/AG-123',
            null,
            newTitle,
            null,
            null,
          ),
        20,
      );
      const title$ = service.tasks$.pipe(map((tasks: Task[]) => tasks.map((task) => task.title)));
      const dates$ = service.tasks$.pipe(map((tasks: Task[]) => tasks.map((task) => task.dueDate)));
      sched.expectObservable(title$).toBe('-x-y', {
        x: anything(),
        y: ['This is another task', newTitle],
      });
      sched.expectObservable(dates$).toBe('-x-x', {
        x: [new Date('2018-10-31T06:00:00.000Z'), new Date('2018-09-30T06:00:00.000Z')],
      });
    });
    it('should not edit the assignee of the task if we are just updating the title', () => {
      const newTitle = 'A new Title😺';
      sched.schedule(
        () =>
          service.editTask(
            'TK-e934332a747f4ca9bc5ecd6cd740bb2e',
            'partner/VUNI/account-group/AG-123',
            null,
            newTitle,
            null,
            null,
          ),
        20,
      );
      const title$ = service.tasks$.pipe(map((tasks: Task[]) => tasks.map((task) => task.title)));
      const assignees$ = service.tasks$.pipe(map((tasks: Task[]) => tasks.map((task) => task.assignees)));
      sched.expectObservable(title$).toBe('-x-y', {
        x: anything(),
        y: ['This is another task', newTitle],
      });
      sched.expectObservable(assignees$).toBe('-x-x', {
        x: [
          ['U-TESTASSIGNEE1', 'U-TESTASSIGNEE2'],
          ['U-TESTASSIGNEE1', 'U-TESTASSIGNEE2'],
        ],
      });
    });

    it('should edit the assignee of the task if a new assignee is passed in', () => {
      const newAssignee = 'U-TESTUSERID';
      const taskId = 'TK-e934332a747f4ca9bc5ecd6cd740bb2e';
      const oldAssignee = 'U-OLDASSIGNEE';

      sched.schedule(
        () => service.editTask(taskId, 'partner/VUNI/account-group/AG-123', null, null, [newAssignee], [oldAssignee]),
        20,
      );
      const assignees$ = service.tasks$.pipe(
        map((tasks: Task[]) => tasks.find((t) => t.identity.taskId === taskId).assignees),
      );
      sched.expectObservable(assignees$).toBe('-x--y', {
        x: ['U-TESTASSIGNEE1', 'U-TESTASSIGNEE2'],
        y: [newAssignee],
      });
    });
  });

  describe('addTask', () => {
    it('should add a new task to tasks$', () => {
      taskSdkMock = new MockTaskSdk();
      service = new TaskCardService(taskSdkMock as any, salesTasksSdkMock, null, null);
      service.loadTasks('AG-123', 'PARTNER');

      const now = new Date();
      sched.schedule(
        () =>
          service.addTask('TK-123', 'A shiny new task✨', now, '/partner/VUNI/account-group/AG-123', null, [
            'U-ASSIGNEE',
          ]),
        20,
      );

      const taskIds$ = service.tasks$.pipe(map((tasks: Task[]) => tasks.map((task: Task) => task.identity.taskId)));
      sched.expectObservable(taskIds$).toBe('-xy', {
        x: anything(),
        y: ['TK-6708fdb58f8c46eeab1eeaeeeba0a6ed', 'TK-e934332a747f4ca9bc5ecd6cd740bb2e', 'TK-123'],
      });
    });
  });

  describe('createTask', () => {
    beforeEach(() => {
      salesTasksSdkMock.createSalesTaskResult = sched.createColdObservable('---x', { x: {} as Result });
      taskSdkMock.hasResults = false;
      service = new TaskCardService(taskSdkMock as any, salesTasksSdkMock, null, null);
      service.loadTasks('AG-123', 'PARTNER');
    });

    afterEach(() => {
      sched.flush();
    });

    it('should create a new task', () => {
      const title = 'A shiny new task✨';
      const now = new Date();
      const assignees = ['U-assignee'];
      const success$ = service.createTask(title, now, assignees, 'PARTNER', 'MARKET', 'AG-123', 'UID-123');

      const title$ = service.tasks$.pipe(map((tasks: Task[]) => tasks.map((task) => task.title)));
      const date$ = service.tasks$.pipe(map((tasks: Task[]) => tasks.map((task) => task.dueDate)));
      const assignees$ = service.tasks$.pipe(map((tasks: Task[]) => tasks.map((task) => task.assignees)));

      sched.expectObservable(success$).toBe('---(x|)', { x: true });
      sched.expectObservable(title$).toBe('-x-y', { x: [], y: [title] });
      sched.expectObservable(date$).toBe('-x-y', { x: [], y: [now] });
      sched.expectObservable(assignees$).toBe('-x-y', { x: [], y: [assignees] });
    });
  });

  describe('addNewTask TaskSdk with error', () => {
    beforeEach(() => {
      taskSdkMock.hasResults = false;
      const translateMock = new MockTranslateService();
      salesTasksSdkMock.createSalesTaskResult = sched.createColdObservable('---#', {}, new Error('error from test'));
      service = new TaskCardService(taskSdkMock as any, salesTasksSdkMock, mockAppService as any, translateMock as any);
      service.loadTasks('AG-123', 'PARTNER');
    });

    afterEach(() => {
      sched.flush();
    });

    it('should not create a new task if sdk errors', () => {
      const title = 'A shiny new task✨';
      const now = new Date();
      taskSdkMock.errorSequence = [true];

      const success$ = service.createTask(title, now, [], 'PARTNER', 'MARKET', 'AG-123', 'UID-123');

      sched.expectObservable(success$).toBe('---(x|)', { x: false });
      sched.expectObservable(service.tasks$).toBe('-x', { x: [] });
    });
  });
});
