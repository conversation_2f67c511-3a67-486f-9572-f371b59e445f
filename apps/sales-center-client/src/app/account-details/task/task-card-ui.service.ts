import { Injectable } from '@angular/core';
import { Status, Task } from '@vendasta/task';
import { BehaviorSubject, combineLatest, Observable } from 'rxjs';
import { filter, map, mapTo, scan, startWith } from 'rxjs/operators';

import { TaskUiInterface } from '../../tasks';
import { TaskCardService } from './task-card.service';
import { merge } from 'rxjs';

@Injectable()
export class TaskCardUiService {
  tasks$: Observable<TaskUiInterface[]>;
  inProgressTasks$: Observable<TaskUiInterface[]>;
  isMoreInProgress$: Observable<boolean>;
  completedTasks$: Observable<TaskUiInterface[]>;
  isMoreCompleted$: Observable<boolean>;

  private readonly inProgressLimit$: Observable<number>;
  private readonly loadNMoreInProgress$$: BehaviorSubject<number> = new BehaviorSubject<number>(5);
  private readonly completedLimit$: Observable<number>;
  private readonly loadNMoreCompleted$$: BehaviorSubject<number> = new BehaviorSubject<number>(5);
  readonly isTasksLoadFailed$: Observable<boolean>;
  readonly isTasksLoaded$: Observable<boolean>;

  constructor(private readonly taskCardService: TaskCardService) {
    this.tasks$ = this.taskCardService.tasks$.pipe(
      map((tasks: Task[]) => this.convertApiTaskToUiTask(tasks)),
      map((tasks: TaskUiInterface[]) => {
        return tasks.sort((taskA, taskB) => {
          return TaskCardUiService.compareDate(taskA.dueDate, taskB.dueDate);
        });
      }),
    );
    this.completedLimit$ = this.loadNMoreCompleted$$.pipe(scan((limit, nMore) => limit + nMore, 0));

    const allCompletedTasks$ = this.tasks$.pipe(
      map((tasks) => tasks.filter((task) => task.status === Status.Completed)),
    );
    this.completedTasks$ = combineLatest([allCompletedTasks$, this.completedLimit$]).pipe(
      map(([tasks, completedLimit]: [TaskUiInterface[], number]) => tasks.slice(0, completedLimit)),
    );
    this.isMoreCompleted$ = combineLatest([allCompletedTasks$, this.completedLimit$]).pipe(
      map(([tasks, completedLimit]: [TaskUiInterface[], number]) => tasks.length > completedLimit),
    );

    this.inProgressLimit$ = this.loadNMoreInProgress$$.pipe(scan((limit, nMore) => limit + nMore, 0));

    const allInProgressTasks$ = this.tasks$.pipe(
      map((tasks) =>
        tasks.filter((task) => {
          return (
            task.status === Status.InProgress || task.status === Status.Open || task.status === Status.WaitingOnCustomer
          );
        }),
      ),
    );
    this.inProgressTasks$ = combineLatest([allInProgressTasks$, this.inProgressLimit$]).pipe(
      map(([tasks, inProgressLimit]: [TaskUiInterface[], number]) => tasks.slice(0, inProgressLimit)),
    );
    this.isMoreInProgress$ = combineLatest([allInProgressTasks$, this.inProgressLimit$]).pipe(
      map(([tasks, inProgressLimit]: [TaskUiInterface[], number]) => tasks.length > inProgressLimit),
    );

    this.isTasksLoadFailed$ = this.taskCardService.isTaskLoadErrored$;
    this.isTasksLoaded$ = merge(
      this.tasks$.pipe(mapTo(true)),
      this.isTasksLoadFailed$.pipe(mapTo(true)),
      this.inProgressTasks$.pipe(filter(Boolean)),
      this.isMoreInProgress$.pipe(filter(Boolean)),
    ).pipe(startWith(false)) as Observable<boolean>;
  }

  static compareDate(date1: Date, date2: Date): number {
    const d1 = new Date(date1);
    const d2 = new Date(date2);

    const same = d1.getTime() === d2.getTime();
    if (same) {
      return 0;
    }

    if (d1 > d2) {
      return 1;
    }

    if (d1 < d2) {
      return -1;
    }
  }

  convertStringStatusToTaskStatus(status: string): Status {
    switch (status) {
      case 'Open':
        return Status.Open;
      case 'In Progress':
        return Status.InProgress;
      case 'Waiting on Customer':
        return Status.WaitingOnCustomer;
      case 'Completed':
        return Status.Completed;
      default: {
        console.error('Unable to convert ' + status + ' to a real status');
        return Status.Open;
      }
    }
  }

  private convertApiTaskToUiTask(tasks: Task[]): TaskUiInterface[] {
    return tasks.map((task) => {
      return <TaskUiInterface>{
        title: task.title,
        dueDate: task.dueDate,
        status: this.convertStringStatusToTaskStatus(task.status),
        assigneeIds: task.assignees,
        taskId: task.identity.taskId,
        overdue: this.taskOverdue(task),
        namespace: task.identity.namespace,
      };
    });
  }

  loadTasks(accountGroupId: string, partnerId: string): void {
    this.taskCardService.loadTasks(accountGroupId, partnerId);
  }

  loadMore(status: Status): void {
    if (status === Status.InProgress) {
      this.loadNMoreInProgress$$.next(10);
    } else if (status === Status.Completed) {
      this.loadNMoreCompleted$$.next(10);
    }
  }

  taskOverdue(task: Task): boolean {
    if (!task.dueDate) return false;

    const oneDayMilliSecs = 1000 * 60 * 60 * 24;
    const currentDate = new Date().getTime();
    const diffDates = task.dueDate.getTime() - currentDate;
    return Math.round(diffDates / oneDayMilliSecs) < 0 && task.status !== Status.Completed;
  }
}
