import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SubscriptionList } from '@vendasta/rx-utils';
import { Status, TaskSdkService } from '@vendasta/task';
import { BehaviorSubject, EMPTY, Observable, combineLatest } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { TaskSubmitEvent, TaskUiInterface } from '../../tasks';
import { TaskCardUiService } from './task-card-ui.service';
import { TaskCardService } from './task-card.service';

@Component({
  selector: 'app-task-card',
  templateUrl: 'task-card.component.html',
  styleUrls: ['./task-card.component.scss'],
  standalone: false,
})
export class TaskCardComponent implements OnInit, OnDestroy {
  @Input() partnerId: string;
  @Input() marketId: string;
  @Input() accountGroupId: string;
  @Input() salespersonId: string;

  tasks$: Observable<TaskUiInterface[]>;
  readonly pendingTasks$: Observable<TaskUiInterface[]>;
  readonly completedTasks$: Observable<TaskUiInterface[]>;
  inProgressMoreThanTen$: Observable<boolean>;
  completedMoreThanTen$: Observable<boolean>;
  readonly isTasksLoadFailed$: Observable<boolean>;
  readonly isTasksEmpty$: Observable<boolean>;
  readonly isTasksLoaded$: Observable<boolean>;
  private readonly visibleTaskView$$ = new BehaviorSubject<string>('pending');
  readonly visibleTaskView$ = this.visibleTaskView$$.asObservable();
  readonly availableStatuses = Status;

  private readonly subscriptions = SubscriptionList.new();

  emptyStateTxt$: Observable<string>;

  editingNewTask = false;
  creatingNewTask = false;

  constructor(
    private readonly taskCardService: TaskCardService,
    private readonly taskCardUiService: TaskCardUiService,
    private readonly translate: TranslateService,
  ) {
    this.tasks$ = this.taskCardUiService.tasks$;
    this.pendingTasks$ = this.taskCardUiService.inProgressTasks$;
    this.completedTasks$ = this.taskCardUiService.completedTasks$;
    this.inProgressMoreThanTen$ = this.taskCardUiService.isMoreInProgress$;
    this.completedMoreThanTen$ = this.taskCardUiService.isMoreCompleted$;
    this.isTasksLoaded$ = this.taskCardUiService.isTasksLoaded$;
    this.isTasksLoadFailed$ = this.taskCardUiService.isTasksLoadFailed$;
    this.isTasksEmpty$ = this.tasks$.pipe(map((ts) => ts.length === 0));
  }

  ngOnInit(): void {
    this.taskCardUiService.loadTasks(this.accountGroupId, this.partnerId);
    this.emptyStateTxt$ = this.translatedEmptyStateMessage();
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }

  setTaskView(value: string): void {
    this.visibleTaskView$$.next(value);
  }

  private translatedEmptyStateMessage(): Observable<string> {
    const activityTxt$ = this.translate.stream('TASKS.SALES_ACTIVITY');
    const followUpTxt$ = this.translate.stream('TASKS.FOLLOW_UP_REQUIRED');
    const createTaskTxt$ = combineLatest([activityTxt$, followUpTxt$]).pipe(
      switchMap(([activity, followUp]) =>
        this.translate.stream('TASKS.ADD_TASKS_MESSAGE', { sales_activity: activity, follow_up: followUp }),
      ),
    );
    const noTasksTxt$ = this.translate.stream('TASKS.ZERO_TASKS_MESSAGE');

    return combineLatest([noTasksTxt$, createTaskTxt$]).pipe(
      map(([activity, createTask]) => [activity, createTask].join(' ')),
    );
  }

  statusChanged(event$: { status: Status; task: TaskUiInterface }): void {
    this.taskCardService.taskStatusChanged(event$.task.taskId, event$.status);
  }

  addTask(task: TaskSubmitEvent): void {
    this.creatingNewTask = true;
    this.subscriptions.add(
      this.taskCardService
        .createTask(
          task.title,
          task.dueDate,
          task.assigneeIds,
          this.partnerId,
          this.marketId,
          this.accountGroupId,
          this.salespersonId,
        )
        .pipe(
          catchError(() => {
            this.creatingNewTask = true;
            return EMPTY;
          }),
          tap(() => (this.creatingNewTask = false)),
        ),
      (success) => (this.editingNewTask = !success),
    );
  }

  cancel(): void {
    this.editingNewTask = false;
  }

  editTask(event$: {
    taskId: string;
    newDueDate: Date;
    newTitle: string;
    newAssigneeIds: string[];
    oldAssigneeIds: string[];
  }): void {
    this.taskCardService.editTask(
      event$.taskId,
      TaskSdkService.buildAccountGroupNamespace(this.partnerId, this.accountGroupId),
      event$.newDueDate,
      event$.newTitle,
      event$.newAssigneeIds,
      event$.oldAssigneeIds,
    );
  }

  viewMoreClicked(status: Status): void {
    // TODO: translate task statuses from task sdk
    this.taskCardUiService.loadMore(status);
  }

  openTaskEditor(): void {
    this.editingNewTask = true;
  }

  retryLoadTasks(): void {
    this.taskCardUiService.loadTasks(this.accountGroupId, this.partnerId);
  }
}
