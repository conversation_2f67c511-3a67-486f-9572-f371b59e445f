/* tslint:disable max-file-line-count */

import { Task } from '@vendasta/task';

export const mockTaskSearchResponse = {
  tasks: [
    {
      version: '1000',
      identity: {
        namespace: 'partner/VUNI/account-group/AG-GX6GDC7K55',
        parentPath: '/',
        taskId: 'TK-6708fdb58f8c46eeab1eeaeeeba0a6ed',
      },
      title: 'This is another task',
      status: 'Completed',
      dueDate: new Date('2018-10-31T06:00:00Z'),
      created: '2018-10-01T21:46:08.047533564Z',
      modified: '2018-10-01T21:46:08.047533564Z',
      assignees: ['U-TESTASSIGNEE1', 'U-TESTASSIGNEE2'],
      metadata: {
        keyValues: [
          {
            values: [
              {
                stringValue: 'VUNI',
                type: 'string',
              },
            ],
            key: 'partner',
          },
          {
            values: [
              {
                stringValue: 'AG-GX6GDC7K55',
                type: 'string',
              },
            ],
            key: 'account-group',
          },
        ],
      },
    },
    {
      version: '1000',
      identity: {
        namespace: 'partner/VUNI/account-group/AG-GX6GDC7K55',
        parentPath: '/',
        taskId: 'TK-e934332a747f4ca9bc5ecd6cd740bb2e',
      },
      title: 'This is a task',
      status: 'In Progress',
      dueDate: new Date('2018-09-30T06:00:00Z'),
      created: '2018-10-01T21:46:08.052844636Z',
      modified: '2018-10-01T21:46:08.052844636Z',
      assignees: ['U-TESTASSIGNEE1', 'U-TESTASSIGNEE2'],
      metadata: {
        keyValues: [
          {
            values: [
              {
                stringValue: 'VUNI',
                type: 'string',
              },
            ],
            key: 'partner',
          },
          {
            values: [
              {
                stringValue: 'AG-GX6GDC7K55',
                type: 'string',
              },
            ],
            key: 'account-group',
          },
        ],
      },
    },
  ],
  nextCursor: '20',
  hits: '2',
};

const mockFatTasksSearchResponse = {
  tasks: [
    {
      version: '7000',
      identity: {
        namespace: 'partner/VUNI/account-group/AG-6CHBZWSR68',
        parentPath: '/',
        taskId: 'TK-dacbc70ab30a4d6f9d012cf6ad69fa5c',
      },
      title: 'four',
      status: 'In Progress',
      dueDate: '2018-10-22T06:00:00Z',
      assignees: ['UID-f4130b29-e22d-4034-bc7f-df54417293fa'],
      history: [
        {
          date: '2018-10-22T20:21:53.377454765Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Title changed from four to five',
        },
        {
          date: '2018-10-22T20:21:53.445559043Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Due Date changed from 2018-10-22 to 2018-10-22',
        },
        {
          date: '2018-10-22T20:22:05.338440376Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Title changed from five to six',
        },
        {
          date: '2018-10-22T20:22:05.339661967Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Due Date changed from 2018-10-22 to 2018-10-22',
        },
        {
          date: '2018-10-22T20:23:26.970954982Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Title changed from six to four',
        },
        {
          date: '2018-10-22T20:23:26.970678622Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Due Date changed from 2018-10-22 to 2018-10-22',
        },
      ],
      created: '2018-10-22T20:21:45.006017293Z',
      modified: '2018-10-22T20:23:26.970678622Z',
      metadata: {
        keyValues: [
          {
            values: [{ stringValue: 'VUNI', type: 'string' }],
            key: 'partner',
          },
          {
            values: [{ stringValue: 'AG-6CHBZWSR68', type: 'string' }],
            key: 'account-group',
          },
          { values: [{ stringValue: 'sales_person', type: 'string' }], key: 'access' },
        ],
      },
    },
    {
      version: '4000',
      identity: {
        namespace: 'partner/VUNI/account-group/AG-6CHBZWSR68',
        parentPath: '/',
        taskId: 'TK-241181f8a0bb4a5aa6bb6c5379da0134',
      },
      title: 'seven',
      status: 'In Progress',
      dueDate: '2018-10-22T20:28:30.936Z',
      assignees: ['UID-f4130b29-e22d-4034-bc7f-df54417293fa'],
      history: [
        {
          date: '2018-10-22T20:28:44.508713014Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
        {
          date: '2018-10-22T20:28:50.395433799Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from Completed to Completed',
        },
        {
          date: '2018-10-22T20:29:11.181858859Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from Completed to In Progress',
        },
      ],
      created: '2018-10-22T20:28:38.472773123Z',
      modified: '2018-10-22T20:29:11.181858859Z',
      metadata: {
        keyValues: [
          {
            values: [{ stringValue: 'VUNI', type: 'string' }],
            key: 'partner',
          },
          {
            values: [{ stringValue: 'AG-6CHBZWSR68', type: 'string' }],
            key: 'account-group',
          },
          { values: [{ stringValue: 'sales_person', type: 'string' }], key: 'access' },
        ],
      },
    },
    {
      version: '1000',
      identity: {
        namespace: 'partner/VUNI/account-group/AG-6CHBZWSR68',
        parentPath: '/',
        taskId: 'TK-72ef4f61160d46ddaad7431638b3d85e',
      },
      title: 'cyndaquil',
      status: 'In Progress',
      dueDate: '2018-10-22T21:39:19.112Z',
      assignees: ['UID-f4130b29-e22d-4034-bc7f-df54417293fa'],
      created: '2018-10-22T21:39:59.922310074Z',
      modified: '2018-10-22T21:39:59.922310074Z',
      metadata: {
        keyValues: [
          {
            values: [{ stringValue: 'VUNI', type: 'string' }],
            key: 'partner',
          },
          {
            values: [{ stringValue: 'AG-6CHBZWSR68', type: 'string' }],
            key: 'account-group',
          },
          { values: [{ stringValue: 'sales_person', type: 'string' }], key: 'access' },
        ],
      },
    },
    {
      version: '1000',
      identity: {
        namespace: 'partner/VUNI/account-group/AG-6CHBZWSR68',
        parentPath: '/',
        taskId: 'TK-da3a01d3a30c475880d554377ff3f140',
      },
      title: 'bulbasaur',
      status: 'In Progress',
      dueDate: '2018-10-22T21:39:19.112Z',
      assignees: ['UID-f4130b29-e22d-4034-bc7f-df54417293fa'],
      created: '2018-10-22T21:39:59.925005267Z',
      modified: '2018-10-22T21:39:59.925005267Z',
      metadata: {
        keyValues: [
          {
            values: [{ stringValue: 'VUNI', type: 'string' }],
            key: 'partner',
          },
          {
            values: [{ stringValue: 'AG-6CHBZWSR68', type: 'string' }],
            key: 'account-group',
          },
          { values: [{ stringValue: 'sales_person', type: 'string' }], key: 'access' },
        ],
      },
    },
    {
      version: '1000',
      identity: {
        namespace: 'partner/VUNI/account-group/AG-6CHBZWSR68',
        parentPath: '/',
        taskId: 'TK-27da3221f79444128957cf0c5c1510e5',
      },
      title: 'tepig',
      status: 'In Progress',
      dueDate: '2018-10-22T21:39:19.112Z',
      assignees: ['UID-f4130b29-e22d-4034-bc7f-df54417293fa'],
      created: '2018-10-22T21:39:59.999458927Z',
      modified: '2018-10-22T21:39:59.999458927Z',
      metadata: {
        keyValues: [
          {
            values: [{ stringValue: 'VUNI', type: 'string' }],
            key: 'partner',
          },
          {
            values: [{ stringValue: 'AG-6CHBZWSR68', type: 'string' }],
            key: 'account-group',
          },
          { values: [{ stringValue: 'sales_person', type: 'string' }], key: 'access' },
        ],
      },
    },
    {
      version: '1000',
      identity: {
        namespace: 'partner/VUNI/account-group/AG-6CHBZWSR68',
        parentPath: '/',
        taskId: 'TK-b68e908ebb4541559de78bdb4249afbf',
      },
      title: 'squirtle',
      status: 'In Progress',
      dueDate: '2018-10-22T21:39:19.112Z',
      assignees: ['UID-f4130b29-e22d-4034-bc7f-df54417293fa'],
      created: '2018-10-22T21:39:59.912665978Z',
      modified: '2018-10-22T21:39:59.912665978Z',
      metadata: {
        keyValues: [
          {
            values: [{ stringValue: 'VUNI', type: 'string' }],
            key: 'partner',
          },
          {
            values: [{ stringValue: 'AG-6CHBZWSR68', type: 'string' }],
            key: 'account-group',
          },
          { values: [{ stringValue: 'sales_person', type: 'string' }], key: 'access' },
        ],
      },
    },
    {
      version: '1000',
      identity: {
        namespace: 'partner/VUNI/account-group/AG-6CHBZWSR68',
        parentPath: '/',
        taskId: 'TK-e271b8c0542b426086cf377aac8f52c0',
      },
      title: 'charmander',
      status: 'In Progress',
      dueDate: '2018-10-22T21:39:19.112Z',
      assignees: ['UID-f4130b29-e22d-4034-bc7f-df54417293fa'],
      created: '2018-10-22T21:39:59.903530639Z',
      modified: '2018-10-22T21:39:59.903530639Z',
      metadata: {
        keyValues: [
          {
            values: [{ stringValue: 'VUNI', type: 'string' }],
            key: 'partner',
          },
          {
            values: [{ stringValue: 'AG-6CHBZWSR68', type: 'string' }],
            key: 'account-group',
          },
          { values: [{ stringValue: 'sales_person', type: 'string' }], key: 'access' },
        ],
      },
    },
    {
      version: '1000',
      identity: {
        namespace: 'partner/VUNI/account-group/AG-6CHBZWSR68',
        parentPath: '/',
        taskId: 'TK-ee48ba8009b94167894675a78272c061',
      },
      title: 'chickorita',
      status: 'In Progress',
      dueDate: '2018-10-22T21:39:19.112Z',
      assignees: ['UID-f4130b29-e22d-4034-bc7f-df54417293fa'],
      created: '2018-10-22T21:39:59.988084819Z',
      modified: '2018-10-22T21:39:59.988084819Z',
      metadata: {
        keyValues: [
          {
            values: [{ stringValue: 'VUNI', type: 'string' }],
            key: 'partner',
          },
          {
            values: [{ stringValue: 'AG-6CHBZWSR68', type: 'string' }],
            key: 'account-group',
          },
          { values: [{ stringValue: 'sales_person', type: 'string' }], key: 'access' },
        ],
      },
    },
    {
      version: '1000',
      identity: {
        namespace: 'partner/VUNI/account-group/AG-6CHBZWSR68',
        parentPath: '/',
        taskId: 'TK-3398275316b34909a6db001cfbc768dd',
      },
      title: 'five',
      status: 'In Progress',
      dueDate: '2018-10-22T20:23:56.296Z',
      assignees: ['UID-f4130b29-e22d-4034-bc7f-df54417293fa'],
      created: '2018-10-22T20:24:01.747108180Z',
      modified: '2018-10-22T20:24:01.747108180Z',
      metadata: {
        keyValues: [
          {
            values: [{ stringValue: 'VUNI', type: 'string' }],
            key: 'partner',
          },
          {
            values: [{ stringValue: 'AG-6CHBZWSR68', type: 'string' }],
            key: 'account-group',
          },
          { values: [{ stringValue: 'sales_person', type: 'string' }], key: 'access' },
        ],
      },
    },
    {
      version: '1000',
      identity: {
        namespace: 'partner/VUNI/account-group/AG-6CHBZWSR68',
        parentPath: '/',
        taskId: 'TK-15595b1218754ecfae095e1e78d3cdeb',
      },
      title: 'six',
      status: 'In Progress',
      dueDate: '2018-10-22T20:25:58.791Z',
      assignees: ['UID-f4130b29-e22d-4034-bc7f-df54417293fa'],
      created: '2018-10-22T20:26:27.240497189Z',
      modified: '2018-10-22T20:26:27.240497189Z',
      metadata: {
        keyValues: [
          {
            values: [{ stringValue: 'VUNI', type: 'string' }],
            key: 'partner',
          },
          {
            values: [{ stringValue: 'AG-6CHBZWSR68', type: 'string' }],
            key: 'account-group',
          },
          { values: [{ stringValue: 'sales_person', type: 'string' }], key: 'access' },
        ],
      },
    },
    {
      version: '1000',
      identity: {
        namespace: 'partner/VUNI/account-group/AG-6CHBZWSR68',
        parentPath: '/',
        taskId: 'TK-78c93bb23aaa4d40971b3046c57dc5c2',
      },
      title: 'totadile',
      status: 'In Progress',
      dueDate: '2018-10-22T21:39:19.112Z',
      assignees: ['UID-f4130b29-e22d-4034-bc7f-df54417293fa'],
      created: '2018-10-22T21:39:59.924071036Z',
      modified: '2018-10-22T21:39:59.924071036Z',
      metadata: {
        keyValues: [
          {
            values: [{ stringValue: 'VUNI', type: 'string' }],
            key: 'partner',
          },
          {
            values: [{ stringValue: 'AG-6CHBZWSR68', type: 'string' }],
            key: 'account-group',
          },
          { values: [{ stringValue: 'sales_person', type: 'string' }], key: 'access' },
        ],
      },
    },
    {
      version: '2000',
      identity: {
        namespace: 'partner/VUNI/account-group/AG-6CHBZWSR68',
        parentPath: '/',
        taskId: 'TK-4a2a2d57b6f9464bafee588964002c51',
      },
      title: 'eleven',
      status: 'Completed',
      dueDate: '2018-10-22T20:41:53.454Z',
      assignees: ['UID-f4130b29-e22d-4034-bc7f-df54417293fa'],
      history: [
        {
          date: '2018-10-22T21:39:14.024767412Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
      ],
      created: '2018-10-22T20:42:00.446483286Z',
      modified: '2018-10-22T21:39:14.024767412Z',
      completionDate: '2018-10-22T21:39:14.024767412Z',
      metadata: {
        keyValues: [
          {
            values: [{ stringValue: 'VUNI', type: 'string' }],
            key: 'partner',
          },
          {
            values: [{ stringValue: 'AG-6CHBZWSR68', type: 'string' }],
            key: 'account-group',
          },
          { values: [{ stringValue: 'sales_person', type: 'string' }], key: 'access' },
        ],
      },
    },
    {
      version: '2000',
      identity: {
        namespace: 'partner/VUNI/account-group/AG-6CHBZWSR68',
        parentPath: '/',
        taskId: 'TK-703e379c2cb84bf78844fa95ff344cfd',
      },
      title: 'ten',
      status: 'Completed',
      dueDate: '2018-10-22T20:39:30.309Z',
      assignees: ['UID-f4130b29-e22d-4034-bc7f-df54417293fa'],
      history: [
        {
          date: '2018-10-22T21:39:06.718890322Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
      ],
      created: '2018-10-22T20:39:36.057406628Z',
      modified: '2018-10-22T21:39:06.718890322Z',
      completionDate: '2018-10-22T21:39:06.718890322Z',
      metadata: {
        keyValues: [
          {
            values: [{ stringValue: 'VUNI', type: 'string' }],
            key: 'partner',
          },
          {
            values: [{ stringValue: 'AG-6CHBZWSR68', type: 'string' }],
            key: 'account-group',
          },
          { values: [{ stringValue: 'sales_person', type: 'string' }], key: 'access' },
        ],
      },
    },
    {
      version: '2000',
      identity: {
        namespace: 'partner/VUNI/account-group/AG-6CHBZWSR68',
        parentPath: '/',
        taskId: 'TK-8157caeb51ee4332a217e462e88a2881',
      },
      title: 'thirteen',
      status: 'Completed',
      dueDate: '2018-10-22T20:47:11.973Z',
      assignees: ['UID-f4130b29-e22d-4034-bc7f-df54417293fa'],
      history: [
        {
          date: '2018-10-22T21:38:58.615970481Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
      ],
      created: '2018-10-22T20:47:20.922254485Z',
      modified: '2018-10-22T21:38:58.615970481Z',
      completionDate: '2018-10-22T21:38:58.615970481Z',
      metadata: {
        keyValues: [
          {
            values: [{ stringValue: 'VUNI', type: 'string' }],
            key: 'partner',
          },
          {
            values: [{ stringValue: 'AG-6CHBZWSR68', type: 'string' }],
            key: 'account-group',
          },
          { values: [{ stringValue: 'sales_person', type: 'string' }], key: 'access' },
        ],
      },
    },
    {
      version: '2000',
      identity: {
        namespace: 'partner/VUNI/account-group/AG-6CHBZWSR68',
        parentPath: '/',
        taskId: 'TK-624e77102e904ec68275d2ea09aa27f2',
      },
      title: 'tasky task',
      status: 'Completed',
      dueDate: '2018-10-22T20:50:03.821Z',
      assignees: ['UID-f4130b29-e22d-4034-bc7f-df54417293fa'],
      history: [
        {
          date: '2018-10-22T21:38:58.885995642Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
      ],
      created: '2018-10-22T20:50:20.135464730Z',
      modified: '2018-10-22T21:38:58.885995642Z',
      completionDate: '2018-10-22T21:38:58.885995642Z',
      metadata: {
        keyValues: [
          {
            values: [{ stringValue: 'VUNI', type: 'string' }],
            key: 'partner',
          },
          {
            values: [{ stringValue: 'AG-6CHBZWSR68', type: 'string' }],
            key: 'account-group',
          },
          { values: [{ stringValue: 'sales_person', type: 'string' }], key: 'access' },
        ],
      },
    },
    {
      version: '2000',
      identity: {
        namespace: 'partner/VUNI/account-group/AG-6CHBZWSR68',
        parentPath: '/',
        taskId: 'TK-d3076e2cca274425911479e5da028885',
      },
      title: 'twelve',
      status: 'Completed',
      dueDate: '2018-10-22T20:43:58.572Z',
      assignees: ['UID-f4130b29-e22d-4034-bc7f-df54417293fa'],
      history: [
        {
          date: '2018-10-22T21:39:05.535089487Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
      ],
      created: '2018-10-22T20:44:17.437089842Z',
      modified: '2018-10-22T21:39:05.535089487Z',
      completionDate: '2018-10-22T21:39:05.535089487Z',
      metadata: {
        keyValues: [
          {
            values: [{ stringValue: 'VUNI', type: 'string' }],
            key: 'partner',
          },
          {
            values: [{ stringValue: 'AG-6CHBZWSR68', type: 'string' }],
            key: 'account-group',
          },
          { values: [{ stringValue: 'sales_person', type: 'string' }], key: 'access' },
        ],
      },
    },
    {
      version: '6000',
      identity: {
        namespace: 'partner/VUNI/account-group/AG-6CHBZWSR68',
        parentPath: '/',
        taskId: 'TK-d09a4857e6c040bb82b62e73a0723369',
      },
      title: 'eight',
      status: 'Completed',
      dueDate: '2018-10-22T06:00:00Z',
      assignees: ['UID-f4130b29-e22d-4034-bc7f-df54417293fa'],
      history: [
        {
          date: '2018-10-22T20:43:45.364207832Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Title changed from eight to 8',
        },
        {
          date: '2018-10-22T20:43:45.363764963Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Due Date changed from 2018-10-22 to 2018-10-22',
        },
        {
          date: '2018-10-22T20:43:51.186699068Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Title changed from 8 to eight',
        },
        {
          date: '2018-10-22T20:43:51.186741399Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Due Date changed from 2018-10-22 to 2018-10-22',
        },
        {
          date: '2018-10-22T21:39:05.610409131Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
      ],
      created: '2018-10-22T20:30:21.409882027Z',
      modified: '2018-10-22T21:39:05.610409131Z',
      completionDate: '2018-10-22T21:39:05.610409131Z',
      metadata: {
        keyValues: [
          {
            values: [{ stringValue: 'VUNI', type: 'string' }],
            key: 'partner',
          },
          {
            values: [{ stringValue: 'AG-6CHBZWSR68', type: 'string' }],
            key: 'account-group',
          },
          { values: [{ stringValue: 'sales_person', type: 'string' }], key: 'access' },
        ],
      },
    },
    {
      version: '10000',
      identity: {
        namespace: 'partner/VUNI/account-group/AG-6CHBZWSR68',
        parentPath: '/',
        taskId: 'TK-a0c7d075c20041c58b48b3efdb529f18',
      },
      title: 'one',
      status: 'Completed',
      dueDate: '2018-10-22T06:00:00Z',
      assignees: ['UID-f4130b29-e22d-4034-bc7f-df54417293fa'],
      history: [
        {
          date: '2018-10-22T19:46:02.274768679Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
        {
          date: '2018-10-22T19:46:05.900530445Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from Completed to In Progress',
        },
        {
          date: '2018-10-22T19:46:12.181132997Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
        {
          date: '2018-10-22T19:46:18.592374087Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from Completed to In Progress',
        },
        {
          date: '2018-10-22T19:46:27.198197666Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
        {
          date: '2018-10-22T19:46:29.887623164Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from Completed to In Progress',
        },
        {
          date: '2018-10-22T20:23:33.928718154Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Title changed from Task 1 to one',
        },
        {
          date: '2018-10-22T20:23:33.930042755Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Due Date changed from 2018-10-22 to 2018-10-22',
        },
        {
          date: '2018-10-22T21:39:06.145607845Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
      ],
      created: '2018-10-22T19:13:58.856642917Z',
      modified: '2018-10-22T21:39:06.145607845Z',
      completionDate: '2018-10-22T21:39:06.145607845Z',
      metadata: {
        keyValues: [
          {
            values: [{ stringValue: 'VUNI', type: 'string' }],
            key: 'partner',
          },
          {
            values: [{ stringValue: 'AG-6CHBZWSR68', type: 'string' }],
            key: 'account-group',
          },
          { values: [{ stringValue: 'sales_person', type: 'string' }], key: 'access' },
        ],
      },
    },
    {
      version: '2000',
      identity: {
        namespace: 'partner/VUNI/account-group/AG-6CHBZWSR68',
        parentPath: '/',
        taskId: 'TK-33e4f95f8c5842749aaef9d5d4d4cc46',
      },
      title: 'nine',
      status: 'Completed',
      dueDate: '2018-10-22T20:32:39.646Z',
      assignees: ['UID-f4130b29-e22d-4034-bc7f-df54417293fa'],
      history: [
        {
          date: '2018-10-22T21:39:12.483077993Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
      ],
      created: '2018-10-22T20:32:48.712108375Z',
      modified: '2018-10-22T21:39:12.483077993Z',
      completionDate: '2018-10-22T21:39:12.483077993Z',
      metadata: {
        keyValues: [
          {
            values: [{ stringValue: 'VUNI', type: 'string' }],
            key: 'partner',
          },
          {
            values: [{ stringValue: 'AG-6CHBZWSR68', type: 'string' }],
            key: 'account-group',
          },
          { values: [{ stringValue: 'sales_person', type: 'string' }], key: 'access' },
        ],
      },
    },
    {
      version: '16000',
      identity: {
        namespace: 'partner/VUNI/account-group/AG-6CHBZWSR68',
        parentPath: '/',
        taskId: 'TK-07dcd5eaa0b74345bbc5c97f0459c405',
      },
      title: 'Call them again',
      status: 'Completed',
      dueDate: '2018-11-29T06:00:00Z',
      assignees: ['UID-f4130b29-e22d-4034-bc7f-df54417293fa'],
      history: [
        {
          date: '2018-10-12T14:46:37.460580957Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Title changed from Call them to Call them again',
        },
        {
          date: '2018-10-12T14:46:37.457426589Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Due Date changed from 2018-10-31 to 2018-11-29',
        },
        {
          date: '2018-10-12T14:46:39.204557581Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
        {
          date: '2018-10-12T14:46:39.754069507Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from Completed to In Progress',
        },
        {
          date: '2018-10-12T14:46:40.140427318Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
        {
          date: '2018-10-12T14:46:42.860930288Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from Completed to In Progress',
        },
        {
          date: '2018-10-12T14:46:43.135579274Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
        {
          date: '2018-10-22T17:25:50.138587671Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from Completed to In Progress',
        },
        {
          date: '2018-10-22T17:29:29.862270690Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
        {
          date: '2018-10-22T17:29:38.357647648Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from Completed to In Progress',
        },
        {
          date: '2018-10-22T17:29:42.717157430Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
        {
          date: '2018-10-22T19:46:06.818373831Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from Completed to In Progress',
        },
        {
          date: '2018-10-22T19:46:12.920235982Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
        {
          date: '2018-10-22T19:46:30.544176136Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from Completed to In Progress',
        },
        {
          date: '2018-10-22T20:42:15.962560785Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
      ],
      created: '2018-10-12T14:46:27.284766149Z',
      modified: '2018-10-22T20:42:15.962560785Z',
      completionDate: '2018-10-22T20:42:15.962560785Z',
      metadata: {
        keyValues: [
          {
            values: [{ stringValue: 'VUNI', type: 'string' }],
            key: 'partner',
          },
          {
            values: [{ stringValue: 'AG-6CHBZWSR68', type: 'string' }],
            key: 'account-group',
          },
          { values: [{ stringValue: 'sales_person', type: 'string' }], key: 'access' },
        ],
      },
    },
    {
      version: '14000',
      identity: {
        namespace: 'partner/VUNI/account-group/AG-6CHBZWSR68',
        parentPath: '/',
        taskId: 'TK-4fd6a441508e40d6bf5dfdf33823652d',
      },
      title: 'pending task',
      status: 'Completed',
      dueDate: '2018-11-30T06:00:00Z',
      assignees: ['UID-f4130b29-e22d-4034-bc7f-df54417293fa'],
      history: [
        {
          date: '2018-10-22T17:34:51.833616297Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
        {
          date: '2018-10-22T17:35:00.702479865Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from Completed to In Progress',
        },
        {
          date: '2018-10-22T17:35:01.327160520Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
        {
          date: '2018-10-22T17:35:04.832657414Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from Completed to In Progress',
        },
        {
          date: '2018-10-22T17:35:05.256973353Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
        {
          date: '2018-10-22T17:35:05.779029628Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from Completed to In Progress',
        },
        {
          date: '2018-10-22T17:35:06.238447908Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
        {
          date: '2018-10-22T17:35:22.597067825Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from Completed to In Progress',
        },
        {
          date: '2018-10-22T19:46:03.467378344Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
        {
          date: '2018-10-22T19:46:07.946960661Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from Completed to In Progress',
        },
        {
          date: '2018-10-22T19:46:13.578036772Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
        {
          date: '2018-10-22T19:46:32.524457891Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from Completed to In Progress',
        },
        {
          date: '2018-10-22T20:42:21.767631890Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
      ],
      created: '2018-10-22T17:12:01.846346066Z',
      modified: '2018-10-22T20:42:21.767631890Z',
      completionDate: '2018-10-22T20:42:21.767631890Z',
      metadata: {
        keyValues: [
          {
            values: [{ stringValue: 'VUNI', type: 'string' }],
            key: 'partner',
          },
          {
            values: [{ stringValue: 'AG-6CHBZWSR68', type: 'string' }],
            key: 'account-group',
          },
          { values: [{ stringValue: 'sales_person', type: 'string' }], key: 'access' },
        ],
      },
    },
    {
      version: '2000',
      identity: {
        namespace: 'partner/VUNI/account-group/AG-6CHBZWSR68',
        parentPath: '/',
        taskId: 'TK-c068ca0b0fb843d7a42e83c8c6d348c7',
      },
      title: 'new task',
      status: 'Completed',
      dueDate: '2018-10-22T20:54:06.465Z',
      assignees: ['UID-f4130b29-e22d-4034-bc7f-df54417293fa'],
      history: [
        {
          date: '2018-10-22T21:39:00.013212373Z',
          user: 'UID-fe4e4808-a42c-4c15-8940-28e76f75466d',
          message: 'Status changed from In Progress to Completed',
        },
      ],
      created: '2018-10-22T20:54:15.043805968Z',
      modified: '2018-10-22T21:39:00.013212373Z',
      completionDate: '2018-10-22T21:39:00.013212373Z',
      metadata: {
        keyValues: [
          {
            values: [{ stringValue: 'VUNI', type: 'string' }],
            key: 'partner',
          },
          {
            values: [{ stringValue: 'AG-6CHBZWSR68', type: 'string' }],
            key: 'account-group',
          },
          { values: [{ stringValue: 'sales_person', type: 'string' }], key: 'access' },
        ],
      },
    },
  ],
  nextCursor: '100',
  hits: '22',
};

export const mockTasks = mockTaskSearchResponse.tasks.map((task) => new Task(task as any));

export const mockLotsOfTasks = mockFatTasksSearchResponse.tasks.map((task: any) => {
  task.dueDate = new Date(task.dueDate); // convert strings to Dates
  return new Task(task);
});
