import { Inject, Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { CreateSalesTaskResponseInterface, TasksSDK, TasksService } from '@vendasta/sales';
import {
  AddAssigneeRequestInterface,
  Persona,
  RemoveAssigneeRequestInterface,
  SearchTaskResponseInterface,
  Status,
  Task,
  TaskSdkService,
} from '@vendasta/task';
import { Observable, ReplaySubject, Subject, combineLatest, of } from 'rxjs';
import { catchError, map, mapTo, switchMap, take, tap } from 'rxjs/operators';
import { AppService, SalesToolNotification, SalesToolNotificationType } from '../../app.service';
import { TaskEvent, buildTasksObservable } from '../../tasks';

@Injectable()
export class TaskCardService {
  public tasks$: Observable<Task[]>;
  // FIXME: make this private and change spec file to assert on public API
  public taskEvents$$: ReplaySubject<TaskEvent> = new ReplaySubject<TaskEvent>(1);
  // TODO: Use WorkStates for better handling of the loading/error states.
  private readonly isTaskLoadErrored$$: Subject<boolean> = new ReplaySubject<boolean>(1);
  public readonly isTaskLoadErrored$ = this.isTaskLoadErrored$$.asObservable();

  /** @deprecated use salesTaskSDK **/
  private readonly taskSdk: TaskSdkService;

  constructor(
    taskSdk: TaskSdkService,
    @Inject(TasksService) private readonly salesTaskSDK: TasksSDK,
    private readonly appService: AppService,
    private readonly translate: TranslateService,
  ) {
    this.taskSdk = taskSdk;
    this.tasks$ = buildTasksObservable(this.taskEvents$$.asObservable());
  }

  public loadTasks(accountGroupId: string, partnerId: string): void {
    this.isTaskLoadErrored$$.next(false);
    this.taskSdk
      .search({
        namespace: TaskSdkService.buildAccountGroupNamespace(partnerId, accountGroupId),
        pageSize: 100,
        access: [Persona.salesPerson],
        status: [Status.InProgress, Status.Completed, Status.Open, Status.WaitingOnCustomer],
      })
      .pipe(
        map((res: SearchTaskResponseInterface) => {
          if (res.tasks === undefined) {
            return [];
          }
          return res.tasks.map((taskI) => {
            return new Task(taskI);
          });
        }),
      )
      .subscribe(
        (tasks) => {
          this.taskEvents$$.next({
            eventType: 'loadTasks',
            tasks: tasks,
          });
          this.isTaskLoadErrored$$.next(false);
        },
        (err) => {
          console.error(err);
          this.isTaskLoadErrored$$.next(true);
        },
      );
  }

  public taskStatusChanged(taskId: string, newStatus: Status): void {
    this.taskEvents$$.next({
      eventType: 'updateTask',
      taskId: taskId,
      status: newStatus,
    });
  }

  public changeTaskStatus(taskId: string, namespace: string, newStatus: Status): Observable<boolean> {
    return this.taskSdk.setStatus(namespace, '/', taskId, newStatus).pipe(
      tap(() =>
        this.taskEvents$$.next({
          eventType: 'updateTask',
          taskId: taskId,
          status: newStatus,
        }),
      ),
      mapTo(true),
      catchError(() => of(false)),
    );
  }

  public editTask(
    taskId: string,
    namespace: string,
    newDueDate: Date,
    newTitle = '',
    newAssigneeIds: string[],
    oldAssigneeIds: string[],
  ): void {
    const setDueDate$ = newDueDate
      ? this.taskSdk.setDueDate(namespace, '/', taskId, newDueDate).pipe(mapTo(true))
      : of(true);
    const setTitle$ = newTitle ? this.taskSdk.setTitle(namespace, '/', taskId, newTitle).pipe(mapTo(true)) : of(true);
    const addAssigneeRequest: AddAssigneeRequestInterface[] = (newAssigneeIds || []).map((assigneeId) => ({
      assignee: assigneeId,
      identity: {
        namespace: namespace,
        parentPath: '/',
        taskId: taskId,
      },
    }));
    const removeAssigneeRequest: RemoveAssigneeRequestInterface[] = (oldAssigneeIds || []).map((assigneeId) => ({
      assignee: assigneeId,
      identity: {
        namespace: namespace,
        parentPath: '/',
        taskId: taskId,
      },
    }));
    const setAssignees$ = newAssigneeIds
      ? this.taskSdk
          .removeMultipleAssignees(removeAssigneeRequest)
          .pipe(switchMap(() => this.taskSdk.addMultipleAssignees(addAssigneeRequest).pipe(mapTo(true))))
      : of(true);

    combineLatest([setDueDate$, setTitle$, setAssignees$]).subscribe(([dueDateResult, titleResult, assigneeResult]) => {
      if (dueDateResult && newDueDate) {
        this.taskEvents$$.next({
          eventType: 'updateTask',
          taskId: taskId,
          newDueDate: newDueDate,
        });
      }
      if (titleResult && newTitle) {
        this.taskEvents$$.next({
          eventType: 'updateTask',
          taskId: taskId,
          newTitle: newTitle,
        });
      }
      if (assigneeResult && newAssigneeIds) {
        this.taskEvents$$.next({
          eventType: 'updateTask',
          taskId: taskId,
          newAssignees: newAssigneeIds,
          oldAssignees: oldAssigneeIds,
        });
      }
    });
  }

  public addTask(
    taskId: string,
    title: string,
    dueDate: Date,
    namespace: string,
    status: Status,
    assignees: string[],
  ): void {
    this.taskEvents$$.next({
      eventType: 'addTask',
      taskId: taskId,
      title: title,
      dueDate: dueDate,
      namespace: namespace,
      status: status,
      newAssignees: assignees,
    });
  }

  public createTask(
    title: string,
    dueDate: Date,
    assigneeIds: string[],
    partnerId: string,
    marketId: string,
    accountGroupId: string,
    salespersonId: string,
  ): Observable<boolean> {
    const assignees = assigneeIds || [salespersonId];
    return this.salesTaskSDK.createSalesTask(title, partnerId, marketId, accountGroupId, dueDate, assignees).pipe(
      take(1),
      switchMap((resp: CreateSalesTaskResponseInterface) => {
        this.addTask(resp.taskId, title, dueDate, resp.taskNamespace, Status.InProgress, assignees);
        return of(true);
      }),
      catchError((err) => {
        this.appService.pushNewMessage(
          new SalesToolNotification(
            this.translate.instant('ERRORS.UNABLE_TO_CREATE_TASK'),
            SalesToolNotificationType.ERROR,
            err.code,
          ),
        );
        return of(false);
      }),
    );
  }
}
