<mat-card appearance="outlined">
  <mat-card-header class="mat-card-header-with-action">
    <mat-card-title>
      <span>{{ 'COMMON.LABELS.TASKS' | translate }}</span>
      <ng-content select="[afterTitle]"></ng-content>
    </mat-card-title>
    <a mat-icon-button color="primary" (click)="openTaskEditor()" target="_blank">
      <mat-icon>add</mat-icon>
    </a>
  </mat-card-header>
  <ng-container *ngIf="isTasksLoaded$ | async; else loading">
    <ng-container *ngIf="isTasksLoadFailed$ | async; then errorMessage; else tasksEmpty"></ng-container>
    <ng-template #tasksEmpty>
      <ng-container *ngIf="isTasksEmpty$ | async; then noTasksExist; else tasksExist"></ng-container>
    </ng-template>

    <ng-template #tasksExist>
      <mat-card-content>
        <mat-button-toggle-group
          class="button-toggle"
          [value]="visibleTaskView$ | async"
          (change)="setTaskView($event.value)"
        >
          <mat-button-toggle value="pending">Pending</mat-button-toggle>
          <mat-button-toggle value="completed">Completed</mat-button-toggle>
        </mat-button-toggle-group>
      </mat-card-content>
      <ng-container *ngIf="(visibleTaskView$ | async) === 'pending'">
        <div class="task-card-content" *ngIf="pendingTasks$ | async as pendingTasks">
          <ng-container *ngIf="editingNewTask; then taskEditor"></ng-container>
          <div class="no-data no-task-info fade-in" *ngIf="pendingTasks.length === 0 && !editingNewTask">
            <uikit-empty-state iconName="assignment">
              <span state-description>
                {{ 'TASKS.ALL_TASKS_COMPLETED_MESSAGE' | translate }}
              </span>
            </uikit-empty-state>
          </div>
          <mat-accordion>
            <mat-expansion-panel *ngFor="let task of pendingTasks" class="task-card-content__task-details">
              <mat-expansion-panel-header collapsedHeight="*" expandedHeight="*">
                <mat-panel-title>
                  {{ task.title }}
                  <span *ngIf="task.overdue">
                    <va-badge small color="warn">Overdue</va-badge>
                  </span>
                </mat-panel-title>
                <mat-panel-description *ngIf="!!task.dueDate">
                  {{ 'TASKS.DUE_ON' | translate : { dateDue: (task.dueDate | date : 'mediumDate') } }}
                </mat-panel-description>
              </mat-expansion-panel-header>
              <app-task-card-item
                [task]="task"
                (statusChangedEvent)="statusChanged($event)"
                (editTaskEvent)="editTask($event)"
              ></app-task-card-item>
            </mat-expansion-panel>
          </mat-accordion>
          <div class="task-card-content__overview_link" *ngIf="inProgressMoreThanTen$ | async">
            <a (click)="viewMoreClicked(availableStatuses.InProgress)">
              {{ 'COMMON.ACTION_LABELS.VIEW_MORE' | translate }}
            </a>
          </div>
        </div>
      </ng-container>
      <ng-container *ngIf="(visibleTaskView$ | async) === 'completed'">
        <div class="task-card-content" *ngIf="completedTasks$ | async as completedTasks">
          <ng-container *ngIf="editingNewTask; then taskEditor"></ng-container>
          <div class="no-data no-task-info fade-in" *ngIf="completedTasks.length === 0 && !editingNewTask">
            <uikit-empty-state iconName="assignment">
              <span state-description>
                {{ 'TASKS.EMPTY_TASKS_COMPLETED_MESSAGE' | translate }}
              </span>
            </uikit-empty-state>
          </div>
          <mat-accordion>
            <mat-expansion-panel *ngFor="let task of completedTasks" class="task-card-content__task-details">
              <mat-expansion-panel-header collapsedHeight="*" expandedHeight="*">
                <mat-panel-title>
                  {{ task.title }}
                </mat-panel-title>
                <mat-panel-description *ngIf="!!task.dueDate">
                  {{ 'TASKS.DUE_ON' | translate : { dateDue: (task.dueDate | date : 'mediumDate') } }}
                </mat-panel-description>
              </mat-expansion-panel-header>
              <app-task-card-item
                [task]="task"
                (statusChangedEvent)="statusChanged($event)"
                (editTaskEvent)="editTask($event)"
              ></app-task-card-item>
            </mat-expansion-panel>
          </mat-accordion>
          <div class="task-card-content__overview_link" *ngIf="completedMoreThanTen$ | async">
            <a (click)="viewMoreClicked(availableStatuses.Completed)">
              {{ 'COMMON.ACTION_LABELS.VIEW_MORE' | translate }}
            </a>
          </div>
        </div>
      </ng-container>
    </ng-template>

    <ng-template #errorMessage>
      <app-error-card
        text="We were unable to load the tasks for this account"
        (retry)="retryLoadTasks()"
      ></app-error-card>
    </ng-template>
    <ng-template #noTasksExist>
      <ng-container *ngIf="editingNewTask; then taskEditor"></ng-container>

      <div class="no-task-info fade-in" *ngIf="!editingNewTask">
        <uikit-empty-state iconName="add_circle_outline">
          <span state-description innerHTML="{{ emptyStateTxt$ | async }}"></span>
        </uikit-empty-state>
      </div>
    </ng-template>
  </ng-container>
</mat-card>

<!-- Task Card Templates -->
<ng-template #taskEditor>
  <div class="task-card-content__task-details fade-in">
    <app-task-item-editor
      [loading]="creatingNewTask"
      [showAccountSelector]="false"
      (submitted)="addTask($event)"
      (cancel)="cancel()"
    ></app-task-item-editor>
  </div>
</ng-template>

<ng-template #loading>
  <mat-spinner [diameter]="20" [strokeWidth]="3"></mat-spinner>
</ng-template>
