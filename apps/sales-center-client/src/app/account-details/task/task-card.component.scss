@use 'design-tokens' as *;

:host {
  display: block;
}

.no-task-info {
  max-width: 540px;
  margin: 0 auto;
}

.grouped-phrase {
  white-space: nowrap;
  overflow: hidden;
}

app-task-card-item {
  min-height: 24px;
}

mat-card-content {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;

  .button-toggle {
    margin-bottom: $spacing-2;
  }
}

.mat-expansion-panel-header-description {
  display: flex;
  justify-content: flex-end;
}

.mat-expansion-panel-header {
  padding-top: 8px;
  padding-bottom: 8px;
  height: fit-content;
}

.mat-expansion-panel-header.mat-expanded {
  padding-top: 8px;
  padding-bottom: 8px;
  height: max-content;
}
