import { Status, Task } from '@vendasta/task';
import { EMPTY, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { TestScheduler } from 'rxjs/testing';
import { TaskCardUiService } from './task-card-ui.service';
import { mockLotsOfTasks, mockTasks } from './task.service.mock';

let sched: TestScheduler;

class MockTaskCardService {
  tasks$: Observable<Task[]>;
  isTaskLoadErrored$ = EMPTY;

  constructor(bigResponse = false) {
    if (bigResponse) {
      this.tasks$ = sched.createColdObservable('-x', { x: mockLotsOfTasks });
    } else {
      this.tasks$ = sched.createColdObservable('-x', { x: mockTasks });
    }
  }

  loadTasks = jest.fn();
}

describe('TaskCardUiService', () => {
  let service: TaskCardUiService;
  let mockTaskCardService: MockTaskCardService;

  beforeEach(() => {
    sched = new TestScheduler((a, b) => expect(a).toEqual(b));
  });
  afterEach(() => {
    sched.flush();
  });

  describe('loadTasks', () => {
    beforeEach(() => {
      mockTaskCardService = new MockTaskCardService();
      service = new TaskCardUiService(mockTaskCardService as any);
    });

    it('should load the tasks in the taskCard service', () => {
      service.loadTasks('AG-123', 'PARTNER');
      expect(mockTaskCardService.loadTasks).toHaveBeenCalled();
    });

    describe('loading tasks from TaskCardService', () => {
      it('should load the tasks from the taskCardService and format them to TaskUiInterfaces, and sort them by date', () => {
        const expected = [
          {
            title: 'This is a task',
            dueDate: new Date('2018-09-30T06:00:00.000Z'),
            status: Status.InProgress,
            taskId: 'TK-e934332a747f4ca9bc5ecd6cd740bb2e',
            assigneeIds: ['U-TESTASSIGNEE1', 'U-TESTASSIGNEE2'],
            overdue: true,
            namespace: 'partner/VUNI/account-group/AG-GX6GDC7K55',
          },
          {
            title: 'This is another task',
            dueDate: new Date('2018-10-31T06:00:00.000Z'),
            status: Status.Completed,
            taskId: 'TK-6708fdb58f8c46eeab1eeaeeeba0a6ed',
            assigneeIds: ['U-TESTASSIGNEE1', 'U-TESTASSIGNEE2'],
            overdue: false,
            namespace: 'partner/VUNI/account-group/AG-GX6GDC7K55',
          },
        ];
        service.loadTasks('AG-123', 'PARTNER');
        sched.expectObservable(service.tasks$).toBe('-x', { x: expected });
      });
      it('should load the pending tasks', () => {
        const expected = [
          {
            title: 'This is a task',
            dueDate: new Date('2018-09-30T06:00:00.000Z'),
            status: Status.InProgress,
            taskId: 'TK-e934332a747f4ca9bc5ecd6cd740bb2e',
            assigneeIds: ['U-TESTASSIGNEE1', 'U-TESTASSIGNEE2'],
            overdue: true,
            namespace: 'partner/VUNI/account-group/AG-GX6GDC7K55',
          },
        ];
        service.loadTasks('AG-123', 'PARTNER');
        sched.expectObservable(service.inProgressTasks$).toBe('-x', { x: expected });
      });
      it('should load the completed tasks', () => {
        const expected = [
          {
            title: 'This is another task',
            dueDate: new Date('2018-10-31T06:00:00.000Z'),
            status: Status.Completed,
            taskId: 'TK-6708fdb58f8c46eeab1eeaeeeba0a6ed',
            assigneeIds: ['U-TESTASSIGNEE1', 'U-TESTASSIGNEE2'],
            overdue: false,
            namespace: 'partner/VUNI/account-group/AG-GX6GDC7K55',
          },
        ];
        service.loadTasks('AG-123', 'PARTNER');
        sched.expectObservable(service.completedTasks$).toBe('-x', { x: expected });
      });
      it('should set isMoreInProgress$ to false if there are less than ten inProgress tasks', () => {
        sched.expectObservable(service.isMoreInProgress$).toBe('-x', { x: false });
      });
      it('should set isMoreCompleted$ to false if there are less than ten completed tasks', () => {
        sched.expectObservable(service.isMoreCompleted$).toBe('-x', { x: false });
      });
      describe('on big responses', () => {
        beforeEach(() => {
          mockTaskCardService = new MockTaskCardService(true);
          service = new TaskCardUiService(mockTaskCardService as any);
        });
        it('should initially set isMoreInProgress$ to true if there are more than ten InProgress tasks', () => {
          service.loadTasks('AG-123', 'PARTNER');
          sched.expectObservable(service.isMoreInProgress$).toBe('-x', { x: true });
        });
        it('should initially set isMoreCompleted$ to true if there are more than ten completed tasks', () => {
          service.loadTasks('AG-123', 'PARTNER');
          sched.expectObservable(service.isMoreCompleted$).toBe('-x', { x: true });
        });
        it('should initially load the first 5 inProgress tasks', () => {
          service.loadTasks('AG-123', 'PARTNER');
          const lengthOfInProgressTasks$ = service.inProgressTasks$.pipe(map((InProgress) => InProgress.length));
          sched.expectObservable(lengthOfInProgressTasks$).toBe('-x', { x: 5 });
        });
        it('should initially load the first 5 completed tasks', () => {
          service.loadTasks('AG-123', 'PARTNER');
          const lengthOfCompletedTasks$ = service.completedTasks$.pipe(map((completed) => completed.length));
          sched.expectObservable(lengthOfCompletedTasks$).toBe('-x', { x: 5 });
        });

        describe('loadMore', () => {
          beforeEach(() => {
            mockTaskCardService = new MockTaskCardService(true);
            service = new TaskCardUiService(mockTaskCardService as any);
          });

          it('should load up to the next 5 inProgress tasks when specified', () => {
            const lengthOfInProgressTasks$ = service.inProgressTasks$.pipe(map((tasks) => tasks.length));
            sched.schedule(() => service.loadMore(Status.InProgress), sched.createTime('----|'));
            sched.expectObservable(lengthOfInProgressTasks$).toBe('-x--y', { x: 5, y: 11 });
          });
          it('should load up to the next 5 completed tasks when specified', () => {
            const lengthOfCompletedTasks$ = service.completedTasks$.pipe(map((tasks) => tasks.length));
            sched.schedule(() => service.loadMore(Status.Completed), sched.createTime('----|'));
            sched.expectObservable(lengthOfCompletedTasks$).toBe('-x--y', { x: 5, y: 11 });
          });
          it('should set isMoreInProgress$ to false if there not an additional 5 after load', () => {
            sched.schedule(() => service.loadMore(Status.InProgress), sched.createTime('----|'));
            sched.expectObservable(service.isMoreInProgress$).toBe('-t--f', { t: true, f: false });
          });
          it('should set isMoreCompleted$ to false if there not an additional 5 after load', () => {
            sched.schedule(() => service.loadMore(Status.Completed), sched.createTime('----|'));
            sched.expectObservable(service.isMoreCompleted$).toBe('-t--f', { t: true, f: false });
          });
        });
      });
    });
  });
});
