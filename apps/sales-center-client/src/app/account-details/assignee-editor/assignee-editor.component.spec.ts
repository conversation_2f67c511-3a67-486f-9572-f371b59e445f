import { makeBulkUpdateOperations } from './assignee-editor.component';

describe('AssigneeEditorComponent', () => {
  test('bulk update operations creation', () => {
    const result = makeBulkUpdateOperations('id1', 'ABC123', ['person1', 'person2']);
    const accountExternalIds = result.toApiJson()[0]['accountGroupExternalIdentifiers'];
    const additionalIDs = accountExternalIds['additionalSalesPersonIds'];
    const partnerID = accountExternalIds['partnerId'];
    const primaryAssigneeId = accountExternalIds['salesPersonId'];

    expect(additionalIDs).toEqual(['person1', 'person2']);
    expect(partnerID).toEqual('ABC123');
    expect(primaryAssigneeId).toEqual('id1');
  });
});
