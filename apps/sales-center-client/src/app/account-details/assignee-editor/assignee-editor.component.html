<h4 mat-dialog-title>
  {{ 'ASSIGNEE_EDITOR.SELECT_ASSIGNEES' | translate : { businessName: business.name } }}
</h4>
<mat-dialog-content>
  <div>
    <mat-form-field>
      <mat-label>
        {{ 'ASSIGNEE_EDITOR.PRIMARY_ASSIGNEE' | translate }}
      </mat-label>
      <mat-select
        *ngIf="currentAssigneeID$ | async as currentAssigneeId; else noAssignee"
        [value]="currentAssigneeId"
        (selectionChange)="setAvailableAssignees($event.value)"
      >
        <mat-option *ngFor="let person of salespeople$ | async" value="{{ person.id }}">
          {{ person.fullName }}
        </mat-option>
      </mat-select>
      <mat-spinner *ngIf="(salespeople$ | async) === null" diameter="24"></mat-spinner>
      <ng-template #noAssignee>
        <mat-select (selectionChange)="setAvailableAssignees($event.value)">
          <mat-option *ngFor="let person of salespeople$ | async" value="{{ person.id }}">
            {{ person.fullName }}
          </mat-option>
        </mat-select>
      </ng-template>
    </mat-form-field>
  </div>
  <mat-form-field>
    <mat-label>{{ 'ASSIGNEE_EDITOR.ADDITIONAL_ASSIGNEES' | translate }}</mat-label>
    <mat-select [value]="currentAdditionalIds$ | async" multiple (valueChange)="additionalAssigneesUpdated($event)">
      <mat-option
        *ngFor="let additionalOption of possibleAdditionalAssignees$ | async"
        [value]="additionalOption.value"
      >
        {{ additionalOption.name }}
      </mat-option>
    </mat-select>
  </mat-form-field>
</mat-dialog-content>
<mat-dialog-actions>
  <button mat-button (click)="closeAction()">
    {{ 'COMMON.ACTION_LABELS.CANCEL' | translate }}
  </button>
  <button mat-raised-button color="primary" (click)="submitAction()">
    {{ 'COMMON.ACTION_LABELS.SUBMIT' | translate }}
  </button>
</mat-dialog-actions>
