import { NgModule } from '@angular/core';
import { VaFormsModule } from '@vendasta/forms';
import { MenuModule } from '../../common/fec/fec-menu';
import { AssigneeEditorComponent } from './assignee-editor.component';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@NgModule({
  imports: [
    VaFormsModule,
    MatAutocompleteModule,
    MenuModule,
    MatSelectModule,
    FormsModule,
    MatButtonModule,
    CommonModule,
    TranslateModule,
    MatDialogModule,
    MatProgressSpinnerModule,
  ],
  declarations: [AssigneeEditorComponent],
})
export class AssigneeEditorModule {}
