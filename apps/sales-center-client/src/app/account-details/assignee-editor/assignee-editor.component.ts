import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, Inject, Input } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import {
  AccountGroupApiService,
  AccountGroupExternalIdentifiersUpdateOperation,
  UpdateOperations,
} from '@galaxy/account-group';
import { Salesperson } from '@galaxy/types';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { BehaviorSubject, EMPTY, Observable, combineLatest } from 'rxjs';
import { map, shareReplay, switchMap, take, tap } from 'rxjs/operators';
import { SalespeopleService } from '../../salespeople/salespeople.service';
import { ViewBusiness } from '../account-info-store.service';

export function makeBulkUpdateOperations(
  currentAssigneeID: string,
  businessID: string,
  currentAdditionalIds: string[],
): UpdateOperations {
  const operations = new UpdateOperations();

  operations.addUpdateOperation(
    new AccountGroupExternalIdentifiersUpdateOperation({
      partnerId: businessID,
      salesPersonId: currentAssigneeID,
      additionalSalesPersonIds: currentAdditionalIds,
    }),
  );
  return operations;
}

interface AssigneeOption {
  name: string;
  value: string;
}

@Component({
  selector: 'app-assignee-editor',
  templateUrl: './assignee-editor.component.html',
  styleUrls: ['./assignee-editor.component.scss'],
  standalone: false,
})
export class AssigneeEditorComponent {
  business: ViewBusiness;
  private readonly initialAssignee: Salesperson;
  private readonly initialAdditionalAssignees: Salesperson[];

  public possibleAdditionalAssignees$: Observable<AssigneeOption[]>;
  private additionalAssigneesChanged = false;
  private readonly currentAdditionalIds$$ = new BehaviorSubject<string[]>([]);
  public readonly currentAdditionalIds$: Observable<string[]>;
  private readonly currentAssigneeID$$ = new BehaviorSubject<string>('');
  public readonly currentAssigneeID$: Observable<string>;
  public readonly salespeople$: Observable<Salesperson[]>;

  constructor(
    private readonly salespeopleService: SalespeopleService,
    public assigneeEditDialogRef: MatDialogRef<AssigneeEditorComponent>,
    @Inject(MAT_DIALOG_DATA)
    public data: {
      assignee: Salesperson;
      business: ViewBusiness;
      additionalAssignees: Salesperson[];
    },
    private readonly accountGroupService: AccountGroupApiService,
    private readonly alertService: SnackbarService,
    private readonly translate: TranslateService,
  ) {
    this.currentAdditionalIds$ = this.currentAdditionalIds$$.asObservable();
    this.currentAssigneeID$ = this.currentAssigneeID$$.asObservable();
    this.initialAssignee = this.data.assignee;
    this.initialAdditionalAssignees = this.data.additionalAssignees;
    this.currentAssigneeID$$.next(this.initialAssignee ? this.initialAssignee.id : '');

    this.business = this.data.business;

    const additionalAssignees: string[] = [];
    if (this.data.additionalAssignees) {
      this.data.additionalAssignees.forEach(function (assignee): void {
        additionalAssignees.push(assignee.id);
      });
    }

    this.currentAdditionalIds$$.next(additionalAssignees);

    this.salespeople$ = this.salespeopleService.loadSalespeople().pipe(shareReplay(1));

    this.possibleAdditionalAssignees$ = combineLatest([
      this.salespeople$,
      this.currentAssigneeID$,
      this.currentAdditionalIds$,
    ]).pipe(
      map(([salespeople, currentAssigneeID, additionalSalespeople]) => {
        const possibleAdditionalAssignees: AssigneeOption[] = [];

        const unavailableAssignees = this.getListOfUnavailableAssignees(additionalSalespeople, salespeople);
        possibleAdditionalAssignees.push(...unavailableAssignees);

        for (const salesperson of salespeople) {
          if (salesperson.id !== currentAssigneeID) {
            possibleAdditionalAssignees.push({ name: salesperson.fullName, value: salesperson.id });
          }
        }
        return possibleAdditionalAssignees;
      }),
    );
  }

  private getListOfUnavailableAssignees(
    salespersonIDs: string[],
    availableSalespeople: Salesperson[],
  ): AssigneeOption[] {
    return salespersonIDs
      .filter((as) => !availableSalespeople.find((s) => s.id === as))
      .map((us) => ({ name: this.translate.instant('ASSIGNEE_EDITOR.ASSIGNEE_UNAVAILABLE'), value: us }));
  }

  @Input() setAvailableAssignees(selectedID: string): void {
    this.currentAssigneeID$$.next(selectedID);
  }

  public additionalAssigneesUpdated(assigneeIDs: string[]): void {
    this.currentAdditionalIds$$.next(assigneeIDs);
    this.additionalAssigneesChanged = true;
  }

  public closeAction(): void {
    this.assigneeEditDialogRef.close({ changesMade: false });
  }

  public submitAction(): void {
    combineLatest([this.currentAssigneeID$, this.salespeople$, this.currentAdditionalIds$])
      .pipe(
        take(1),
        switchMap(([currentAssigneeID, salespeople, currentAdditionalIds]) => {
          console.log(this.additionalAssigneesChanged);
          if (!(this.primaryAssigneeChanged(currentAssigneeID) || this.additionalAssigneesChanged)) {
            this.alertService.openErrorSnack('ASSIGNEE_EDITOR.NO_ASSIGNEES_CHANGED');
            return EMPTY;
          } else if (currentAssigneeID === '') {
            this.alertService.openErrorSnack('ASSIGNEE_EDITOR.EMPTY_PRIMARY_NOT_ALLOWED');
            return EMPTY;
          } else {
            return this.constructAndSubmitBulkUpdate(currentAssigneeID, salespeople, currentAdditionalIds);
          }
        }),
      )
      .subscribe();
  }

  private primaryAssigneeChanged(currentAssigneeId: string): boolean {
    return !(this.initialAssignee && currentAssigneeId === this.initialAssignee.id);
  }

  private constructAndSubmitBulkUpdate(
    currentAssigneeID: string,
    salespeople: Salesperson[],
    currentAdditionalIds: string[],
  ): Observable<HttpResponse<null>> {
    const accountGroupId = this.business.businessId;
    const cleanAdditionalAssignees = currentAdditionalIds.filter((spId) => spId !== currentAssigneeID);

    if ((this.initialAdditionalAssignees || []).find((sp) => sp.id === currentAssigneeID)) {
      const firstOperations = makeBulkUpdateOperations(
        this.initialAssignee.id,
        this.business.partnerId,
        cleanAdditionalAssignees,
      );
      return this.accountGroupService.bulkUpdate(accountGroupId, firstOperations).pipe(
        switchMap(() => {
          const operations = makeBulkUpdateOperations(
            currentAssigneeID,
            this.business.partnerId,
            cleanAdditionalAssignees,
          );
          return this.doUpdate(accountGroupId, operations, currentAssigneeID, salespeople, cleanAdditionalAssignees);
        }),
      );
    } else {
      const operations = makeBulkUpdateOperations(currentAssigneeID, this.business.partnerId, cleanAdditionalAssignees);
      return this.doUpdate(accountGroupId, operations, currentAssigneeID, salespeople, cleanAdditionalAssignees);
    }
  }

  doUpdate(
    accountGroupId: string,
    operations: UpdateOperations,
    currentAssigneeID: string,
    salespeople: Salesperson[],
    currentAdditionalIds: string[],
  ): Observable<HttpResponse<null>> {
    return this.accountGroupService.bulkUpdate(accountGroupId, operations).pipe(
      tap(
        () => {
          this.updateResultSuccessAction(currentAssigneeID, salespeople, currentAdditionalIds);
        },
        (error) => {
          this.updateResultFailureAction(error);
        },
      ),
    );
  }

  private updateResultSuccessAction(
    currentAssigneeID: string,
    salespeople: Salesperson[],
    currentAdditionalIds: string[],
  ): void {
    this.alertService.openSuccessSnack('ASSIGNEE_EDITOR.UPDATE_ASSIGNEE_SUCCESS');
    const newAssignee = salespeople.filter((person) => person.id === currentAssigneeID)[0];
    const newAdAssignees: Salesperson[] = [];

    for (const id of currentAdditionalIds) {
      newAdAssignees.push(salespeople.filter((person) => person.id === id)[0]);
    }
    this.assigneeEditDialogRef.close({
      changesMade: true,
      primaryAssignee: newAssignee,
      additionalAssignees: newAdAssignees,
    });
  }

  private updateResultFailureAction(error: HttpErrorResponse): void {
    if (error.status === 400) {
      this.alertService.openErrorSnack(error.error.message);
    } else {
      this.alertService.openErrorSnack('ERRORS.SOMETHING_WENT_WRONG');
    }
  }
}
