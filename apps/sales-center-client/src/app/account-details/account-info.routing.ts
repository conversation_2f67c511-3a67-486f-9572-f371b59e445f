import { AccountInfoComponent } from './account-info.component';
import { GeneralInfoEditComponent } from './general-info/edit/general-info-edit.component';
import { RouterModule, Routes } from '@angular/router';
import { NgModule } from '@angular/core';
import { AccessFeatureGuard } from '../guards';
import { Feature } from '../access';
import { OpportunityDetailsComponent } from '../sales-opportunities/opportunity-details.component';

const routes: Routes = [
  {
    path: ':accountGroupId/account-info-edit',
    component: GeneralInfoEditComponent,
  },
  {
    path: ':accountGroupId/opportunity/:opportunityId',
    component: OpportunityDetailsComponent,
    canActivate: [AccessFeatureGuard],
    data: { feature: Feature.pipeline },
  },
  {
    path: ':accountGroupId',
    component: AccountInfoComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AccountInfoRouting {}
