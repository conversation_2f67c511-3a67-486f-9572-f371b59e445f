import { NgModule } from '@angular/core';
import { ContactsCardComponent } from './contacts-card.component';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { UIKitModule } from '@vendasta/uikit';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { ContactFormsModule } from '../../common/contacts';
import { ContactsV2ListModule } from '../../common/contacts/contacts-v2-list/contacts-v2-list.module';

@NgModule({
  imports: [
    MatCardModule,
    MatIconModule,
    TranslateModule,
    UIKitModule,
    MatDividerModule,
    MatButtonModule,
    ContactFormsModule,
    ContactsV2ListModule,
  ],
  declarations: [ContactsCardComponent],
  exports: [ContactsCardComponent],
  providers: [],
})
export class ContactsCardModule {}
