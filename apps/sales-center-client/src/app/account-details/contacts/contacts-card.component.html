<mat-card appearance="outlined">
  <mat-card-header class="mat-card-header-with-action">
    <mat-card-title>
      <span>{{ 'CONTACTS.TITLE' | translate }}</span>
      <ng-content select="[afterTitle]"></ng-content>
    </mat-card-title>
    <button mat-icon-button color="primary" appAddContact [businessId]="accountGroupId">
      <mat-icon>add</mat-icon>
    </button>
  </mat-card-header>
  <app-contacts-v2-list [accountGroupId]="accountGroupId"></app-contacts-v2-list>
</mat-card>
