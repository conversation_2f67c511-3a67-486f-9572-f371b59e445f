import { Injectable } from '@angular/core';
import { GetUTMAttributesRequestInterface, ProspectApiService, UTMAttributes } from '@vendasta/prospect';
import { Observable, ReplaySubject } from 'rxjs';

@Injectable()
export class UTMAttributesStoreService {
  utmAttributes$$ = new ReplaySubject<UTMAttributes>(null);
  utmAttributes$: Observable<UTMAttributes> = this.utmAttributes$$.asObservable();

  constructor(private readonly prospectApiService: ProspectApiService) {}

  loadUTMAttributes(prospectID: string): void {
    const req: GetUTMAttributesRequestInterface = {
      prospectId: prospectID,
    };

    this.prospectApiService.getUtmAttributes(req).subscribe({
      next: (res) => {
        this.utmAttributes$$.next(res.utmAttributes);
      },
      error: () => {
        this.utmAttributes$$.next({} as UTMAttributes);
      },
    });
  }
}
