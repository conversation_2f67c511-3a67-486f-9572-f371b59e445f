import { Component, OnInit, Input } from '@angular/core';
import { Observable } from 'rxjs';
import { UTMAttributes } from '@vendasta/prospect';
import { UTMAttributesStoreService } from './utm-attributes-store.service';

@Component({
  selector: 'app-utm-attributes-card',
  templateUrl: './utm-attributes-card.component.html',
  styleUrls: ['./utm-attributes-card.component.scss'],
  standalone: false,
})
export class UTMAttributesCardComponent implements OnInit {
  @Input() prospectId: string;

  utmAttributes$: Observable<UTMAttributes>;

  constructor(private readonly utmAttributesStoreService: UTMAttributesStoreService) {}

  ngOnInit(): void {
    this.utmAttributesStoreService.loadUTMAttributes(this.prospectId);
    this.utmAttributes$ = this.utmAttributesStoreService.utmAttributes$;
  }
}
