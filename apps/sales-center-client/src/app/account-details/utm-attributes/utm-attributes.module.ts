import { MatCardModule } from '@angular/material/card';

import { MatDividerModule } from '@angular/material/divider';

import { NgModule } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import { UTMAttributesCardComponent } from './utm-attributes-card.component';
import { ProspectApiService } from '@vendasta/prospect';
import { UTMAttributesStoreService } from './utm-attributes-store.service';
import { CommonModule } from '@angular/common';

@NgModule({
  imports: [MatCardModule, MatDividerModule, CommonModule, TranslateModule],
  declarations: [UTMAttributesCardComponent],
  exports: [UTMAttributesCardComponent],
  providers: [ProspectApiService, UTMAttributesStoreService],
})
export class UTMAttributesModule {}
