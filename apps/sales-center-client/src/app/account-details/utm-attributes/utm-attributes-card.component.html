<mat-card appearance="outlined">
  <mat-card-header>
    <mat-card-title>
      <span>{{ 'UTM_ATTRIBUTES.TITLE' | translate }}</span>
      <ng-content select="[afterTitle]"></ng-content>
    </mat-card-title>
  </mat-card-header>

  <mat-card-content class="utm-attributes-list">
    <div *ngIf="utmAttributes$ | async as utmAttributes">
      <div class="utm-attributes-list__item">
        <div class="utm-attribute__key">
          {{ 'UTM_ATTRIBUTES.CAMPAIGN' | translate }}
        </div>
        <div class="utm-attribute__value">
          {{ utmAttributes?.utmCampaign || '-' }}
        </div>
        <div class="utm-attribute__key">
          {{ 'UTM_ATTRIBUTES.MEDIUM' | translate }}
        </div>
        <div class="utm-attribute__value">
          {{ utmAttributes?.utmMedium || '-' }}
        </div>
        <div class="utm-attribute__key">
          {{ 'UTM_ATTRIBUTES.SOURCE' | translate }}
        </div>
        <div class="utm-attribute__value">
          {{ utmAttributes?.utmSource || '-' }}
        </div>
        <div class="utm-attribute__key">
          {{ 'UTM_ATTRIBUTES.CONTENT' | translate }}
        </div>
        <div class="utm-attribute__value">
          {{ utmAttributes?.utmContent || '-' }}
        </div>
        <div class="utm-attribute__key">
          {{ 'UTM_ATTRIBUTES.TERM' | translate }}
        </div>
        <div class="utm-attribute__value">
          {{ utmAttributes?.utmTerm || '-' }}
        </div>
      </div>
    </div>
  </mat-card-content>
</mat-card>
