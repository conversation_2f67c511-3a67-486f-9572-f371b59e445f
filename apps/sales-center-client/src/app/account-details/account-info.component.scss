@use 'design-tokens' as *;

.financialHealth {
  float: left;
  @media screen and (max-width: $media--tablet-minimum) {
    float: none;
  }
}

.divider {
  margin: 10px 0 9px 0;
}

.subtitle {
  margin-bottom: $spacing-3;
}

.archive-info-banner {
  margin-top: $spacing-2;
  padding: 0 10px;
  margin-bottom: $spacing-3;
}

.main-account-info-container > * {
  padding-bottom: $spacing-3;
}

.container-item {
  margin: 5px;
}

app-error-card {
  margin: 80px 40px;
}

.business-priorities-card {
  margin-bottom: $spacing-3;
}

::ng-deep automata-start-manual-automation-form .dialog-content {
  width: 450px;
  padding-left: $spacing-1;
  padding-right: $spacing-1;

  // Compress node items on tablet and phone.
  @media (max-width: $media--tablet-minimum) {
    width: 100vw;
  }
}

:host ::ng-deep .extended-item {
  margin: 0 !important;
}

.copy-icon {
  font-size: $spacing-3;
  height: $spacing-3;
  width: $spacing-3;
  margin-bottom: $spacing-2;
}
