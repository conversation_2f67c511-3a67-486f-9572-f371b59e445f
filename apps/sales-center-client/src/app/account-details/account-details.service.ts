import { Inject, Injectable } from '@angular/core';
import { map, switchMap, take } from 'rxjs/operators';

import { HttpClient } from '@angular/common/http';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ManualStartWorkStateMap } from '@vendasta/rx-utils/work-state';
import { Observable } from 'rxjs';
import { PARTNER_ID_TOKEN } from '../common/providers';
import { SalesActivity, SalesActivityService } from '../sales-activity';

const archiveBusinessUrl = '/internalApi/v2/business/update-archive-status/';
interface PostResponse {
  data: {
    sales_activity: {
      account_group_id: string;
      sales_record_id: string;
      event_datetime: string;
      opportunity_id: string;
      sales_status: string;
      sales_person_action: string;
      custom_action_name: string;
      salesperson_name: string;
      salesperson_id: string;
      product_name: string;
      package_ids: string[];
      product_price: number;
      closed_lost_reason_name: string;
      closed_lost_reason: string;
      notes: string;
      campaign_name: string;
      campaign_start_date: string;
      campaign_end_date: string;
      connected: string;
      last_edit_datetime: string;
      origin_id?: string;
      audio_key?: string;
      submitted_by?: string;
      deleted_date?: string;
    };
  };
}

@Injectable()
export class AccountDetailsService {
  private readonly archiveActivityWSM = new ManualStartWorkStateMap<string, boolean>();

  constructor(
    private readonly http: HttpClient,
    private readonly salesActivityService: SalesActivityService,
    private readonly alert: SnackbarService,
    @Inject(PARTNER_ID_TOKEN) private readonly partnerId$: Observable<string>,
  ) {}

  public archiveBusinessResult$(accountGroupId: string): Observable<boolean> {
    return this.archiveActivityWSM.getWorkResults$(accountGroupId);
  }

  public archivingBusiness$(accountGroupId: string): Observable<boolean> {
    return this.archiveActivityWSM.isLoading$(accountGroupId);
  }

  public archiveBusiness(accountGroupId: string, reason: string, archiveFlag = true): void {
    this.archiveActivityWSM.startWork(accountGroupId, (success, fail) => {
      this.archiveBusinessFromNGPage(accountGroupId, reason, archiveFlag).subscribe(
        (salesActivity) => {
          success(archiveFlag);
          this.salesActivityService.addActivity(salesActivity);
          const successAction = archiveFlag
            ? 'ACCOUNT_DETAILS.SNACKBAR_MESSAGES.ARCHIVE.SUCCESS'
            : 'ACCOUNT_DETAILS.SNACKBAR_MESSAGES.UNARCHIVE.SUCCESS';
          this.alert
            .openSuccessSnack(successAction, {
              action: 'COMMON.ACTION_LABELS.UNDO',
            })
            .onAction()
            .subscribe(() => {
              const archiving = !archiveFlag;
              this.archiveBusiness(accountGroupId, '', archiving);
            });
        },
        () => {
          fail();
          const failedAction = archiveFlag
            ? 'ACCOUNT_DETAILS.SNACKBAR_MESSAGES.ARCHIVE.ERROR'
            : 'ACCOUNT_DETAILS.SNACKBAR_MESSAGES.UNARCHIVE.ERROR';
          this.alert.openErrorSnack(failedAction, {
            action: 'COMMON.ACTION_LABELS.RETRY',
          });
        },
      );
    });
  }

  private archiveBusinessFromNGPage(
    accountGroupId: string,
    reason: string,
    archiveFlag: boolean,
  ): Observable<SalesActivity> {
    return this.partnerId$.pipe(
      switchMap((pid) =>
        this.http
          .post(
            archiveBusinessUrl,
            {
              partnerId: pid,
              accountGroupId: accountGroupId,
              reason: reason,
              archiveFlag: archiveFlag,
            },
            { withCredentials: true },
          )
          .pipe(
            take(1),
            map((response: PostResponse) =>
              this.salesActivityService.deserializeActivity(response.data.sales_activity),
            ),
          ),
      ),
    );
  }
}
