import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { SocialURLs } from '@galaxy/account-group';
import { FeatureFlagService } from '@galaxy/partner';
import { Option } from '@vendasta/forms';
import { SubscriptionList } from '@vendasta/rx-utils';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { openSecureNewTab } from '../../common/secure';
import { ACCOUNT_INFO, ACCOUNT_INFO_EDIT } from '../../urls';
import { ViewBusiness } from '../account-info-store.service';
import { CustomerIdentifiers } from '../sub-title/customer-id/customer-id.service';

export interface RetryMarketNameEvent {
  partnerId: string;
  marketId: string;
}

@Component({
  selector: 'app-general-info-card',
  templateUrl: './general-info-card.component.html',
  styleUrls: ['./general-info-card.component.scss'],
  standalone: false,
})
export class GeneralInfoCardComponent implements OnInit, OnChanges, OnDestroy {
  name: string;
  streetAddress: string;
  city: string;
  state: string;
  countryCode: string;
  zip: string;
  phoneNumber: string;
  website: string;
  serviceCategories: string[];
  hasServiceCategories: boolean;
  accountGroupId: string;
  businessInfoUrl$: Observable<string>;
  vCategoryIds: string[];

  private partnerId: string;
  private marketId: string;

  @Input() tags: Option[];
  @Input() customerIdentifiers: CustomerIdentifiers[];
  @Input() showMarkets: boolean;
  @Input() marketName: string;
  @Input() marketLoadFailed: boolean;
  @Input() marketLoading: boolean;
  @Input() socialUrls: SocialURLs;
  @Input() pipelineBoardSlideoutStyling = false;
  @Output() retryMarketName = new EventEmitter<RetryMarketNameEvent>();
  private SEARCH_TERM: string;
  accountInfoEditUrl$: Observable<string>;
  private readonly businessId$$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  businessId$: Observable<string> = this.businessId$$.asObservable();
  private readonly subscriptions = SubscriptionList.new();

  constructor(private readonly feature: FeatureFlagService) {
    this.businessInfoUrl$ = this.businessId$.pipe(map((bizId) => ACCOUNT_INFO(bizId)));
    this.accountInfoEditUrl$ = this.businessId$.pipe(map((bizId) => ACCOUNT_INFO_EDIT(bizId)));
  }

  ngOnInit(): void {
    this.businessId$$.next(this.accountGroupId);

    if (this.accountGroupId) {
      this.businessId$$.next(this.accountGroupId);
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (!!changes.business && !!changes.business.currentValue) {
      this.businessId$$.next(changes.business.currentValue.accountGroupId);
    }

    if (changes?.['customerIdentifiers'] && !changes?.['customerIdentifiers'].firstChange) {
      this.customerIdentifiers = changes['customerIdentifiers'].currentValue;
      this.vCategoryIds = this.customerIdentifiers?.['vCategoryIds'];
    }
  }

  @Input() set business(b: ViewBusiness) {
    if (b) {
      this.name = b.name;
      this.partnerId = b.partnerId;
      this.marketId = b.marketId;
      this.streetAddress = b.streetAddress;
      this.city = b.city;
      this.state = b.state;
      this.countryCode = b.countryCode;
      this.zip = b.zip;
      this.phoneNumber = b.phoneNumber;
      this.website = b.website;
      this.serviceCategories = b.serviceCategories;
      this.hasServiceCategories = b.serviceCategories && b.serviceCategories.length > 0;
      this.accountGroupId = b.businessId;
    }
  }

  handleRetryMarketName(): void {
    this.retryMarketName.emit({
      partnerId: this.partnerId,
      marketId: this.marketId,
    });
  }

  openUrl(url: string): void {
    openSecureNewTab(url);
  }

  googleSearchForBusiness(): void {
    this.SEARCH_TERM = this.name;
    if (this.city !== '') {
      this.SEARCH_TERM += ` ${this.city}`;
    }
    if (this.state !== '') {
      this.SEARCH_TERM += ` ${this.state}`;
    }
    openSecureNewTab(`http://www.google.com/search?q=${this.SEARCH_TERM}`);
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }
}
