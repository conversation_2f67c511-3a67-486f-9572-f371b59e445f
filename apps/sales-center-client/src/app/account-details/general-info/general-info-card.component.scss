@use 'design-tokens' as *;
@import '../../common/badge';

:host {
  display: block;
}

a {
  color: $blue;
}

$number-of-media-icons: 4;
$media-icon-width: 50px;
$media-icon-height: $media-icon-width;
$spacing-between-icons: 4px;

$media-icon-width-for-board: 25px;
$media-icon-height-for-board: $media-icon-width-for-board;
$spacing-between-icons-for-board: 4px;
$standard-card-content-spacing: 16px;

.social-media-container {
  display: flex;
  justify-content: center;
  margin-top: $standard-card-content-spacing;
}
.social-media-row {
  width: (($media-icon-width * $number-of-media-icons) + ($number-of-media-icons * $spacing-between-icons));
  display: flex;
  justify-content: space-between;

  mat-icon {
    width: $media-icon-width;
    height: $media-icon-height;
  }
}

.social-media-row-for-board {
  width: (($media-icon-width-for-board * $number-of-media-icons) + ($number-of-media-icons * $spacing-between-icons));
  display: flex;
  justify-content: space-between;

  mat-icon {
    width: $media-icon-width-for-board;
    height: $media-icon-height-for-board;
  }
}

.social-icon-active {
  cursor: pointer;
}

.social-icon-inactive {
  opacity: 0.5;
}

.contact-info {
  padding-top: 8px;

  div {
    line-height: 1.5em;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}

.card-title {
  font-weight: 400;
  margin-top: $standard-card-content-spacing;
  margin-bottom: 0;
}

.async-text__subheading {
  color: $tertiary-font-color;
}

.service-category-entry {
  color: $tertiary-font-color;
}

.va-card__subtitle {
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 0;
  margin-top: $standard-card-content-spacing;
}

.tag-container {
  padding-top: 8px;
}

.tag-list > va-badge {
  margin: 2px;
}

.general-info-card {
  display: inline-block;
  text-overflow: ellipsis;
}

.business-name {
  font-size: medium;
  font-weight: bold;
}
