import { NgModule } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { CommonModule } from '@angular/common';
import { AsyncTextModule } from '../async-text/async-text.module';
import { GeneralInfoCardComponent } from './general-info-card.component';
import { GeneralInfoEditComponent } from './edit/general-info-edit.component';
import { VFormModule } from '@vendasta/vform';
import { RouterModule } from '@angular/router';
import { NavigationModule } from '../../navigation/navigation.module';
import { VaBadgeModule } from '@vendasta/uikit';
import { TaxonomyServiceInterfaceToken } from '@vendasta/taxonomy';
import { TaxonomyWrapperService } from '../../business-category';
import { TranslateModule } from '@ngx-translate/core';
import { LocalTimeCardModule } from '@vendasta/sales-ui';
import { MatListModule } from '@angular/material/list';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatButtonModule } from '@angular/material/button';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { BusinessCategoryModule } from '@galaxy/business-category';
import { BusinessProfileModule } from '@vendasta/businesses';

@NgModule({
  imports: [
    CommonModule,
    MatCardModule,
    MatInputModule,
    MatDividerModule,
    MatDialogModule,
    MatIconModule,
    AsyncTextModule,
    RouterModule,
    VFormModule,
    NavigationModule,
    VaBadgeModule,
    TranslateModule,
    LocalTimeCardModule,
    MatListModule,
    MatGridListModule,
    MatButtonModule,
    GalaxyPageModule,
    BusinessCategoryModule,
    BusinessProfileModule,
  ],
  declarations: [GeneralInfoCardComponent, GeneralInfoEditComponent],
  exports: [GeneralInfoCardComponent, GeneralInfoEditComponent],
  providers: [{ provide: TaxonomyServiceInterfaceToken, useClass: TaxonomyWrapperService }],
})
export class GeneralInfoCardModule {}
