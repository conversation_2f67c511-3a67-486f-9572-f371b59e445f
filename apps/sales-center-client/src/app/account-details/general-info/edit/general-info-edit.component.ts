import { Component, Inject, Injectable, OnDestroy, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Configuration } from '@galaxy/partner';
import { BusinessProfileComponent, BusinessProfileTab, HiddenField } from '@vendasta/businesses';
import { FormOptions } from '@vendasta/vform';
import { firstValueFrom, Observable, of, Subscription } from 'rxjs';
import { catchError, map, take } from 'rxjs/operators';
import { Feature, SSCAccessService } from '../../../access';
import { FeatureFlagService } from '../../../core';
import { Features } from '../../../features';
import { AccountTag, BusinessLoader, ManageAccountsService } from '../../../manage-accounts/manage-accounts.service';
import { BreadCrumb } from '../../../navigation';

const ACCOUNT_DETAIL_URL = `/info/`;

@Component({
  selector: 'app-edit-general-info',
  templateUrl: './general-info-edit.component.html',
  standalone: false,
})
@Injectable()
export class GeneralInfoEditComponent implements OnDestroy {
  @ViewChild(BusinessProfileComponent) businessProfile: BusinessProfileComponent;
  formOptions: FormOptions;
  showError = false;
  readonly accountGroupId$: Observable<string>;
  configurations$: Observable<Configuration>;
  businessName: string;
  breadCrumb$: Observable<BreadCrumb[]>;
  businessInfoUrl$: Observable<string>;
  subscriptions: Subscription[] = [];
  hiddenFields: HiddenField[] = [HiddenField.Markets, HiddenField.Sales, HiddenField.Map, HiddenField.SocialFourSquare];
  accountTags$: Observable<AccountTag[]>;
  enabledTabs: BusinessProfileTab[] = [
    BusinessProfileTab.Primary,
    BusinessProfileTab.Hours,
    BusinessProfileTab.Social,
    BusinessProfileTab.Descriptions,
    BusinessProfileTab.Professional,
    BusinessProfileTab.Images,
    BusinessProfileTab.Attributes,
    BusinessProfileTab.Administration,
  ];

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly featureService: FeatureFlagService,
    private readonly accessService: SSCAccessService,
    @Inject(ManageAccountsService) private readonly manageAccountsService: BusinessLoader,
  ) {
    firstValueFrom(featureService.featureFlagEnabled$(Features.SalespersonCanEditCustomerID))
      .then((isEditingCustomerIDEnabled) => {
        if (!isEditingCustomerIDEnabled) {
          this.hiddenFields.push(HiddenField.CustomerIdentifier);
        }
      })
      .catch(() => {
        this.hiddenFields.push(HiddenField.CustomerIdentifier);
      });
    this.accessService
      .hasAccessToFeature(Feature.customFields)
      .pipe(take(1))
      .subscribe({
        next: (hasAccessToCustomFields) => {
          if (!hasAccessToCustomFields) {
            this.enabledTabs = this.enabledTabs.filter((tab) => tab !== BusinessProfileTab.Attributes);
          }
        },
      });

    this.accountGroupId$ = this.getAccountGroupIdFromUrl();
    this.businessInfoUrl$ = this.accountGroupId$.pipe(map((id) => `${ACCOUNT_DETAIL_URL}${id}`));

    this.accountTags$ = this.manageAccountsService.getAccountTags().pipe(catchError(() => of([])));
  }

  handleError(hasError: boolean): void {
    if (hasError) {
      this.showError = true;
    }
  }

  private getAccountGroupIdFromUrl(): Observable<string> {
    return this.activatedRoute.params.pipe(map((params) => params?.accountGroupId));
  }

  handleOnSave(): void {
    this.subscriptions.push(
      this.businessInfoUrl$.subscribe((businessInfoUrl) => {
        this.router.navigateByUrl(businessInfoUrl);
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
  }
}
