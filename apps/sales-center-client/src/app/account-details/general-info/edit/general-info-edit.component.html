<glxy-page [pagePadding]="false">
  <glxy-page-toolbar>
    <glxy-page-nav>
      <glxy-page-nav-button
        [previousPageTitle]="'ACCOUNT_DETAILS.BREADCRUMBS.PREVIOUS_PAGE' | translate"
        [previousPageUrl]="businessInfoUrl$ | async"
        [useHistory]="true"
        [historyBackButtonTitle]="'COMMON.ACTION_LABELS.BACK' | translate"
      ></glxy-page-nav-button>
      <glxy-page-title>
        {{ 'ACCOUNT_DETAILS.EDIT_ACCOUNT_INFO' | translate }}
      </glxy-page-title>
    </glxy-page-nav>
  </glxy-page-toolbar>
  <business-profile
    [accountGroupId]="accountGroupId$ | async"
    [editDisabled]="false"
    [enabledTabs]="enabledTabs"
    [tags]="accountTags$ | async"
    [hiddenFields]="hiddenFields"
    (saved)="handleOnSave()"
  >
  </business-profile>
</glxy-page>
