<ng-container *ngIf="pipelineBoardSlideoutStyling; then infoCardSlideout; else infoCardDefault"></ng-container>

<ng-template #infoCardSlideout>
  <ng-container [ngTemplateOutlet]="contactInfoSlideout"></ng-container>
  <ng-container [ngTemplateOutlet]="businessCategoriesLayoutFirst"></ng-container>
</ng-template>

<ng-template #infoCardDefault>
  <mat-card appearance="outlined">
    <mat-card-header class="mat-card-header-with-action">
      <mat-card-title>
        <span>{{ 'ACCOUNT_DETAILS.GENERAL_INFO.TITLE' | translate }}</span>
        <ng-content select="[afterTitle]"></ng-content>
      </mat-card-title>
      <a mat-icon-button color="primary" [routerLink]="accountInfoEditUrl$ | async">
        <mat-icon>edit</mat-icon>
      </a>
    </mat-card-header>
    <mat-card-content>
      <ng-container [ngTemplateOutlet]="contactInfoDefault"></ng-container>
      <ng-container [ngTemplateOutlet]="socialMediaLayoutFirst"></ng-container>
    </mat-card-content>
  </mat-card>
</ng-template>

<ng-template #contactInfoSlideout>
  <div>
    <a class="business-name" [routerLink]="businessInfoUrl$ | async" [queryParams]="{ origin: 'pipeline-board' }">
      {{ name }}
    </a>
    <div>{{ streetAddress }}</div>
    <div>{{ city }}, {{ state }}, {{ countryCode }}</div>
    <div>{{ zip }}</div>
    <ng-container [ngTemplateOutlet]="phoneAndWebsite"></ng-container>
  </div>
</ng-template>

<ng-template #contactInfoDefault>
  <div class="contact-info">
    <div>{{ streetAddress }}</div>
    <div>{{ city }}, {{ state }}, {{ countryCode }} &nbsp;{{ zip }}</div>
    <ng-container [ngTemplateOutlet]="phoneAndWebsite"></ng-container>
  </div>
</ng-template>

<ng-template #businessCategoriesLayoutFirst>
  <ng-container [ngTemplateOutlet]="businessCategories"></ng-container>
  <sales-ui-localtime-card
    [accountGroupId]="businessId$ | async"
    [editTimezoneUrl]="accountInfoEditUrl$ | async"
  ></sales-ui-localtime-card>
  <ng-container [ngTemplateOutlet]="socialMedia"></ng-container>
</ng-template>

<ng-template #socialMediaLayoutFirst>
  <ng-container [ngTemplateOutlet]="socialMedia"></ng-container>
  <ng-container [ngTemplateOutlet]="businessCategories"></ng-container>
</ng-template>

<ng-template #phoneAndWebsite>
  <div>
    <a [attr.href]="'tel:' + phoneNumber" data-action="clicked-business-tel" data-action-group="clicked-tel">
      {{ phoneNumber }}
    </a>
  </div>
  <div>
    <a [attr.href]="website" target="_blank">
      {{ website }}
    </a>
  </div>
</ng-template>

<ng-template #businessCategories>
  <div class="more-details">
    <div>
      <h3 class="card-title">
        {{ 'ACCOUNT_DETAILS.GENERAL_INFO.BUSINESS_CATEGORY_LABEL' | translate }}
      </h3>
      <div class="name-and-icon">
        <category-overview
          class="async-text__subheading"
          [categoryIds]="vCategoryIds"
          [placeholder]="'COMMON.NONE_LABEL' | translate"
        ></category-overview>
      </div>
    </div>
    <div *ngIf="hasServiceCategories">
      <h3 class="card-title">
        {{ 'ACCOUNT_DETAILS.GENERAL_INFO.SERVICE_CATEGORY_LABEL' | translate }}
      </h3>
      <ng-container *ngIf="serviceCategories.length === 1 && serviceCategories[0] === ''; else listCategories">
        <div class="service-category-entry">
          {{ 'COMMON.NONE_LABEL' | translate }}
        </div>
      </ng-container>
      <ng-template #listCategories>
        <div *ngFor="let sc of serviceCategories">
          <div class="service-category-entry">
            {{ sc }}
          </div>
        </div>
      </ng-template>
    </div>
    <div *ngIf="showMarkets">
      <h3 class="card-title">{{ 'COMMON.MARKET_LABEL' | translate }}</h3>
      <app-async-text
        class="async-text__subheading"
        failTooltipText="{{ 'ERRORS.UNABLE_TO_LOAD_DISPLAY_VALUE' | translate : { value: 'market' } }}  {{
          'COMMON.ACTION_LABELS.CLICK_TO_RETRY' | translate
        }}"
        [loadFailed]="marketLoadFailed"
        [loading]="marketLoading"
        (retry)="handleRetryMarketName()"
      >
        {{ marketName }}
      </app-async-text>
    </div>
    <div>
      <h3 class="card-title">
        {{ 'ACCOUNT_DETAILS.GENERAL_INFO.TAGS_LABEL' | translate }}
      </h3>
      <div *ngIf="tags?.length > 0; else noTags">
        <div class="tag-container">
          <span class="tag-list" *ngFor="let tag of tags">
            <va-badge small color="gray">{{ tag.name }}</va-badge>
          </span>
        </div>
      </div>
      <ng-template #noTags>
        <span class="async-text__subheading">
          {{ 'COMMON.NONE_LABEL' | translate }}
        </span>
      </ng-template>
    </div>
  </div>
</ng-template>

<ng-template #socialMedia>
  <div class="social-media-container">
    <div [ngClass]="pipelineBoardSlideoutStyling ? 'social-media-row-for-board' : 'social-media-row'">
      <mat-icon
        *ngIf="socialUrls?.facebookUrl !== undefined; else inactiveFacebook"
        svgIcon="Facebook"
        class="social-icon-active"
        (click)="openUrl(socialUrls?.facebookUrl)"
      ></mat-icon>
      <ng-template #inactiveFacebook>
        <mat-icon svgIcon="Facebook" class="social-icon-inactive"></mat-icon>
      </ng-template>
      <mat-icon
        *ngIf="socialUrls?.twitterUrl !== undefined; else inactiveTwitter"
        svgIcon="Twitter"
        class="social-icon-active"
        (click)="openUrl(socialUrls?.twitterUrl)"
      ></mat-icon>
      <ng-template #inactiveTwitter>
        <mat-icon svgIcon="Twitter" class="social-icon-inactive"></mat-icon>
      </ng-template>
      <mat-icon
        *ngIf="socialUrls?.linkedinUrl !== undefined; else inactiveLinkedin"
        svgIcon="LinkedIn"
        class="social-icon-active"
        (click)="openUrl(socialUrls?.linkedinUrl)"
      ></mat-icon>
      <ng-template #inactiveLinkedin>
        <mat-icon svgIcon="LinkedIn" class="social-icon-inactive"></mat-icon>
      </ng-template>
      <mat-icon svgIcon="business-search" class="social-icon-active" (click)="googleSearchForBusiness()"></mat-icon>
    </div>
  </div>
</ng-template>
