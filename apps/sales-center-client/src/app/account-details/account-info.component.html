<glxy-page [pagePadding]="false">
  <glxy-page-toolbar>
    <glxy-page-nav>
      <glxy-page-nav-button
        previousPageUrl="/manage"
        [previousPageTitle]="'ACCOUNT_DETAILS.BREADCRUMBS.PREVIOUS_PAGE' | translate"
        [useHistory]="true"
        [historyBackButtonTitle]="'COMMON.ACTION_LABELS.BACK' | translate"
      ></glxy-page-nav-button>
      <glxy-page-title>
        {{ businessName$ | async }}
        <a (click)="copyBusinessName()"><mat-icon class="copy-icon">content_copy</mat-icon></a>
      </glxy-page-title>
    </glxy-page-nav>
    <glxy-page-title-actions
      *ngIf="businessInfo$ | async"
      app-account-lifecycle
      [marketingInfo]="marketingInfo$ | async"
      [accountGroupId]="businessId$ | async"
    ></glxy-page-title-actions>
    <glxy-page-actions>
      <button
        mat-flat-button
        color="primary"
        [disabled]="(enablePageActions$ | async) === false"
        [matMenuTriggerFor]="actions"
      >
        Actions
        <mat-icon iconPositionEnd>arrow_drop_down</mat-icon>
      </button>
      <mat-menu #actions="matMenu">
        <ng-container *ngIf="(enablePageActions$ | async) && (businessId$ | async) as businessId">
          <button mat-menu-item (click)="addSalesActivityAction()">
            {{ 'ACCOUNT_DETAILS.PAGE_MENU.ADD_SALES_ACTIVITY' | translate }}
          </button>
          <button mat-menu-item (click)="startEmailCampaign()">
            {{ 'ACCOUNT_DETAILS.PAGE_MENU.START_EMAIL_CAMPAIGN' | translate }}
          </button>
          <button
            data-action="create-opportunity:started"
            mat-menu-item
            *ngIf="hasAccessToOpportunities$ | async"
            (click)="createOpportunityAction()"
          >
            {{ 'ACCOUNT_DETAILS.PAGE_MENU.CREATE_OPPORTUNITY' | translate }}
          </button>
          <a
            *ngIf="(proposalBuilderDisabled$ | async) === false"
            [routerLink]="proposalUrl"
            [queryParams]="{ organizationId: businessId }"
            mat-menu-item
          >
            {{ 'ACCOUNT_DETAILS.PAGE_MENU.CREATE_PROPOSAL' | translate }}
          </a>
          <a
            mat-menu-item
            *ngIf="canCreateOrders$ | async"
            [routerLink]="'/order-management/' + businessId + '/create'"
            [queryParams]="{ origin: 'account' }"
          >
            {{ 'ACCOUNT_DETAILS.PAGE_MENU.CREATE_ORDER' | translate }}
          </a>
          <button mat-menu-item appAddContact [businessId]="businessId">
            {{ 'ACCOUNT_DETAILS.PAGE_MENU.ADD_CONTACT' | translate }}
          </button>
          <a mat-menu-item (click)="directToAccountInfoEdit(businessId)">
            {{ 'ACCOUNT_DETAILS.PAGE_MENU.EDIT_ACCOUNT' | translate }}
          </a>
          <button
            mat-menu-item
            *ngIf="(businessIsArchived$ | async) === false"
            (click)="archiveAccountAction(businessId)"
          >
            {{ 'ACCOUNT_DETAILS.PAGE_MENU.ARCHIVE' | translate }}
          </button>
        </ng-container>
      </mat-menu>
    </glxy-page-actions>
  </glxy-page-toolbar>

  <app-side-drawer-container>
    <div
      *ngIf="(businessIsArchived$ | async) && (businessId$ | async) as businessId"
      class="col col-xs-12 archive-info-banner"
    >
      <app-status-banner
        statusText="{{ 'ACCOUNT_DETAILS.ARCHIVED_ACCOUNT_EXPLANATION' | translate }}"
        actionTitle="{{ 'COMMON.ACTION_LABELS.UNDO_ARCHIVE' | translate }}"
        [type]="archiveBannerType"
        [processingAction]="(archivingBusiness$ | async) && (businessIsArchived$ | async)"
        (actionOnClick)="unarchiveAccountAction(businessId)"
      ></app-status-banner>
    </div>
    <ng-container *ngIf="businessId$ | async as businessId">
      <sales-ui-slide-out-panel mode="over">
        <div content class="content-centered">
          <div class="col col-xs-12 subtitle">
            <ng-container *ngIf="businessInfo$ | async as bizInfo">
              <app-sub-title
                [businessInfo]="bizInfo"
                [customerIdentifiers]="customerIdentifiers$"
                [associatedPartnerId$]="associatedPartnerId$"
              ></app-sub-title>
              <mat-divider class="divider"></mat-divider>
              <app-financial-health
                class="financialHealth"
                [accountGroupId]="bizInfo.businessId"
                [associatedPartnerId$]="associatedPartnerId$"
              ></app-financial-health>
              <app-account-health [accountGroupId]="bizInfo.businessId"></app-account-health>
            </ng-container>
          </div>
          <ng-container *ngIf="isBusinessInfoLoading$ | async; else page">
            <div class="row row-gutters">
              <div class="col col-xs-12 col-md-4 main-account-info-container">
                <div>
                  <uikit-card-stencil></uikit-card-stencil>
                </div>
                <div>
                  <uikit-card-stencil></uikit-card-stencil>
                </div>
                <div>
                  <uikit-card-stencil></uikit-card-stencil>
                </div>
                <div>
                  <uikit-card-stencil></uikit-card-stencil>
                </div>
                <div>
                  <uikit-card-stencil></uikit-card-stencil>
                </div>
                <div>
                  <uikit-card-stencil></uikit-card-stencil>
                </div>
              </div>
              <div class="col col-xs-12 col-md-8 main-account-info-container">
                <div>
                  <uikit-card-stencil></uikit-card-stencil>
                </div>
                <div>
                  <uikit-card-stencil></uikit-card-stencil>
                </div>
                <div>
                  <uikit-card-stencil></uikit-card-stencil>
                </div>
              </div>
            </div>
          </ng-container>

          <ng-template #page>
            <ng-container *ngIf="isBusinessLoadFailed$ | async; else info">
              <div class="body">
                <app-error-card
                  text="{{ 'ERRORS.UNABLE_TO_LOAD_ACCOUNT' | translate }}"
                  (retry)="refreshBusiness()"
                ></app-error-card>
              </div>
            </ng-container>
          </ng-template>

          <ng-template #ordersTemplate>
            <div *ngIf="hasAccessToOrders$ | async">
              <sales-ui-sales-order-short-list
                ordersPageForBusinessUrl="/sales-orders"
                [ordersPageForBusinessQuery]="buildOrdersPageForBusinessQuery(businessId)"
                [getOrderDetailsUrl]="orderUrlCallback"
                [getFulfillmentFormUrl]="fulfillmentFormUrlCallback"
                [businessId]="businessId"
                [partnerId]="partnerId$ | async"
                [showCreateButton]="canCreateOrders$ | async"
                (createClicked)="createOrder()"
              >
                <app-pin-activate afterTitle id="orders"></app-pin-activate>
              </sales-ui-sales-order-short-list>
            </div>
          </ng-template>

          <ng-template #info>
            <ng-container *ngIf="partnerId$ | async as partnerId">
              <div class="row">
                <div class="col col-md-12 business-priorities-card">
                  <app-editable-business-priorities
                    [partnerId]="partnerId"
                    [marketId]="marketId$ | async"
                    [businessId]="businessId"
                    [businessInfo]="businessInfo$ | async"
                    [associatedPartnerId$]="associatedPartnerId$"
                  ></app-editable-business-priorities>
                </div>
              </div>
              <div class="row">
                <div class="col col-md-12">
                  <app-ssc-projects [accountGroupId]="businessId" [partnerId]="partnerId"></app-ssc-projects>
                </div>
              </div>

              <div appPinHost class="row row-gutters">
                <div class="col col-xs-12 col-md-8 col-md-push-4 main-account-info-container">
                  <app-pin-target [active]="!largeDisplay"></app-pin-target>
                  <div>
                    <app-recent-activity
                      [partnerId]="partnerId"
                      [businessId]="businessId"
                      [salesPersonId]="salesPersonId$ | async"
                      [hotness]="hotness$ | async"
                      (contactButtonClicked)="scrollToContactCard()"
                    ></app-recent-activity>
                  </div>
                  <div>
                    <app-sales-activity
                      #salesActivities
                      [currency]="currency$ | async"
                      [isAccountDetails]="true"
                      [partnerId]="partnerId"
                      [marketId]="marketId$ | async"
                    ></app-sales-activity>
                  </div>
                  <div *ngIf="canAccessImportedUserActions$ | async">
                    <app-imported-user-actions [businessId$]="businessId$"></app-imported-user-actions>
                  </div>
                </div>
                <div class="col col-xs-12 col-md-4 col-md-pull-8 main-account-info-container">
                  <app-pin-target [active]="largeDisplay"></app-pin-target>
                  <ng-container *appPinnable="'campaigns'">
                    <div *ngIf="canViewCampaigns$ | async">
                      <app-campaigns-card
                        data-cy="campaigns-card"
                        [businessId]="businessId"
                        (createNewCampaign)="startEmailCampaign()"
                      >
                        <app-pin-activate afterTitle id="campaigns"></app-pin-activate>
                      </app-campaigns-card>
                    </div>
                  </ng-container>
                  <ng-container *appPinnable="'opportunities'">
                    <div *ngIf="accessToPipeline$ | async">
                      <app-sales-opportunity-card
                        data-cy="sales-opportunity-card"
                        #opportunities
                        [partnerId]="partnerId"
                        [marketId]="marketId$ | async"
                        [accountGroupId]="businessId$ | async"
                        (openOpportunityCreateForm)="openOpportunityCreateForm()"
                      >
                        <app-pin-activate afterTitle id="opportunities"></app-pin-activate>
                      </app-sales-opportunity-card>
                    </div>
                  </ng-container>

                  <ng-container *appPinnable="'active-products-card'">
                    <ng-container *ngIf="configuration$ | async as configuration">
                      <div *ngIf="!configuration.hideProducts">
                        <app-active-products-card>
                          <app-pin-activate afterTitle id="active-products-card"></app-pin-activate>
                        </app-active-products-card>
                      </div>
                    </ng-container>
                  </ng-container>

                  <ng-container *appPinnable="'reports'">
                    <div>
                      <app-reports-card
                        [business]="businessInfo$ | async"
                        [partnerId]="partnerId"
                        [marketId]="marketId$ | async"
                        [showExecReportLink]="(configuration$ | async)?.hasBusinessCenterAccess"
                        [snapshotName]="snapshotName$ | async"
                      >
                        <app-pin-activate afterTitle id="reports"></app-pin-activate>
                      </app-reports-card>
                    </div>
                  </ng-container>

                  <ng-container *appPinnable="'tasks'">
                    <div>
                      <app-task-card
                        [partnerId]="partnerId"
                        [marketId]="marketId$ | async"
                        [accountGroupId]="businessId"
                        [salespersonId]="salesPersonId$ | async"
                      >
                        <app-pin-activate afterTitle id="tasks"></app-pin-activate>
                      </app-task-card>
                    </div>
                  </ng-container>

                  <ng-container *appPinnable="'notes'">
                    <div>
                      <app-notes-card
                        #notes
                        [businessId]="businessId"
                        [notes]="notes$ | async"
                        [saving]="isNotesSaving$ | async"
                        (submitEvent)="saveNotes($event)"
                      >
                        <app-pin-activate afterTitle id="notes"></app-pin-activate>
                      </app-notes-card>
                    </div>
                  </ng-container>

                  <ng-container *appPinnable="'contacts-card'">
                    <app-contacts-card
                      id="contacts-card-ref"
                      [accountGroupId]="businessId"
                      [countryCode]="countryCode$ | async"
                    >
                      <app-pin-activate afterTitle id="contacts-card"></app-pin-activate>
                    </app-contacts-card>
                  </ng-container>

                  <ng-container *appPinnable="'orders'">
                    <ng-container *ngTemplateOutlet="ordersTemplate"></ng-container>
                  </ng-container>

                  <ng-container *appPinnable="'localtime'">
                    <div>
                      <sales-ui-localtime-card
                        [accountGroupId]="businessId"
                        [editTimezoneUrl]="businessInfoEditUrl$ | async"
                      >
                        <app-pin-activate afterTitle id="localtime"></app-pin-activate>
                      </sales-ui-localtime-card>
                    </div>
                  </ng-container>

                  <ng-container *appPinnable="'general-info'">
                    <div>
                      <app-general-info-card
                        [customerIdentifiers]="customerIdentifiers$ | async"
                        [business]="businessInfo$ | async"
                        [tags]="businessTags$ | async"
                        [socialUrls]="socialMediaUrls$ | async"
                        [showMarkets]="true"
                        [marketName]="marketName$ | async"
                        [marketLoadFailed]="marketNameLoadFailed$ | async"
                        [marketLoading]="marketNameLoading$ | async"
                        (retryMarketName)="retryMarketName($event)"
                      >
                        <app-pin-activate afterTitle id="general-info"></app-pin-activate>
                      </app-general-info-card>
                    </div>
                  </ng-container>

                  <ng-container *appPinnable="'new-custom-fields'">
                    <div *ngIf="hasAccessToCustomFields$ | async">
                      <app-new-custom-fields-card [businessId]="businessId" [partnerId]="partnerId">
                        <app-pin-activate afterTitle id="new-custom-fields"></app-pin-activate>
                      </app-new-custom-fields-card>
                    </div>
                  </ng-container>

                  <ng-container *appPinnable="'enriched-data-fields'">
                    <div>
                      <app-enriched-data-fields *ngIf="customFieldFeature$ | async" [businessId]="businessId">
                        <app-pin-activate afterTitle id="enriched-data-fields"></app-pin-activate>
                      </app-enriched-data-fields>
                    </div>
                  </ng-container>

                  <ng-container *appPinnable="'utm-attributes'">
                    <div>
                      <app-utm-attributes-card *ngIf="inboundLeadAttributionFeature$ | async" [prospectId]="businessId">
                        <app-pin-activate afterTitle id="utm-attributes"></app-pin-activate>
                      </app-utm-attributes-card>
                    </div>
                  </ng-container>
                  <ng-container *appPinnable="'manual-automations'">
                    <div>
                      <app-manual-automations-card
                        [businessId]="businessId"
                        [partnerId]="partnerId"
                        [marketId]="marketId$ | async"
                        (startAutomation)="selectManualOperationAction()"
                      >
                        <app-pin-activate afterTitle id="manual-automations"></app-pin-activate>
                      </app-manual-automations-card>
                    </div>
                  </ng-container>
                </div>
              </div>
            </ng-container>
          </ng-template>
        </div>
        <ng-container drawer-content *ngIf="sidePanelState$ | async as sidePanelState">
          <div [ngSwitch]="sidePanelState.state" class="scroll-drawer-content">
            <div id="top-of-sidepanel"></div>
            <ng-container *ngIf="createOpportunityData$ | async as data">
              <app-create-opportunity-form
                #createOppFormOnAccountDetails
                [data]="data"
                *ngSwitchCase="SidePanelState.opportunityCreate"
                (saved)="handleSavedOpportunity($event)"
              ></app-create-opportunity-form>
            </ng-container>
            <ng-container *ngIf="businessInfo$ | async as businessInfo">
              <app-manual-automations-sidebar
                *ngSwitchCase="SidePanelState.manualAutomation"
                [business]="businessInfo"
              ></app-manual-automations-sidebar>
              <app-campaign-side-panel
                *ngSwitchCase="SidePanelState.campaignCreate"
                [accountGroupID]="businessId"
              ></app-campaign-side-panel>
            </ng-container>
          </div>
        </ng-container>
      </sales-ui-slide-out-panel>
    </ng-container>
  </app-side-drawer-container>
</glxy-page>
