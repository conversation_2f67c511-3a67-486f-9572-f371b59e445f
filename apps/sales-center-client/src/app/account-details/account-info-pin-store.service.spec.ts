import { firstValueFrom, of } from 'rxjs';
import {
  ALTERNATE_PINNED_CARD,
  AccountInfoPinStore,
  DEFAULT_PINNED_CARD,
  GetterSetter,
} from './account-info-pin-store.service';

describe('AccountInfoPinStore', () => {
  describe('pinnedId$', () => {
    let emptyStorage: GetterSetter;
    let fullStorage: GetterSetter;
    beforeEach(() => {
      emptyStorage = {
        get: () => null as never,
        set: () => {
          return;
        },
      };
      fullStorage = {
        get: () => 'found',
        set: () => {
          return;
        },
      };
    });

    const useDefaultLayout = true;
    describe('when partner uses alternate layout', () => {
      const useAlternateLayout = of(!useDefaultLayout);
      it('should emit the alternate default pinned item ID if nothing is in storage', async () => {
        const s = new AccountInfoPinStore(useAlternateLayout, emptyStorage);
        expect.assertions(1);
        const id = await firstValueFrom(s.pinnedIds$);
        expect(id).toEqual([ALTERNATE_PINNED_CARD]);
      });
      it('should emit the stored pinned item ID if found in storage', async () => {
        const s = new AccountInfoPinStore(useAlternateLayout, fullStorage);
        expect.assertions(1);
        const id = await firstValueFrom(s.pinnedIds$);
        expect(id).toEqual(['found']);
      });
    });
    describe('when partner uses standard layout', () => {
      it('should emit the standard default pinned item ID if nothing is in storage', async () => {
        const s = new AccountInfoPinStore(of(useDefaultLayout), emptyStorage);
        expect.assertions(1);
        const id = await firstValueFrom(s.pinnedIds$);
        expect(id).toEqual([DEFAULT_PINNED_CARD]);
      });
      it('should emit the stored pinned item ID if found in storage', async () => {
        const s = new AccountInfoPinStore(of(useDefaultLayout), fullStorage);
        expect.assertions(1);
        const id = await firstValueFrom(s.pinnedIds$);
        expect(id).toEqual(['found']);
      });
    });
  });
});
