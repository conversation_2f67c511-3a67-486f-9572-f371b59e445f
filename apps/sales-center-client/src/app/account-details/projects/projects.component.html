<ng-container *ngIf="projects$ | async as projects">
  <div class="expansion-panel-sizing">
    <mat-expansion-panel id="project-milestones" [expanded]="false">
      <mat-expansion-panel-header>
        <div class="panel-header-text">
          {{ 'ACCOUNT_DETAILS.FULFILLMENT_PROJECTS.TITLE' | translate }}
        </div>
      </mat-expansion-panel-header>
      <div class="row row-gutters project-container">
        <ng-container *ngFor="let project of projects">
          <div class="col col-xs-12 col-sm-6 col-md-4 project">
            <ng-container *ngIf="project | projectSubtask | async as subtasks">
              <ng-container *ngIf="project | productImages | async as images">
                <div class="project-milestone">
                  <uikit-project-milestones
                    [projectTitle]="project.title"
                    [subtasks]="subtasks"
                    [headerImageUrl]="images.banner"
                    [iconUrl]="images.icon"
                  ></uikit-project-milestones>
                </div>
              </ng-container>
            </ng-container>
          </div>
        </ng-container>
      </div>
    </mat-expansion-panel>
  </div>
</ng-container>
