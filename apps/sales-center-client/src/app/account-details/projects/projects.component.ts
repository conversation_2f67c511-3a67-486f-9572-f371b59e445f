import { Component, Input, OnInit, Pipe, PipeTransform } from '@angular/core';
import { GetMultiProductsResponse, ProductService } from '@vendasta/marketplace-packages';
import { DomainKeyValue, KeyValueTypes, TaskInterface } from '@vendasta/task';
import { of } from 'rxjs';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ProjectMilestonesService } from './projects.service';

@Component({
  selector: 'app-ssc-projects',
  templateUrl: './projects.component.html',
  styleUrls: ['./projects.component.scss'],
  standalone: false,
})
export class ProjectsComponent implements OnInit {
  @Input() partnerId: string;
  @Input() accountGroupId: string;
  projects$: Observable<TaskInterface[]> = null;

  constructor(private readonly projectMilestonesService: ProjectMilestonesService) {}

  ngOnInit(): void {
    this.projects$ = this.projectMilestonesService.getProjects(this.partnerId, this.accountGroupId);
  }
}

export interface ProductImages {
  icon: string;
  banner: string;
}

const noBannerImage =
  'https://vstatic-prod.apigateway.co/concierge-client/assets/images/project-milestones/no-banner.png';

@Pipe({
  name: 'productImages',
  standalone: false,
})
export class ProductImagesPipe implements PipeTransform {
  constructor(private readonly productService: ProductService) {}

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  transform(project: TaskInterface, args?: any): Observable<ProductImages> {
    const kv = DomainKeyValue.fromKeyValue(project.metadata);
    const productId: string = kv.get('ProductId', KeyValueTypes.STRING)[0] as string;
    if (!productId) {
      return of<ProductImages>({ icon: '', banner: noBannerImage });
    }
    const partnerId = project.identity.namespace.split('/')[1];
    return this.productService.getMultiProducts(partnerId, [productId]).pipe(
      map<GetMultiProductsResponse, ProductImages>((multiresp) => {
        return {
          icon: multiresp.products[0].product.iconUrl ? multiresp.products[0].product.iconUrl : null,
          banner: multiresp.products[0].product.headerImageUrl
            ? multiresp.products[0].product.headerImageUrl
            : noBannerImage,
        };
      }),
    );
  }
}

@Pipe({
  name: 'projectSubtask',
  standalone: false,
})
export class ProjectSubtaskPipe implements PipeTransform {
  constructor(private readonly projectMilestonesService: ProjectMilestonesService) {}

  transform(project: TaskInterface): Observable<TaskInterface[]> {
    return this.projectMilestonesService.getSubtasksForProject(project);
  }
}
