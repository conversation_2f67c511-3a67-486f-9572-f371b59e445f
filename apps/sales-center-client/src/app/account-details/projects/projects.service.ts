import { Injectable } from '@angular/core';
import { GetTasksRequestInterface, SearchTaskRequestInterface, TaskInterface, TaskSdkService } from '@vendasta/task';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

const DEFAULT_TASK_SEARCH_SIZE = 50;

@Injectable()
export class ProjectMilestonesService {
  constructor(private readonly taskService: TaskSdkService) {}

  private static filterToVisibleTasks(tasks: TaskInterface[]): TaskInterface[] {
    if (!tasks) {
      return tasks;
    }
    return tasks.filter((task) => !task.deleted);
  }

  getProjects(partnerId: string, accountGroupId: string): Observable<TaskInterface[]> {
    const namespace = 'partner/' + partnerId + '/account-group/' + accountGroupId;
    const req: SearchTaskRequestInterface = {
      pageSize: DEFAULT_TASK_SEARCH_SIZE,
      namespace: namespace,
      access: ['digital_agent'],
      types: ['Project'],
    };
    return this.taskService.search(req).pipe(map((resp) => resp.tasks));
  }

  getSubtasksForProject(project: TaskInterface): Observable<TaskInterface[]> {
    const req: GetTasksRequestInterface = { identities: project.subtasks };

    return this.taskService
      .getMulti(req)
      .pipe(map((resp) => ProjectMilestonesService.filterToVisibleTasks(resp?.tasks)));
  }
}
