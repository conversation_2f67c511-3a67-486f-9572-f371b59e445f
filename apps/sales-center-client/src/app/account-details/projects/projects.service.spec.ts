import { Observable } from 'rxjs';
import { TestScheduler } from 'rxjs/testing';
import { GetTasksRequestInterface, SearchTaskRequestInterface } from '@vendasta/task';
import { ProjectMilestonesService } from './projects.service';

let scheduler: TestScheduler;
const tasks = [{ deleted: null }, { deleted: new Date() }, { deleted: null }];

class TaskSdkServiceMock {
  searchedRequest: any;
  getMutliRequest: any;

  search(request: SearchTaskRequestInterface): Observable<any> {
    this.searchedRequest = request;
    return scheduler.createColdObservable('x', {
      x: { tasks: ['oneTask'] },
    });
  }

  getMulti(req: GetTasksRequestInterface): Observable<any> {
    this.getMutliRequest = req;
    return scheduler.createColdObservable('y', {
      y: { tasks: tasks },
    });
  }
}

describe('ProjectMilestonesService', () => {
  let service: ProjectMilestonesService;
  let mockTaskSdk: TaskSdkServiceMock;

  describe('getProjects', () => {
    beforeEach(() => {
      scheduler = new TestScheduler((a, b) => expect(a).toEqual(b));
      mockTaskSdk = new TaskSdkServiceMock();
    });

    afterEach(() => {
      scheduler.flush();
    });

    it('should search for the projects for the right business', function (): void {
      service = new ProjectMilestonesService(mockTaskSdk as any);
      const obv$ = service.getProjects('ABC', 'AG-123');
      scheduler.expectObservable(obv$).toBe('x', { x: ['oneTask'] });

      expect(mockTaskSdk.searchedRequest).toStrictEqual({
        pageSize: 50,
        namespace: 'partner/ABC/account-group/AG-123',
        access: ['digital_agent'],
        types: ['Project'],
      });
    });
  });

  describe('getSubtasksForProject', () => {
    beforeEach(() => {
      scheduler = new TestScheduler((a, b) => expect(a).toEqual(b));
      mockTaskSdk = new TaskSdkServiceMock();
    });

    afterEach(() => {
      scheduler.flush();
    });

    it('should return only the subtasks that have not been deleted of the project', function (): void {
      service = new ProjectMilestonesService(mockTaskSdk as any);
      const project = { subtasks: ['subtasks'] };

      const obv$ = service.getSubtasksForProject(project as any);
      scheduler.expectObservable(obv$).toBe('y', {
        y: [{ deleted: null }, { deleted: null }],
      });

      expect(mockTaskSdk.getMutliRequest).toStrictEqual({ identities: project.subtasks });
    });
  });
});
