@use 'design-tokens' as *;

.financialHealth {
  margin: 1px 0 5px 0;
  display: flex;
  flex-flow: row;
}

.title {
  color: $dark-gray;
  margin-right: 10px;
}

.container {
  display: grid;
  grid-template-columns: 5px;
}

.column1 {
  grid-column: 1;
}

.column2 {
  grid-column: 2;
}

.circle {
  font-size: smaller;
  display: inline;
}

.green {
  color: $green;
  display: inline;
}

.yellow {
  color: $warn-icon-color;
  display: inline;
}

.red {
  color: $dark-red;
  display: inline;
}

.state {
  display: inline;
  font-size: 14px;
  font-weight: 500;
  margin-left: 13px;
}

.substate {
  display: inline;
  margin-left: 5px;
  font-size: 13px;
  font-weight: 500;
  color: $gray;
}

.badge {
  display: block;
  margin: 6px 0 4px 0;
}

a {
  display: block;
  font-size: 14px;
  margin-left: 13px;
}
