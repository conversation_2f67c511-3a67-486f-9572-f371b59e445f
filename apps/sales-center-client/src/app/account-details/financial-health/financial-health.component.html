<div
  class="financialHealth"
  *ngIf="(accessAssociatedPartnerInfo$ | async) && currentFinancialHealth$ | async as currentFinancialHealth"
>
  <div class="title">Financial Health:</div>
  <div class="container">
    <div class="{{ currentFinancialHealth.stateColor }} column1">
      <mat-icon class="circle">fiber_manual_record</mat-icon>
    </div>
    <div class="column2">
      <div class="state">{{ currentFinancialHealth.state }}</div>
      <div class="substate">{{ currentFinancialHealth.subState }}</div>
      <va-badge class="badge" color="green" *ngIf="currentFinancialHealth.inRetention">In Retention</va-badge>
      <span *ngIf="associatedPartnerId$ | async as partnerId">
        <a href="{{ partnerCenterHost }}/superadmin/financial-health/{{ partnerId }}" target="_blank">View details</a>
      </span>
    </div>
  </div>
</div>
