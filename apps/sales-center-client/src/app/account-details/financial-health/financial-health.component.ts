import { Component, Inject, Input, OnInit } from '@angular/core';
import { Observable } from 'rxjs';
import { FinancialHealthService, PartnerFinancialHealth } from './financial-health.service';
import { ACCESS_ASSOCIATED_PARTNER_INFO_TOKEN } from '../../partner/partner-overrides';
import { AjaxBusinessApiService } from '../../business';
import { HostProvider, PartnerCenterHostService } from '../../core';
import { filter, switchMap } from 'rxjs/operators';

@Component({
  selector: 'app-financial-health',
  templateUrl: './financial-health.component.html',
  styleUrls: ['./financial-health.component.scss'],
  standalone: false,
})
export class FinancialHealthComponent implements OnInit {
  @Input() accountGroupId: string;
  @Input() associatedPartnerId$: Observable<string>;

  partnerCenterHost: string;
  currentFinancialHealth$: Observable<PartnerFinancialHealth>;

  constructor(
    private readonly financialHealthService: FinancialHealthService,
    private readonly businessService: AjaxBusinessApiService,
    @Inject(ACCESS_ASSOCIATED_PARTNER_INFO_TOKEN) public readonly accessAssociatedPartnerInfo$: Observable<boolean>,
    @Inject(PartnerCenterHostService) private readonly partnerCenterHostService: HostProvider,
  ) {}

  ngOnInit(): void {
    this.partnerCenterHost = this.partnerCenterHostService.host();
    this.currentFinancialHealth$ = this.associatedPartnerId$.pipe(
      filter((partnerId) => !!partnerId),
      switchMap((associatedPartnerId) => {
        return this.financialHealthService.getCurrentFinancialHealth(associatedPartnerId);
      }),
    );
  }
}
