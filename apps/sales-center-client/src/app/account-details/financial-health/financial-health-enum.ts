import { PartnerHealthState, PartnerHealthSubState } from '@galaxy/partner';

export const FinancialHealthState = {};
FinancialHealthState[PartnerHealthState.PARTNER_HEALTH_STATE_HEALTHY] = 'Healthy';
FinancialHealthState[PartnerHealthState.PARTNER_HEALTH_STATE_AT_RISK] = 'At Risk';
FinancialHealthState[PartnerHealthState.PARTNER_HEALTH_STATE_RESTRICTED] = 'Restricted';

export const FinancialHealthStateColor = {};
FinancialHealthStateColor[PartnerHealthState.PARTNER_HEALTH_STATE_HEALTHY] = 'green';
FinancialHealthStateColor[PartnerHealthState.PARTNER_HEALTH_STATE_AT_RISK] = 'yellow';
FinancialHealthStateColor[PartnerHealthState.PARTNER_HEALTH_STATE_RESTRICTED] = 'red';

export const FinancialHealthSubState = {};
FinancialHealthSubState[PartnerHealthSubState.PARTNER_HEALTH_SUB_STATE_UNSPECIFIED] = ''; // Don't show Unspecified substate
FinancialHealthSubState[PartnerHealthSubState.PARTNER_HEALTH_SUB_STATE_ACTIVE] = 'Active';
FinancialHealthSubState[PartnerHealthSubState.PARTNER_HEALTH_SUB_STATE_SUBSCRIPTION_OVERDUE] = 'Subscription Overdue';
FinancialHealthSubState[PartnerHealthSubState.PARTNER_HEALTH_SUB_STATE_SUBSCRIPTION_SUSPENDED] =
  'Subscription Suspended';
FinancialHealthSubState[PartnerHealthSubState.PARTNER_HEALTH_SUB_STATE_PAYMENT_PLAN] = 'Payment Plan';
