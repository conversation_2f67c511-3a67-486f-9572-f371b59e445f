import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  PartnerHealthsService,
  PartnerHealthInterface,
  PartnerHealthState,
  PartnerHealthSubState,
} from '@galaxy/partner';
import { FinancialHealthState, FinancialHealthStateColor, FinancialHealthSubState } from './financial-health-enum';

@Injectable()
export class FinancialHealthService {
  constructor(private readonly partnerHealthService: PartnerHealthsService) {}

  getCurrentFinancialHealth(partner_id: string): Observable<PartnerFinancialHealth> {
    return this.partnerHealthService
      .get(partner_id)
      .pipe(map((partnerHealth) => this.convertToPartnerFinancialHealth(partnerHealth)));
  }

  convertToPartnerFinancialHealth(partnerHealth: PartnerHealthInterface): PartnerFinancialHealth {
    const state = partnerHealth.state ? partnerHealth.state : 0;
    const subState = partnerHealth.subState ? partnerHealth.subState : 0;
    return {
      state: this.getStateDisplayName(state),
      stateColor: this.getStateColor(state),
      subState: this.getSubStateDisplayName(subState),
      inRetention: partnerHealth.inRetention,
    };
  }

  getStateDisplayName(partnerHealthState: PartnerHealthState): string {
    return FinancialHealthState[partnerHealthState];
  }

  getStateColor(partnerHealthState: PartnerHealthState): string {
    return FinancialHealthStateColor[partnerHealthState];
  }

  getSubStateDisplayName(partnerHealthSubState: PartnerHealthSubState): string {
    return FinancialHealthSubState[partnerHealthSubState];
  }
}

export interface PartnerFinancialHealth {
  state: string;
  stateColor: string;
  subState: string;
  inRetention: boolean;
}
