import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { AsyncTextModule } from '../async-text/async-text.module';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { EnrichedDataFieldsComponent } from './enriched-data-fields.component';
import { EnrichedDataFieldsDialogComponent } from './enriched-data-fields-dialog.component';
import { EnrichedDataFieldsService } from './enriched-data-fields.service';

@NgModule({
  providers: [EnrichedDataFieldsService],
  declarations: [EnrichedDataFieldsDialogComponent, EnrichedDataFieldsComponent],
  imports: [
    AsyncTextModule,
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatDividerModule,
    MatButtonModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    ReactiveFormsModule,
    TranslateModule,
    MatProgressSpinnerModule,
  ],
  exports: [EnrichedDataFieldsComponent],
})
export class EnrichedDataFieldsModule {}
