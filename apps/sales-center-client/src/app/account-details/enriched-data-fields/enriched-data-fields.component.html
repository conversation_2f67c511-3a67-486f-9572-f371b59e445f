<mat-card appearance="outlined">
  <mat-card-header class="mat-card-header-with-action">
    <mat-card-title>
      <span>{{ 'ENRICHED_FIELDS.TITLE.TITLE' | translate }}</span>
      <ng-content select="[afterTitle]"></ng-content>
    </mat-card-title>

    <button mat-icon-button color="primary" (click)="openEditDialog()">
      <mat-icon>edit</mat-icon>
    </button>
  </mat-card-header>

  <mat-card-content class="utm-attributes-list">
    <div *ngIf="accountGroup$ | async; else loading">
      <div class="utm-attributes-list__item">
        <ng-container *ngIf="numEmployees$ | async as numEmployees">
          <div class="enriched-field__key">
            {{ 'ENRICHED_FIELDS.NUM_EMPLOYEES' | translate }}
          </div>
          <div class="enriched-field__value">
            {{ numEmployees === UNSET_NUM_OF_EMPLOYEES ? '-' : numEmployees }}
          </div>
        </ng-container>
        <ng-container *ngIf="estimatedAnnualRevenue$ | async as revenue">
          <div class="enriched-field__key">
            {{ 'ENRICHED_FIELDS.BUSINESS_REVENUE' | translate }}
          </div>
          <div class="enriched-field__value">
            {{ revenue | translate }}
          </div>
        </ng-container>
      </div>
    </div>
  </mat-card-content>
</mat-card>

<ng-template #loading>
  <div class="enriched-field__key">
    {{ 'ENRICHED_FIELDS.NUM_EMPLOYEES' | translate }}
  </div>
  <div class="enriched-field__value">
    <div class="stencil-shimmer shimmer"></div>
  </div>
  <div class="enriched-field__key">
    {{ 'ENRICHED_FIELDS.BUSINESS_REVENUE' | translate }}
  </div>
  <div class="enriched-field__value">
    <div class="stencil-shimmer shimmer"></div>
  </div>
</ng-template>
