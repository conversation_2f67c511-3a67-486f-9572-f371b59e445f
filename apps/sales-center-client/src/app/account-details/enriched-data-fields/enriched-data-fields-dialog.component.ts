import { Component, Inject, <PERSON><PERSON><PERSON><PERSON>, OnInit, Optional } from '@angular/core';
import { AbstractControl, UntypedFormControl, UntypedFormGroup, ValidatorFn } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { EstimatedAnnualRevenue } from '@vendasta/account-group';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Subject, combineLatest } from 'rxjs';
import { take, takeUntil } from 'rxjs/operators';
import { BusinessRevenueOptions } from './enriched-data-fields.component';
import { EnrichedDataFieldsService } from './enriched-data-fields.service';

export interface EnrichedFieldDialogData {
  businessId: string;
}

const UPDATE_SUCCESS_TRANSLATION_KEY = 'ENRICHED_FIELDS.UPDATE_SUCCESS_MESSAGE';
const UPDATE_FAIL_TRANSLATION_KEY = 'ENRICHED_FIELDS.UPDATE_ERROR_MESSAGE';

export function positiveNumberValidator(): ValidatorFn {
  return (control: AbstractControl): { [key: string]: any } => {
    const isNotPositive = Number(control.value) <= 0;
    return isNotPositive ? { nonPositive: { value: control.value } } : null;
  };
}

/**
 * Modal for updating enriched fields on an account.
 *
 * This is NOT intended to be a standalone dialog component.
 * It is a sister-component with enriched-data-fields-card component
 * joined by the shared custom-fields-store service.
 */
@Component({
  selector: 'app-enriched-data-fields-dialog',
  templateUrl: './enriched-data-fields-dialog.component.html',
  styleUrls: ['./enriched-data-fields.component.scss'],
  standalone: false,
})
export class EnrichedDataFieldsDialogComponent implements OnInit, OnDestroy {
  saving$ = this.enrichedDataService.saving$;
  destroyed$$ = new Subject<void>();

  businessId: string;
  form = new UntypedFormGroup({
    numEmployees: new UntypedFormControl(1, positiveNumberValidator()),
    estimatedAnnualRevenue: new UntypedFormControl(
      EstimatedAnnualRevenue.ESTIMATED_ANNUAL_REVENUE_UNSPECIFIED.toString(),
    ),
  });
  EstimatedRevenueOptions = Object.entries(BusinessRevenueOptions).map(([value, label]) => {
    return { label, value };
  });

  constructor(
    public dialogRef: MatDialogRef<EnrichedDataFieldsDialogComponent>,
    private readonly enrichedDataService: EnrichedDataFieldsService,
    @Inject(MAT_DIALOG_DATA) private readonly data: EnrichedFieldDialogData,
    private readonly translate: TranslateService,
    @Optional() private readonly alerts?: SnackbarService,
  ) {}

  ngOnInit(): void {
    this.businessId = this.data.businessId;
    combineLatest([this.enrichedDataService.numEmployees$, this.enrichedDataService.estimatedAnnualRevenue$])
      .pipe(takeUntil(this.destroyed$$))
      .subscribe(([numEmployees, estimatedAnnualRevenue]) => {
        this.form.get('numEmployees').patchValue(numEmployees || 1);
        this.form.get('estimatedAnnualRevenue').patchValue(estimatedAnnualRevenue);
      });
  }

  ngOnDestroy(): void {
    this.destroyed$$.next();
    this.destroyed$$.unsubscribe();
  }

  public onSave(): void {
    if (this.form.dirty && !this.form.invalid) {
      this.enrichedDataService
        .updateAdditionalCompanyInfo({
          estimatedAnnualRevenue: Number(this.form.get('estimatedAnnualRevenue').value),
          numEmployees: this.form.get('numEmployees').value,
        })
        .pipe(take(1))
        .subscribe(
          () => {
            this.alerts.openSuccessSnack(UPDATE_SUCCESS_TRANSLATION_KEY);
            this.dialogRef.close(true);
          },
          () => {
            this.alerts.openErrorSnack(UPDATE_FAIL_TRANSLATION_KEY);
          },
        );
    }
  }
}
