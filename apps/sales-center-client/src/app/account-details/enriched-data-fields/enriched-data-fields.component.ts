import { Component, Input, OnInit } from '@angular/core';
import { EstimatedAnnualRevenue } from '@vendasta/account-group';
import { EnrichedDataFieldsService } from './enriched-data-fields.service';
import { map, take } from 'rxjs/operators';
import { EnrichedDataFieldsDialogComponent } from './enriched-data-fields-dialog.component';
import { MatDialog } from '@angular/material/dialog';

export const BusinessRevenueOptions = {
  [EstimatedAnnualRevenue.ESTIMATED_ANNUAL_REVENUE_UNSPECIFIED.toString()]:
    'ENRICHED_FIELDS.BUSINESS_REVENUE_OPTIONS.ESTIMATED_ANNUAL_REVENUE_UNSPECIFIED',
  [EstimatedAnnualRevenue.ESTIMATED_ANNUAL_REVENUE_0_1M.toString()]:
    'ENRICHED_FIELDS.BUSINESS_REVENUE_OPTIONS.ESTIMATED_ANNUAL_REVENUE_0_1M',
  [EstimatedAnnualRevenue.ESTIMATED_ANNUAL_REVENUE_1M_10M.toString()]:
    'ENRICHED_FIELDS.BUSINESS_REVENUE_OPTIONS.ESTIMATED_ANNUAL_REVENUE_1M_10M',
  [EstimatedAnnualRevenue.ESTIMATED_ANNUAL_REVENUE_10M_50M.toString()]:
    'ENRICHED_FIELDS.BUSINESS_REVENUE_OPTIONS.ESTIMATED_ANNUAL_REVENUE_10M_50M',
  [EstimatedAnnualRevenue.ESTIMATED_ANNUAL_REVENUE_50M_100M.toString()]:
    'ENRICHED_FIELDS.BUSINESS_REVENUE_OPTIONS.ESTIMATED_ANNUAL_REVENUE_50M_100M',
  [EstimatedAnnualRevenue.ESTIMATED_ANNUAL_REVENUE_100M_250M.toString()]:
    'ENRICHED_FIELDS.BUSINESS_REVENUE_OPTIONS.ESTIMATED_ANNUAL_REVENUE_100M_250M',
  [EstimatedAnnualRevenue.ESTIMATED_ANNUAL_REVENUE_250M_500M.toString()]:
    'ENRICHED_FIELDS.BUSINESS_REVENUE_OPTIONS.ESTIMATED_ANNUAL_REVENUE_250M_500M',
  [EstimatedAnnualRevenue.ESTIMATED_ANNUAL_REVENUE_500M_1B.toString()]:
    'ENRICHED_FIELDS.BUSINESS_REVENUE_OPTIONS.ESTIMATED_ANNUAL_REVENUE_500M_1B',
  [EstimatedAnnualRevenue.ESTIMATED_ANNUAL_REVENUE_1B_10B.toString()]:
    'ENRICHED_FIELDS.BUSINESS_REVENUE_OPTIONS.ESTIMATED_ANNUAL_REVENUE_1B_10B',
  [EstimatedAnnualRevenue.ESTIMATED_ANNUAL_REVENUE_10B_OR_MORE.toString()]:
    'ENRICHED_FIELDS.BUSINESS_REVENUE_OPTIONS.ESTIMATED_ANNUAL_REVENUE_10B_OR_MORE',
};

const UNSET_NUM_OF_EMPLOYEES = 'UNSET';

@Component({
  selector: 'app-enriched-data-fields',
  templateUrl: './enriched-data-fields.component.html',
  styleUrls: ['./enriched-data-fields.component.scss'],
  standalone: false,
})
export class EnrichedDataFieldsComponent implements OnInit {
  @Input() businessId: string;

  accountGroup$ = this.enrichedDataService.accountGroup$;

  UNSET_NUM_OF_EMPLOYEES = UNSET_NUM_OF_EMPLOYEES;
  numEmployees$ = this.enrichedDataService.numEmployees$.pipe(
    map((numEmployees) => (numEmployees > 0 ? numEmployees : UNSET_NUM_OF_EMPLOYEES)),
  );
  estimatedAnnualRevenue$ = this.enrichedDataService.estimatedAnnualRevenue$.pipe(
    map((revenue) => BusinessRevenueOptions[revenue]),
  );

  constructor(
    private readonly enrichedDataService: EnrichedDataFieldsService,
    private readonly dialog: MatDialog,
  ) {}

  ngOnInit(): void {
    this.enrichedDataService.init(this.businessId);
  }

  openEditDialog(): void {
    const data = {
      businessId: this.businessId,
    };
    const dialogRef = this.dialog.open(EnrichedDataFieldsDialogComponent, { width: '400px', data });
    dialogRef.afterClosed().pipe(take(1)).subscribe();
  }
}
