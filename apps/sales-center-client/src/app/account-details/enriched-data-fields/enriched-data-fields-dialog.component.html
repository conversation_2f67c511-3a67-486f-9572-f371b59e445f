<mat-dialog-content *ngIf="form">
  <form [formGroup]="form">
    <mat-form-field class="form-field">
      <mat-label>{{ 'ENRICHED_FIELDS.NUM_EMPLOYEES' | translate }}</mat-label>
      <input type="number" min="1" matInput formControlName="numEmployees" [id]="'numEmployees'" />
      <mat-error *ngIf="form.get('numEmployees').invalid">
        {{ 'ENRICHED_FIELDS.INVALID_NUM_EMPLOYEES' | translate }}
      </mat-error>
    </mat-form-field>

    <mat-form-field class="form-field">
      <mat-label>{{ 'ENRICHED_FIELDS.BUSINESS_REVENUE' | translate }}</mat-label>
      <mat-select [formControl]="form.get('estimatedAnnualRevenue')" [id]="'estimatedAnnualRevenue'">
        <mat-option *ngFor="let option of EstimatedRevenueOptions" [value]="option.value">
          {{ option.label | translate }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </form>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-button matDialogClose color="primary" [disabled]="saving$ | async">
    {{ 'COMMON.ACTION_LABELS.CANCEL' | translate }}
  </button>
  <ng-container *ngIf="saving$ | async; else submitButton">
    <mat-spinner class="button-spinner" [diameter]="25" [strokeWidth]="3"></mat-spinner>
  </ng-container>
  <ng-template #submitButton>
    <button mat-raised-button color="primary" [disabled]="form?.pristine" (click)="onSave()">
      {{ 'COMMON.ACTION_LABELS.SAVE' | translate }}
    </button>
  </ng-template>
</mat-dialog-actions>
