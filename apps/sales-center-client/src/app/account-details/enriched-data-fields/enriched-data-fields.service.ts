import { HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  AccountGroup,
  AccountGroupApiService,
  AdditionalCompanyInfoInterface,
  EstimatedAnnualRevenue,
} from '@vendasta/account-group';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { catchError, map, switchMap, take, tap } from 'rxjs/operators';

@Injectable()
export class EnrichedDataFieldsService {
  private businessId: string;
  private accountGroup$$: BehaviorSubject<AccountGroup> = new BehaviorSubject(null);
  accountGroup$: Observable<AccountGroup> = this.accountGroup$$.asObservable();

  numEmployees$ = this.accountGroup$.pipe(
    map((accountGroup) => accountGroup?.additionalCompanyInfo?.numEmployees || 0),
  );
  estimatedAnnualRevenue$ = this.accountGroup$.pipe(
    map(
      (accountGroup) =>
        accountGroup?.additionalCompanyInfo?.estimatedAnnualRevenue?.toString() ||
        EstimatedAnnualRevenue.ESTIMATED_ANNUAL_REVENUE_UNSPECIFIED.toString(),
    ),
  );

  private saving$$: BehaviorSubject<boolean> = new BehaviorSubject(false);
  saving$: Observable<boolean> = this.saving$$.asObservable();

  constructor(private readonly accountGroupService: AccountGroupApiService) {}

  init(businessId: string): void {
    this.businessId = businessId;
    this.fetchAccountGroup();
  }

  fetchAccountGroup(): void {
    this.accountGroupService
      .getMulti({
        accountGroupIds: [this.businessId],
        projectionFilter: {
          additionalCompanyInfo: true,
        },
      })
      .pipe(take(1))
      .subscribe((response) => {
        if (response?.accountGroups?.length > 0 && response.accountGroups[0]?.accountGroup !== null) {
          this.accountGroup$$.next(response.accountGroups[0].accountGroup);
        }
      });
  }

  updateAdditionalCompanyInfo(additionalCompanyInfo: AdditionalCompanyInfoInterface): Observable<HttpResponse<null>> {
    this.saving$$.next(true);
    return this.accountGroup$.pipe(
      take(1),
      switchMap(() =>
        this.accountGroupService.bulkUpdate({
          accountGroupId: this.businessId,
          updateOperations: [
            {
              additionalCompanyInfo,
              fieldMask: {
                paths: ['numEmployees', 'estimatedAnnualRevenue'],
              },
            },
          ],
        }),
      ),
      tap(() => {
        this.saving$$.next(false);
        this.fetchAccountGroup();
      }),
      catchError((err) => {
        this.saving$$.next(false);
        return throwError(err);
      }),
    );
  }
}
