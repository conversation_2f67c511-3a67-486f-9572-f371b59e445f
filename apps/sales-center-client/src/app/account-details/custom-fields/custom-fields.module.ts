import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { CustomFieldsCardComponent } from './custom-fields-card.component';
import { CustomFieldsDialogComponent } from './custom-fields-dialog.component';
import { CustomFieldsStoreService } from './custom-fields-store.service';
import { CustomFieldsApiService } from '../../custom-fields';
import { AsyncTextModule } from '../async-text/async-text.module';
import { CustomFieldItemComponent } from './custom-field-item.component';
import { ReactiveFormsModule } from '@angular/forms';
import { EmptyStateModule } from '@vendasta/uikit';
import { TranslateModule } from '@ngx-translate/core';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@NgModule({
  providers: [CustomFieldsStoreService, CustomFieldsApiService],
  declarations: [CustomFieldsDialogComponent, CustomFieldsCardComponent, CustomFieldItemComponent],
  imports: [
    AsyncTextModule,
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatDividerModule,
    MatButtonModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    ReactiveFormsModule,
    EmptyStateModule,
    TranslateModule,
    MatProgressSpinnerModule,
  ],
  exports: [CustomFieldsCardComponent],
})
export class CustomFieldsModule {}
