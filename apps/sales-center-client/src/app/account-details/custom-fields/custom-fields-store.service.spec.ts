import { Observable, of, throwError } from 'rxjs';
import { TestScheduler } from 'rxjs/testing';
import { BusinessApiService, BusinessCustomField, BusinessResponse } from '../../business';
import { UpdateIsReadResponse, UpdateNotesResponse } from '../../business/business.service';
import { CustomField, GetResponse as CustomFieldsResponse, CustomFieldsService } from '../../custom-fields';
import { CustomFieldsStoreService } from './custom-fields-store.service';

let sched: TestScheduler;

const mockCFApiResponse: CustomFieldsResponse = {
  customFields: [new CustomField('f1', 'select', 'Field One')],
};
const mockBusinessCFResponse: BusinessCustomField[] = [new BusinessCustomField({ name: 'f1', value: 'value' })];

class MockCustomFieldsApiService implements CustomFieldsService {
  response: CustomFieldsResponse;
  happy: boolean;

  constructor(happy = true, response: CustomFieldsResponse = mockCFApiResponse) {
    this.happy = happy;
    this.response = response;
  }

  // FIXME: figure out how to schedule promises
  getPartnerMarketCustomFields = () =>
    this.happy ? Promise.resolve(mockCFApiResponse) : Promise.reject(new Error('fail'));
}
class MockBusinessService implements BusinessApiService {
  cfResponse: BusinessCustomField[];
  getCFHappy: boolean;
  updateCFHappy: boolean;

  constructor(getHappy = true, response: BusinessCustomField[] = mockBusinessCFResponse, updateHappy = true) {
    this.getCFHappy = getHappy;
    this.cfResponse = response;
    this.updateCFHappy = updateHappy;
  }

  getBusiness = () => of({} as BusinessResponse); // only to satisfy interface
  updateNotes = () => of({} as UpdateNotesResponse); // only to satisfy interface
  // FIXME: figure out how to schedule promises
  getCustomFields = () => (this.getCFHappy ? of(mockBusinessCFResponse) : throwError(new Error('fail')));
  updateCustomFields = () => (this.updateCFHappy ? of(null) : throwError(new Error('fail')));

  updateIsRead(): Observable<UpdateIsReadResponse> {
    return undefined;
  }
}

describe('CustomFieldsStoreService', () => {
  let service: CustomFieldsStoreService;
  let apiCFSerMock: MockCustomFieldsApiService;
  let businessApiMock: MockBusinessService;

  describe('fetchCustomFields', () => {
    beforeEach(() => {
      sched = new TestScheduler((a, b) => expect(a).toEqual(b));
    });
    afterEach(() => {
      sched.flush();
    });

    it('should fetch custom fields if successful', () => {
      apiCFSerMock = new MockCustomFieldsApiService();
      businessApiMock = new MockBusinessService();
      service = new CustomFieldsStoreService(null, apiCFSerMock, businessApiMock);

      const actual$ = service.fetchCustomFields('AG-123', 'ABC', 'm1');

      const expected = [new CustomField('f1', 'select', 'Field One', undefined, 'value')];
      sched.expectObservable(actual$).toBe('x', { x: expected });
    });

    it('should not fetch custom fields if fails to get custom fields', () => {
      apiCFSerMock = new MockCustomFieldsApiService(false);
      businessApiMock = new MockBusinessService();
      service = new CustomFieldsStoreService(null, apiCFSerMock, businessApiMock);

      const actual$ = service.fetchCustomFields('AG-123', 'ABC', 'm1');

      sched.expectObservable(actual$).toBe('');
    });

    it('should not fetch custom fields if fails to get business custom fields', () => {
      apiCFSerMock = new MockCustomFieldsApiService();
      businessApiMock = new MockBusinessService(false);
      service = new CustomFieldsStoreService(null, apiCFSerMock, businessApiMock);

      const actual$ = service.fetchCustomFields('AG-123', 'ABC', 'm1');

      sched.expectObservable(actual$).toBe('');
    });
  });

  describe('customFieldsFailed$', () => {
    beforeEach(() => {
      sched = new TestScheduler((a, b) => expect(a).toEqual(b));
    });
    afterEach(() => {
      sched.flush();
    });

    it('should emit false if successful fetching custom fields', () => {
      apiCFSerMock = new MockCustomFieldsApiService();
      businessApiMock = new MockBusinessService();
      service = new CustomFieldsStoreService(null, apiCFSerMock, businessApiMock);
      service.fetchCustomFields('AG-123', 'ABC', 'm1');

      const actual$ = service.customFieldsFailed$('AG-123');

      const expected = false;
      sched.expectObservable(actual$).toBe('x', { x: expected });
    });

    it('should emit true if failed to fetch custom fields', () => {
      apiCFSerMock = new MockCustomFieldsApiService(false);
      businessApiMock = new MockBusinessService();
      service = new CustomFieldsStoreService(null, apiCFSerMock, businessApiMock);
      service.fetchCustomFields('AG-123', 'ABC', 'm1');

      const actual$ = service.customFieldsFailed$('AG-123');

      const expected = true;
      sched.expectObservable(actual$).toBe('x', { x: expected });
    });

    it('should not emit if have not fetched custom fields', () => {
      apiCFSerMock = new MockCustomFieldsApiService();
      businessApiMock = new MockBusinessService();
      service = new CustomFieldsStoreService(null, apiCFSerMock, businessApiMock);

      const actual$ = service.customFieldsFailed$('AG-123');

      sched.expectObservable(actual$).toBe('');
    });
  });

  describe('isLoadingCustomFields$', () => {
    beforeEach(() => {
      sched = new TestScheduler((a, b) => expect(a).toEqual(b));
    });
    afterEach(() => {
      sched.flush();
    });

    it('should emit true if while successfully fetching custom fields then false', () => {
      apiCFSerMock = new MockCustomFieldsApiService();
      businessApiMock = new MockBusinessService();
      service = new CustomFieldsStoreService(null, apiCFSerMock, businessApiMock);
      service.fetchCustomFields('AG-123', 'ABC', 'm1');

      sched.expectObservable(service.isLoadingCustomFields$('AG-123')).toBe('x', { x: false });
    });

    it('should emit true if while unsuccessfully fetching custom fields then false', () => {
      apiCFSerMock = new MockCustomFieldsApiService(false);
      businessApiMock = new MockBusinessService();
      service = new CustomFieldsStoreService(null, apiCFSerMock, businessApiMock);
      service.fetchCustomFields('AG-123', 'ABC', 'm1');

      const actual$ = service.isLoadingCustomFields$('AG-123');

      // FIXME: should be asserting the marbles looks like '(ft)f' or 'ftf' or something like this.
      // Need to figure out how to schedule the promises successfully.
      sched.expectObservable(actual$).toBe('f', { t: true, f: false });
    });

    it('should emit false if have not fetched custom fields', () => {
      apiCFSerMock = new MockCustomFieldsApiService();
      businessApiMock = new MockBusinessService();
      service = new CustomFieldsStoreService(null, apiCFSerMock, businessApiMock);

      const actual$ = service.isLoadingCustomFields$('AG-123');

      sched.expectObservable(actual$).toBe('f', { f: false });
    });
  });
});
