import { Inject, Injectable } from '@angular/core';
import { ManualStartWorkStateMap, ObservableWorkStateMap } from '@vendasta/rx-utils/work-state';
import {
  CustomField,
  CustomFieldsApiService,
  CustomFieldsService,
  GetResponse as GetCustomFieldsResponse,
} from '../../custom-fields';
import { AjaxBusinessApiService, BusinessApiService, BusinessCustomField } from '../../business';
import { combineLatest, from, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { PardotCustomFields } from './custom-fields-dialog.component';

@Injectable()
export class CustomFieldsStoreService {
  private readonly customFieldsGetState = new ManualStartWorkStateMap<string, CustomField[]>();
  private readonly customFieldsUpdateState = new ObservableWorkStateMap<string, null>();
  private readonly pardotFieldsStateMap = new ObservableWorkStateMap<string, PardotCustomFields>();

  constructor(
    private readonly http: HttpClient,
    @Inject(CustomFieldsApiService) private readonly api: CustomFieldsService,
    @Inject(AjaxBusinessApiService) private readonly businessApi: BusinessApiService,
  ) {}

  public fetchCustomFields(businessId: string, partnerId: string, marketId: string): Observable<CustomField[]> {
    this.customFieldsGetState.startWork(businessId, (success, fail) => {
      this.fetchCustomFieldsWork(businessId, partnerId, marketId).subscribe(success, fail);
    });
    return this.customFields$(businessId);
  }

  private fetchCustomFieldsWork(businessId: string, partnerId: string, marketId: string): Observable<CustomField[]> {
    const partnerMarketCustomFields$: Observable<CustomField[]> = this.fetchPartnerMarketCustomFields(
      partnerId,
      marketId,
    );
    const businessCustomFields$: Observable<Map<string, BusinessCustomField>> =
      this.fetchBusinessCustomFields(businessId);
    return combineLatest([partnerMarketCustomFields$, businessCustomFields$]).pipe(
      map(([pmCFs, bizCFs]: [CustomField[], Map<string, BusinessCustomField>]): CustomField[] => {
        return this.mergePartnerMarketAndBusinessCustomFields(pmCFs, bizCFs);
      }),
    );
  }

  private mergePartnerMarketAndBusinessCustomFields(
    availableMarketFields: CustomField[],
    businessSetFieldsMap: Map<string, BusinessCustomField>,
  ): CustomField[] {
    const bizCustomFields = [];
    let found: BusinessCustomField;
    availableMarketFields.forEach((cf) => {
      found = businessSetFieldsMap.get(cf.name);
      if (found) {
        cf.setValue(found.value);
      }
      bizCustomFields.push(new CustomField(cf.name, cf.fieldType, cf.label, cf.options, cf.value));
    });
    return bizCustomFields;
  }

  private fetchPartnerMarketCustomFields(partnerId: string, marketId: string): Observable<CustomField[]> {
    const req = { partnerId: partnerId, marketId: marketId };
    const promise$ = this.api.getPartnerMarketCustomFields(req);
    return from(promise$).pipe(
      map((resp: GetCustomFieldsResponse): CustomField[] => {
        return resp.customFields;
      }),
    );
  }

  private fetchBusinessCustomFields(businessId: string): Observable<Map<string, BusinessCustomField>> {
    const promise$ = this.businessApi.getCustomFields(businessId);
    return promise$.pipe(
      map((fields: BusinessCustomField[]): Map<string, BusinessCustomField> => {
        const customFieldsMap = new Map<string, BusinessCustomField>();
        fields.forEach((f) => customFieldsMap.set(f.name, f));
        return customFieldsMap;
      }),
    );
  }

  customFields$(businessId: string): Observable<CustomField[]> {
    return this.customFieldsGetState.getWorkResults$(businessId);
  }

  public isLoadingCustomFields$(businessId: string): Observable<boolean> {
    return this.customFieldsGetState.isLoading$(businessId);
  }

  public customFieldsFailed$(businessId: string): Observable<boolean> {
    return this.customFieldsGetState.isSuccess$(businessId).pipe(map((v) => !v));
  }

  public updateCustomFields(
    businessId: string,
    partnerId: string,
    marketId: string,
    customFields: CustomField[],
  ): void {
    const fields: BusinessCustomField[] = customFields.map(
      (f) => new BusinessCustomField({ name: f.name, value: f.value }),
    );
    this.customFieldsUpdateState.startWork(businessId, this.businessApi.updateCustomFields(businessId, fields));
  }

  public isSavingCustomFields$(businessId: string): Observable<boolean> {
    return this.customFieldsUpdateState.isLoading$(businessId);
  }

  public updateCustomFieldsSuccessEvent$(businessId: string): Observable<boolean> {
    return this.customFieldsUpdateState.successEvent$(businessId);
  }

  public getPardotFields$(accountGroupId: string, pardot: string): Observable<PardotCustomFields> {
    this.pardotFieldsStateMap.startWork(accountGroupId, this.getPardotInformation$(accountGroupId, pardot));
    return this.pardotFieldsStateMap.getWorkResults$(accountGroupId);
  }

  public pardotFieldsSuccess$(accountGroupId: string): Observable<boolean> {
    return this.pardotFieldsStateMap.successEvent$(accountGroupId);
  }

  private getPardotInformation$(accountGroupId: string, pardot: string): Observable<PardotCustomFields> {
    return this.http
      .post(
        '/internalApi/v1/accounts/infer-pardot-information/',
        {
          accountGroupId: accountGroupId,
          pardot: pardot,
        },
        { withCredentials: true },
      )
      .pipe(
        map((res: any) => {
          return res.data;
        }),
      );
  }
}
