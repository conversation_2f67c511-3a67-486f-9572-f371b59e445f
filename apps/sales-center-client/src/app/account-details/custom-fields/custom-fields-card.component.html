<mat-card appearance="outlined">
  <mat-card-header class="mat-card-header-with-action">
    <mat-card-title>
      <span>{{ 'CUSTOM_FIELDS.TITLE' | translate }}</span>
      <ng-content select="[afterTitle]"></ng-content>
    </mat-card-title>
    <button mat-icon-button color="primary" (click)="openCustomFieldsDialog()">
      <mat-icon>edit</mat-icon>
    </button>
  </mat-card-header>

  <mat-card-content class="custom-fields-list">
    <!-- Loading/Error State -->
    <app-async-text
      [loadFailed]="businessFieldsFailed$ | async"
      [loading]="loadingBusinessFields$ | async"
      (retry)="retryFetchingCustomFields()"
    ></app-async-text>

    <!-- Content State -->
    <div
      *ngIf="
        (businessFieldsFailed$ | async) === false &&
        (loadingBusinessFields$ | async) === false &&
        (businessFields$ | async) as fields
      "
    >
      <ng-container *ngIf="fields.length === 0">
        <div class="fade-in" class="zeroState">
          <uikit-empty-state iconName="format_list_bulleted">
            <span state-description>
              {{ 'CUSTOM_FIELDS.EMPTY_CUSTOM_FIELDS_DESCRIPTION' | translate }}
            </span>
          </uikit-empty-state>
        </div>
      </ng-container>
      <ng-container *ngFor="let field of fields">
        <div class="custom-fields-list__item">
          <app-custom-field-item [field]="field"></app-custom-field-item>
        </div>
      </ng-container>
    </div>
  </mat-card-content>

  <ng-container
    *ngIf="(displayAllCustomFields$ | async) === false && (totalNumOfFields$ | async) > defaultNumOfFieldsToDisplay"
  >
    <mat-card-actions align="end">
      <button mat-button color="primary" (click)="showAllCustomFields(true)">
        {{ 'COMMON.ACTION_LABELS.VIEW_ALL' | translate }}
      </button>
    </mat-card-actions>
  </ng-container>
</mat-card>
