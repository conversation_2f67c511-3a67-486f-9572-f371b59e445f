<div class="custom-field">
  <div class="custom-field__label">{{ field?.label }}</div>
  <div class="custom-field__value">
    <ng-container
      *ngTemplateOutlet="fieldVal; context: { $implicit: field?.fieldType === 'url', value: field?.value }"
    ></ng-container>
  </div>
</div>

<ng-template #fieldVal let-isUrl let-value="value">
  <a [href]="value" target="_newWindow" *ngIf="isUrl">{{ value }}</a>
  <ng-container *ngIf="!isUrl">
    <div>{{ value || '-' }}</div>
  </ng-container>
</ng-template>
