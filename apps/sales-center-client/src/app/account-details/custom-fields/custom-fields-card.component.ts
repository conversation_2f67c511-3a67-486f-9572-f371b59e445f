import { Component, HostListener, Input, OnInit } from '@angular/core';
import { CustomFieldsStoreService } from './custom-fields-store.service';
import { MatDialog } from '@angular/material/dialog';
import { BehaviorSubject, combineLatest, Observable } from 'rxjs';
import { CustomFieldsDialogComponent } from './custom-fields-dialog.component';
import { CustomField } from '../../custom-fields';
import { filter, map, take } from 'rxjs/operators';

const DEFAULT_DISPLAY_NUM_OF_CUSTOM_FIELDS = 3;
const HIDDEN_CUSTOM_FIELDS = [
  'partner_id',
  'goal',
  'main_goal',
  'training_priorities',
  'early_wins',
  'lead_type',
  'pardot_lead_created_date',
  'pardot_prospect_url',
  'referral_pid',
  'vbp_test_lead_source',
  'estimated_vendasta_arr',
  'persona',
  'blog_title',
  'num_employees',
  'company_annual_revenue',
];

@Component({
  selector: 'app-custom-fields-card',
  templateUrl: './custom-fields-card.component.html',
  styleUrls: ['./custom-fields-card.component.scss'],
  standalone: false,
})
export class CustomFieldsCardComponent implements OnInit {
  @Input() businessId: string;
  @Input() partnerId: string;
  @Input() marketId: string;
  public readonly defaultNumOfFieldsToDisplay = DEFAULT_DISPLAY_NUM_OF_CUSTOM_FIELDS;
  public businessFields$: Observable<CustomField[]>;
  public loadingBusinessFields$: Observable<boolean>;
  public businessFieldsFailed$: Observable<boolean>;
  public displayAllCustomFields$: Observable<boolean>;
  private readonly displayAllCustomFields$$: BehaviorSubject<boolean> = new BehaviorSubject(false);
  public totalNumOfFields$: Observable<number>;

  constructor(
    private readonly dialog: MatDialog,
    private readonly store: CustomFieldsStoreService,
  ) {}

  ngOnInit(): void {
    this.displayAllCustomFields$ = this.displayAllCustomFields$$.asObservable();
    this.businessFields$ = this.buildDisplayBusinessFields$();
    this.loadingBusinessFields$ = this.store.isLoadingCustomFields$(this.businessId);
    this.businessFieldsFailed$ = this.store.customFieldsFailed$(this.businessId);
  }

  private buildDisplayBusinessFields$(): Observable<CustomField[]> {
    const allCustomFields$ = this.store
      .fetchCustomFields(this.businessId, this.partnerId, this.marketId)
      .pipe(map((customFields) => customFields.filter((cf) => HIDDEN_CUSTOM_FIELDS.indexOf(cf.name) === -1)));
    this.totalNumOfFields$ = allCustomFields$.pipe(map((allFields) => allFields.length));
    return combineLatest([allCustomFields$, this.displayAllCustomFields$]).pipe(
      map(([allFields, shouldShowAll]: [CustomField[], boolean]): CustomField[] => {
        if (shouldShowAll || allFields.length < DEFAULT_DISPLAY_NUM_OF_CUSTOM_FIELDS) {
          return allFields;
        }
        return allFields.slice(0, DEFAULT_DISPLAY_NUM_OF_CUSTOM_FIELDS);
      }),
    );
  }

  public openCustomFieldsDialog(): void {
    const data = {
      businessId: this.businessId,
      partnerId: this.partnerId,
      marketId: this.marketId,
      customFields$: this.store.customFields$(this.businessId),
    };
    const dialogRef = this.dialog.open(CustomFieldsDialogComponent, { width: '400px', data });
    dialogRef
      .afterClosed()
      .pipe(
        filter((fetchMore) => fetchMore),
        take(1),
      )
      .subscribe(() => this.store.fetchCustomFields(this.businessId, this.partnerId, this.marketId));
  }

  public showAllCustomFields(displayAll: boolean): void {
    this.displayAllCustomFields$$.next(displayAll);
  }

  public retryFetchingCustomFields(): void {
    this.businessFields$ = this.buildDisplayBusinessFields$();
  }

  @HostListener('window:keydown', ['$event'])
  showOmniSearch(event: any): void {
    const tag = event.target.tagName.toLowerCase();
    if (
      event.key === 'c' &&
      tag !== 'input' &&
      tag !== 'textarea' &&
      tag !== 'span' &&
      !event.ctrlKey &&
      !event.metaKey &&
      !event.shiftKey &&
      !event.altKey
    ) {
      if (!this.dialog.openDialogs || !this.dialog.openDialogs.length) {
        this.openCustomFieldsDialog();
      }
    }
  }
}
