import { Component, Inject, On<PERSON><PERSON>roy, OnInit, Optional } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { SubscriptionList } from '@vendasta/rx-utils';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { CustomField } from '../../custom-fields';
import { CustomFieldsStoreService } from './custom-fields-store.service';

export interface CustomFieldDialogData {
  businessId: string;
  partnerId: string;
  marketId: string;
  customFields$: Observable<CustomField[]>;
}

export interface PardotCustomFields {
  cohort?: string;
  leadConversionPoint?: string;
  leadSource?: string;
  leadDescription?: string;
  inboundOutbound?: string;
  adName?: string;
  blogTitle?: string;
}

const UPDATE_SUCCESS_TRANSLATION_KEY = 'CUSTOM_FIELDS.UPDATE_SUCCESS_MESSAGE';
const UPDATE_FAIL_TRANSLATION_KEY = 'CUSTOM_FIELDS.UPDATE_UNSUCCESSFUL_MESSAGE';
const UPDATE_PARDOT_SUCCESS_TRANSLATION_KEY = 'CUSTOM_FIELDS.UPDATE_FROM_PARDOT_SUCCESS_MESSAGE';
const UPDATE_PARDOT_FAIL_TRANSLATION_KEY = 'CUSTOM_FIELDS.UPDATE_FROM_PARDOT_UNSUCCESSFUL_MESSAGE';

/**
 * Modal for updating custom fields on an account.
 *
 * This is NOT intended to be a standalone dialog component.
 * It is a sister-component with custom-fields-card component
 * joined by the shared custom-fields-store service.
 */
@Component({
  selector: 'app-custom-fields-dialog',
  templateUrl: './custom-fields-dialog.component.html',
  styleUrls: ['./custom-fields-card.component.scss'],
  standalone: false,
})
export class CustomFieldsDialogComponent implements OnInit, OnDestroy {
  businessId: string;
  partnerId: string;
  marketId: string;
  customFields$: Observable<CustomField[]>;
  saving$: Observable<boolean>;
  form: UntypedFormGroup;
  subscriptions = SubscriptionList.new();

  constructor(
    public dialogRef: MatDialogRef<CustomFieldsDialogComponent>,
    private readonly store: CustomFieldsStoreService,
    @Inject(MAT_DIALOG_DATA) private readonly data: CustomFieldDialogData,
    private readonly translate: TranslateService,
    @Optional() private readonly alerts?: SnackbarService,
  ) {}

  ngOnInit(): void {
    this.businessId = this.data.businessId;
    this.partnerId = this.data.partnerId;
    this.marketId = this.data.marketId;
    this.customFields$ = this.data.customFields$;
    this.subscriptions.add(this.customFields$, (fields) => (this.form = this.toFormGroup(fields)));
    this.saving$ = this.store.isSavingCustomFields$(this.businessId);
    const updateSuccessEvent$ = this.store.updateCustomFieldsSuccessEvent$(this.businessId);
    this.subscriptions.add(updateSuccessEvent$, (success) => {
      if (success) {
        this.alerts.openSuccessSnack(UPDATE_SUCCESS_TRANSLATION_KEY);
        this.dialogRef.close(true);
      } else {
        this.alerts.openErrorSnack(UPDATE_FAIL_TRANSLATION_KEY);
      }
    });
    this.subscriptions.add(
      this.store.pardotFieldsSuccess$(this.businessId).pipe(
        map((success) => {
          if (success) {
            this.alerts.openSuccessSnack(UPDATE_PARDOT_SUCCESS_TRANSLATION_KEY);
          } else {
            this.alerts.openErrorSnack(UPDATE_PARDOT_FAIL_TRANSLATION_KEY);
          }
        }),
      ),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }

  toFormGroup(fields: CustomField[]): UntypedFormGroup {
    const group = {};
    fields.forEach((f) => {
      group[f.name] = new UntypedFormControl(f.value || '');
    });
    return new UntypedFormGroup(group);
  }

  public onSave(): void {
    if (this.form.dirty) {
      const customFields = Object.entries(this.form.value).map(([key, value]: [string, string]) => {
        return new CustomField(key, '', '', undefined, value);
      });
      this.store.updateCustomFields(this.businessId, this.partnerId, this.marketId, customFields);
    }
  }
}
