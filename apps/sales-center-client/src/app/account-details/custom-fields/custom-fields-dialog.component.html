<mat-dialog-content *ngIf="form">
  <form [formGroup]="form">
    <ng-container *ngFor="let field of customFields$ | async">
      <div [ngSwitch]="field.fieldType">
        <mat-form-field *ngSwitchCase="'url'" class="form-field">
          <input matInput [formControlName]="field.name" [placeholder]="field.label" [id]="field.name" />
        </mat-form-field>

        <mat-form-field *ngSwitchCase="'text'" class="form-field">
          <input matInput [formControlName]="field.name" [placeholder]="field.label" [id]="field.name" />
        </mat-form-field>

        <mat-form-field *ngSwitchCase="'select'" class="form-field">
          <mat-select [formControlName]="field.name" [placeholder]="field.label" [id]="field.name">
            <mat-option *ngFor="let option of field.options" [value]="option">
              {{ option }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </ng-container>
  </form>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-button matDialogClose color="primary" [disabled]="saving$ | async">
    {{ 'COMMON.ACTION_LABELS.CANCEL' | translate }}
  </button>
  <ng-container *ngIf="saving$ | async; else submitButton">
    <mat-spinner class="button-spinner" [diameter]="25" [strokeWidth]="3"></mat-spinner>
  </ng-container>
  <ng-template #submitButton>
    <button mat-raised-button color="primary" [disabled]="form?.pristine" (click)="onSave()">
      {{ 'COMMON.ACTION_LABELS.SAVE' | translate }}
    </button>
  </ng-template>
</mat-dialog-actions>
