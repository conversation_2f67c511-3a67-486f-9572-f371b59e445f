<mat-accordion>
  <mat-expansion-panel>
    <mat-expansion-panel-header>
      {{ 'COMMON.USER_ACTIONS.IMPORTED_USER_ACTIONS_TITLE' | translate}}
    </mat-expansion-panel-header>
    <mat-divider></mat-divider>
    <table mat-table [dataSource]="displayedUserActions$ | async" class="imported-actions__table">
      <ng-container matColumnDef="timestamp">
        <th mat-header-cell *matHeaderCellDef>{{ 'COMMON.USER_ACTIONS.TIMESTAMP' | translate }}</th>
        <td mat-cell *matCellDef="let userActionEvent;">{{ formatDate(userActionEvent.created) }}</td>
      </ng-container>
      <ng-container matColumnDef="description">
        <th mat-header-cell *matHeaderCellDef>{{ 'COMMON.USER_ACTIONS.DESCRIPTION' | translate }}</th>
        <td mat-cell *matCellDef="let userActionEvent;">{{userActionEvent.description}}</td>
      </ng-container>
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
    <div *ngIf="this.hasMoreResultsAvailable$ | async">
      <button mat-button (click)="loadMoreUserActions()">{{ 'COMMON.ACTION_LABELS.LOAD_MORE' | translate}}</button>
    </div>
  </mat-expansion-panel>
</mat-accordion>
