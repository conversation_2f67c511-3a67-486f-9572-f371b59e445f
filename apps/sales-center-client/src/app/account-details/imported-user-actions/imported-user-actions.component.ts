import { Component, Input } from '@angular/core';
import { Observable } from 'rxjs';
import { take } from 'rxjs/operators';
import { ImportedUserActionsService } from './imported-user-actions.service';

@Component({
  selector: 'app-imported-user-actions',
  templateUrl: './imported-user-actions.html',
  styleUrls: ['./imported-user-actions.scss'],
  standalone: false,
})
export class ImportedUserActionsComponent {
  hasMoreResultsAvailable$ = this.importedUserActionsService.hasMoreResultsAvailable$;
  @Input() set businessId$(businessId: Observable<string>) {
    businessId.pipe(take(1)).subscribe((agId) => this.setAccountGroupId(agId));
  }

  displayedColumns: string[] = ['timestamp', 'description'];
  displayedUserActions$ = this.importedUserActionsService.combinedUserActions$;

  constructor(private readonly importedUserActionsService: ImportedUserActionsService) {}

  formatDate(date: Date): string {
    const offsetMs = date.getTimezoneOffset() * 60 * 1000;
    const dateLocal = new Date(date.getTime() - offsetMs);
    return dateLocal.toISOString().slice(0, 19).replace(/-/g, '/').replace('T', ' ');
  }

  loadMoreUserActions(): void {
    this.importedUserActionsService.loadMoreUserActions();
  }

  private setAccountGroupId(agId: string): void {
    this.importedUserActionsService.setAccountGroupId(agId);
  }
}
