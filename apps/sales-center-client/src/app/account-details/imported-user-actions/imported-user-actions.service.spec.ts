import { TestBed } from '@angular/core/testing';

import { ImportedUserActionsService } from './imported-user-actions.service';
import { UserAction, UserActionService } from '@vendasta/sales';
import { TestScheduler } from 'rxjs/testing';
import { PagedResponse } from '@vendasta/sales/lib/user-action/user-action.service';

type UserActionInterface = Pick<UserAction, 'created' | 'category' | 'description'>;
// TODO Export this from the sales lib
export enum ActionCategory {
  ACTION_CATEGORY_UNKNOWN = 0,
  ACTION_CATEGORY_CLICK = 1,
  ACTION_CATEGORY_VIEW = 2,
  ACTION_CATEGORY_LINGER = 3,
  ACTION_CATEGORY_TYPED = 4,
  ACTION_CATEGORY_PURCHASED = 5,
  ACTION_CATEGORY_ADDED_TO_CART = 6,
}

class MockUserActionService extends UserActionService {}

describe('ImportedUserActionsService', () => {
  let sched: TestScheduler;
  let importedUserActionsService: ImportedUserActionsService;
  const listOfPreviouslyRetrievedUserActions: UserActionInterface[] = [
    {
      created: new Date('2020-01-01T00:00:00.000Z'),
      category: ActionCategory.ACTION_CATEGORY_ADDED_TO_CART,
      description: 'Test description 1',
    },
    {
      created: new Date('2020-02-02T00:00:00.000Z'),
      category: ActionCategory.ACTION_CATEGORY_CLICK,
      description: 'Test description 2',
    },
    {
      created: new Date('2020-03-03T00:00:00.000Z'),
      category: ActionCategory.ACTION_CATEGORY_LINGER,
      description: 'Test description 3',
    },
  ];
  const newRetrievedUserAction: UserActionInterface[] = [
    {
      created: new Date('2020-04-04T00:00:00.000Z'),
      category: ActionCategory.ACTION_CATEGORY_PURCHASED,
      description: 'Test description 4',
    },
  ];

  const initialResponse: PagedResponse<UserActionInterface> = {
    nextCursor: 'InitialCursorValue',
    hasMore: true,
    results: listOfPreviouslyRetrievedUserActions,
  };
  const followupResponse: PagedResponse<UserActionInterface> = {
    nextCursor: 'FollowupCursorValue',
    hasMore: false,
    results: newRetrievedUserAction,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({});
    sched = new TestScheduler((a, b) => expect(a).toEqual(b));
    const mockUserActionService = new MockUserActionService(null);
    importedUserActionsService = new ImportedUserActionsService(mockUserActionService);
  });
  afterEach(() => {
    sched.flush();
  });
  it('should combine retrieved results with what has already been retrieved', () => {
    sched.schedule(
      () => importedUserActionsService.processResponse(initialResponse as any, false),
      sched.createTime('|'),
    );
    sched.schedule(
      () => importedUserActionsService.processResponse(followupResponse as any, true),
      sched.createTime('-|'),
    );

    sched
      .expectObservable(importedUserActionsService.combinedUserActions$)
      .toBe('xy', { x: initialResponse.results, y: initialResponse.results.concat(followupResponse.results) });
  });
  it('should set a flag to indicate if no more results are available', () => {
    sched.schedule(
      () => importedUserActionsService.processResponse(followupResponse as any, false),
      sched.createTime('|'),
    );
    sched.expectObservable(importedUserActionsService.hasMoreResultsAvailable$).toBe('x', { x: false });
  });
  it('should replace already retrieved results if we are reloading data', () => {
    sched.schedule(
      () => importedUserActionsService.processResponse(initialResponse as any, false),
      sched.createTime('|'),
    );
    sched.schedule(
      () => importedUserActionsService.processResponse(followupResponse as any, false),
      sched.createTime('-|'),
    );
    sched
      .expectObservable(importedUserActionsService.combinedUserActions$)
      .toBe('xy', { x: initialResponse.results, y: followupResponse.results });
  });
});
