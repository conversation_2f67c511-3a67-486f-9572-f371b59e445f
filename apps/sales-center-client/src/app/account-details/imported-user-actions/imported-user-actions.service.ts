import { Injectable } from '@angular/core';
import { UserAction, UserActionService } from '@vendasta/sales';
import { map, take, withLatestFrom } from 'rxjs/operators';
import { BehaviorSubject, combineLatest, Observable, ReplaySubject } from 'rxjs';
import { SubscriptionList } from '@vendasta/rx-utils';
import { PagedResponse } from '@vendasta/sales/lib/user-action/user-action.service';

interface ResultsMetadata {
  hasMore: boolean;
  nextCursor: string;
}

enum LoadResultsCommand {
  InitialLoad,
  LoadMore,
}

const USER_ACTION_PAGE_SIZE = 25;

@Injectable()
export class ImportedUserActionsService {
  private readonly subscriptions = SubscriptionList.new();
  private readonly retrievedUserActions$$ = new BehaviorSubject<UserAction[]>(<UserAction[]>[]);
  private readonly combinedUserActions$$ = new BehaviorSubject<UserAction[]>(<UserAction[]>[]);
  readonly combinedUserActions$ = this.combinedUserActions$$.asObservable();
  private readonly resultsMetadata$$ = new ReplaySubject<ResultsMetadata>(1);
  private readonly accountGroupId$$ = new ReplaySubject<string>();
  private readonly accountGroupId$ = this.accountGroupId$$.asObservable();
  readonly hasMoreResultsAvailable$: Observable<boolean> = this.resultsMetadata$$
    .asObservable()
    .pipe(map((metadata) => !!metadata.hasMore));
  private readonly loadResultsCommand$$ = new BehaviorSubject<LoadResultsCommand>(LoadResultsCommand.InitialLoad);

  constructor(private readonly userActionService: UserActionService) {
    this.subscriptions.add(this.accountGroupId$, (agId) => {
      this.listUserActions(agId, null, USER_ACTION_PAGE_SIZE);
    });
    this.subscriptions.add(
      this.retrievedUserActions$$
        .asObservable()
        .pipe(withLatestFrom(this.combinedUserActions$, this.loadResultsCommand$$.asObservable())),
      ([retrievedUserActions, combinedUserActions, command]) => {
        this.combineRetrievedUserActions(command, retrievedUserActions, combinedUserActions);
      },
    );
  }

  private combineRetrievedUserActions(command, retrievedUserActions, combinedUserActions): void {
    if (command === LoadResultsCommand.InitialLoad) {
      this.combinedUserActions$$.next(retrievedUserActions);
    } else if (command === LoadResultsCommand.LoadMore) {
      this.combinedUserActions$$.next(combinedUserActions.concat(retrievedUserActions));
    }
  }

  private listUserActions(agId: string, cursor: string, pageSize: number): void {
    this.subscriptions.add(this.userActionService.listUserActions(agId, cursor, pageSize).pipe(take(1)), (response) => {
      this.processResponse(response, !!cursor);
    });
  }

  processResponse(response: PagedResponse<UserAction>, combineWithPreviousResults: boolean): void {
    if (combineWithPreviousResults) {
      this.loadResultsCommand$$.next(LoadResultsCommand.LoadMore);
    } else {
      this.loadResultsCommand$$.next(LoadResultsCommand.InitialLoad);
    }
    this.storeResponseMetadata(response.hasMore, response.nextCursor);
    this.setRetrievedUserActions(response.results);
  }

  private setRetrievedUserActions(userActions: UserAction[]): void {
    this.retrievedUserActions$$.next(userActions || []);
  }

  private storeResponseMetadata(hasMore: boolean, nextCursor: string): void {
    const moreResults: ResultsMetadata = {
      hasMore: hasMore,
      nextCursor: nextCursor,
    };
    this.resultsMetadata$$.next(moreResults);
  }

  loadMoreUserActions(): void {
    this.subscriptions.add(
      combineLatest([this.accountGroupId$, this.resultsMetadata$$]).pipe(take(1)),
      ([agId, metadata]) => {
        this.listUserActions(agId, metadata.nextCursor, USER_ACTION_PAGE_SIZE);
      },
    );
  }

  setAccountGroupId(agId: string): void {
    this.accountGroupId$$.next(agId);
  }
}
