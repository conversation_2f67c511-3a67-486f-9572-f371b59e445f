import { Component, Inject, Input, OnInit } from '@angular/core';
import { FormGroupDirective, NgForm, UntypedFormBuilder, UntypedFormControl, Validators } from '@angular/forms';
import { ErrorStateMatcher } from '@angular/material/core';
import { TranslateService } from '@ngx-translate/core';
import { WorkStates } from '@vendasta/rx-utils/work-state';
import { SalespersonService } from '@vendasta/sales';
import { Observable, combineLatest, of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { PARTNER_ID_TOKEN } from '../../common/providers';
import { AccountHealthInfo, AccountHealthService, HEALTH_RATING_CHARACTER_LIMIT } from './account-health.service';

export class ReasonErrorStateMatcher implements ErrorStateMatcher {
  isErrorState(control: UntypedFormControl | null, form: FormGroupDirective | NgForm | null): boolean {
    const isSubmitted = form && form.submitted;
    return !!(control && control.invalid && (control.dirty || control.touched || isSubmitted));
  }
}

@Component({
  selector: 'app-account-health',
  templateUrl: './account-health.component.html',
  styleUrls: ['./account-health.component.scss'],
  standalone: false,
})
export class AccountHealthComponent implements OnInit {
  @Input() accountGroupId: string;

  public readonly characterLimit = HEALTH_RATING_CHARACTER_LIMIT;
  public readonly accountHealthRatings = [1, 2, 3, 4, 5];
  public reasonControl: UntypedFormControl;
  public editorSalespersonName$: Observable<string>;
  public workStates: WorkStates<AccountHealthInfo>;

  matcher = new ReasonErrorStateMatcher();

  constructor(
    private readonly accountHealthService: AccountHealthService,
    private readonly salesPersonService: SalespersonService,
    private readonly formBuilder: UntypedFormBuilder,
    @Inject(PARTNER_ID_TOKEN) private readonly partnerId$: Observable<string>,
    private readonly translate: TranslateService,
  ) {
    this.reasonControl = this.formBuilder.control('', {
      validators: [Validators.maxLength(HEALTH_RATING_CHARACTER_LIMIT)],
      updateOn: 'change',
    });
  }

  ngOnInit(): void {
    this.workStates = this.accountHealthService.healthStates(this.accountGroupId);

    this.accountHealthService.getAccountHealthInfo(this.accountGroupId);

    this.editorSalespersonName$ = combineLatest([this.workStates.workResults$, this.partnerId$]).pipe(
      switchMap(([ah, partnerId]) => {
        this.reasonControl.setValue(ah.reason || '');
        if (ah.updatedBy === null) {
          return of('');
        }
        return this.salesPersonService.getSalespersonByUserId(partnerId, ah.updatedBy).pipe(
          map((sp) => {
            return sp.firstName + ' ' + sp.lastName;
          }),
          catchError(() => {
            return of(this.translate.instant('ERRORS.UNABLE_TO_LOAD_USER'));
          }),
        );
      }),
    );
  }

  setHealthRating(previousRating: number, newRating: number, currentReason: string): void {
    this.accountHealthService.updateAccountRating(this.accountGroupId, newRating, previousRating, currentReason);
  }

  setHealthReason(previousReason: string, newReason: string, currentRating: number): void {
    if (newReason === '') {
      this.reasonControl.setValue(previousReason);
      return;
    }
    this.accountHealthService.updateAccountReason(this.accountGroupId, newReason, previousReason, currentRating);
  }
}
