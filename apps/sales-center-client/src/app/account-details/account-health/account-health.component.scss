@use 'design-tokens' as *;

:host {
  width: inherit;
  color: $dark-gray;
}

.account-health-item {
  display: flex;
  justify-content: flex-end;

  @media screen and (max-width: $media--tablet-minimum) {
    justify-content: flex-start;
  }
}

.account-health-icon {
  font-size: 24px;
  vertical-align: middle;
  color: $gray;
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
  transition-duration: 500ms;
  border-radius: 15px;
  background-size: 10px 10%;
}

.selected {
  color: $dark-red;
}

.account-health-info {
  width: 240px;
  font-size: 11px;
  color: $dark-gray;
  margin-top: $spacing-2;

  textarea {
    text-align: right;
  }

  mat-error {
    text-align: left;
    font-size: 10px;
  }

  mat-hint {
    text-align: left;
    font-size: 10px;
  }

  @media screen and (max-width: $media--tablet-minimum) {
    text-align: left;
  }
}

.account-health-stats {
  width: 240px;
  font-size: 10px;
  text-align: right;
  color: $gray;

  @media screen and (max-width: $media--tablet-minimum) {
    text-align: left;
  }
}

.account-health-stats:hover {
  color: $darker-gray;
}
