import { Injectable } from '@angular/core';
import { AccountGroupService, HealthUpdateOperation, ProjectionFilter, UpdateOperations } from '@galaxy/account-group';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ObservableWorkStateMap, WorkStates, getStates } from '@vendasta/rx-utils/work-state';
import { SalespersonService } from '@vendasta/sales';
import { EMPTY, Observable } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';

export const HEALTH_RATING_CHARACTER_LIMIT = 120;

export interface AccountHealthInfo {
  rating: number;
  updatedOn: Date;
  updatedBy: string;
  reason: string;
}

@Injectable()
export class AccountHealthService {
  private readonly accountHealthWorkStateMap = new ObservableWorkStateMap<string, AccountHealthInfo>();
  private readonly emptyState: AccountHealthInfo = {
    rating: 0,
    updatedOn: null,
    updatedBy: null,
    reason: '',
  };

  constructor(
    private readonly salesPersonService: SalespersonService,
    private readonly accountGroupService: AccountGroupService,
    private readonly alertService: SnackbarService,
    private readonly translate: TranslateService,
  ) {}

  public getAccountHealthInfo(accountGroupId: string): void {
    this.accountHealthWorkStateMap.startWork(accountGroupId, this.get(accountGroupId));
  }

  public healthStates(accountGroupId): WorkStates<AccountHealthInfo> {
    return getStates(this.accountHealthWorkStateMap, accountGroupId);
  }

  private get(accountGroupId: string): Observable<AccountHealthInfo> {
    const projectionFilter = new ProjectionFilter({ health: true });

    return this.accountGroupService.get(accountGroupId, projectionFilter).pipe(
      map((ag) => {
        if (!ag || !ag.health) {
          return this.emptyState;
        }
        return <AccountHealthInfo>{
          rating: ag.health.rating,
          reason: ag.health.reason,
          updatedBy: ag.health.updatedBy,
          updatedOn: ag.health.updatedOn,
        };
      }),
    );
  }

  public updateAccountRating(
    accountGroupId: string,
    newRating: number,
    previousRating: number,
    currentReason: string,
  ): void {
    const trueRating = previousRating === newRating ? 0 : newRating;
    const reason = trueRating === 0 ? '' : currentReason;

    const healthUpdateOperation = new HealthUpdateOperation({ rating: trueRating, reason: reason });
    const UpdateOp = new UpdateOperations();
    UpdateOp.addUpdateOperation(healthUpdateOperation);

    this.accountHealthWorkStateMap.startWork(
      accountGroupId,
      this.accountGroupService.bulkUpdate(accountGroupId, UpdateOp).pipe(
        catchError(() => {
          this.alertService.openErrorSnack('ACCOUNT_DETAILS.HEALTH.UPDATE_RATING_FAIL');
          return EMPTY;
        }),
        switchMap(() => this.get(accountGroupId)),
      ),
    );
  }

  public updateAccountReason(
    accountGroupId: string,
    newReason: string,
    previousReason: string,
    currentRating: number,
  ): Observable<never> {
    const healthUpdateOperation = new HealthUpdateOperation({ rating: currentRating, reason: newReason });
    const UpdateOp = new UpdateOperations();
    UpdateOp.addUpdateOperation(healthUpdateOperation);

    if (newReason.length > HEALTH_RATING_CHARACTER_LIMIT) {
      this.alertService.openErrorSnack('ACCOUNT_DETAILS.HEALTH.MAX_CHARACTERS', {
        interpolateTranslateParams: { max: HEALTH_RATING_CHARACTER_LIMIT.toString() },
      });
      return EMPTY;
    }
    if (newReason === previousReason) {
      return EMPTY;
    }

    this.accountHealthWorkStateMap.startWork(
      accountGroupId,
      this.accountGroupService.bulkUpdate(accountGroupId, UpdateOp).pipe(
        catchError(() => {
          this.alertService.openErrorSnack('ACCOUNT_DETAILS.HEALTH.UPDATE_REASON_FAIL');
          return EMPTY;
        }),
        switchMap(() => this.get(accountGroupId)),
      ),
    );
  }
}
