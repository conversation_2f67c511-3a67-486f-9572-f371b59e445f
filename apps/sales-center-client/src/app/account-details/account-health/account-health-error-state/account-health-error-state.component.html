<div class="error">
  <div class="account-health-error-item">
    <span>
      {{ 'ACCOUNT_DETAILS.HEALTH.ACCOUNT_HEALTH' | translate }}
      <ng-container *ngFor="let rating of ratingIconCount">
        <mat-icon class="account-health-error-icon">favorite_border</mat-icon>
      </ng-container>
    </span>
  </div>
  <div class="account-health-error-item">
    <span class="account-health-error-info">
      {{ 'ACCOUNT_DETAILS.HEALTH.UNABLE_TO_LOAD' | translate }}
    </span>
  </div>
  <div class="account-health-error-item">
    <span class="account-health-error-stats">[...][...]</span>
  </div>
</div>
