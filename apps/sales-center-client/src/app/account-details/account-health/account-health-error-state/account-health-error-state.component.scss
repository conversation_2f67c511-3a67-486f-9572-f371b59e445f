@use 'design-tokens' as *;

.account-health-error-item {
  display: flex;
  justify-content: flex-end;
  user-select: none;

  @media screen and (max-width: $media--tablet-minimum) {
    justify-content: flex-start;
  }
}

.account-health-error-icon {
  font-size: 24px;
  vertical-align: middle;
  color: $gray;
}

.account-health-error-info {
  width: 240px;
  font-size: 11px;
  text-align: right;
  color: $dark-gray;
  overflow: hidden;

  @media screen and (max-width: $media--tablet-minimum) {
    text-align: left;
  }
}

.account-health-error-stats {
  font-size: 10px;
  text-align: right;
  color: $gray;
  @media screen and (max-width: $media--tablet-minimum) {
    text-align: left;
  }
}
