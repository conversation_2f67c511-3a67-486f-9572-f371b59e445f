import { NgModule } from '@angular/core';
import { AccountHealthComponent } from './account-health.component';
import { CommonModule } from '@angular/common';
import { AccountHealthService } from './account-health.service';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AsyncUiModule } from '@vendasta/uikit';
import { AccountHealthLoadingStateComponent } from './account-health-loading-state/account-health-loading-state.component';
import { AccountHealthErrorStateComponent } from './account-health-error-state/account-health-error-state.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@NgModule({
  declarations: [AccountHealthComponent, AccountHealthLoadingStateComponent, AccountHealthErrorStateComponent],
  imports: [
    MatIconModule,
    CommonModule,
    TranslateModule,
    MatInputModule,
    FormsModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    AsyncUiModule,
    MatProgressSpinnerModule,
  ],
  exports: [AccountHealthComponent],
  providers: [AccountHealthService],
})
export class AccountHealthModule {}
