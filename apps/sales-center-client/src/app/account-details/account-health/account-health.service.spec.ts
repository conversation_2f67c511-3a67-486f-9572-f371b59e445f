import { HttpResponse } from '@angular/common/http';
import { AccountGroup, AccountGroupHealth } from '@galaxy/account-group';
import { Salesperson } from '@vendasta/sales';
import { Observable, of } from 'rxjs';
import { TestScheduler } from 'rxjs/testing';
import { MockTranslateService } from '../../common/translate.mock';
import { AccountHealthInfo, AccountHealthService } from './account-health.service';

class MockSalespersonService {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  getSalesperson(partnerId: string, salespersonId?: string, userId?: string): Observable<Salesperson> {
    return of(<Salesperson>{
      firstName: '<PERSON> Rae',
      lastName: 'Cyrus',
      salespersonId: 'UID-12345',
    });
  }
}

class MockAlertService {
  openErrorSnack(): void {
    return undefined;
  }

  openSuccessSnack(): void {
    return;
  }
}

const accountGroup1: AccountGroup = {
  accountGroupId: 'AG-123',
  health: {
    rating: 3,
    reason: "They're okay",
    updatedBy: 'uid-1',
    updatedOn: new Date('2019-11-11'),
  } as AccountGroupHealth,
} as AccountGroup;

const accountGroup1Updated: AccountGroup = {
  accountGroupId: 'AG-123',
  health: {
    rating: 1,
    reason: "They're okay",
    updatedBy: 'uid-1',
    updatedOn: new Date('2019-11-11'),
  } as AccountGroupHealth,
} as AccountGroup;

const accountGroup2: AccountGroup = {
  accountGroupId: 'AG-124',
  health: {
    rating: 5,
    reason: 'Best Ever!',
    updatedBy: 'UID-2',
    updatedOn: new Date('2019-10-10'),
  } as AccountGroupHealth,
} as AccountGroup;

const accountGroup2Updated: AccountGroup = {
  accountGroupId: 'AG-124',
  health: {
    rating: 5,
    reason: 'Worst Ever!',
    updatedBy: 'UID-2',
    updatedOn: new Date('2019-10-10'),
  } as AccountGroupHealth,
} as AccountGroup;

let scheduler: TestScheduler;

describe('AccountHealthService', () => {
  let service: AccountHealthService;
  let mockSalespersonService: MockSalespersonService;
  let mockAlertService: MockAlertService;
  let mockTranslateService: MockTranslateService;

  beforeEach(() => {
    mockSalespersonService = new MockSalespersonService();
    mockTranslateService = new MockTranslateService();
    mockAlertService = new MockAlertService();
    scheduler = new TestScheduler((a, b) => expect(a).toEqual(b));
  });

  afterEach(() => {
    scheduler.flush();
  });

  describe('getAccountHealthInfo', () => {
    it('should return valid AccountHealthInfo object', function (): void {
      class MockAccountGroupServiceForValidRequest {
        get(): Observable<AccountGroup> {
          return scheduler.createColdObservable('x', {
            x: accountGroup2,
          });
        }
      }

      const mockAccountGroupService = new MockAccountGroupServiceForValidRequest();
      service = new AccountHealthService(
        mockSalespersonService as any,
        mockAccountGroupService as any,
        mockAlertService as any,
        mockTranslateService as any,
      );

      const expected: AccountHealthInfo = {
        rating: 5,
        reason: 'Best Ever!',
        updatedBy: 'UID-2',
        updatedOn: new Date('2019-10-10'),
      };
      service.getAccountHealthInfo(accountGroup1.accountGroupId);

      scheduler
        .expectObservable(service.accountHealthWorkStateMap.getWorkResults$(accountGroup1.accountGroupId))
        .toBe('x', { x: expected });
    });
    it('should return the empty state AccountHealth object if the call returns undefined', function (): void {
      class MockAccountGroupServiceForValidRequest {
        get(): Observable<AccountGroup> {
          return scheduler.createColdObservable('x', {
            x: null,
          });
        }
      }

      const mockAccountGroupService = new MockAccountGroupServiceForValidRequest();
      service = new AccountHealthService(
        mockSalespersonService as any,
        mockAccountGroupService as any,
        mockAlertService as any,
        mockTranslateService as any,
      );
      service.getAccountHealthInfo('');

      scheduler
        .expectObservable((service as any).accountHealthWorkStateMap.getWorkResults$(accountGroup1.accountGroupId))
        .toBe('');
    });
  });

  describe('updateAccountRating', () => {
    it('should update rating and new account health value', function (): void {
      class MockAccountGroupServiceForValidRequest {
        get(): Observable<AccountGroup> {
          return scheduler.createColdObservable('-x', {
            x: accountGroup1Updated,
          });
        }

        bulkUpdate(): Observable<HttpResponse<null>> {
          return scheduler.createColdObservable('x', {
            x: <HttpResponse<null>>{
              status: 200,
            },
          });
        }
      }

      const mockAccountGroupService = new MockAccountGroupServiceForValidRequest();
      service = new AccountHealthService(
        mockSalespersonService as any,
        mockAccountGroupService as any,
        mockAlertService as any,
        mockTranslateService as any,
      );
      service.updateAccountRating(accountGroup1.accountGroupId, 1, 3, "They're okay");

      scheduler.flush();
      scheduler

        .expectObservable(service.accountHealthWorkStateMap.getWorkResults$(accountGroup1.accountGroupId))
        .toBe('-x', { x: accountGroup1Updated.health });
    });
    it('should not emit anything on returned observable if update fails', function (): void {
      class MockAccountGroupServiceForFailedRequest {
        bulkUpdate(): Observable<HttpResponse<null>> {
          return scheduler.createColdObservable('#', undefined, 'fail');
        }
      }

      const mockAccountGroupService = new MockAccountGroupServiceForFailedRequest();
      service = new AccountHealthService(
        mockSalespersonService as any,
        mockAccountGroupService as any,
        mockAlertService as any,
        mockTranslateService as any,
      );

      service.updateAccountRating(
        accountGroup1.accountGroupId,
        accountGroup1Updated.health.rating,
        accountGroup1.health.rating,
        "They're okay",
      );

      scheduler.flush();

      scheduler.expectObservable(service.accountHealthWorkStateMap.isSuccess$(accountGroup1.accountGroupId)).toBe('');
    });
  });

  describe('updateAccountReason', () => {
    it('should update reason and new account health value', function (): void {
      class MockAccountGroupServiceForValidRequest {
        get(): Observable<AccountGroup> {
          return scheduler.createColdObservable('-x', {
            x: accountGroup2Updated,
          });
        }

        bulkUpdate(): Observable<HttpResponse<null>> {
          return scheduler.createColdObservable('x', {
            x: <HttpResponse<null>>{
              status: 200,
            },
          });
        }
      }

      const mockAccountGroupService = new MockAccountGroupServiceForValidRequest();
      service = new AccountHealthService(
        mockSalespersonService as any,
        mockAccountGroupService as any,
        mockAlertService as any,
        mockTranslateService as any,
      );
      service.updateAccountReason(
        accountGroup2.accountGroupId,
        accountGroup2Updated.health.reason,
        accountGroup2.health.reason,
        accountGroup2.health.rating,
      );

      scheduler.flush();
      scheduler

        .expectObservable(service.accountHealthWorkStateMap.getWorkResults$(accountGroup2.accountGroupId))
        .toBe('-x', { x: accountGroup2Updated.health });
    });
    it('should not emit anything on returned observable if update fails', function (): void {
      class MockAccountGroupServiceForFailedRequest {
        bulkUpdate(): Observable<HttpResponse<null>> {
          return scheduler.createColdObservable('#', undefined, 'fail');
        }
      }

      const mockAccountGroupService = new MockAccountGroupServiceForFailedRequest();
      service = new AccountHealthService(
        mockSalespersonService as any,
        mockAccountGroupService as any,
        mockAlertService as any,
        mockTranslateService as any,
      );
      service.updateAccountReason(
        accountGroup2.accountGroupId,
        accountGroup2Updated.health.reason,
        accountGroup2.health.reason,
        accountGroup2.health.rating,
      );

      scheduler.flush();

      scheduler
        .expectObservable((service as any).accountHealthWorkStateMap.getWorkResults$(accountGroup2.accountGroupId))
        .toBe('');
    });
    it("should return EMPTY without updating if value doesn't change", function (): void {
      class MockAccountGroupServiceForFailedRequest {}

      const mockAccountGroupService = new MockAccountGroupServiceForFailedRequest();
      service = new AccountHealthService(
        mockSalespersonService as any,
        mockAccountGroupService as any,
        mockAlertService as any,
        mockTranslateService as any,
      );

      service.updateAccountReason(
        accountGroup2.accountGroupId,
        accountGroup2.health.reason,
        accountGroup2.health.reason,
        accountGroup2.health.rating,
      );

      scheduler.flush();
      scheduler
        .expectObservable((service as any).accountHealthWorkStateMap.getWorkResults$(accountGroup2.accountGroupId))
        .toBe('');
    });
  });
});
