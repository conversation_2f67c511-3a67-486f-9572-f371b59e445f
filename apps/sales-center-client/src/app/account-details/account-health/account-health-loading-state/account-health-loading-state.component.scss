@use 'design-tokens' as *;
@import 'shimmer';

.account-health-item {
  display: flex;
  justify-content: flex-end;

  @media screen and (max-width: $media--tablet-minimum) {
    justify-content: flex-start;
  }
}

.account-health-icon {
  font-size: 24px;
  vertical-align: middle;
  color: $gray;
  cursor: pointer;
}

.account-health-loading-area {
  width: 240px;
  height: 1.9em;
  justify-self: flex-end;
  border-bottom: $dark-gray solid 1px;
}

.account-health-stats-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 16px;
  font-size: 10px;
  text-align: right;
  color: $gray;
  @media screen and (max-width: $media--tablet-minimum) {
    text-align: left;
  }
}

$color: $dark-gray;
$timing: 2s;

.shimmer {
  text-align: center;
  color: rgba(255, 255, 255, 0.1);
  background: -webkit-gradient(linear, left top, right top, from($color), to($color), color-stop(0.5, #fff));
  background: -moz-gradient(linear, left top, right top, from($color), to($color), color-stop(0.5, #fff));
  background: gradient(linear, left top, right top, from($color), to($color), color-stop(0.5, #fff));
  background-size: 125px 100%;
  -webkit-background-clip: text;
  -moz-background-clip: text;
  background-clip: text;
  -webkit-animation-name: shimmer;
  -moz-animation-name: shimmer;
  animation-name: shimmer;
  -webkit-animation-duration: $timing;
  -moz-animation-duration: $timing;
  animation-duration: $timing;
  -webkit-animation-iteration-count: infinite;
  -moz-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  background-repeat: no-repeat;
  background-position: 0 0;
  background-color: $color;
}

@keyframes shimmer {
  0% {
    background-position: top left;
  }
  100% {
    background-position: top right;
  }
}
