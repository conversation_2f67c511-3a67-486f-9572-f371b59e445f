<div class="account-health-item">
  <span>
    {{ 'ACCOUNT_DETAILS.HEALTH.ACCOUNT_HEALTH' | translate }}
    <ng-container *ngFor="let rating of ratingIconCount">
      <mat-icon class="account-health-icon shimmer">favorite</mat-icon>
    </ng-container>
  </span>
</div>
<div class="account-health-item">
  <div class="account-health-loading-area"></div>
</div>
<div class="account-health-item">
  <div class="account-health-stats-loading">
    [
    <mat-spinner diameter="12"></mat-spinner>
    ]
  </div>
</div>
