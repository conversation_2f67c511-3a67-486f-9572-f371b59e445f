<uikit-async-ui
  [loading]="workStates.isLoading$ | async"
  [data]="workStates.workResults$ | async"
  [error]="(workStates.isSuccess$ | async) === false"
>
  <ng-container *successData="let accountHealth">
    <div class="account-health-item">
      <span>
        {{ 'ACCOUNT_DETAILS.HEALTH.ACCOUNT_HEALTH' | translate }}
        <ng-container *ngFor="let rating of accountHealthRatings">
          <mat-icon
            [class.selected]="accountHealth.rating >= rating"
            class="account-health-icon"
            (click)="setHealthRating(accountHealth.rating, rating, accountHealth.reason)"
          >
            favorite
          </mat-icon>
        </ng-container>
      </span>
    </div>
    <div class="account-health-item">
      <mat-form-field class="account-health-info">
        <mat-label>{{ 'ACCOUNT_DETAILS.HEALTH.REASON' | translate }}</mat-label>
        <textarea
          matInput
          cdkTextareaAutosize
          cdkAutosizeMinRows="1"
          cdkAutosizeMaxRows="3"
          [formControl]="reasonControl"
          [errorStateMatcher]="matcher"
          value="{{ accountHealth.reason || '' }}"
          (keydown.enter)="setHealthReason(accountHealth.reason, reasonControl.value || '', accountHealth.rating)"
          (focusout)="setHealthReason(accountHealth.reason, reasonControl.value || '', accountHealth.rating)"
        ></textarea>
        <mat-hint>{{ reasonControl.value?.length || accountHealth.reason?.length || 0 }}/{{ characterLimit }}</mat-hint>
        <mat-error *ngIf="reasonControl.invalid">{{ reasonControl.value?.length || 0 }}/{{ characterLimit }}</mat-error>
      </mat-form-field>
    </div>
    <div class="account-health-item">
      <span class="account-health-stats">
        <ng-container class="account-health-info">[{{ editorSalespersonName$ | async }}]</ng-container>
        [{{ (accountHealth.updatedOn | date : 'MMM dd, yyyy h:mm a') || 'ACCOUNT_DETAILS.HEALTH.UNSET' | translate }}]
      </span>
    </div>
  </ng-container>
  <ng-container loading>
    <app-account-health-loading-state [ratingIconCount]="accountHealthRatings"></app-account-health-loading-state>
  </ng-container>
  <ng-container error>
    <app-account-health-error-state [ratingIconCount]="accountHealthRatings"></app-account-health-error-state>
  </ng-container>
</uikit-async-ui>
