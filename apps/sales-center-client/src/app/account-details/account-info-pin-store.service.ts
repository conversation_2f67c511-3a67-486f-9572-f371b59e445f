import { Inject, Injectable } from '@angular/core';
import { LocalStorageService } from '@vendasta/uikit';
import { BehaviorSubject, Observable, ReplaySubject } from 'rxjs';
import { map, shareReplay, startWith, switchMap } from 'rxjs/operators';
import { DEFAULT_ACCOUNTS_ORDERS_LAYOUT_TOKEN } from '../partner/partner-overrides';
import { PinStore } from '../uikit-staging/pin/providers';

const localStorageKey = 'account_details.pinned_card_left';

export const DEFAULT_PINNED_CARD = 'campaigns';
export const ALTERNATE_PINNED_CARD = 'orders';

export interface GetterSetter {
  get(localStorageKey: string): string;

  set(localStorageKey: string, pinId: string): void;
}

@Injectable({
  providedIn: 'any',
})
export class AccountInfoPinStore implements PinStore {
  pinnedIds$$ = new ReplaySubject<string[]>(1);
  readonly pinnedIds$: Observable<string[]>;
  pinAdded$$ = new BehaviorSubject<string>('');
  pinAdded$ = this.pinAdded$$.asObservable();

  constructor(
    @Inject(DEFAULT_ACCOUNTS_ORDERS_LAYOUT_TOKEN) useDefaultLayout: Observable<boolean>,
    @Inject(LocalStorageService) private readonly localStorage: GetterSetter,
  ) {
    const lsPin = this.getPinsFromLocalStorage();
    this.pinnedIds$ = useDefaultLayout.pipe(
      map<boolean, string>((useDefault) => (useDefault ? DEFAULT_PINNED_CARD : ALTERNATE_PINNED_CARD)),
      map<string, string[]>((defaultCard) => (lsPin?.length > 0 ? lsPin : [defaultCard])),
      switchMap((defaultPin: string[]) => this.pinnedIds$$.pipe(startWith(defaultPin))),
      shareReplay(1),
    );
  }

  addPinnedId(pinId: string): void {
    const pins = this.getPinsFromLocalStorage();
    pins.push(pinId);
    this.localStorage.set(localStorageKey, JSON.stringify(pins));
    this.pinnedIds$$.next(pins);
    this.pinAdded$$.next(pinId);
  }

  removePinnedId(pinId: string) {
    const pins = this.getPinsFromLocalStorage();
    const index = pins.indexOf(pinId);
    if (index > -1) {
      pins.splice(index, 1);
    }
    this.localStorage.set(localStorageKey, JSON.stringify(pins));
    this.pinnedIds$$.next(pins);
  }

  getPinsFromLocalStorage(): string[] {
    const value = this.localStorage.get(localStorageKey);
    // if there is nothing in local storage nothing was pinned
    if (!value) {
      return [];
    }

    try {
      // if we can json parse the list then just return it
      return JSON.parse(value);
    } catch (_) {
      // if we can't json parse the value then it was a single string
      return [value];
    }
  }
}
