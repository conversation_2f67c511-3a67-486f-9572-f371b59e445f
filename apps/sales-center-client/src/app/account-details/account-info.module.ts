import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AccountGroupApiService } from '@galaxy/account-group';
import { LexiconModule } from '@galaxy/lexicon';
import { PartnerService, WhitelabelService } from '@galaxy/partner';
import { SnapshotCheckoutModule } from '@galaxy/snapshot';
import { TranslateModule } from '@ngx-translate/core';
import { ProspectApiService } from '@vendasta/prospect';
import {
  SalesOrderShortListModule,
  SidePanelStateService,
  SlideOutPanelModule,
  SlideOutPanelService,
  LocalTimeCardModule,
} from '@vendasta/sales-ui';
import { CommonPipesModule, EmptyStateModule, UIKitModule, VaIconModule, VaStencilsModule } from '@vendasta/uikit';
import baseTranslation from '../../assets/i18n/en_devel.json';
import { AccountTagsModule } from '../account-tags/account-tags.module';
import { AstbeModule, AsteriskService } from '../astbe_sdk';
import { AsteriskModule } from '../asterisk/asterisk.module';
import { BannerModule } from '../banner';
import { BusinessCategoryModule } from '../business-category';
import { BusinessModule } from '../business/business.module';
import { CampaignsModule } from '../campaigns';
import { BusinessPrioritiesService } from '../common/business-priorities.service';
import { ContactFormsModule } from '../common/contacts';
import { CreateOpportunityFormModule } from '../common/sales-opportunities/create-opportunity-form';
import { VoiceRecognitionModule } from '../common/voice-recognition/voice-recognition.module';
import { WEBLATE_COMPONENT_NAME } from '../constants';
import { CoreModule as AppCoreModule } from '../core';
import { PreferredCurrencyModule } from '@vendasta/sales-common';
import { NavigationModule } from '../navigation/navigation.module';
import { DEFAULT_ACCOUNTS_ORDERS_LAYOUT_TOKEN, defaultAccountsOrdersLayoutFactory } from '../partner/partner-overrides';
import { PartnerModule } from '../partner/partner.module';
import { ProductActivationsModule } from '../product-activations/product-activations.module';
import { SalesActivityModule } from '../sales-activity/sales-activity.module';
import { SalesOpportunitiesModule } from '../sales-opportunities/sales-opportunities.module';
import { TasksModule } from '../tasks';
import { VbcSdkModule } from '../vbc_sdk/vbc-sdk.module';
import { AccountDetailsService } from './account-details.service';
import { AccountHealthModule } from './account-health/account-health.module';
import { AccountInfoStoreService } from './account-info-store.service';
import { AccountInfoComponent } from './account-info.component';
import { AccountInfoRouting } from './account-info.routing';
import { AccountLifecycleComponent } from './account-lifecycle/account-lifecycle.component';
import { ArchiveBusinessDialogComponent } from './archive-business-dialog.component';
import { AssigneeEditorModule } from './assignee-editor/assignee-editor.module';
import { AsyncTextModule } from './async-text/async-text.module';
import { BusinessPrioritiesModule } from './business-priorities/business-priorities.module';
import { CampaignsCardModule } from './campaigns-card';
import { ContactsCardModule } from './contacts/contacts-card.module';
import { ErrorCardModule } from './error-card/error-card.module';
import { FinancialHealthModule } from './financial-health/financial-health.module';
import { GeneralInfoCardModule } from './general-info/general-info-card.module';
import { ImportedUserActionsComponent } from './imported-user-actions/imported-user-actions.component';
import { ImportedUserActionsService } from './imported-user-actions/imported-user-actions.service';
import { ManualAutomationsModule } from './manual-automations/manual-automations.module';
import { NotesComponent } from './notes/notes.component';
import { ProductImagesPipe, ProjectsComponent, ProjectSubtaskPipe } from './projects/projects.component';
import { ProjectMilestonesService } from './projects/projects.service';
import { RecentActivityCardComponent } from './recent-activity/recent-activity-card.component';
import { RecentActivityListComponent } from './recent-activity/recent-activity-list.component';
import { RecentActivityService } from './recent-activity/recent-activity.service';
import { UserActivitiesService } from './recent-activity/user-activities.service';
import { ReportsCardModule } from './reports/reports-card.module';
import { SalesOpportunityCardModule } from './sales-opportunity/sales-opportunity-card.module';
import { SalesOpportunityCardService } from './sales-opportunity/sales-opportunity-card.service';
import { SubTitleModule } from './sub-title/sub-title.module';
import { TaskCardUiService } from './task/task-card-ui.service';
import { TaskCardComponent } from './task/task-card.component';
import { TaskCardModule } from './task/task-card.module';
import { TaskCardService } from './task/task-card.service';
import { UTMAttributesModule } from './utm-attributes/utm-attributes.module';

import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { ProductAnalyticsModule } from '@vendasta/product-analytics';
import { SalesToolCommonModule } from '../common';
import { SideDrawerModule } from '../common/side-drawer/side-drawer.module';
import { SnapshotScorecardModule } from '../snapshot-scorecard/snapshot-scorecard.module';
import { PinModule } from '../uikit-staging/pin';
import { CustomFieldsModule } from './custom-fields';
import { EnrichedDataFieldsModule } from './enriched-data-fields';
import { NewCustomFieldsCardModule } from './new-custom-fields/new-custom-fields-card.component';

@NgModule({
  imports: [
    AssigneeEditorModule,
    SalesOpportunitiesModule,
    AsteriskModule,
    CommonModule,
    AstbeModule,
    SalesActivityModule,
    MatInputModule,
    MatIconModule,
    MatCheckboxModule,
    MatCardModule,
    MatButtonModule,
    MatDividerModule,
    MatFormFieldModule,
    MatMenuModule,
    LocalTimeCardModule,
    MatDialogModule,
    MatTooltipModule,
    SnapshotCheckoutModule,
    VaStencilsModule,
    UIKitModule,
    AppCoreModule,
    ErrorCardModule,
    MatTabsModule,
    TasksModule,
    CommonPipesModule,
    GalaxyPipesModule,
    BusinessModule,
    ReactiveFormsModule,
    VaIconModule,
    EmptyStateModule,
    BusinessCategoryModule,
    PartnerModule,
    AsyncTextModule,
    SubTitleModule,
    ReportsCardModule,
    CustomFieldsModule,
    PreferredCurrencyModule,
    BannerModule,
    SalesOpportunityCardModule,
    NavigationModule,
    ProductActivationsModule,
    BusinessPrioritiesModule,
    CampaignsModule,
    GeneralInfoCardModule,
    CampaignsCardModule,
    EmptyStateModule,
    VbcSdkModule,
    AccountInfoRouting,
    TranslateModule,
    LexiconModule.forChild({
      componentName: WEBLATE_COMPONENT_NAME,
      baseTranslation: baseTranslation,
    }),
    ContactFormsModule,
    AccountHealthModule,
    FinancialHealthModule,
    ContactsCardModule,
    SalesOrderShortListModule,
    UTMAttributesModule,
    ManualAutomationsModule,
    MatTableModule,
    MatExpansionModule,
    AccountTagsModule,
    MatButtonToggleModule,
    VoiceRecognitionModule,
    TaskCardModule,
    CreateOpportunityFormModule,
    SlideOutPanelModule,
    SalesToolCommonModule,
    SideDrawerModule,
    SnapshotScorecardModule,
    PinModule,
    GalaxyPageModule,
    ContactFormsModule,
    ProductAnalyticsModule,
    MatProgressSpinnerModule,
    EnrichedDataFieldsModule,
    MatSelectModule,
    NewCustomFieldsCardModule,
    GalaxyFormFieldModule,
  ],
  declarations: [
    ArchiveBusinessDialogComponent,
    TaskCardComponent,
    AccountInfoComponent,
    NotesComponent,
    RecentActivityCardComponent,
    ArchiveBusinessDialogComponent,
    RecentActivityListComponent,
    ImportedUserActionsComponent,
    ProjectsComponent,
    ProductImagesPipe,
    ProjectSubtaskPipe,
    AccountLifecycleComponent,
  ],
  providers: [
    AsteriskService,
    AccountDetailsService,
    TaskCardService,
    TaskCardUiService,
    SalesOpportunityCardService,
    AccountInfoStoreService,
    RecentActivityService,
    AccountGroupApiService,
    UserActivitiesService,
    BusinessPrioritiesService,
    ImportedUserActionsService,
    WhitelabelService,
    PartnerService,
    ProspectApiService,
    ProjectMilestonesService,
    {
      provide: DEFAULT_ACCOUNTS_ORDERS_LAYOUT_TOKEN,
      useFactory: defaultAccountsOrdersLayoutFactory,
      deps: [PartnerService],
    },
    SidePanelStateService,
    SlideOutPanelService,
  ],
})
export class AccountInfoModule {}
