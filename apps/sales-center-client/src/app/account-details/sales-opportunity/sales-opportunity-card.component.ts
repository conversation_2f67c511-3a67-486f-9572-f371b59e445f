import { Component, EventEmitter, Inject, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { Opportunity, Pipeline, USER_PIPELINES_TOKEN } from '@vendasta/sales-ui';
import { combineLatest, Observable, ReplaySubject, Subject } from 'rxjs';
import { map, startWith, withLatestFrom } from 'rxjs/operators';
import { SalesOpportunityCardService } from './sales-opportunity-card.service';

import { SubscriptionList } from '@vendasta/rx-utils';
import {
  ALL_OPPORTUNITIES_ID,
  buildOpportunityCardState$,
  INITIAL_VISIBLE_OPPORTUNITIES,
  OpportunityCardView,
  OpportunityViewEvent,
} from './helpers/view-more-accumulator/view-more-accumulator';

export const showLoadMore = (showAllClicked, numOpportunities) =>
  !showAllClicked && numOpportunities > INITIAL_VISIBLE_OPPORTUNITIES;

export enum GenericPipelineStages {
  Open = 'open',
  ClosedWon = 'closed-won',
  ClosedLost = 'closed-lost',
}

@Component({
  selector: 'app-sales-opportunity-card',
  templateUrl: 'sales-opportunity-card.component.html',
  styleUrls: ['./sales-opportunity-card.component.scss'],
  standalone: false,
})
export class SalesOpportunityCardComponent implements OnInit, OnDestroy {
  opportunities$: Observable<Opportunity[]>;
  loading$: Observable<boolean>;
  showError$: Observable<boolean>;
  showLoadMore$: Observable<boolean>;
  currency$: Observable<string>;

  showAllOpportunitiesForPipeline$$ = new Subject<void>();

  opportunityViewEvents$$ = new Subject<OpportunityViewEvent>();
  viewMoreMap$: Observable<Map<string, OpportunityCardView>>;

  readonly ALL_OPPORTUNITIES_ID = ALL_OPPORTUNITIES_ID;
  readonly genericPipelineStages = Object.values(GenericPipelineStages);

  @Input() partnerId: string;
  @Input() marketId: string;
  @Input() accountGroupId: string;
  @Output() openOpportunityCreateForm = new EventEmitter();

  pipelineCardSelection$$ = new ReplaySubject<string>(1);
  pipelineCardSelection$ = this.pipelineCardSelection$$.pipe(startWith(ALL_OPPORTUNITIES_ID));

  pipelineStageSelection$$ = new ReplaySubject<string>(1);
  pipelineStageSelection$ = this.pipelineStageSelection$$.pipe(startWith('open'));

  private readonly subscriptions = SubscriptionList.new();

  constructor(
    private readonly salesOpportunityCardService: SalesOpportunityCardService,
    @Inject(USER_PIPELINES_TOKEN) readonly pipelines$: Observable<Pipeline[]>,
  ) {
    this.subscriptions.add(
      this.showAllOpportunitiesForPipeline$$.pipe(withLatestFrom(this.pipelineCardSelection$)),
      ([, currentSelection]) => {
        this.opportunityViewEvents$$.next(<OpportunityViewEvent>{
          eventType: 'showMore',
          showMorePipelineID: currentSelection ? currentSelection : ALL_OPPORTUNITIES_ID,
        });
      },
    );

    this.viewMoreMap$ = buildOpportunityCardState$(this.opportunityViewEvents$$);

    this.subscriptions.add(this.salesOpportunityCardService.opportunities$, (opportunities) => {
      opportunities.forEach((o) => {
        this.opportunityViewEvents$$.next(<OpportunityViewEvent>{ eventType: 'addOpportunity', opp: o });
      });
    });

    this.opportunities$ = combineLatest([
      this.viewMoreMap$,
      this.pipelineCardSelection$,
      this.pipelineStageSelection$,
    ]).pipe(
      map(([view, selection, stage]) => {
        const selectedOppsView = view.get(selection);
        const selectedOpps =
          stage === this.ALL_OPPORTUNITIES_ID
            ? selectedOppsView?.opps
            : selectedOppsView?.opps.filter((v) => v.pipelineStage === stage);
        return selectedOppsView?.showLoadMore
          ? selectedOpps.slice(0, INITIAL_VISIBLE_OPPORTUNITIES)
          : selectedOpps || [];
      }),
    );
    this.loading$ = this.salesOpportunityCardService.loading$;
    this.showError$ = this.salesOpportunityCardService.showError$;
    this.showLoadMore$ = combineLatest([this.viewMoreMap$, this.pipelineCardSelection$]).pipe(
      map(([views, selection]) => {
        return views.get(selection) ? views.get(selection).showLoadMore : false;
      }),
      startWith(false),
    );
  }

  ngOnInit(): void {
    this.currency$ = this.salesOpportunityCardService.currency$;
    this.salesOpportunityCardService.loadSalesOpportunities(this.partnerId, this.marketId, this.accountGroupId);
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }

  openSalesOpportunityCreateModal(): void {
    this.openOpportunityCreateForm.emit();
  }

  showAllOpportunities(): void {
    this.showAllOpportunitiesForPipeline$$.next();
  }

  retryLoading(): void {
    this.salesOpportunityCardService.loadSalesOpportunities(this.partnerId, this.marketId, this.accountGroupId);
  }

  changePipelineSelection(id: string): void {
    this.pipelineCardSelection$$.next(id);
  }

  changePipelineStage(stage: string): void {
    this.pipelineStageSelection$$.next(stage);
  }
}
