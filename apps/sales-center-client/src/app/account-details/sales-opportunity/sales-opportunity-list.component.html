<ng-container *ngIf="!showError; then normalState; else errorState"></ng-container>

<ng-template #errorState>
  <div class="error-padding">
    <app-async-text
      failTooltipText="{{ 'ERRORS.UNABLE_TO_LOAD_DISPLAY_VALUE' | translate : { value: 'business category' } }}  {{
        'COMMON.ACTION_LABELS.CLICK_TO_RETRY' | translate
      }}"
      [loadFailed]="showError"
      [loading]="loading"
      (retry)="retryLoading()"
    ></app-async-text>
  </div>
</ng-template>

<ng-template #normalState>
  <ng-container *ngIf="loading; then loadingState; else loaded"></ng-container>
</ng-template>

<ng-template #loaded>
  <ng-container *ngIf="(opportunities$ | async)?.length > 0; then opportunitiesState; else emptyState"></ng-container>
</ng-template>

<ng-template #opportunitiesState>
  <div class="container">
    <div class="row list-item" *ngFor="let opp of opportunities$ | async">
      <div class="col col-xs-11">
        <div class="row">
          <div class="col col-xs-7 col-sm-9">
            <div class="opportunity-name">
              <a *ngIf="opp.name" [routerLink]="opp.opportunityDetailsUrl">
                {{ opp.name }}
              </a>
            </div>
          </div>
          <div class="col col-xs-4 col-sm-3 right-align" *ngIf="currency !== null">
            <span class="opportunity-revenue" *ngIf="!opp.isClosed">
              {{ opp.projectedFirstYearValue | formatCurrency : currency }}
            </span>
          </div>
        </div>
        <div class="row">
          <div class="col col-xs-6">
            <div class="title-tag">
              <va-badge color="positive" *ngIf="opp.isClosed && opp.pipelineStage === CLOSEDWON">
                {{ 'SALES_OPPORTUNITIES.CLOSED_WON' | translate }}
              </va-badge>
              <va-badge color="warn" *ngIf="opp.isClosed && opp.pipelineStage === CLOSEDLOST">
                {{ 'SALES_OPPORTUNITIES.CLOSED_LOST' | translate }}
              </va-badge>
              <ng-container class="tag" *ngIf="!opp.isClosed">
                <ng-container *ngIf="pipelines$ | async as pipelines">
                  <va-badge color="purple" *ngIf="opp.isValidPipelineID; else deletedPipelineBadge">
                    {{ getStageNameFromPipelines(pipelines, opp.pipelineId, opp.probability) }}
                  </va-badge>
                </ng-container>
              </ng-container>
            </div>
          </div>
          <div class="col col-xs-6 right-align">
            <small class="opportunity-updated-date">
              {{ opp.updated | date : 'MMM dd, y - h:mm a' }}
            </small>
          </div>
        </div>
      </div>
      <div class="col col-xs-1">
        <a [routerLink]="opp.opportunityDetailsUrl" class="arrow-container">
          <mat-icon class="opp-arrow">arrow_forward_ios</mat-icon>
        </a>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #deletedPipelineBadge>
  <va-badge color="red">
    {{ 'PIPELINE.DELETED_PIPELINE' | translate }}
  </va-badge>
</ng-template>

<ng-template #emptyState>
  <div class="zeroState">
    <div class="money-icon-div">
      <mat-icon svgIcon="money-icon" class="money-icon"></mat-icon>
    </div>
    <uikit-empty-state>
      <span state-description>
        {{ 'SALES_OPPORTUNITIES.EMPTY_OPPORTUNITIES_DESCRIPTION' | translate }}
      </span>
    </uikit-empty-state>
  </div>
</ng-template>

<ng-template #loadingState>
  <div class="spinner-padding">
    <mat-spinner [diameter]="25" [strokeWidth]="3"></mat-spinner>
  </div>
</ng-template>
