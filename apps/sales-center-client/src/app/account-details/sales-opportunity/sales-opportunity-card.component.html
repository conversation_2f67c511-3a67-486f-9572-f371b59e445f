<mat-card appearance="outlined">
  <mat-card-header id="opportunity-label" class="mat-card-header-with-action">
    <mat-card-title>
      <span>{{ 'SALES_OPPORTUNITIES.OPPORTUNITIES_LABEL' | translate }}</span>
      <ng-content select="[afterTitle]"></ng-content>
    </mat-card-title>
    <button mat-icon-button color="primary" (click)="openSalesOpportunityCreateModal()">
      <mat-icon>add</mat-icon>
    </button>
  </mat-card-header>

  <mat-card-content class="opportunities-list">
    <mat-form-field appearance="outline" class="opportunity-card__pipeline-selector">
      <mat-label>Pipeline</mat-label>
      <mat-select [value]="ALL_OPPORTUNITIES_ID" (selectionChange)="changePipelineSelection($event.value)">
        <mat-option [value]="ALL_OPPORTUNITIES_ID">All</mat-option>
        <mat-option *ngFor="let pipeline of pipelines$ | async" [value]="pipeline.id">
          {{ pipeline.name }}
        </mat-option>
      </mat-select>
    </mat-form-field>
    <mat-form-field appearance="outline" class="opportunity-card__pipeline-selector">
      <mat-label>Stage</mat-label>
      <mat-select [value]="'open'" (selectionChange)="changePipelineStage($event.value)">
        <mat-option [value]="ALL_OPPORTUNITIES_ID">All</mat-option>
        <mat-option *ngFor="let stage of genericPipelineStages" [value]="stage">
          {{ stage }}
        </mat-option>
      </mat-select>
    </mat-form-field>
    <app-sales-opportunity-list
      [opportunities]="opportunities$ | async"
      [loading]="loading$ | async"
      [currency]="currency$ | async"
      [showError]="showError$ | async"
      (retry)="retryLoading()"
    ></app-sales-opportunity-list>
  </mat-card-content>

  <mat-card-actions align="end" *ngIf="showLoadMore$ | async">
    <button mat-button color="primary" (click)="showAllOpportunities()">
      {{ 'COMMON.ACTION_LABELS.VIEW_ALL' | translate }}
    </button>
  </mat-card-actions>
</mat-card>
