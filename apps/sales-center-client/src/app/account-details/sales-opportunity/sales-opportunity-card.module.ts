import { NgModule } from '@angular/core';
import { SalesOpportunityCardComponent } from './sales-opportunity-card.component';
import { SalesOpportunityListComponent } from './sales-opportunity-list.component';
import { EmptyStateModule, UIKitModule } from '@vendasta/uikit';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';
import { SalesToolCommonModule } from '../../common';
import { AsyncTextModule } from '../async-text/async-text.module';
import { PreferredCurrencyModule, PipelineStagePipe, SelectedPipelinePipe } from '@vendasta/sales-common';
import { TranslateModule } from '@ngx-translate/core';
import { MatSelectModule } from '@angular/material/select';
import { PipelinePipesModule } from './pipes/pipeline-pipes.module';
import { SlideOutPanelService } from '@vendasta/sales-ui';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@NgModule({
  imports: [
    SalesToolCommonModule,
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatDividerModule,
    MatButtonModule,
    UIKitModule,
    AsyncTextModule,
    PreferredCurrencyModule,
    EmptyStateModule,
    TranslateModule,
    MatSelectModule,
    PipelinePipesModule,
    MatProgressSpinnerModule,
  ],
  exports: [SalesOpportunityCardComponent],
  declarations: [SalesOpportunityCardComponent, SalesOpportunityListComponent],
  providers: [PipelineStagePipe, SelectedPipelinePipe, SlideOutPanelService],
})
export class SalesOpportunityCardModule {}
