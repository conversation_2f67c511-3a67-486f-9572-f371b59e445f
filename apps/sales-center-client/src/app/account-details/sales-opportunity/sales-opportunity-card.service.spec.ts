import { TestScheduler } from 'rxjs/testing';
import { SalesOpportunityCardService } from './sales-opportunity-card.service';
import { listOpportunitiesMockResponse } from '@vendasta/sales-ui';
import { map } from 'rxjs/operators';
import { Observable, of } from 'rxjs';
import { Configuration } from '../../partner';

let scheduler: TestScheduler;

class SalesOpportunityApiMock {
  loadOpportunities = jest.fn(() => {
    return scheduler.createColdObservable('x', { x: listOpportunitiesMockResponse });
  });
}

describe('SalesOpportunityCardServiceSpec', () => {
  let service: SalesOpportunityCardService;

  beforeEach(() => (scheduler = new TestScheduler((a, b) => expect(a).toEqual(b))));
  afterEach(() => scheduler.flush());

  describe('loadSalesOpportunities', () => {
    let mockConfig: Observable<Configuration>;
    beforeEach(() => {
      mockConfig = of({
        defaultDisplayCurrency: 'USD',
      });

      service = new SalesOpportunityCardService(new SalesOpportunityApiMock() as any, mockConfig);
    });

    it('should load the opportunities', () => {
      service.loadSalesOpportunities('ABC', 'default', 'AG-123');
      const oppNames$ = service.opportunities$.pipe(map((opps) => opps.map((opp) => opp.name)));
      scheduler.expectObservable(oppNames$).toBe('x', { x: ['Ricks Guitars', 'Customer Voice'] });
    });
  });
});
