import { Observable } from 'rxjs';
import { Opportunity } from '@vendasta/sales-ui';
import { Inject, Injectable } from '@angular/core';
import { ObservableWorkState } from '@vendasta/rx-utils/work-state';
import { map } from 'rxjs/operators';
import { SalesOpportunitiesService } from '../../sales-opportunities/sales-opportunities.service';
import { Configuration, PARTNER_CONFIG_TOKEN } from '../../partner';

@Injectable()
export class SalesOpportunityCardService {
  private readonly opportunitiesWorkstate = new ObservableWorkState<Opportunity[]>();
  readonly currency$: Observable<string> = this.configuration$.pipe(map((c) => c.defaultDisplayCurrency));

  constructor(
    private readonly salesOpportunityService: SalesOpportunitiesService,
    @Inject(PARTNER_CONFIG_TOKEN) public readonly configuration$: Observable<Configuration>,
  ) {}

  get opportunities$(): Observable<Opportunity[]> {
    return this.opportunitiesWorkstate.workResults$;
  }

  get loading$(): Observable<boolean> {
    return this.opportunitiesWorkstate.isLoading$;
  }

  get showError$(): Observable<boolean> {
    return this.opportunitiesWorkstate.isSuccess$.pipe(map((val) => !val));
  }

  loadSalesOpportunities(partnerId: string, marketId: string, accountGroupId: string): void {
    // FIXME: page and use hasMore* from api response to determine to use whether to show Load More button
    // see fixme in below api service to expose hasMore from api response
    this.opportunitiesWorkstate.startWork(
      this.salesOpportunityService.loadOpportunities(partnerId, marketId, accountGroupId, null, [], 100),
    );
  }
}
