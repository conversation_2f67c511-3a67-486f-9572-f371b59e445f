import { Pipe, PipeTransform } from '@angular/core';
import { Opportunity, Pipeline } from '@vendasta/sales-ui';
import { DEFAULT_PIPELINE_ID } from '@vendasta/sales-ui';

@Pipe({
  name: 'isOppOnDefaultPipeline',
  standalone: false,
})
export class IsOppOnDefaultPipelinePipe implements PipeTransform {
  transform(opp: Opportunity, currentPipeline: Pipeline): boolean {
    return currentPipeline.id === DEFAULT_PIPELINE_ID && opp.pipelineId === DEFAULT_PIPELINE_ID;
  }
}

@Pipe({
  name: 'isOppAssignedToPipeline',
  standalone: false,
})
export class IsOppAssignedToPipelinePipe implements PipeTransform {
  transform(opp: Opportunity, currentPipeline: Pipeline): boolean {
    return opp.pipelineId !== DEFAULT_PIPELINE_ID && currentPipeline.id !== DEFAULT_PIPELINE_ID;
  }
}

@Pipe({
  name: 'isOppUnassigned',
  standalone: false,
})
export class IsOppUnassignedPipe implements PipeTransform {
  transform(opp: Opportunity, currentPipeline: Pipeline): any {
    return opp.pipelineId === DEFAULT_PIPELINE_ID && currentPipeline.id !== DEFAULT_PIPELINE_ID;
  }
}
