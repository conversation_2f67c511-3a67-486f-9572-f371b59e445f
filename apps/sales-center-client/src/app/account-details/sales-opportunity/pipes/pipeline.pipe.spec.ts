import { DEFAULT_PIPELINE_ID, Opportunity, Pipeline } from '@vendasta/sales-ui';
import { IsOppAssignedToPipelinePipe, IsOppOnDefaultPipelinePipe, IsOppUnassignedPipe } from './pipeline.pipe';
import { PipelineStagePipe, SelectedPipelinePipe } from '@vendasta/sales-common';

const currentPipelineNotOnDefault = {
  id: 'some-pipeline-id-that-is-not-default',
};
const opportunityNotOnDefaultPipeline = {
  pipelineId: 'non-pipeline-default-id',
};
const currentPipelineOnDefault = {
  id: DEFAULT_PIPELINE_ID,
};
const opportunityOnDefaultPipeline = {
  pipelineId: DEFAULT_PIPELINE_ID,
};

describe('Pipeline pipes tests', () => {
  describe('isOppOnDefaultPipeline', () => {
    const pipeToTest = new IsOppOnDefaultPipelinePipe();
    it('Should return true if the current pipeline and opportunity pipeline are equal to the default', () => {
      const actualValue = pipeToTest.transform(
        opportunityOnDefaultPipeline as Opportunity,
        currentPipelineOnDefault as Pipeline,
      );
      expect(actualValue).toBe(true);
    });
    it('Should return false if user pipeline id is not equal to the default', () => {
      const actualValue = pipeToTest.transform(
        opportunityOnDefaultPipeline as Opportunity,
        currentPipelineNotOnDefault as Pipeline,
      );
      expect(actualValue).toBe(false);
    });
    it('Should return false if opportunity pipeline id is not equal to the default', () => {
      const actualValue = pipeToTest.transform(
        opportunityNotOnDefaultPipeline as Opportunity,
        currentPipelineOnDefault as Pipeline,
      );
      expect(actualValue).toBe(false);
    });
  });

  describe('isOppAssignedToPipeline', () => {
    const pipeToTest = new IsOppAssignedToPipelinePipe();

    it('Should return true if user is not on a default pipeline and the opportunity is not set to a default pipeline', () => {
      const actualValue = pipeToTest.transform(
        opportunityNotOnDefaultPipeline as Opportunity,
        currentPipelineNotOnDefault as Pipeline,
      );
      expect(actualValue).toBe(true);
    });
    it('Should return false if user is on a default pipeline', () => {
      const actualValue = pipeToTest.transform(
        opportunityNotOnDefaultPipeline as Opportunity,
        currentPipelineOnDefault as Pipeline,
      );
      expect(actualValue).toBe(false);
    });
    it('Should return false if opportunity is on a default pipeline id', () => {
      const actualValue = pipeToTest.transform(
        opportunityNotOnDefaultPipeline as Opportunity,
        currentPipelineOnDefault as Pipeline,
      );
      expect(actualValue).toBe(false);
    });
  });

  describe('isOppUnassigned', () => {
    const pipeToTest = new IsOppUnassignedPipe();

    it('Should return true if the user is not on the current pipeline and the opportunity is set to the default pipeline', () => {
      const actualValue = pipeToTest.transform(
        opportunityOnDefaultPipeline as Opportunity,
        currentPipelineNotOnDefault as Pipeline,
      );
      expect(actualValue).toBe(true);
    });
    it('Should return false if the user is on a default pipeline id', () => {
      const actualValue = pipeToTest.transform(
        opportunityNotOnDefaultPipeline as Opportunity,
        currentPipelineOnDefault as Pipeline,
      );
      expect(actualValue).toBe(false);
    });
    it('Should return false if the opportunity is not set to a default pipeline id', () => {
      const actualValue = pipeToTest.transform(
        opportunityNotOnDefaultPipeline as Opportunity,
        currentPipelineNotOnDefault as Pipeline,
      );
      expect(actualValue).toBe(false);
    });
  });

  describe('selectedPipeline', () => {
    const pipelinesToPickFrom = [
      {
        id: 'id-1',
      },
      {
        id: 'id-2',
      },
      {
        id: 'id-3',
        name: 'Pied Piper',
      },
    ];
    const pipelineIDToFind = 'id-3';
    const pipeToTest = new SelectedPipelinePipe();
    it('Should return the selected pipeline from the list', () => {
      const actualPipeline = pipeToTest.transform(pipelinesToPickFrom as Pipeline[], pipelineIDToFind);
      expect(actualPipeline.name).toBe('Pied Piper');
    });
  });

  describe('pipelineStage', () => {
    const stageToTest = {
      stageId: 'stage-420',
      name: 'a-nice-stage',
      probability: 69,
    };

    it('Should return a stage', () => {
      const pipelineToTest = new Pipeline([stageToTest], 'pipeline-1', 'some-pipeline');
      const pipeToTest = new PipelineStagePipe();
      const actualValue = pipeToTest.transform(pipelineToTest, 40);
      expect(actualValue.name).toBe('a-nice-stage');
    });
  });
});
