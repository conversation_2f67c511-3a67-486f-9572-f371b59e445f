@use 'design-tokens' as *;

:host {
  display: block;
}

.list-item {
  display: flex;
  font-size: 16px;
  padding: 16px 0;
  border-bottom: 1px solid $light-gray;
}

.list-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.opportunity-name {
  margin: 0 8px 0 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.opportunity-revenue,
.opportunity-updated-date {
  color: $gray;
}

.right-align {
  text-align: end;
}

.flex-header-row {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
}

.opportunity-card__pipeline-selector {
  width: 100%;
}

.opp-arrow {
  color: $dark-gray;
  font-size: 16px;
  height: 16px;
  padding-left: 8px;
}

.arrow-container {
  flex-grow: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.spinner-padding {
  padding: 16px;
}

.zeroState {
  margin: 0 auto;
  padding-top: 16px;
}

.money-icon-div {
  text-align: center;
  color: $secondary-font-color;
  margin-top: 0;
  margin-bottom: 8px;
}

.money-icon {
  height: 36px;
  width: 36px;
}
