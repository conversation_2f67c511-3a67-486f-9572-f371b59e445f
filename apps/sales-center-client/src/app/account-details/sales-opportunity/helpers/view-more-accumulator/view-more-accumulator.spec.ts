import { Subject } from 'rxjs';
import {
  ALL_OPPORTUNITIES_ID,
  buildOpportunityCardState$,
  OpportunityCardView,
  OpportunityViewEvent,
} from './view-more-accumulator';
import { Opportunity } from '@vendasta/sales-ui';
import { TestScheduler } from 'rxjs/testing';
import { schedule } from '@vendasta/rx-utils';
import { map } from 'rxjs/operators';

const emptyOpportunity = { pipelineId: 'fake-id-123' } as Opportunity;

describe('view-more-accumulator functionality', () => {
  describe('constructor', () => {
    it('Should build a map with a single all opportunities key', (done) => {
      const emptySubject$$ = new Subject<OpportunityViewEvent>();
      const initialAllOppsView = <OpportunityCardView>{ showLoadMore: false, opps: [] };
      const actualValue$ = buildOpportunityCardState$(emptySubject$$);
      actualValue$.subscribe((m) => {
        expect(m.get(ALL_OPPORTUNITIES_ID)).toMatchObject(initialAllOppsView);
        done();
      });
    });
  });
  describe('Adding opportunities', () => {
    let sched: TestScheduler;
    beforeEach(() => {
      sched = new TestScheduler((a, b) => expect(a).toEqual(b));
    });
    afterEach(() => sched.flush());
    it('Should create a new key and add an opportunity if the key does not exist', () => {
      const emptySubject$$ = new Subject<OpportunityViewEvent>();
      const accumulator$ = buildOpportunityCardState$(emptySubject$$);
      schedule(sched, '---|', () =>
        emptySubject$$.next(<OpportunityViewEvent>{ eventType: 'addOpportunity', opp: emptyOpportunity }),
      );
      schedule(sched, '----|', () =>
        emptySubject$$.next(<OpportunityViewEvent>{ eventType: 'addOpportunity', opp: emptyOpportunity }),
      );

      const opportunities$ = accumulator$.pipe(map((m) => m.get('fake-id-123')?.opps));
      sched.expectObservable(opportunities$.pipe(map((o) => o?.length))).toBe('x--yz', {
        x: undefined,
        y: 1,
        z: 2,
      });
    });
    it('Should set the showLoadMore flag to true once the number of opps is greater than 3', () => {
      const emptySubject$$ = new Subject<OpportunityViewEvent>();
      const accumulator$ = buildOpportunityCardState$(emptySubject$$);
      schedule(sched, '---|', () =>
        emptySubject$$.next(<OpportunityViewEvent>{ eventType: 'addOpportunity', opp: emptyOpportunity }),
      );
      schedule(sched, '----|', () =>
        emptySubject$$.next(<OpportunityViewEvent>{ eventType: 'addOpportunity', opp: emptyOpportunity }),
      );
      schedule(sched, '-----|', () =>
        emptySubject$$.next(<OpportunityViewEvent>{ eventType: 'addOpportunity', opp: emptyOpportunity }),
      );
      schedule(sched, '------|', () =>
        emptySubject$$.next(<OpportunityViewEvent>{ eventType: 'addOpportunity', opp: emptyOpportunity }),
      );
      sched.expectObservable(accumulator$.pipe(map((m) => m.get('fake-id-123')?.showLoadMore))).toBe('v--wxyz', {
        v: undefined,
        w: false,
        x: false,
        y: false,
        z: true,
      });
    });
    it('Should correctly count the number of opportunities in the all opportunities key', () => {
      const emptySubject$$ = new Subject<OpportunityViewEvent>();
      const accumulator$ = buildOpportunityCardState$(emptySubject$$);
      schedule(sched, '---|', () =>
        emptySubject$$.next(<OpportunityViewEvent>{ eventType: 'addOpportunity', opp: emptyOpportunity }),
      );
      schedule(sched, '----|', () =>
        emptySubject$$.next(<OpportunityViewEvent>{ eventType: 'addOpportunity', opp: emptyOpportunity }),
      );
      schedule(sched, '-----|', () =>
        emptySubject$$.next(<OpportunityViewEvent>{ eventType: 'addOpportunity', opp: emptyOpportunity }),
      );
      schedule(sched, '------|', () =>
        emptySubject$$.next(<OpportunityViewEvent>{ eventType: 'addOpportunity', opp: emptyOpportunity }),
      );
      sched.expectObservable(accumulator$.pipe(map((m) => m.get(ALL_OPPORTUNITIES_ID)?.opps?.length))).toBe('v--wxyz', {
        v: 0,
        w: 1,
        x: 2,
        y: 3,
        z: 4,
      });
    });
    it('Should toggle showLoadMore flag to false if we send a showMore event', () => {
      const emptySubject$$ = new Subject<OpportunityViewEvent>();
      const accumulator$ = buildOpportunityCardState$(emptySubject$$);
      schedule(sched, '---|', () =>
        emptySubject$$.next(<OpportunityViewEvent>{ eventType: 'addOpportunity', opp: emptyOpportunity }),
      );
      schedule(sched, '----|', () =>
        emptySubject$$.next(<OpportunityViewEvent>{ eventType: 'addOpportunity', opp: emptyOpportunity }),
      );
      schedule(sched, '-----|', () =>
        emptySubject$$.next(<OpportunityViewEvent>{ eventType: 'addOpportunity', opp: emptyOpportunity }),
      );
      schedule(sched, '------|', () =>
        emptySubject$$.next(<OpportunityViewEvent>{ eventType: 'addOpportunity', opp: emptyOpportunity }),
      );
      schedule(sched, '---------|', () =>
        emptySubject$$.next(<OpportunityViewEvent>{ eventType: 'showMore', showMorePipelineID: ALL_OPPORTUNITIES_ID }),
      );
      sched
        .expectObservable(accumulator$.pipe(map((m) => m.get(ALL_OPPORTUNITIES_ID)?.showLoadMore)))
        .toBe('v--wxyz--a', {
          v: false,
          w: false,
          x: false,
          y: false,
          z: true,
          a: false,
        });
    });
  });
});
