import { Observable, Subject } from 'rxjs';
import { scan, shareReplay, startWith } from 'rxjs/operators';
import { Opportunity } from '@vendasta/sales-ui';

export interface OpportunityCardView {
  opps: Opportunity[];
  showLoadMore: boolean;
}

export const INITIAL_VISIBLE_OPPORTUNITIES = 3;

export type OpportunityCardViewEvents = 'addOpportunity' | 'showMore';

export interface OpportunityViewEvent {
  opp: Opportunity;
  eventType: OpportunityCardViewEvents;
  showMorePipelineID: string;
}

export const ALL_OPPORTUNITIES_ID = 'all-opportunities';

export function buildOpportunityCardState$(
  eventSubject$$: Subject<OpportunityViewEvent>,
): Observable<Map<string, OpportunityCardView>> {
  const startValue = new Map<string, OpportunityCardView>().set(ALL_OPPORTUNITIES_ID, <OpportunityCardView>{
    showLoadMore: false,
    opps: [],
  });

  return eventSubject$$.pipe(
    scan((acc: Map<string, OpportunityCardView>, event: OpportunityViewEvent) => {
      if (event.eventType === 'showMore') {
        return setLoadMoreForPipelineView(event.showMorePipelineID, acc);
      }
      if (event.eventType === 'addOpportunity') {
        return addOpportunityForPipelineView(event.opp.pipelineId, event.opp, acc);
      }
    }, startValue),
    startWith(startValue),
    shareReplay(1),
  );
}

function updateAllOppsView(acc: Map<string, OpportunityCardView>, opp: Opportunity): Map<string, OpportunityCardView> {
  const allOppsUpdatedView = addOppsToView(acc.get(ALL_OPPORTUNITIES_ID), opp);
  acc.set(ALL_OPPORTUNITIES_ID, allOppsUpdatedView);
  if (allOppsUpdatedView.opps.length > INITIAL_VISIBLE_OPPORTUNITIES) {
    allOppsUpdatedView.showLoadMore = true;
    acc.set(ALL_OPPORTUNITIES_ID, allOppsUpdatedView);
  }
  return acc;
}

function addOppsToView(view: OpportunityCardView, opp: Opportunity): OpportunityCardView {
  view.opps.push(opp);
  return view;
}

function setLoadMoreForPipelineView(
  ID: string,
  acc: Map<string, OpportunityCardView>,
): Map<string, OpportunityCardView> {
  const viewToChange = acc.get(ID);
  if (viewToChange) {
    viewToChange.showLoadMore = false;
    acc.set(ID, viewToChange);
  }
  return acc;
}

function addOpportunityForPipelineView(
  ID: string,
  opp: Opportunity,
  acc: Map<string, OpportunityCardView>,
): Map<string, OpportunityCardView> {
  const savedView = acc.get(ID);
  if (!savedView) {
    const newOppView = <OpportunityCardView>{
      opps: [opp],
      showLoadMore: false,
    };
    acc.set(opp.pipelineId, newOppView);
  } else {
    const updatedExistingOppsView = addOppsToView(savedView, opp);
    if (updatedExistingOppsView.opps.length > INITIAL_VISIBLE_OPPORTUNITIES) {
      updatedExistingOppsView.showLoadMore = true;
    }
    acc.set(ID, updatedExistingOppsView);
  }
  return updateAllOppsView(acc, opp);
}
