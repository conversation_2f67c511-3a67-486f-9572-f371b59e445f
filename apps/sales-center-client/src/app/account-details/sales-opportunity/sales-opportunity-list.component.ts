import { Component, EventEmitter, Inject, Input, Output } from '@angular/core';
import { PipelineStagePipe, SelectedPipelinePipe } from '@vendasta/sales-common';
import { Opportunity, Pipeline, USER_PIPELINES_TOKEN, USER_PIPELINE_TOKEN } from '@vendasta/sales-ui';
import { Observable, ReplaySubject, combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';
import { SalesActivity } from '../../sales-activity';

interface OpportunityCardListItem extends Opportunity {
  isValidPipelineID?: boolean;
}

@Component({
  selector: 'app-sales-opportunity-list',
  templateUrl: 'sales-opportunity-list.component.html',
  styleUrls: ['./sales-opportunity-card.component.scss'],
  standalone: false,
})
export class SalesOpportunityListComponent {
  readonly pipelines$ = combineLatest([this.pipelineToken$, this.pipelinesToken$]).pipe(
    map(([, pipelines]) => pipelines),
  );

  private readonly opportunities$$ = new ReplaySubject<Opportunity[]>();

  readonly opportunities$ = combineLatest([this.opportunities$$.asObservable(), this.pipelines$]).pipe(
    map(([opportunity, pipelines]: [OpportunityCardListItem[], Pipeline[]]) =>
      opportunity.map((opp) => {
        opp.isValidPipelineID = this.isPipelineIdValid(pipelines, opp.pipelineId);
        return opp;
      }),
    ),
  );

  readonly CLOSEDLOST = SalesActivity.STATUS_ENUM.CLOSED_LOST;
  readonly CLOSEDWON = SalesActivity.STATUS_ENUM.CLOSED_WON;

  @Input() set opportunities(opportunities: Opportunity[]) {
    this.opportunities$$.next(opportunities);
  }

  @Input() loading = true;
  @Input() showError = false;
  @Input() currency: string;
  @Output() retry = new EventEmitter<null>();

  constructor(
    @Inject(USER_PIPELINE_TOKEN) readonly pipelineToken$: Observable<Pipeline>,
    @Inject(USER_PIPELINES_TOKEN) readonly pipelinesToken$: Observable<Pipeline[]>,
    private selectedPipelinePipe: SelectedPipelinePipe,
    private pipelineStagePipe: PipelineStagePipe,
  ) {}

  retryLoading(): void {
    this.retry.emit(null);
  }

  getStageNameFromPipeline(pipeline: Pipeline, probability: number): string {
    const stage = this.pipelineStagePipe.transform(pipeline, probability);
    return stage.name;
  }

  private getPipelineById(pipelines: Pipeline[], pipelineId: string): Pipeline {
    return this.selectedPipelinePipe.transform(pipelines, pipelineId);
  }

  isPipelineIdValid(pipelines: Pipeline[], pipelineId: string): boolean {
    return !!this.getPipelineById(pipelines, pipelineId);
  }

  getStageNameFromPipelines(pipelines: Pipeline[], pipelineId: string, probability: number): string {
    const pipeline = this.getPipelineById(pipelines, pipelineId);
    return this.getStageNameFromPipeline(pipeline, probability);
  }
}
