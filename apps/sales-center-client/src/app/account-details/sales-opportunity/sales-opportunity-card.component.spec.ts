import { showLoadMore } from './sales-opportunity-card.component';
import { INITIAL_VISIBLE_OPPORTUNITIES } from './helpers/view-more-accumulator/view-more-accumulator';

describe('showLoadMore', () => {
  test('value is true when button has not been pressed and opportunities exceeds initial show value', () => {
    const loadMoreClicked = false;
    const numOpportunities = INITIAL_VISIBLE_OPPORTUNITIES + 1;

    const actual = showLoadMore(loadMoreClicked, numOpportunities);

    expect(actual).toBe(true);
  });
  test('value is false when button has not been pressed and opportunities is less or equal to initial show value', () => {
    const loadMoreClicked = false;
    const numOpportunitiesEqual = INITIAL_VISIBLE_OPPORTUNITIES;
    const numOpportunitiesLess = INITIAL_VISIBLE_OPPORTUNITIES - 1;
    const numOpportunitiesZero = 0;

    const actualEqual = showLoadMore(loadMoreClicked, numOpportunitiesEqual);
    const actualLess = showLoadMore(loadMoreClicked, numOpportunitiesLess);
    const actualZero = showLoadMore(loadMoreClicked, numOpportunitiesZero);

    expect(actualEqual).toBe(false);
    expect(actualLess).toBe(false);
    expect(actualZero).toBe(false);
  });
  test('value is false when button has been pressed and opportunities exceeds initial show value', () => {
    const loadMoreClicked = true;
    const numOpportunities = INITIAL_VISIBLE_OPPORTUNITIES + 1;

    const actual = showLoadMore(loadMoreClicked, numOpportunities);

    expect(actual).toBe(false);
  });
  test('value is false when button has been pressed and opportunities is less or equal to initial show value', () => {
    const loadMoreClicked = true;
    const numOpportunitiesEqual = INITIAL_VISIBLE_OPPORTUNITIES;
    const numOpportunitiesLess = INITIAL_VISIBLE_OPPORTUNITIES - 1;
    const numOpportunitiesZero = 0;

    const actualEqual = showLoadMore(loadMoreClicked, numOpportunitiesEqual);
    const actualLess = showLoadMore(loadMoreClicked, numOpportunitiesLess);
    const actualZero = showLoadMore(loadMoreClicked, numOpportunitiesZero);

    expect(actualEqual).toBe(false);
    expect(actualLess).toBe(false);
    expect(actualZero).toBe(false);
  });
});
