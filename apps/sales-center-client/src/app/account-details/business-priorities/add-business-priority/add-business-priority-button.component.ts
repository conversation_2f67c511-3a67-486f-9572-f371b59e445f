import { Component, EventEmitter, Inject, Input, Output } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { AddBusinessPriorityComponent } from './add-business-priority.component';

@Component({
  selector: 'app-business-priority-button',
  template: `
    <button mat-button color="accent" (click)="openManager()">
      <mat-icon inline="true">add</mat-icon>
      {{ 'ACCOUNT_DETAILS.BUSINESS_PRIORITIES.ADD_NEW' | translate }}
    </button>
  `,
  standalone: false,
})
export class AddBusinessPriorityButtonComponent {
  @Input() showDescription: boolean;
  @Input() dialogTitle: string;
  @Output() saveStrategy: EventEmitter<string[]> = new EventEmitter<string[]>();

  constructor(
    private readonly dialog: MatDialog,
    @Inject(MAT_DIALOG_DATA) public data: string,
  ) {}

  openManager(): void {
    const dialogRef = this.dialog.open(AddBusinessPriorityComponent, {
      width: '585px',
      autoFocus: false,
      panelClass: 'strategy-panel',
      data: { showDescription: this.showDescription, title: this.dialogTitle },
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.saveStrategy.emit(result.data);
      }
    });
  }
}
