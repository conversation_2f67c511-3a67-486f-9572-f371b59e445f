import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { UntypedFormControl } from '@angular/forms';

@Component({
  selector: 'app-priority-dialog',
  templateUrl: './add-business-priority.component.html',
  styleUrls: ['./add-business-priority.component.scss'],
  standalone: false,
})
export class AddBusinessPriorityComponent {
  newTitle = '';
  newDescription = '';
  strategyFormControl = new UntypedFormControl();
  constructor(
    public dialogRef: MatDialogRef<AddBusinessPriorityComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {}

  saveNew(): void {
    this.dialogRef.close({ data: { title: this.newTitle, description: this.newDescription } });
  }

  onClose(): void {
    this.dialogRef.close();
  }
}
