<h2 mat-dialog-title>New {{ data.title }}</h2>
<mat-dialog-content class="strategy-content" autoFocus="false">
  <form class="add-new-strategy-form">
    <mat-form-field>
      <label>
        <input
          matInput
          [formControl]="strategyFormControl"
          #title
          placeholder="Title"
          maxlength="100"
          [required]="true"
          [(ngModel)]="newTitle"
        />
      </label>
      <mat-hint align="end">{{ title.value.length }} / 100</mat-hint>
    </mat-form-field>
    <mat-form-field *ngIf="data.showDescription">
      <label>
        <textarea
          matInput
          cdkTextareaAutosize
          #autosize="cdkTextareaAutosize"
          maxlength="1000"
          [formControl]="strategyFormControl"
          #description
          placeholder="Description"
          [(ngModel)]="newDescription"
        ></textarea>
      </label>
    </mat-form-field>
  </form>
</mat-dialog-content>
<mat-dialog-actions>
  <button mat-stroked-button (click)="onClose()" mat-dialog-close>Cancel</button>
  <button mat-raised-button color="primary" (click)="saveNew()" mat-dialog-close>Save</button>
</mat-dialog-actions>
