import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogConfig, MatDialogRef } from '@angular/material/dialog';
import { GetPartnerAccountGroupMappingResponseInterface } from '@galaxy/partner';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Observable, combineLatest, of } from 'rxjs';
import { catchError, map, publishReplay, refCount, switchMap, take } from 'rxjs/operators';
import { EditPartnerIdDialogService } from './edit-partner-id-dialog.service';

@Component({
  selector: 'app-edit-partner-id',
  templateUrl: './edit-partner-id-dialog.component.html',
  styleUrls: ['./edit-partner-id-dialog.component.scss'],
  standalone: false,
})
export class EditPartnerIdDialogComponent {
  dialogConfig: MatDialogConfig;
  newPartnerID = '';
  stepOne = true;
  validPID = true;
  saving = false;
  loadingStepTwoData = false;
  stepTwoPageData$: Observable<{
    currentPID: string;
    currentAGID: string;
    currentName: string;
    withPID: string;
    withAGID: string;
    withName: string;
    isSwapping: boolean;
  }>;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: { pid: string; name: string; accountGroupId: string },
    private dialogRef: MatDialogRef<EditPartnerIdDialogComponent>,
    private editPartnerIdService: EditPartnerIdDialogService,
    private readonly snackbarService: SnackbarService,
    private translateService: TranslateService,
  ) {}

  pidUpdated(): void {
    this.validPID = true;
  }

  next(): void {
    this.loadingStepTwoData = true;
    this.stepOne = false;
    this.getStepTwoPageData(this.newPartnerID);
  }

  getStepTwoPageData(pid: string): void {
    const partnerAccountGroup$ = this.editPartnerIdService.getAccountGroupForPID(pid).pipe(
      take(1),
      catchError(() => {
        this.validPID = false;
        this.loadingStepTwoError('');
        return of(null);
      }),
      publishReplay(1),
      refCount(),
    );

    const newAccountGroupName$: Observable<string> = partnerAccountGroup$.pipe(
      take(1),
      switchMap((pag: GetPartnerAccountGroupMappingResponseInterface) => {
        if (!pag) {
          return of('');
        }
        return this.editPartnerIdService.getBusinessName(pag.accountGroupId);
      }),
    );

    partnerAccountGroup$
      .pipe(
        take(1),
        switchMap((pag: GetPartnerAccountGroupMappingResponseInterface) => {
          if (!pag) {
            return of(false);
          }
          return this.editPartnerIdService.getBusinessNameFailed(pag.accountGroupId);
        }),
      )
      .subscribe((failed) => {
        if (failed) {
          this.loadingStepTwoError(
            this.translateService.instant('ACCOUNT_DETAILS.EDIT_PARTNER_ID.GET_BUSINESS_NAME_ERROR'),
          );
        }
      });

    this.stepTwoPageData$ = combineLatest([partnerAccountGroup$, newAccountGroupName$]).pipe(
      take(1),
      map(([pag, newName]) => {
        this.loadingStepTwoData = false;
        if (!!pag && !!newName) {
          return {
            currentPID: this.data.pid,
            currentAGID: this.data.accountGroupId,
            currentName: this.data.name,
            withPID: pag.partnerId,
            withAGID: pag.accountGroupId,
            withName: newName,
            isSwapping: !!this.data.pid, // Only if the business is currently mapped is it a swap
          };
        }
      }),
    );
  }

  loadingStepTwoError(errorMessage: string): void {
    this.stepOne = true;
    this.loadingStepTwoData = false;
    if (errorMessage !== '') {
      this.snackbarService.openErrorSnack(errorMessage);
    }
  }

  save(isSwap: boolean, currentPID: string, currentAGID: string, withPID: string, withAGID: string): void {
    this.saving = true;
    if (isSwap) {
      this.swapAccounts(currentPID, currentAGID, withPID, withAGID);
    } else {
      this.updateAccount(withPID, currentAGID);
    }
  }

  swapAccounts(partnerId: string, accountGroupId: string, withPartnerId: string, withAccountGroupId: string): void {
    this.editPartnerIdService.swapAccountGroup(partnerId, accountGroupId, withPartnerId, withAccountGroupId).subscribe(
      (response) => {
        if (response.success) {
          this.snackbarService.openSuccessSnack('ACCOUNT_DETAILS.EDIT_PARTNER_ID.SAVE_PARTNER_ID_SUCCESS');
          this.saving = false;
          this.close(true);
        } else {
          if (response.error) {
            this.snackbarService.openErrorSnack(response.error);
          } else {
            this.snackbarService.openErrorSnack('ACCOUNT_DETAILS.EDIT_PARTNER_ID.SAVE_PARTNER_ID_ERROR');
          }
          this.saving = false;
        }
      },
      (error) => {
        this.snackbarService.openErrorSnack(error.toString());
      },
    );
  }

  updateAccount(partnerId: string, accountGroupId: string): void {
    this.editPartnerIdService.updateAccountGroup(partnerId, accountGroupId).subscribe((response) => {
      if (response.success) {
        this.snackbarService.openSuccessSnack('ACCOUNT_DETAILS.EDIT_PARTNER_ID.SAVE_PARTNER_ID_SUCCESS');
        this.saving = false;
        this.close(true);
      } else {
        if (response.error) {
          this.snackbarService.openErrorSnack(response.error);
        } else {
          this.snackbarService.openErrorSnack('ACCOUNT_DETAILS.EDIT_PARTNER_ID.SAVE_PARTNER_ID_ERROR');
        }
        this.saving = false;
      }
    });
  }

  close(updateSuccess: boolean): void {
    const newPID = updateSuccess ? this.newPartnerID : null;
    this.dialogRef.close(newPID);
  }
}
