<h2 mat-dialog-title>
  {{ 'ACCOUNT_DETAILS.EDIT_PARTNER_ID.TITLE' | translate }}
</h2>

<!-- Step One -->
<ng-container *ngIf="stepOne; else stepTwo">
  <mat-dialog-content>
    <div class="edit-partner-content">
      <p>
        {{ 'ACCOUNT_DETAILS.EDIT_PARTNER_ID.LINK_ID_DESCRIPTION' | translate: { account_name: data.name } }}
      </p>
      <input
        type="text"
        class="input-pid"
        [(ngModel)]="newPartnerID"
        (ngModelChange)="pidUpdated()"
        placeholder="{{ 'ACCOUNT_DETAILS.EDIT_PARTNER_ID.PARTNER_ID' | translate }}"
      />
      <mat-error *ngIf="!validPID">
        {{ 'ACCOUNT_DETAILS.EDIT_PARTNER_ID.PARTNER_ID_INVALID' | translate }}
      </mat-error>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions>
    <button class="cancel" mat-flat-button type="button" (click)="close(false)">
      {{ 'COMMON.ACTION_LABELS.CANCEL' | translate }}
    </button>
    <button class="send" mat-flat-button color="primary" [disabled]="newPartnerID.length === 0" (click)="next()">
      {{ 'COMMON.ACTION_LABELS.NEXT' | translate }}
    </button>
  </mat-dialog-actions>
</ng-container>

<!-- Step Two -->
<ng-template #stepTwo>
  <ng-container *ngIf="stepTwoPageData$ | async as stepTwoPageData; else loadingContent">
    <mat-dialog-content>
      <div class="edit-partner-content">
        <p>
          {{
            'ACCOUNT_DETAILS.EDIT_PARTNER_ID.CURRENTLY_LINKED_DESCRIPTION'
              | translate: { partner_id: stepTwoPageData.withPID }
          }}
          <a href="/info/{{ stepTwoPageData.withAGID }}" target="_blank">
            {{ stepTwoPageData.withName }}
          </a>
          .
          <mat-icon class="new-window">open_in_new</mat-icon>
        </p>

        <div *ngIf="stepTwoPageData.isSwapping; else updatePIDContent">
          <span>
            {{ 'ACCOUNT_DETAILS.EDIT_PARTNER_ID.PROCEEDING' | translate }}
          </span>
          <ol class="edit-pid-list">
            <li>
              {{
                'ACCOUNT_DETAILS.EDIT_PARTNER_ID.CURRENTLY_LINKED_STEP_ONE'
                  | translate
                    : {
                        partner_id: newPartnerID,
                        account_name: stepTwoPageData.currentName
                      }
              }}
            </li>
            <li>
              <a href="/info/{{ stepTwoPageData.withAGID }}" target="_blank">
                {{ stepTwoPageData.withName }}
              </a>
              <mat-icon class="new-window">open_in_new</mat-icon>
              &nbsp;{{
                'ACCOUNT_DETAILS.EDIT_PARTNER_ID.CURRENTLY_LINKED_STEP_TWO'
                  | translate: { current_partner_id: stepTwoPageData.currentPID }
              }}
            </li>
          </ol>
        </div>

        <ng-template #updatePIDContent>
          <span>
            {{ 'ACCOUNT_DETAILS.EDIT_PARTNER_ID.PROCEEDING' | translate }}
          </span>
          <ol class="edit-pid-list">
            <li>
              {{
                'ACCOUNT_DETAILS.EDIT_PARTNER_ID.NOT_LINKED_STEP_ONE'
                  | translate
                    : {
                        partner_id: stepTwoPageData.withPID,
                        account_name: stepTwoPageData.currentName
                      }
              }}
            </li>
            <li>
              <a href="/info/{{ stepTwoPageData.withAGID }}" target="_blank">
                {{ stepTwoPageData.withName }}
              </a>
              <mat-icon class="new-window">open_in_new</mat-icon>
              {{ 'ACCOUNT_DETAILS.EDIT_PARTNER_ID.NOT_LINKED_STEP_TWO' | translate }}
            </li>
          </ol>
        </ng-template>
      </div>
    </mat-dialog-content>

    <mat-dialog-actions>
      <button class="cancel" mat-flat-button type="button" (click)="close(false)">
        {{ 'COMMON.ACTION_LABELS.CANCEL' | translate }}
      </button>
      <ng-container *ngIf="!saving; else loadingButton">
        <button
          class="send"
          mat-flat-button
          color="primary"
          (click)="
            save(
              stepTwoPageData.isSwapping,
              stepTwoPageData.currentPID,
              stepTwoPageData.currentAGID,
              stepTwoPageData.withPID,
              stepTwoPageData.withAGID
            )
          "
        >
          {{ 'ACCOUNT_DETAILS.EDIT_PARTNER_ID.SAVE' | translate }}
        </button>
      </ng-container>
    </mat-dialog-actions>
  </ng-container>
</ng-template>

<!-- Reusable Templates -->
<ng-template #loadingContent>
  <mat-progress-spinner class="content-spinner" mode="indeterminate" diameter="48"></mat-progress-spinner>
</ng-template>

<ng-template #loadingButton>
  <button class="send" mat-flat-button color="primary">
    <mat-progress-spinner class="button-spinner" mode="indeterminate" diameter="30"></mat-progress-spinner>
  </button>
</ng-template>
