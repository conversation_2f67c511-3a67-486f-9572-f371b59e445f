import { Observable, of } from 'rxjs';
import { catchError, map, take } from 'rxjs/operators';
import { GetPartnerAccountGroupMappingResponseInterface, PartnerAccountGroupService } from '@galaxy/partner';
import { AccountInfoStoreService } from '../../account-info-store.service';
import { Injectable } from '@angular/core';

export interface AccountMappingReturnInterface {
  success: boolean;
  error: string;
}

@Injectable()
export class EditPartnerIdDialogService {
  constructor(
    private partnerAccountGroupService: PartnerAccountGroupService,
    private accountInfoService: AccountInfoStoreService,
  ) {}

  getAccountGroupForPID(partnerID: string): Observable<GetPartnerAccountGroupMappingResponseInterface> {
    return this.partnerAccountGroupService
      .getPartnerAccountGroupMapping(partnerID, null)
      .pipe(map((accountInfo) => (accountInfo ? accountInfo : null)));
  }

  getBusinessName(accountGroupID: string): Observable<string> {
    return this.accountInfoService
      .fetchBusinessInfo(accountGroupID)
      .pipe(map((businessInfo) => (businessInfo ? businessInfo.name : '')));
  }

  getBusinessNameFailed(accountGroupID: string): Observable<boolean> {
    return this.accountInfoService.isFailed$(accountGroupID);
  }

  swapAccountGroup(
    partnerID: string,
    accountGroupId: string,
    withPartnerID: string,
    withAccountGroupId: string,
  ): Observable<AccountMappingReturnInterface> {
    return this.partnerAccountGroupService
      .swapPartnerAccountGroup(partnerID, accountGroupId, withPartnerID, withAccountGroupId)
      .pipe(
        map((success) => {
          return { success: success, error: '' };
        }),
        catchError((error) => {
          return of({ success: false, error: error });
        }),
        take(1),
      );
  }

  updateAccountGroup(partnerID: string, accountGroup: string): Observable<AccountMappingReturnInterface> {
    return this.partnerAccountGroupService.updatePartnerAccountGroup(partnerID, accountGroup).pipe(
      map((success) => {
        return { success: success, error: '' };
      }),
      catchError((error) => {
        return of({ success: false, error: error });
      }),
      take(1),
    );
  }
}
