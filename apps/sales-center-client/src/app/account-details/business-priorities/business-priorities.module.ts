import { DragDropModule } from '@angular/cdk/drag-drop';
import { LayoutModule } from '@angular/cdk/layout';
import { CommonModule as AngularCommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { PartnerService } from '@galaxy/partner';
import { TranslateModule } from '@ngx-translate/core';
import { VaFormsModule } from '@vendasta/forms';
import { GalaxySnackbarModule } from '@vendasta/galaxy/snackbar-service';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { HostService, OnboardingStrategiesApiService } from '@vendasta/prospect';
import { VaStencilsModule } from '@vendasta/uikit';
import { SalesToolCommonModule } from '../../common';
import { ACCESS_ASSOCIATED_PARTNER_INFO_TOKEN, canAccessAssociatedPartnerInfo } from '../../partner/partner-overrides';
import { AddBusinessPriorityButtonComponent } from './add-business-priority/add-business-priority-button.component';
import { AddBusinessPriorityComponent } from './add-business-priority/add-business-priority.component';
import { BusinessPrioritiesService } from './business-priorities.service';
import { EditPartnerIdDialogComponent } from './edit-partner-id-dialog/edit-partner-id-dialog.component';
import { EditPartnerIdDialogService } from './edit-partner-id-dialog/edit-partner-id-dialog.service';
import { EditableBusinessPrioritiesComponent } from './editable-business-priorities/editable-business-priorities.component';

@NgModule({
  declarations: [
    AddBusinessPriorityButtonComponent,
    AddBusinessPriorityComponent,
    EditPartnerIdDialogComponent,
    EditableBusinessPrioritiesComponent,
  ],
  imports: [
    AngularCommonModule,
    MatAutocompleteModule,
    MatChipsModule,
    MatIconModule,
    MatCardModule,
    MatDividerModule,
    DragDropModule,
    LayoutModule,
    MatSelectModule,
    MatTooltipModule,
    MatButtonModule,
    MatInputModule,
    MatExpansionModule,
    SalesToolCommonModule,
    VaStencilsModule,
    VaFormsModule,
    MatDialogModule,
    TranslateModule,
    GalaxySnackbarModule,
    GalaxyTooltipModule,
    MatProgressSpinnerModule,
  ],
  exports: [AddBusinessPriorityButtonComponent, AddBusinessPriorityComponent, EditableBusinessPrioritiesComponent],
  providers: [
    BusinessPrioritiesService,
    OnboardingStrategiesApiService,
    EditPartnerIdDialogService,
    HostService,
    {
      provide: ACCESS_ASSOCIATED_PARTNER_INFO_TOKEN,
      useFactory: canAccessAssociatedPartnerInfo,
      deps: [PartnerService],
    },
  ],
})
export class BusinessPrioritiesModule {}
