import { Clipboard } from '@angular/cdk/clipboard';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { Component, ElementRef, Inject, Input, OnInit, ViewChild, OnDestroy } from '@angular/core';
import { MatAutocomplete } from '@angular/material/autocomplete';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { EMPTY, firstValueFrom, iif, Observable } from 'rxjs';
import { map, switchMap, take } from 'rxjs/operators';
import {
  TextDialogComponent,
  TextModalDefaultSizes,
  TextModalDialogResult,
} from '../../../common/text-modal/text-modal.component';
import { ACCESS_ASSOCIATED_PARTNER_INFO_TOKEN } from '../../../partner/partner-overrides';
import { ViewBusiness } from '../../account-info-store.service';
import { BusinessPrioritiesService, BusinessPrioritiesStore } from '../business-priorities.service';
import { EditPartnerIdDialogComponent } from '../edit-partner-id-dialog/edit-partner-id-dialog.component';

enum PriorityType {
  BusinessPriorities = 'businessPriorities',
  TrainingPriorities = 'trainingPriorities',
  QuickWins = 'quickWins',
}

const ONBOARDING_PANEL_STATE_KEY = 'onboardingStrategyPanelState';

@Component({
  selector: 'app-editable-business-priorities',
  templateUrl: './editable-business-priorities.component.html',
  styleUrls: ['./editable-business-priorities.component.scss'],
  standalone: false,
})
export class EditableBusinessPrioritiesComponent implements OnInit, OnDestroy {
  @Input() partnerId: string;
  @Input() marketId: string;
  @Input() businessId: string;
  @Input() businessInfo: ViewBusiness;
  @Input() associatedPartnerId$: Observable<string>;
  @ViewChild('auto') matAutocomplete: MatAutocomplete;
  @ViewChild('trInput') trInput: ElementRef<HTMLInputElement>;

  PriorityType = PriorityType;
  dataAnalyticsGroup = 'business-priorities';
  dataAnalyticsEditAction = 'clicked-edit-button-';
  dataAnalyticsSaveAction = 'clicked-save-button-';
  dataAnalyticsEventName = 'business-priorities-event';

  isExtraSmallScreen: boolean;
  panelIsOpen: boolean;
  store$: Observable<BusinessPrioritiesStore>;
  trainingPrioritiesDescription$: Observable<string>;
  trainingPrioritiesTitle$: Observable<string>;
  goalDescription$: Observable<string>;
  goalTitle$: Observable<string>;
  earlyWinDescription$: Observable<string>;
  earlyWinTitle$: Observable<string>;

  constructor(
    private readonly businessPrioritiesService: BusinessPrioritiesService,
    public breakpointObserver: BreakpointObserver,
    private readonly translate: TranslateService,
    private dialog: MatDialog,
    private clipboard: Clipboard,
    private analyticsService: ProductAnalyticsService,
    @Inject(ACCESS_ASSOCIATED_PARTNER_INFO_TOKEN) public readonly accessAssociatedPartnerInfo$: Observable<boolean>,
  ) {
    this.store$ = this.businessPrioritiesService.businessPrioritiesStore$;
  }

  openPanel(): void {
    localStorage.setItem(ONBOARDING_PANEL_STATE_KEY, 'true');
    this.panelIsOpen = true;
  }

  closePanel(): void {
    localStorage.setItem(ONBOARDING_PANEL_STATE_KEY, 'false');
    this.panelIsOpen = false;
  }

  initPanelState(): void {
    // checks to see if there is a previously stored open/closed state for the
    // onboarding panel. If not, it defaults to open for desktop and closed
    // for mobile
    this.isExtraSmallScreen = this.breakpointObserver.isMatched(Breakpoints.XSmall);
    const storedPanelState = localStorage.getItem(ONBOARDING_PANEL_STATE_KEY);
    // convert storedPanelState string to bool:
    const storedPanelBool = storedPanelState === 'true';

    if (storedPanelState) {
      // If they have interacted with the panel, restore expanded state
      this.panelIsOpen = storedPanelBool;
    } else if (this.isExtraSmallScreen) {
      // collapse the onboarding panel if mobile
      this.panelIsOpen = false;
    } else {
      // show onboarding panel on tablet and desktop by default
      this.panelIsOpen = true;
    }
  }

  ngOnInit(): void {
    // Set the initial Open/Closed state of the onboarding panel
    this.initPanelState();
    this.businessPrioritiesService.initializeStore(
      this.businessId,
      this.partnerId,
      this.marketId,
      this.associatedPartnerId$,
    );
    this.goalDescription$ = this.businessPrioritiesService.goalDescription$;
    this.goalTitle$ = this.businessPrioritiesService.goalTitle$;
    this.trainingPrioritiesDescription$ = this.businessPrioritiesService.trainingPrioritiesDescription$;
    this.trainingPrioritiesTitle$ = this.businessPrioritiesService.trainingPrioritiesTitle$;
    this.earlyWinDescription$ = this.businessPrioritiesService.earlyWinDescription$;
    this.earlyWinTitle$ = this.businessPrioritiesService.earlyWinTitle$;
  }

  ngOnDestroy(): void {
    this.businessPrioritiesService.onDestroy();
  }

  openViewOnlyTextModal(priorityType: PriorityType, value: string, title: string): void {
    this.openTextModal(priorityType, value, title, false);
  }

  openEditableTextModal(priorityType: PriorityType, value: string, title: string): void {
    this.openTextModal(priorityType, value, title, true);
  }

  private openTextModal(priorityType: PriorityType, value: string, title: string, editable: boolean): void {
    const dialogRef = this.dialog.open(TextDialogComponent, {
      width: TextModalDefaultSizes.width,
      height: TextModalDefaultSizes.height,
      data: {
        title: title,
        placeholder: this.getTitleForPriorityType(priorityType),
        text: value,
        editMode: editable,
      },
    });

    const breakpointSub = this.breakpointObserver
      .observe([Breakpoints.Small, Breakpoints.XSmall, Breakpoints.Handset])
      .subscribe((result) => {
        if (result.matches) {
          dialogRef.updateSize(TextModalDefaultSizes.maxWidth, TextModalDefaultSizes.maxHeight);
        } else {
          dialogRef.updateSize(TextModalDefaultSizes.width, TextModalDefaultSizes.height);
          dialogRef.updatePosition();
        }
      });

    dialogRef.afterClosed().subscribe((result) => {
      breakpointSub.unsubscribe();
      this.updatePriority(priorityType, result);
    });
  }

  updatePriority(priorityType: PriorityType, result: TextModalDialogResult): void {
    if (result) {
      switch (priorityType) {
        case PriorityType.BusinessPriorities:
          firstValueFrom(this.goalDescription$).then((goalID) =>
            this.businessPrioritiesService.updateGoal(goalID, result.text, result.title),
          );
          break;
        case PriorityType.TrainingPriorities:
          this.businessPrioritiesService.setTrainingPrioritiesDescription(result.text, result.title);
          break;
        case PriorityType.QuickWins:
          this.businessPrioritiesService.updateEarlyWins(result.text, result.title);
          break;
        default:
          break;
      }
    }

    this.analyticsService.trackEvent(this.dataAnalyticsEventName, 'text-field', 'save', null, {
      group: this.dataAnalyticsGroup,
      action: this.dataAnalyticsSaveAction + priorityType,
    });
  }

  getTitleForPriorityType(priorityType: PriorityType): string {
    switch (priorityType) {
      case PriorityType.BusinessPriorities:
        return this.translate.instant('ACCOUNT_DETAILS.BUSINESS_PRIORITIES.TITLE');
      case PriorityType.TrainingPriorities:
        return this.translate.instant('ACCOUNT_DETAILS.BUSINESS_PRIORITIES.TRAINING_PRIORITIES');
      case PriorityType.QuickWins:
        return this.translate.instant('ACCOUNT_DETAILS.BUSINESS_PRIORITIES.QUICK_WINS');
      default:
        return '';
    }
  }

  copyStringToClipboard(str: string): void {
    this.clipboard.copy(str);
  }

  editPartnerID(pid: string, name: string, accountGroupId: string): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = true;
    dialogConfig.width = '550px';
    dialogConfig.minHeight = '300px';
    dialogConfig.data = {
      pid: pid,
      name: name,
      accountGroupId: accountGroupId,
    };
    const dialogRef = this.dialog.open(EditPartnerIdDialogComponent, dialogConfig);
    dialogRef
      .afterClosed()
      .pipe(
        take(1),
        switchMap((partnerId) =>
          iif(
            () => !!partnerId,
            this.store$.pipe(
              map((storeInfo) => {
                storeInfo.partnerInfo.partnerId = partnerId;
              }),
              take(1),
            ),
            EMPTY,
          ),
        ),
      )
      .subscribe();
  }

  checkForOverflow(element) {
    return element.offsetHeight < element.scrollHeight || element.offsetWidth < element.scrollWidth;
  }
}
