<mat-expansion-panel
  id="business-priorities"
  [expanded]="panelIsOpen"
  *ngIf="store$ | async as store"
  (opened)="openPanel()"
  (closed)="closePanel()"
>
  <mat-expansion-panel-header>
    <mat-panel-title>
      {{ 'ACCOUNT_DETAILS.BUSINESS_PRIORITIES.TITLE' | translate }}
    </mat-panel-title>
  </mat-expansion-panel-header>

  <section class="business-priorities-container" *ngIf="!store.loading">
    <!-- Main Goal -->
    <ng-container
      [ngTemplateOutlet]="businessPriorityTemplate"
      [ngTemplateOutletContext]="{
        value: goalDescription$ | async,
        title: goalTitle$ | async,
        placeholder: 'ACCOUNT_DETAILS.BUSINESS_PRIORITIES.MAIN_GOAL',
        priority: PriorityType.BusinessPriorities,
        emptyButton: 'ACCOUNT_DETAILS.BUSINESS_PRIORITIES.ADD_MAIN_GOAL',
        emptyDescription: 'ACCOUNT_DETAILS.BUSINESS_PRIORITIES.EMPTY_MAIN_GOAL_DESCRIPTION'
      }"
    ></ng-container>

    <!-- Training priorities -->
    <ng-container
      [ngTemplateOutlet]="businessPriorityTemplate"
      [ngTemplateOutletContext]="{
        value: trainingPrioritiesDescription$ | async,
        title: trainingPrioritiesTitle$ | async,
        placeholder: 'ACCOUNT_DETAILS.BUSINESS_PRIORITIES.TRAINING_PRIORITIES',
        priority: PriorityType.TrainingPriorities,
        emptyButton: 'ACCOUNT_DETAILS.BUSINESS_PRIORITIES.ADD_TRAINING_PRIORITIES',
        emptyDescription: 'ACCOUNT_DETAILS.BUSINESS_PRIORITIES.EMPTY_TRAINING_DESCRIPTION'
      }"
    ></ng-container>

    <!-- Quick Wins -->
    <ng-container
      [ngTemplateOutlet]="businessPriorityTemplate"
      [ngTemplateOutletContext]="{
        value: earlyWinDescription$ | async,
        title: earlyWinTitle$ | async,
        placeholder: 'ACCOUNT_DETAILS.BUSINESS_PRIORITIES.QUICK_WINS',
        priority: PriorityType.QuickWins,
        emptyButton: 'ACCOUNT_DETAILS.BUSINESS_PRIORITIES.ADD_QUICK_WINS',
        emptyDescription: 'ACCOUNT_DETAILS.BUSINESS_PRIORITIES.QUICK_WINS_HINT'
      }"
    ></ng-container>

    <!-- Partner information -->
    <div class="priority-section partner-info" *ngIf="accessAssociatedPartnerInfo$ | async">
      <div class="priority-section-title">
        <span>Partner information</span>
      </div>

      <div class="priority-section-content">
        <div class="partner-info-list">
          <div class="partner-info-list--item">
            <div class="key">Partner ID:</div>
            <div
              class="value is-link"
              (click)="copyStringToClipboard(store.partnerInfo.partnerId)"
              [glxyTooltip]="'ACCOUNT_DETAILS.COPY_PID' | translate"
            >
              {{ (store.partnerInfo.partnerId | uppercase) || '-' }}
            </div>
            <a (click)="editPartnerID(store.partnerInfo.partnerId, businessInfo.name, businessInfo.accountGroupId)">
              <mat-icon inline class="edit-pid-link" [glxyTooltip]="'ACCOUNT_DETAILS.EDIT_PID' | translate">
                edit
              </mat-icon>
            </a>

            <ng-container *ngIf="!!store.partnerInfo.partnerId">
              <a
                class="impersonate-link"
                href="https://partners.vendasta.com/?_pid={{ store.partnerInfo.partnerId | uppercase }}"
                target="_blank"
                rel="noopener"
              >
                <mat-icon [glxyTooltip]="'ACCOUNT_DETAILS.IMPERSONATE' | translate">person</mat-icon>
              </a>
            </ng-container>
          </div>

          <ng-container>
            <div class="partner-info-list--item">
              <div class="key">Subscription:</div>
              <div class="value">
                {{ store.partnerInfo.subscriptionLevel }}
              </div>
            </div>
          </ng-container>

          <ng-container *ngIf="store.partnerInfo.numberOfEmployees">
            <div class="partner-info-list--item">
              <div class="key">Employees:</div>
              <div class="value">
                {{ store.partnerInfo.numberOfEmployees }}
              </div>
            </div>
          </ng-container>

          <ng-container *ngIf="businessInfo.countryCode">
            <div class="partner-info-list--item">
              <div class="key">Country:</div>
              <div class="value">
                {{ businessInfo.countryCode }}
              </div>

              <a
                class="flag-icon"
                [glxyTooltip]="'ACCOUNT_DETAILS.SHOW_ON_MAP' | translate"
                href="https://www.google.ca/maps/search/country:{{ businessInfo.countryCode | lowercase }}"
                target="_blank"
                rel="noopener"
              >
                <img src="https://www.countryflags.io/{{ businessInfo.countryCode | uppercase }}/flat/24.png" alt="" />
              </a>
            </div>
          </ng-container>
        </div>
      </div>
    </div>
  </section>

  <uikit-list-stencil [showHeader]="false" rowHeight="184px" [numRows]="1" *ngIf="store.loading"></uikit-list-stencil>
</mat-expansion-panel>

<ng-template
  #businessPriorityTemplate
  let-value="value"
  let-title="title"
  let-placeholder="placeholder"
  let-priority="priority"
  let-emptyButton="emptyButton"
  let-emptyDescription="emptyDescription"
>
  <div class="priority-section">
    <div class="priority-section-title">
      <div>
        <ng-container *ngIf="title">{{ title }}</ng-container>
        <ng-container *ngIf="!title">{{ placeholder | translate }}</ng-container>
      </div>
      <button
        class="edit-button"
        *ngIf="value && value !== ''"
        mat-icon-button
        color="primary"
        (click)="openEditableTextModal(priority, value, title)"
        [attr.data-action-group]="dataAnalyticsGroup"
        [attr.data-action]="dataAnalyticsEditAction + priority"
      >
        <mat-icon>edit</mat-icon>
      </button>
    </div>

    <div class="priority-section-content">
      <!-- Show Value -->
      <div *ngIf="value">
        <div #priorityValue class="business-priorities--description" [innerHTML]="value"></div>
        <div class="show-more-div" *ngIf="checkForOverflow(priorityValue)">
          <button
            mat-stroked-button
            color="basic"
            class="show-more-button"
            (click)="openViewOnlyTextModal(priority, value, title)"
          >
            {{ 'COMMON.ACTION_LABELS.SHOW_MORE' | translate }}
          </button>
        </div>
      </div>

      <!-- Empty State -->
      <ng-container *ngIf="!value">
        <div class="empty-state-with-button">
          <p>{{ emptyDescription | translate }}</p>
          <button mat-stroked-button (click)="openEditableTextModal(priority, value, title)">
            {{ emptyButton | translate }}
          </button>
        </div>
      </ng-container>
    </div>
  </div>
</ng-template>
