@use 'design-tokens' as dt;

// Set the two responsive breakpoints that work
// best with the design of the card
$width-large: dt.$media--desktop-medium-minimum;
$width-small: dt.$media--tablet-minimum;
$pid-values-font-size: dt.$font-preset-5-size;

:host #business-priorities.mat-expansion-panel ::ng-deep {
  .mat-expansion-panel-body {
    padding: 0;
  }
}

// Main Business Priorities Container
mat-panel-title {
  font-size: dt.$font-preset-3-size;
}

.priority-section-title {
  height: 40px;
  color: dt.$tertiary-font-color;
  font: 400 15px/24px Roboto, 'Helvetica Neue', sans-serif;
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  flex-grow: 0;
  flex-shrink: 0;
}

// Main Containter
.business-priorities-container {
  display: flex;
  align-content: stretch;
}

// Individual Business Priorities Items
.priority-section {
  flex: 1 1 25%;
  position: relative;
  padding: dt.$spacing-3 dt.$spacing-3 0 dt.$spacing-3;

  display: flex;
  flex-direction: column;

  overflow: hidden;

  &:not(:first-child) {
    // Add the border between sections
    @media only screen and (min-width: dt.$media--desktop-medium-minimum) {
      border-left: 1px solid dt.$border-color;
    }
  }
}

// Empty state content
.empty-state-with-button {
  color: dt.$secondary-font-color;
  text-align: center;
  padding: dt.$spacing-2 dt.$spacing-3;
  max-width: 260px;
  margin: auto;
}

.edit-button {
  position: absolute;
  top: 0;
  right: 0;
}

.priority-section-content {
  padding: dt.$spacing-2 0;
  margin-top: -(dt.$spacing-3);
  flex-grow: 3;
  flex-shrink: 3;
}

.business-priorities--description {
  overflow: hidden;
  word-wrap: break-word;
  max-height: 210px;
}

//  VMF Partner Information
.partner-info-list {
  list-style-type: none;
  padding-left: 0;
  margin: 0;
}
.partner-info-list--item {
  padding-bottom: dt.$spacing-1;
  margin-bottom: dt.$spacing-1;
  display: flex;
  align-items: center;
  border-bottom: 1px solid dt.$border-color;

  &:last-child {
    border-bottom: none;
  }
}
.partner-info-list .key {
  font-size: $pid-values-font-size;
  font-weight: 500;
  padding-right: dt.$spacing-1;
  min-width: 95px;
}
.partner-info-list .value {
  font-family: dt.$monospace-font-family;
  font-weight: 400;
  background: dt.$field-border-color;
  word-break: break-all;
  display: inline-block;
  border-radius: dt.$default-border-radius;
  font-size: $pid-values-font-size;
  padding: dt.$spacing-1 dt.$spacing-2;

  &.is-link {
    cursor: pointer;
  }
  &.is-link:hover {
    background: dt.$contrast-icon-color;
  }
  &.is-link:active {
    background: dt.$primary-background-selected-color;
  }
}

.partner-info-list {
  .flag-icon {
    margin-left: dt.$spacing-2;
    line-height: 0;
  }

  .impersonate-link {
    margin-left: dt.$spacing-2;
  }

  .edit-pid-link {
    margin-left: dt.$spacing-2;
  }
}

// Media Queries

// When screen width < 1280px then let
// business priorities items display as 2x2 grid
@media only screen and (max-width: $width-large) {
  .business-priorities-container {
    flex-wrap: wrap;
    padding: 0;
  }

  .priority-section {
    flex: 1 1 40%;
    padding: dt.$spacing-2 dt.$spacing-4;
    border-bottom: 1px solid dt.$border-color;
  }
  .priority-section:nth-child(odd) {
    border-right: 1px solid dt.$border-color;
  }
  .priority-section:nth-child(odd):last-child {
    border-right: none;
  }
  .priority-section.hide-border-mobile {
    border-bottom: none;
  }

  .empty-state-with-button {
    padding: 0 0 dt.$spacing-3;
  }
}

// Collapses down to one column on small screens
@media only screen and (max-width: $width-small) {
  .business-priorities-container {
    display: block;
  }
  .priority-section {
    padding: dt.$spacing-2 dt.$spacing-3;
  }
  .priority-section:nth-child(odd) {
    border-right: none;
  }

  .priority-section.partner-info {
    border-top: 1px solid dt.$border-color;
  }
}

.show-more-div {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 64px;
  background: linear-gradient(transparent 0%, dt.$primary-background-color 75%, dt.$primary-background-color 100%);

  .show-more-button {
    top: 50%;
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    margin: 0 auto;
    display: block;
    background: white;
  }
}
