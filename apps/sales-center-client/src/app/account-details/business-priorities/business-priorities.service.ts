import { Inject, Injectable } from '@angular/core';
import { PartnerAccountGroupService, SubscriptionTiersService } from '@galaxy/partner';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import {
  GetPriorityRequestInterface,
  OnboardingStrategiesApiService,
  ReplaceEarlyWinForProspectRequestInterface,
  SetPriorityRequestInterface,
} from '@vendasta/prospect';
import {
  BehaviorSubject,
  Observable,
  ReplaySubject,
  Subscription,
  combineLatest,
  firstValueFrom,
  merge,
  of,
} from 'rxjs';
import { catchError, map, scan, shareReplay, switchMap, take } from 'rxjs/operators';
import { CustomField } from '../../custom-fields';
import { LoggedInUserInfoService } from '../../logged-in-user-info';
import { Configuration, PartnerSdkWrapperService } from '../../partner';
import { ACCESS_ASSOCIATED_PARTNER_INFO_TOKEN } from '../../partner/partner-overrides';
import { CustomFieldsStoreService } from '../custom-fields/custom-fields-store.service';

export interface BusinessPrioritiesStore {
  earlyWins: string;
  trainingPriorities: string[];
  goal: string;
  loading: boolean;
  partnerInfo: PartnerInfo;
}

export interface PartnerInfo {
  partnerId: string;
  numberOfEmployees?: string;
  subscriptionLevel?: string;
  numberOfAccounts?: string;
  country?: string;
}

@Injectable({
  providedIn: 'root',
})
export class BusinessPrioritiesService {
  businessPrioritiesStore$$: ReplaySubject<BusinessPrioritiesStore> = new ReplaySubject(1);
  private readonly trainingPriorities$$ = new BehaviorSubject<string[]>([]);
  readonly trainingPriorities$ = this.trainingPriorities$$.asObservable();
  private readonly goal$$ = new BehaviorSubject<string>('');
  readonly goal$ = this.goal$$.asObservable();
  private readonly goalDescription$$ = new BehaviorSubject<string>('');
  readonly goalDescription$ = this.goalDescription$$.asObservable();
  private readonly goalTitle$$ = new BehaviorSubject<string>('');
  readonly goalTitle$ = this.goalTitle$$.asObservable();
  private readonly trainingPrioritiesDescription$$ = new BehaviorSubject<string>('');
  readonly trainingPrioritiesDescription$ = this.trainingPrioritiesDescription$$.asObservable();
  private readonly trainingPrioritiesTitle$$ = new BehaviorSubject<string>('');
  readonly trainingPrioritiesTitle$ = this.trainingPrioritiesTitle$$.asObservable();
  private readonly earlyWinDescription$$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  readonly earlyWinDescription$ = this.earlyWinDescription$$.asObservable();
  private readonly earlyWinTitle$$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  readonly earlyWinTitle$ = this.earlyWinTitle$$.asObservable();
  businessId = '';
  associatedPartnerId$: Observable<string>;
  customFields$: Observable<CustomField[]>;
  storeSubscription: Subscription;
  partnerId$: Observable<string> = this.loggedInUserService.partnerId$;

  constructor(
    private readonly customFieldService: CustomFieldsStoreService,
    private readonly partnerService: PartnerSdkWrapperService,
    private readonly apiService: OnboardingStrategiesApiService,
    private readonly loggedInUserService: LoggedInUserInfoService,
    private readonly subscriptionService: SubscriptionTiersService,
    private readonly partnerAccountGroupService: PartnerAccountGroupService,
    private readonly snackbarService: SnackbarService,
    private readonly translateService: TranslateService,
    @Inject(ACCESS_ASSOCIATED_PARTNER_INFO_TOKEN) private readonly canAccessAssociatedPartnerInfo$: Observable<boolean>,
  ) {}

  get businessPrioritiesStore$(): Observable<BusinessPrioritiesStore> {
    return this.businessPrioritiesStore$$.asObservable();
  }

  loadTrainingPrioritiesForProspect(): void {
    this.apiService
      .listPrioritiesForProspect({ prospectId: this.businessId })
      .pipe(
        take(1),
        map((resp) => resp.priorityIds || []),
      )
      .subscribe((p) => this.trainingPriorities$$.next(p));
  }

  private loadTrainingPrioritiesDescription(): void {
    const request: GetPriorityRequestInterface = {
      prospectId: this.businessId,
    };
    firstValueFrom(this.apiService.getPriority(request))
      .then((priority) => {
        this.trainingPrioritiesDescription$$.next(priority.description);
        this.trainingPrioritiesTitle$$.next(priority.title);
      })
      .catch(() =>
        this.snackbarService.openErrorSnack('ACCOUNT_DETAILS.BUSINESS_PRIORITIES.ERROR_GETTING_TRAINING_PRIORITIES'),
      );
  }

  setTrainingPrioritiesDescription(description: string, title: string): void {
    const request: SetPriorityRequestInterface = {
      prospectId: this.businessId,
      description: description,
      title: title,
    };
    firstValueFrom(this.apiService.setPriority(request))
      .then(() => {
        this.trainingPrioritiesDescription$$.next(description);
        this.trainingPrioritiesTitle$$.next(title);
      })
      .catch(() =>
        this.snackbarService.openErrorSnack('ACCOUNT_DETAILS.BUSINESS_PRIORITIES.ERROR_SETTING_TRAINING_PRIORITIES'),
      );
  }

  loadGoalForProspect(): void {
    firstValueFrom(this.apiService.getGoalForProspect({ prospectId: this.businessId }))
      .then((goal) => {
        this.goal$$.next(goal.goalId);
        this.goalDescription$$.next(goal.description);
        this.goalTitle$$.next(goal.title);
      })
      .catch(() => this.snackbarService.openErrorSnack('ACCOUNT_DETAILS.BUSINESS_PRIORITIES.ERROR_GETTING_MAIN_GOAL'));
  }

  initializeStore(
    businessId: string,
    partnerId: string,
    marketId: string,
    associatedPartnerId$: Observable<string>,
  ): void {
    this.businessId = businessId;
    this.associatedPartnerId$ = associatedPartnerId$;
    this.customFields$ = this.customFieldService.fetchCustomFields(businessId, partnerId, marketId);
    this.storeSubscription = merge(
      this.customFieldService.isLoadingCustomFields$(businessId).pipe(map((loading) => ({ loading }))),
      this.fetchPartnerInfomation().pipe(map((partnerInfo) => ({ partnerInfo }))),
    )
      .pipe(
        scan((acc, cur) => ({ ...acc, ...cur }), {
          loading: true,
          partnerInfo: {
            partnerId: '',
          },
        }),
        catchError((err) => {
          console.error(err);
          return of({});
        }),
      )
      .subscribe(this.businessPrioritiesStore$$);
    this.loadGoalForProspect();
    this.loadTrainingPrioritiesForProspect();
    this.loadTrainingPrioritiesDescription();
    this.loadEarlyWin();
  }

  fetchPartnerInfomation(): Observable<PartnerInfo> {
    return combineLatest([this.canAccessAssociatedPartnerInfo$, this.associatedPartnerId$]).pipe(
      switchMap(([canAccess, associatedPartnerId]) => {
        if (!canAccess || !associatedPartnerId) {
          return of({ partnerId: '' });
        }

        const subscriptionLevelAndPID$ = this.partnerService.getConfiguration$(associatedPartnerId, '').pipe(
          map(
            (res: Configuration): PartnerInfo => ({
              partnerId: associatedPartnerId,
              subscriptionLevel: res.subscriptionLevel,
            }),
          ),
        );

        const subscriptionNameAndPid$: Observable<PartnerInfo> = subscriptionLevelAndPID$.pipe(
          switchMap((partnerInfo: PartnerInfo) =>
            this.subscriptionService
              .getSubscriptionTier(partnerInfo.subscriptionLevel, partnerInfo.partnerId)
              .pipe(map((res) => ({ partnerId: partnerInfo.partnerId, subscriptionLevel: res.name }))),
          ),
        );

        const numberOfEmployees$ = this.customFields$.pipe(
          map((fields) => fields.find((f) => f.name === 'num_employees').value || '-'),
        );

        return combineLatest([subscriptionNameAndPid$, numberOfEmployees$]).pipe(
          map(([nameAndPid, numberOfEmployees]) => {
            return {
              partnerId: associatedPartnerId,
              numberOfEmployees: numberOfEmployees,
              subscriptionLevel: nameAndPid.subscriptionLevel,
              numberOfAccounts: nameAndPid.numberOfAccounts,
              country: nameAndPid.country,
            };
          }),
          shareReplay(1),
        );
      }),
    );
  }

  updateGoal(goalID: string, description: string, title: string): void {
    this.apiService
      .replaceGoalForProspect({ prospectId: this.businessId, goalId: goalID, description: description, title: title })
      .pipe(take(1))
      .subscribe(() => {
        this.loadGoalForProspect();
      });
  }

  updateEarlyWins(earlyWins: string, title: string): void {
    const request: ReplaceEarlyWinForProspectRequestInterface = {
      earlyWin: { description: earlyWins, title: title },
      prospectId: this.businessId,
    };
    this.apiService
      .replaceEarlyWinForProspect(request)
      .pipe(take(1))
      .subscribe(() => {
        this.loadEarlyWin();
      });
  }

  private loadEarlyWin(): void {
    this.apiService
      .getEarlyWinForProspect({ prospectId: this.businessId })
      .pipe(take(1))
      .subscribe((ew) => {
        if (ew.earlyWin) {
          this.earlyWinDescription$$.next(ew.earlyWin.description);
          this.earlyWinTitle$$.next(ew.earlyWin.title);
        }
      });
  }

  onDestroy(): void {
    this.businessPrioritiesStore$$.next({
      loading: true,
      earlyWins: '',
      trainingPriorities: [],
      goal: '',
      partnerInfo: {
        partnerId: '',
      },
    });
    this.storeSubscription.unsubscribe();
  }
}
