<mat-card appearance="outlined">
  <mat-card-header>
    <mat-card-title>
      {{ 'ACCOUNT_DETAILS.RECENT_ACTIVITY.TITLE' | translate }}
    </mat-card-title>
  </mat-card-header>
  <mat-card-content>
    <app-recent-activity-list
      [recentActivities]="recentActivities$ | async"
      [productHotness]="productHotness$ | async"
      [loading]="loading$ | async"
      [success]="success$ | async"
      (retry)="retryLoading()"
      (contactClicked)="contactClicked()"
    ></app-recent-activity-list>
  </mat-card-content>
  <mat-card-actions align="end">
    <a mat-button color="primary" (click)="performViewAllClick()">
      {{ 'COMMON.ACTION_LABELS.VIEW_ALL' | translate }}
    </a>
  </mat-card-actions>
</mat-card>
