import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { combineLatest, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { RecentActivityService } from './recent-activity.service';
import { ProductHotness, RecentActivity } from './recent-activity';

import { Router } from '@angular/router';

@Component({
  selector: 'app-recent-activity',
  templateUrl: './recent-activity-card.component.html',
  styleUrls: ['./recent-activity-card.component.scss'],
  standalone: false,
})
export class RecentActivityCardComponent implements OnInit {
  @Input() businessId: string;
  @Input() partnerId: string;
  @Input() hotness: number;
  @Input() salesPersonId: string;
  @Output() contactButtonClicked = new EventEmitter();

  loading$: Observable<boolean>;
  success$: Observable<boolean>;
  recentActivities$: Observable<RecentActivity[]>;
  productHotness$: Observable<ProductHotness[]>;
  historyDetailUrl = '';
  constructor(
    private readonly recentActivityService: RecentActivityService,
    private readonly router: Router,
  ) {}

  ngOnInit(): void {
    this.recentActivityService.loadRecentActivities(this.businessId, this.partnerId);
    this.recentActivityService.loadProductHotness(this.businessId, this.partnerId);
    this.loading$ = combineLatest([
      this.recentActivityService.isLoadingRecentActivities$(this.businessId),
      this.recentActivityService.isLoadingProductHotness$(this.businessId),
    ]).pipe(
      map(([activityLoading, hotnessLoading]) => {
        return activityLoading || hotnessLoading;
      }),
    );
    this.success$ = combineLatest([
      this.recentActivityService.isSuccessRecentActivities$(this.businessId),
      this.recentActivityService.isSuccessProductHotness$(this.businessId),
    ]).pipe(
      map(([activitySuccess, hotnessSuccess]) => {
        return activitySuccess && hotnessSuccess;
      }),
    );
    this.recentActivities$ = this.recentActivityService.recentActivities$(this.businessId);
    this.productHotness$ = this.recentActivityService.productHotness$(this.businessId);
    this.historyDetailUrl = `/history/${this.businessId}`;
  }

  retryLoading(): void {
    this.recentActivityService.loadRecentActivities(this.businessId, this.partnerId);
  }

  contactClicked(): void {
    this.contactButtonClicked.emit();
  }

  performViewAllClick(): void {
    this.router.navigateByUrl(this.historyDetailUrl);
  }
}
