/* eslint-disable @typescript-eslint/no-unused-vars */
import { HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { TestScheduler } from 'rxjs/testing';
import { RecentActivityService } from './recent-activity.service';

import {
  getRecentActivityMock,
  getRecentActivityUserEventMock,
  getRecentActivityUserEventMockEmptyName,
  mockRecentActivity,
  mockUserRecentActivity,
  mockUserRecentActivityEmptyName,
} from './mock/recent-activity.mock';
import { ApiService } from '../../common';
import { TranslateService } from '@ngx-translate/core';

let scheduler: TestScheduler;
const DEFAULT_ACTIVITY_AGID = 'AG-FTV5SPLSJG';
const USER_ACTIVITY_AGID = 'AG-USEREVENT';
const EMPTY_NAME_USER_EVENT_AGID = 'AG-EMPTYNAME';

class MockUserActivityService {
  buildActivityDetailDescription(data: any): string {
    return 'requested salesperson assistance';
  }

  shouldUseGenericContactDisplayName(actionDetail: string): boolean {
    return true;
  }
}

class MockTranslateService {
  instant(key: string | Array<string>, interpolateParams?: unknown): string | unknown {
    return 'A Business App user';
  }
}

class MockWhiteLabelService {
  getBranding(partnerId: string): string {
    return 'Business App';
  }
}

class MockErrorApiService {
  get(url: string, params?: HttpParams): Observable<unknown> {
    return scheduler.createColdObservable('-#', undefined, Error('an error'));
  }
}

class MockApiService {
  get(url: string, params?: HttpParams): Observable<unknown> {
    const accountGroupId = params?.get('accountGroupId');
    switch (accountGroupId) {
      case DEFAULT_ACTIVITY_AGID:
        return scheduler.createColdObservable('-x', { x: getRecentActivityMock });
      case USER_ACTIVITY_AGID:
        return scheduler.createColdObservable('-x', { x: getRecentActivityUserEventMock });
      case EMPTY_NAME_USER_EVENT_AGID:
        return scheduler.createColdObservable('-x', { x: getRecentActivityUserEventMockEmptyName });
      default:
        return scheduler.createColdObservable('-#', undefined, Error('an error'));
    }
  }
}

describe('RecentActivityService', () => {
  let service: RecentActivityService;
  let mockApiService: MockApiService | MockErrorApiService;
  let mockUserActivityService: MockUserActivityService;
  let mockWhiteLabelService: MockWhiteLabelService;
  let mockTranslateService: MockTranslateService;

  beforeEach(() => {
    scheduler = new TestScheduler((a, b) => expect(a).toEqual(b));
    mockWhiteLabelService = new MockWhiteLabelService();
  });

  afterEach(() => {
    scheduler.flush();
  });

  describe('loadContacts', () => {
    test('Should load recent activities by account group id', () => {
      mockApiService = new MockApiService();
      service = new RecentActivityService(
        mockApiService as ApiService,
        mockUserActivityService as never,
        mockWhiteLabelService as never,
        null as never,
      );
      service.loadRecentActivities(DEFAULT_ACTIVITY_AGID, undefined as never);
      scheduler
        .expectObservable(service.recentActivities$(DEFAULT_ACTIVITY_AGID))
        .toBe('-x', { x: mockRecentActivity });
    });

    test('Should load user events properly', () => {
      mockApiService = new MockApiService();
      mockUserActivityService = new MockUserActivityService();
      service = new RecentActivityService(
        mockApiService as ApiService,
        mockUserActivityService as any,
        mockWhiteLabelService as any,
        undefined as never,
      );
      service.loadRecentActivities(USER_ACTIVITY_AGID, undefined as never);
      scheduler
        .expectObservable(service.recentActivities$(USER_ACTIVITY_AGID))
        .toBe('-x', { x: mockUserRecentActivity });
    });

    test('Should handle activities on api error properly', () => {
      mockApiService = new MockErrorApiService();
      mockUserActivityService = new MockUserActivityService();
      service = new RecentActivityService(
        mockApiService as ApiService,
        mockUserActivityService as any,
        mockWhiteLabelService as any,
        undefined as never,
      );
      service.loadRecentActivities(USER_ACTIVITY_AGID, undefined as never);
      scheduler.expectObservable(service.recentActivities$(USER_ACTIVITY_AGID)).toBe('', []);
    });

    test('Should display a proper name when api returns a null name', () => {
      mockApiService = new MockApiService();
      mockUserActivityService = new MockUserActivityService();
      mockTranslateService = new MockTranslateService();
      service = new RecentActivityService(
        mockApiService as ApiService,
        mockUserActivityService as any,
        mockWhiteLabelService as any,
        mockTranslateService as any,
      );
      service.loadRecentActivities(EMPTY_NAME_USER_EVENT_AGID, undefined as never);
      scheduler
        .expectObservable(service.recentActivities$(EMPTY_NAME_USER_EVENT_AGID))
        .toBe('-x', { x: mockUserRecentActivityEmptyName });
    });
  });
});
