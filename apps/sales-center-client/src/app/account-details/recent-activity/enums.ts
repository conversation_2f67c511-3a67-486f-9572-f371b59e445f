export enum ActivityTypes {
  ACCOUNT_CREATE = 'account-created',
  EMAIL_DROPPED = 'email-dropped',
  EMAIL_DELIVERED = 'email-delivered',
  EMAIL_BOUNCE = 'email-bounce',
  EMAIL_OPEN = 'email-open',
  EMAIL_CLICK = 'email-click',
  EMAIL_UNSUBSCRIBE = 'email-unsubscribe',
  EMAIL_SPAM_REPORT = 'email-spamreport',
  MORE_INFO_CLICK = 'more-info-click',
  ASK_QUESTION = 'ask-question',
  SNAPSHOT_OPEN = 'snapshot-open',
  CLAIM_VBC_USER_CLICK = 'claim-vbc-user-click',
  LISTING_SYNC_PRO_INTEREST = 'listing-sync-pro-interest',
  PARTNER_PRODUCT_VIEW = 'partner-product-view',
  PARTNER_PRODUCT_ENABLE = 'partner-product-enable',
  PARTNER_PRODUCT_ACTIVATE = 'partner-product-activate',
  PACKAGE_INTEREST = 'package-interest',
  PRODUCT_INTEREST = 'product-interest',
  BULK_ACTION_IMPORT_LIST = 'import-list',
  BULK_ACTION_ADD_TO_CAMPAIGN = 'add-to-campaign',
  CLAIM_VBC_USER = 'claim-vbc-user',
  PARTNER_CONTACT_VENDOR = 'partner-contact-vendor',
  VENDOR_CONTACT_PARTNER_INTERCEPT = 'vendor-contact-partner-intercept',
  INSTANT_DEMO_REQUEST = 'instant-demo-request',
}
