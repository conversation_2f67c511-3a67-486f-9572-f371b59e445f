import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { ActivityData } from './recent-activity.service';

const GENERIC_BUSINESS_CENTER_NAME = 'Business App';

export interface TranslationParams {
  businessCenterName: string;
  visitedPageURL: string;
  formName?: string;
  leadSource?: string;
}

enum HeimdallUserEvents {
  REQUEST_SALESPERSON_ASSISTANCE = 'nav-request-assistance',
  UPDATE_BUSINESS_PROFILE = 'business-profile-update',
  CREATE_SINGLE_CUSTOMER = 'create-single-customer',
  CREATE_BULK_CUSTOMERS = 'create-bulk-customers',
  CLICKED_ADD_PRODUCTS = '/store',
  NAVIGATED_TO_STORE_PAGE = 'navigation-store',
  VIEWED_EXECUTIVE_REPORT = 'navigation-executive-report',
  CONNECTED_FACEBOOK = 'connected-facebook',
  CONNECTED_GOOGLE_MY_BUSINESS = 'connected-google-my-business',
  CALL_TO_ACTION = 'upgrade-cta-guard',
  NAV_UPGRADE = 'nav-upgrade',
  LISTING_DETAILS_UPGRADE = 'listings-details-upgrade',
  REVIEW_UPGRADE = 'review-upgrade',
  CONNECT_ACCOUNTS_UPGRADE = 'connect-accounts-upgrade',
  SIDEBAR_UPGRADE = 'sidebar-upgrade',
  UPGRADE_TO_PRO = 'upgrade-to-pro',
  WEBSITE__PAGE_VIEW = 'website--page-view',
}

enum UserEvents {
  DUPLICATE_LEAD_SUBMISSION = 'duplicate_lead_submission',
}

const WEBSITE_ACTIONS = ['website--page-view'];

const I18N_TRANSLATION_KEYS = [
  'ACTIVITY_DETAIL.REPUTATION_MANAGEMENT_LOCKED_PAGES.CALL_TO_ACTION',
  'ACTIVITY_DETAIL.REPUTATION_MANAGEMENT_LOCKED_PAGES.NAV_UPGRADE',
  'ACTIVITY_DETAIL.REPUTATION_MANAGEMENT_LOCKED_PAGES.LISTINGS_UPGRADE',
  'ACTIVITY_DETAIL.REPUTATION_MANAGEMENT_LOCKED_PAGES.REVIEW_UPGRADE',
  'ACTIVITY_DETAIL.REPUTATION_MANAGEMENT_LOCKED_PAGES.CONNECT_ACCOUNTS',
  'ACTIVITY_DETAIL.SOCIAL_MARKETING_LOCKED_PAGES.SIDEBAR_UPGRADE',
  'ACTIVITY_DETAIL.ADVERTISING_INTELLIGENCE_LOCKED_PAGES.UPGRADE_TO_PRO',
  'ACTIVITY_DETAIL.REQUEST_SALESPERSON_ASSISTANCE',
  'ACTIVITY_DETAIL.UPDATE_BUSINESS_PROFILE',
  'ACTIVITY_DETAIL.CREATE_SINGLE_CUSTOMER',
  'ACTIVITY_DETAIL.CREATE_BULK_CUSTOMERS',
  'ACTIVITY_DETAIL.CLICKED_ADD_PRODUCTS',
  'ACTIVITY_DETAIL.NAVIGATED_TO_STORE_PAGE',
  'ACTIVITY_DETAIL.VIEWED_EXECUTIVE_REPORT',
  'ACTIVITY_DETAIL.CONNECTED_FACEBOOK',
  'ACTIVITY_DETAIL.CONNECTED_GOOGLE_MY_BUSINESS',
  'ACTIVITY_DETAIL.WEBSITE__PAGE_VIEW_ACTIVITY',
  'ACTIVITY_DETAIL.WEBSITE__PAGE_VIEW_HISTORY',
  'ACTIVITY_DETAIL.DUPLICATE_LEAD_SUBMISSION_ACTIVITY',
  'ACTIVITY_DETAIL.DUPLICATE_LEAD_SUBMISSION_HISTORY',
];

@Injectable()
export class UserActivitiesService {
  constructor(private readonly i18n: TranslateService) {}

  private retrieveTranslatedDetailNames(businessCenterName: string, activityDetail: string): string {
    let translationParams: TranslationParams;
    if (businessCenterName) {
      translationParams = {
        businessCenterName: businessCenterName,
        visitedPageURL: activityDetail,
        formName: this.getQueryVariable(activityDetail, 'formName'),
        leadSource: this.getQueryVariable(activityDetail, 'leadSource'),
      };
    } else {
      translationParams = {
        businessCenterName: GENERIC_BUSINESS_CENTER_NAME,
        visitedPageURL: activityDetail,
        formName: this.getQueryVariable(activityDetail, 'formName'),
        leadSource: this.getQueryVariable(activityDetail, 'leadSource'),
      };
    }
    return this.i18n.instant(I18N_TRANSLATION_KEYS, translationParams);
  }

  private retrieveTranslatedDetailNames$(businessCenterName: string, activityData: ActivityData): Observable<string> {
    let translationParams: TranslationParams;
    if (businessCenterName) {
      translationParams = {
        businessCenterName: businessCenterName,
        visitedPageURL: activityData.activity_detail,
        formName: this.getQueryVariable(activityData.activity_detail, 'formName'),
        leadSource: this.getQueryVariable(activityData.activity_detail, 'leadSource'),
      };
    } else {
      translationParams = {
        businessCenterName: GENERIC_BUSINESS_CENTER_NAME,
        visitedPageURL: activityData.activity_detail,
        formName: this.getQueryVariable(activityData.activity_detail, 'formName'),
        leadSource: this.getQueryVariable(activityData.activity_detail, 'leadSource'),
      };
    }
    return this.i18n.stream(I18N_TRANSLATION_KEYS, translationParams);
  }

  getQueryVariable(activityDetail: string, targetParam: string): string {
    if (!activityDetail || activityDetail === '') {
      return activityDetail;
    }
    const queryParams = new URLSearchParams(activityDetail);
    return queryParams.get(targetParam) || '';
  }

  translateUserEvent(userEvent: string, businessCenterName: string, activityDetail: string): string {
    const translations = this.retrieveTranslatedDetailNames(businessCenterName, activityDetail);
    switch (userEvent) {
      case HeimdallUserEvents.CALL_TO_ACTION:
        return translations['ACTIVITY_DETAIL.REPUTATION_MANAGEMENT_LOCKED_PAGES.CALL_TO_ACTION'];
      case HeimdallUserEvents.NAV_UPGRADE:
        return translations['ACTIVITY_DETAIL.REPUTATION_MANAGEMENT_LOCKED_PAGES.NAV_UPGRADE'];
      case HeimdallUserEvents.LISTING_DETAILS_UPGRADE:
        return translations['ACTIVITY_DETAIL.REPUTATION_MANAGEMENT_LOCKED_PAGES.LISTINGS_UPGRADE'];
      case HeimdallUserEvents.REVIEW_UPGRADE:
        return translations['ACTIVITY_DETAIL.REPUTATION_MANAGEMENT_LOCKED_PAGES.REVIEW_UPGRADE'];
      case HeimdallUserEvents.CONNECT_ACCOUNTS_UPGRADE:
        return translations['ACTIVITY_DETAIL.REPUTATION_MANAGEMENT_LOCKED_PAGES.CONNECT_ACCOUNTS'];
      case HeimdallUserEvents.SIDEBAR_UPGRADE:
        return translations['ACTIVITY_DETAIL.SOCIAL_MARKETING_LOCKED_PAGES.SIDEBAR_UPGRADE'];
      case HeimdallUserEvents.UPGRADE_TO_PRO:
        return translations['ACTIVITY_DETAIL.ADVERTISING_INTELLIGENCE_LOCKED_PAGES.UPGRADE_TO_PRO'];
      case HeimdallUserEvents.REQUEST_SALESPERSON_ASSISTANCE:
        return translations['ACTIVITY_DETAIL.REQUEST_SALESPERSON_ASSISTANCE'];
      case HeimdallUserEvents.CREATE_SINGLE_CUSTOMER:
        return translations['ACTIVITY_DETAIL.CREATE_SINGLE_CUSTOMER'];
      case HeimdallUserEvents.CREATE_BULK_CUSTOMERS:
        return translations['ACTIVITY_DETAIL.CREATE_BULK_CUSTOMERS'];
      case HeimdallUserEvents.CLICKED_ADD_PRODUCTS:
        return translations['ACTIVITY_DETAIL.CLICKED_ADD_PRODUCTS'];
      case HeimdallUserEvents.UPDATE_BUSINESS_PROFILE:
        return translations['ACTIVITY_DETAIL.UPDATE_BUSINESS_PROFILE'];
      case HeimdallUserEvents.NAVIGATED_TO_STORE_PAGE:
        return translations['ACTIVITY_DETAIL.NAVIGATED_TO_STORE_PAGE'];
      case HeimdallUserEvents.VIEWED_EXECUTIVE_REPORT:
        return translations['ACTIVITY_DETAIL.VIEWED_EXECUTIVE_REPORT'];
      case HeimdallUserEvents.CONNECTED_FACEBOOK:
        return translations['ACTIVITY_DETAIL.CONNECTED_FACEBOOK'];
      case HeimdallUserEvents.CONNECTED_GOOGLE_MY_BUSINESS:
        return translations['ACTIVITY_DETAIL.CONNECTED_GOOGLE_MY_BUSINESS'];
      case HeimdallUserEvents.WEBSITE__PAGE_VIEW:
        return translations['ACTIVITY_DETAIL.WEBSITE__PAGE_VIEW_HISTORY'];
      case UserEvents.DUPLICATE_LEAD_SUBMISSION:
        return translations['ACTIVITY_DETAIL.DUPLICATE_LEAD_SUBMISSION_HISTORY'];
      default:
        return userEvent;
    }
  }

  translateUserEvent$(userEvent: string, businessCenterName: string, activityData: ActivityData): Observable<string> {
    const translations = this.retrieveTranslatedDetailNames$(businessCenterName, activityData);
    switch (userEvent) {
      case HeimdallUserEvents.CALL_TO_ACTION:
        return translations.pipe(map((tr) => tr['ACTIVITY_DETAIL.REPUTATION_MANAGEMENT_LOCKED_PAGES.CALL_TO_ACTION']));
      case HeimdallUserEvents.NAV_UPGRADE:
        return translations.pipe(map((tr) => tr['ACTIVITY_DETAIL.REPUTATION_MANAGEMENT_LOCKED_PAGES.NAV_UPGRADE']));
      case HeimdallUserEvents.LISTING_DETAILS_UPGRADE:
        return translations.pipe(
          map((tr) => tr['ACTIVITY_DETAIL.REPUTATION_MANAGEMENT_LOCKED_PAGES.LISTINGS_UPGRADE']),
        );
      case HeimdallUserEvents.REVIEW_UPGRADE:
        return translations.pipe(map((tr) => tr['ACTIVITY_DETAIL.REPUTATION_MANAGEMENT_LOCKED_PAGES.REVIEW_UPGRADE']));
      case HeimdallUserEvents.CONNECT_ACCOUNTS_UPGRADE:
        return translations.pipe(
          map((tr) => tr['ACTIVITY_DETAIL.REPUTATION_MANAGEMENT_LOCKED_PAGES.CONNECT_ACCOUNTS']),
        );
      case HeimdallUserEvents.SIDEBAR_UPGRADE:
        return translations.pipe(map((tr) => tr['ACTIVITY_DETAIL.SOCIAL_MARKETING_LOCKED_PAGES.SIDEBAR_UPGRADE']));
      case HeimdallUserEvents.UPGRADE_TO_PRO:
        return translations.pipe(
          map((tr) => tr['ACTIVITY_DETAIL.ADVERTISING_INTELLIGENCE_LOCKED_PAGES.UPGRADE_TO_PRO']),
        );
      case HeimdallUserEvents.REQUEST_SALESPERSON_ASSISTANCE:
        return translations.pipe(map((tr) => tr['ACTIVITY_DETAIL.REQUEST_SALESPERSON_ASSISTANCE']));
      case HeimdallUserEvents.CREATE_SINGLE_CUSTOMER:
        return translations.pipe(map((tr) => tr['ACTIVITY_DETAIL.CREATE_SINGLE_CUSTOMER']));
      case HeimdallUserEvents.CREATE_BULK_CUSTOMERS:
        return translations.pipe(map((tr) => tr['ACTIVITY_DETAIL.CREATE_BULK_CUSTOMERS']));
      case HeimdallUserEvents.CLICKED_ADD_PRODUCTS:
        return translations.pipe(map((tr) => tr['ACTIVITY_DETAIL.CLICKED_ADD_PRODUCTS']));
      case HeimdallUserEvents.UPDATE_BUSINESS_PROFILE:
        return translations.pipe(map((tr) => tr['ACTIVITY_DETAIL.UPDATE_BUSINESS_PROFILE']));
      case HeimdallUserEvents.NAVIGATED_TO_STORE_PAGE:
        return translations.pipe(map((tr) => tr['ACTIVITY_DETAIL.NAVIGATED_TO_STORE_PAGE']));
      case HeimdallUserEvents.VIEWED_EXECUTIVE_REPORT:
        return translations.pipe(map((tr) => tr['ACTIVITY_DETAIL.VIEWED_EXECUTIVE_REPORT']));
      case HeimdallUserEvents.CONNECTED_FACEBOOK:
        return translations.pipe(map((tr) => tr['ACTIVITY_DETAIL.CONNECTED_FACEBOOK']));
      case HeimdallUserEvents.CONNECTED_GOOGLE_MY_BUSINESS:
        return translations.pipe(map((tr) => tr['ACTIVITY_DETAIL.CONNECTED_GOOGLE_MY_BUSINESS']));
      case HeimdallUserEvents.WEBSITE__PAGE_VIEW:
        return translations.pipe(map((tr) => tr['ACTIVITY_DETAIL.WEBSITE__PAGE_VIEW_ACTIVITY']));
      case UserEvents.DUPLICATE_LEAD_SUBMISSION:
        return translations.pipe(map((tr) => tr['ACTIVITY_DETAIL.DUPLICATE_LEAD_SUBMISSION_ACTIVITY']));
      default:
        return of(userEvent);
    }
  }

  buildActivityDetailDescription(data: ActivityData, businessCenterName: string): Observable<string> {
    return this.translateUserEvent$(data.action_detail, businessCenterName, data);
  }

  buildHistoryAction(action: string, businessCenterName: string, activityDetail: string): string {
    return this.translateUserEvent(action, businessCenterName, activityDetail) as string;
  }

  shouldUseGenericContactDisplayName(actionDetail: string): boolean {
    return !WEBSITE_ACTIONS.includes(actionDetail);
  }
}
