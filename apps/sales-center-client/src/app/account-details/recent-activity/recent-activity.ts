import { Observable } from 'rxjs';

export interface RecentActivity {
  displayName: string;
  activityDetail: string;
  activityDate: Date;
  activityTypeName: string;
  activityType: string;
  activityDetailName: string;
  accountGroupId: string;
  actionDetail?: string;
  userActivityDetail$: Observable<string>;
  userFullName: string;
  products: RecentActivityProduct[];
}

export interface ProductHotness {
  displayName: string;
  hotness: number;
  productName: string;
}

export class RecentActivityProduct {
  id: string;
  name: string;

  constructor({ product_id, product_name }: any) {
    this.id = product_id;
    this.name = product_name;
  }
}
