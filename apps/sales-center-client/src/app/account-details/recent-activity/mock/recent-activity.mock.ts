import { RecentActivityProduct } from '../recent-activity';
import { EMPTY } from 'rxjs';

export const getRecentActivityMock = [
  {
    account_group_id: 'AG-FTV5SPLSJG',
    action_detail: null,
    activity_date: '2018-08-14T22:03:30Z',
    activity_detail: 'Expensive Comics',
    activity_detail_name: 'Expensive Comics',
    activity_type: 'package-interest',
    activity_type_name: 'Interested in Package',
    display_name: '<PERSON><PERSON> Demotest',
    products: [
      {
        product_id: 'SOL-18D7AECE3C55437ABBCA6C9D264365D8',
        product_name: 'Expensive Comic',
      },
    ],
  },
];

export const getRecentActivityUserEventMock = [
  {
    account_group_id: 'AG-USEREVENT',
    activity_performed_by: 'Cloud Strife',
    action_detail: 'nav-request-assistance',
    activity_date: '2018-08-14T22:03:30Z',
    activity_detail: 'Expensive Comics',
    activity_detail_name: 'Expensive Comics',
    activity_type: 'user-event',
    activity_type_name: 'Interested in Package',
    display_name: '<PERSON><PERSON>',
    products: [
      {
        product_id: 'SOL-18D7AECE3C55437ABBCA6C9D264365D8',
        product_name: 'Expensive Comic',
      },
    ],
  },
];

export const getRecentActivityUserEventMockEmptyName = [
  {
    account_group_id: 'AG-EMPTYNAME',
    activity_performed_by: 'Cloud Strife',
    action_detail: 'nav-request-assistance',
    activity_date: '2018-08-14T22:03:30Z',
    activity_detail: 'Expensive Comics',
    activity_detail_name: 'Expensive Comics',
    activity_type: 'user-event',
    activity_type_name: 'Interested in Package',
    display_name: null,
    products: [
      {
        product_id: 'SOL-18D7AECE3C55437ABBCA6C9D264365D8',
        product_name: 'Expensive Comic',
      },
    ],
  },
];

const product = new RecentActivityProduct({
  product_id: 'SOL-18D7AECE3C55437ABBCA6C9D264365D8',
  product_name: 'Expensive Comic',
});

export const mockRecentActivity = [
  {
    displayName: 'Shuo Demotest',
    activityDetail: 'Expensive Comics',
    activityDate: '2018-08-14T22:03:30Z',
    activityTypeName: 'Interested in Package',
    activityType: 'package-interest',
    activityDetailName: 'Expensive Comics',
    actionDetail: null,
    userActivityDetail$: EMPTY,
    products: [product],
  },
];

export const mockUserRecentActivity = [
  {
    displayName: 'Shuo Demotest',
    activityDetail: 'Expensive Comics',
    activityDate: '2018-08-14T22:03:30Z',
    activityTypeName: 'Interested in Package',
    activityType: 'user-event',
    activityDetailName: 'Expensive Comics',
    actionDetail: 'nav-request-assistance',
    userActivityDetail$: 'requested salesperson assistance',
    products: [product],
  },
];

export const mockUserRecentActivityEmptyName = [
  {
    displayName: 'A Business App user',
    activityDetail: 'Expensive Comics',
    activityDate: '2018-08-14T22:03:30Z',
    activityTypeName: 'Interested in Package',
    activityType: 'user-event',
    activityDetailName: 'Expensive Comics',
    actionDetail: 'nav-request-assistance',
    userActivityDetail$: 'requested salesperson assistance',
    products: [product],
  },
];
