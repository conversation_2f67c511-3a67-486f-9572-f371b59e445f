<ng-container *ngIf="hasRecentActivity; then recentActivitiesState; else emptyState"></ng-container>

<ng-template #recentActivitiesState>
  <div *ngIf="productHotness.length > 0">
    <div class="hotness-list-item" *ngFor="let hotness of productHotness">
      <span *ngFor="let i of buildFlamesArray(hotness.hotness)">
        <mat-icon class="hotness-icon" svgIcon="flame"></mat-icon>
      </span>
      <ng-container *ngIf="hotness.displayName; then name; else noName"></ng-container>
      <ng-template #name>
        <span
          class="hotness-text"
          translate
          [translateParams]="{
            who: hotness.displayName,
            what: hotness.productName
          }"
        >
          ACCOUNT_DETAILS.RECENT_ACTIVITY.ACTIVITY_DESCRIPTION.PERSON_INTERESTED_IN_PRODUCT
        </span>
      </ng-template>
      <ng-template #noName>
        <span class="hotness-text" translate [translateParams]="{ what: hotness.productName }">
          ACCOUNT_DETAILS.RECENT_ACTIVITY.ACTIVITY_DESCRIPTION.INTERESTED_IN_PRODUCT
        </span>
      </ng-template>
    </div>
  </div>
  <div class="list-item" *ngFor="let recentActivity of recentActivities">
    <ng-container [ngSwitch]="recentActivity.activityType">
      <div *ngSwitchCase="userEvent">
        <span
          *ngIf="recentActivity.activityDetail === 'cancellation-requested-event'"
          [innerHtml]="
            'CANCELLATION_REQUESTS.ACTIVITY_CANCELLATION_REQUESTED'
              | translate
                : {
                    userName: recentActivity.displayName,
                    productName: recentActivity.products[0].name
                  }
          "
        ></span>
        <span *ngIf="recentActivity.activityDetail !== 'cancellation-requested-event'">
          <strong>{{ recentActivity.displayName }}</strong>
          {{ recentActivity.userActivityDetail$ | async }}
        </span>
      </div>
      <div *ngSwitchDefault>
        <span>
          {{ recentActivity.displayName }}
          <span *ngIf="recentActivity.actionDetail">
            {{ recentActivity.actionDetail }}
          </span>
          <strong>{{ recentActivity.activityTypeName }}</strong>
          <div [innerHtml]="recentActivity.activityDetailName"></div>
        </span>
      </div>
    </ng-container>
    <span class="list-item-accessory">
      <div class="recent-activity-date">
        <span class="activity-date">
          {{ (recentActivity.activityDate | date: 'medium') || 'COMMON.NOT_APPLICABLE_LABEL' | translate }}
        </span>
      </div>
    </span>
  </div>
</ng-template>

<ng-template #emptyState>
  <div *ngIf="loading; else callToAction">
    <div class="error-padding">
      <app-async-text [loading]="loading" [loadFailed]="!success" (retry)="retryLoading()"></app-async-text>
    </div>
  </div>
  <ng-template #callToAction>
    <div class="call-to-action">
      <uikit-empty-state iconName="create" ctaPrimaryText="Contact" (ctaPrimaryEvent)="contactButtonClicked()">
        <span class="call-to-action-text" state-description>
          {{ 'ACCOUNT_DETAILS.CALL_TO_ACTION.DONT_WAIT_FOR_PROSPECT' | translate }}
          <br />
          {{ 'ACCOUNT_DETAILS.CALL_TO_ACTION.ENGAGED_PROSPECTS_LIKELY_TO_SIGN' | translate }}
        </span>
      </uikit-empty-state>
    </div>
  </ng-template>
</ng-template>
