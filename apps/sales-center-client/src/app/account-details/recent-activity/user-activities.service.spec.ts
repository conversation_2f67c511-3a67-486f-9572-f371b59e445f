import { TranslateService } from '@ngx-translate/core';
import { Observable } from 'rxjs';
import { TestScheduler } from 'rxjs/testing';
import { ActivityData } from './recent-activity.service';
import { TranslationParams, UserActivitiesService } from './user-activities.service';

let scheduler: TestScheduler;

class MockTranslateForBrandingService {
  instant(key: string | Array<string>, interpolateParams?: unknown): string | any {
    return {
      'ACTIVITY_DETAIL.REQUEST_SALESPERSON_ASSISTANCE': `Request Assistance for ${
        (interpolateParams as TranslationParams).businessCenterName
      }`,
    };
  }
}
class MockTranslateService {
  instant(): string | any {
    return { 'ACTIVITY_DETAIL.REQUEST_SALESPERSON_ASSISTANCE': 'Request Assistance' };
  }
  stream(): Observable<string | any> {
    return scheduler.createColdObservable('-x', {
      x: { 'ACTIVITY_DETAIL.REQUEST_SALESPERSON_ASSISTANCE': 'Request Assistance' },
    });
  }
}

describe('UserActivityService', () => {
  beforeEach(() => {
    scheduler = new TestScheduler((a, b) => expect(a).toEqual(b));
  });
  afterEach(() => {
    scheduler.flush();
  });
  describe('getStringEvent', () => {
    test('Should return a string based off a valid user event', () => {
      const mockTranslateService = new MockTranslateService();
      const userActivityService: UserActivitiesService = new UserActivitiesService(
        mockTranslateService as TranslateService,
      );
      const actualEvent = userActivityService.buildHistoryAction(
        'nav-request-assistance',
        undefined as never,
        undefined as never,
      );
      expect(actualEvent).toBe('Request Assistance');
    });
  });
  test('Should return an observable of type string based off a valid user event', () => {
    const mockTranslateService = new MockTranslateService();
    const userActivityService: UserActivitiesService = new UserActivitiesService(
      mockTranslateService as TranslateService,
    );
    const mockActivityData: ActivityData = {
      activity_detail: '',
      action_detail: 'nav-request-assistance',
    };
    const actualEvent = userActivityService.buildActivityDetailDescription(mockActivityData, undefined as never);
    scheduler.expectObservable(actualEvent).toBe('-x', { x: 'Request Assistance' });
  });
  test('Should return the user-event action when no Heimdall event was found for it', () => {
    const mockTranslateService = new MockTranslateService();
    const userActivityService: UserActivitiesService = new UserActivitiesService(
      mockTranslateService as TranslateService,
    );
    const actualEvent = userActivityService.buildHistoryAction('fake-event', undefined as never, undefined as never);
    expect(actualEvent).toBe('fake-event');
  });
  test('Should return a generic branding if an undefined branding was passed in', () => {
    const mockTranslateService = new MockTranslateForBrandingService();
    const userActivityService: UserActivitiesService = new UserActivitiesService(
      mockTranslateService as TranslateService,
    );
    const actualEvent = userActivityService.buildHistoryAction(
      'nav-request-assistance',
      undefined as never,
      undefined as never,
    );
    expect(actualEvent).toBe('Request Assistance for Business App');
  });
});
