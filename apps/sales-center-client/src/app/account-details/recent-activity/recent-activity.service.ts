import { HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { WhitelabelService } from '@galaxy/partner';
import { TranslateService } from '@ngx-translate/core';
import { ManualStartWorkStateMap } from '@vendasta/rx-utils/work-state';
import { EMPTY, Observable, combineLatest } from 'rxjs';
import { map, shareReplay } from 'rxjs/operators';
import { ApiService } from '../../common';
import { ActivityTypes } from './enums';
import { ProductHotness, RecentActivity, RecentActivityProduct } from './recent-activity';
import { UserActivitiesService } from './user-activities.service';

const RECENT_ACTIVITY_URL = '/internalApi/v2/recent-activity/get/';
const PRODUCT_HOTNESS_URL = '/api/v1/business/product-hotness/';

export const USER_EVENT_LABEL = 'user-event';

export interface ActivityData {
  action_detail: string;
  activity_detail: string;
}

export interface RecentActivityResponse {
  account_group_id: string;
  action_detail: string;
  activity_date: Date;
  activity_detail: string;
  activity_detail_name: string;
  activity_type: string;
  activity_type_name: string;
  display_name: string;
  products: LocalRecentActivityProduct[];
}

export interface LocalRecentActivityProduct {
  product_id: string;
  product_name: string;
}

@Injectable()
export class RecentActivityService {
  constructor(
    private readonly apiService: ApiService,
    private readonly userActivityService: UserActivitiesService,
    private readonly whiteLabelService: WhitelabelService,
    private readonly translate: TranslateService,
  ) {}
  private readonly recentActivitiesWorkStateMap = new ManualStartWorkStateMap<string, RecentActivity[]>();
  private readonly productHotnessWorkStateMap = new ManualStartWorkStateMap<string, ProductHotness[]>();

  static isActivityUserEvent(data: RecentActivityResponse): boolean {
    if (data) {
      return data.activity_type === USER_EVENT_LABEL;
    }
    return false;
  }

  loadProductHotness(accountGroupId, partnerId: string): void {
    const params = new HttpParams().set('accountGroupId', accountGroupId).set('partnerId', partnerId);
    this.productHotnessWorkStateMap.startWork(accountGroupId, (success, fail) => {
      this.apiService
        .get(PRODUCT_HOTNESS_URL, params)
        .pipe(
          map((response) => {
            return response
              ? response.product_hotness_json.map((data) => {
                  return <ProductHotness>{
                    displayName: data.display_name,
                    productName: data.product_name,
                    hotness: data.hotness,
                  };
                })
              : [];
          }),
        )
        .subscribe(success, () => fail());
    });
  }

  loadRecentActivities(accountGroupId: string, partnerId: string): void {
    const params = new HttpParams().set('accountGroupId', accountGroupId);
    const branding$ = this.whiteLabelService.getBranding(partnerId);
    this.recentActivitiesWorkStateMap.startWork(accountGroupId, (success, fail) => {
      const recentActivityApiCall$ = this.apiService.get(RECENT_ACTIVITY_URL, params);
      combineLatest([recentActivityApiCall$, branding$])
        .pipe(
          map(([response, branding]) => {
            return response
              ? response.map((data) => {
                  return this.buildActivityFromResponse(
                    data as RecentActivityResponse,
                    branding?.apps?.VBC?.name || '',
                  );
                })
              : [];
          }),
          map((activityArray) =>
            activityArray.filter((activity) => activity.activityType !== ActivityTypes.ACCOUNT_CREATE),
          ),
          shareReplay(1),
        )
        .subscribe(success, () => fail());
    });
  }

  isLoadingRecentActivities$(accountGroupId: string): Observable<boolean> {
    return this.recentActivitiesWorkStateMap.isLoading$(accountGroupId);
  }

  isLoadingProductHotness$(accountGroupId: string): Observable<boolean> {
    return this.productHotnessWorkStateMap.isLoading$(accountGroupId);
  }

  isSuccessRecentActivities$(accountGroupId: string): Observable<boolean> {
    return this.recentActivitiesWorkStateMap.isSuccess$(accountGroupId);
  }

  isSuccessProductHotness$(accountGroupId: string): Observable<boolean> {
    return this.productHotnessWorkStateMap.isSuccess$(accountGroupId);
  }

  recentActivities$(accountGroupId: string): Observable<RecentActivity[]> {
    return this.recentActivitiesWorkStateMap.getWorkResults$(accountGroupId);
  }

  productHotness$(accountGroupId: string): Observable<ProductHotness[]> {
    return this.productHotnessWorkStateMap.getWorkResults$(accountGroupId);
  }
  buildActivityFromResponse(data: RecentActivityResponse, businessCenterName: string): RecentActivity {
    const products = data.products.map((product) => new RecentActivityProduct(product));
    let userActivityDetail$: Observable<string>;
    if (RecentActivityService.isActivityUserEvent(data)) {
      userActivityDetail$ = this.userActivityService.buildActivityDetailDescription(
        data as ActivityData,
        businessCenterName,
      );
      const useGenericContactName = this.userActivityService.shouldUseGenericContactDisplayName(data.action_detail);
      if (!data.display_name && useGenericContactName) {
        data.display_name = this.translate.instant('ACTIVITY_DETAIL.GENERIC_CONTACT');
      }
    } else {
      userActivityDetail$ = EMPTY as Observable<string>;
    }

    return <RecentActivity>{
      actionDetail: data.action_detail,
      displayName: data.display_name,
      activityDate: data.activity_date,
      activityDetail: data.activity_detail,
      activityType: data.activity_type,
      activityDetailName: data.activity_detail_name,
      activityTypeName: data.activity_type_name,
      userActivityDetail$: userActivityDetail$,
      products: products,
    };
  }
}
