import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ProductHotness, RecentActivity } from './recent-activity';
import { USER_EVENT_LABEL } from './recent-activity.service';

@Component({
  selector: 'app-recent-activity-list',
  templateUrl: './recent-activity-list.component.html',
  styleUrls: ['./recent-activity-card.component.scss'],
  standalone: false,
})
export class RecentActivityListComponent {
  userEvent = USER_EVENT_LABEL;
  @Input() recentActivities: RecentActivity[] = [];
  @Input() productHotness: ProductHotness[] = [];
  @Input() loading = true;
  @Input() success = true;
  @Output() retry = new EventEmitter<null>();
  @Output() contactClicked = new EventEmitter();

  get hasRecentActivity(): boolean {
    const recentActivitiesExist = this.recentActivities && this.recentActivities.length !== 0;
    return recentActivitiesExist && !this.loading && this.success;
  }

  get emptyRecentActivity(): boolean {
    const recentActivitiesExist = this.recentActivities && this.recentActivities.length !== 0;
    return !recentActivitiesExist && !this.loading && this.success;
  }

  retryLoading(): void {
    this.retry.emit();
  }

  buildFlamesArray(num: number): Array<string> {
    return Array(num).fill('🔥');
  }

  contactButtonClicked(): void {
    this.contactClicked.emit();
  }
}
