@use 'design-tokens' as *;

.call-to-action {
  margin: 60px;
}

.call-to-action-text {
  font-size: $font-preset-3-size;
}

.list-item {
  padding: 12px $spacing-3;
  word-break: break-all;
}

.hotness-list-item {
  display: flex;
  padding: 12px $spacing-3;
  border-bottom: none;
  background-color: $light-red;
}

.hotness-text {
  margin: auto $spacing-2;
  color: $red;
}

.hotness-icon {
  color: $red;
}

.list-item:last-child {
  border-bottom: none;
}

.recent-activity-date {
  color: $grey;
}

.spinner-padding {
  padding: $spacing-3;
  text-align: center;
}

.error-padding {
  padding-top: $spacing-3;
  padding-bottom: $spacing-3;
}
