<mat-card appearance="outlined">
  <mat-card-header class="mat-card-header-with-action">
    <mat-card-title>
      <span>{{ 'ACCOUNT_DETAILS.CAMPAIGNS.CAMPAIGNS' | translate }}</span>
      <ng-content select="[afterTitle]"></ng-content>
    </mat-card-title>
    <button
      *ngIf="canSendCampaigns$ | async"
      mat-icon-button
      color="primary"
      [matTooltip]="'ACCOUNT_DETAILS.CAMPAIGNS.START_CAMPAIGN' | translate"
      (click)="start()"
    >
      <mat-icon>send</mat-icon>
    </button>
  </mat-card-header>
  <mat-card-content>
    <uikit-async-ui [data]="recipientCampaigns$ | async" [loading]="loading$ | async" [error]="error$ | async">
      <ng-container *successData="let recipientCampaigns">
        <app-campaigns-card-list
          *ngIf="recipientCampaigns.length > 0; else empty"
          [recipientCampaigns]="recipientCampaigns"
          [businessId]="businessId"
          (statusChanged)="handleStatusChange($event)"
        ></app-campaigns-card-list>
      </ng-container>
    </uikit-async-ui>
  </mat-card-content>
  <ng-container *ngIf="hasMany$ | async">
    <mat-card-actions align="end">
      <button mat-button *ngIf="(showingMore$ | async) === false" (click)="showMore()">
        {{ 'COMMON.ACTION_LABELS.VIEW_MORE' | translate }}
      </button>
      <button mat-button *ngIf="showingMore$ | async" (click)="showLess()">
        {{ 'COMMON.ACTION_LABELS.VIEW_LESS' | translate }}
      </button>
    </mat-card-actions>
  </ng-container>
</mat-card>

<ng-template #empty>
  <glxy-empty-state>
    <glxy-empty-state-hero class="set-padding">
      <mat-icon class="empty-state__icon">mail_outline</mat-icon>
    </glxy-empty-state-hero>
    <p class="empty-state__description">
      {{ 'ACCOUNT_DETAILS.CAMPAIGNS.NO_CAMPAIGNS' | translate }}
    </p>
    <glxy-empty-state-actions *ngIf="canSendCampaigns$ | async">
      <button mat-button (click)="start()" color="primary">
        {{ 'ACCOUNT_DETAILS.CAMPAIGNS.START_CAMPAIGN' | translate }}
      </button>
    </glxy-empty-state-actions>
  </glxy-empty-state>
</ng-template>
