import { RecipientCampaignsUiModel } from './recipient-campaigns-ui.model';
import { RecipientCampaign } from '../../campaigns';
import { of } from 'rxjs';
import { TestScheduler } from 'rxjs/testing';
import { schedule } from '@vendasta/rx-utils';

describe('RecipientCampaignsUiModel', () => {
  let sched: TestScheduler;
  beforeEach(() => {
    sched = new TestScheduler((a, b) => expect(a).toEqual(b));
  });
  afterEach(() => {
    sched.flush();
  });
  it('should produce the correct values from a campaign addition', () => {
    const campaign1 = <RecipientCampaign>{
      recipient_campaign_id: 'RC-1',
      campaign_id: 'C-1',
      status: 'active',
    };
    const base = of([campaign1]);
    const model = new RecipientCampaignsUiModel(base);
    schedule(sched, '--|', () =>
      model.addNewCampaign(<RecipientCampaign>{
        recipient_campaign_id: 'RC-NEW',
        campaign_id: 'C-NEW',
        status: 'active',
      }),
    );
    sched.expectObservable(model.asObservable()).toBe('x-y', {
      x: [{ recipient_campaign_id: 'RC-1', campaign_id: 'C-1', status: 'active' }],
      y: [
        { recipient_campaign_id: 'RC-NEW', campaign_id: 'C-NEW', status: 'active' },
        { recipient_campaign_id: 'RC-1', campaign_id: 'C-1', status: 'active' },
      ],
    });
  });
});
