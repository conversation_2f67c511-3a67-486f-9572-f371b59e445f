import { Component, Inject, Input, Output } from '@angular/core';
import { CampaignsCardUiService } from './campaigns-card.ui-service';
import { BehaviorSubject, combineLatest, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { StatusChangeEvent } from '../../campaigns';
import { UiRecipientCampaign } from '../../campaigns';
import { EventEmitter } from '@angular/core';
import { ADD_TO_CAMPAIGNS_TOKEN } from '../../common/providers/tokens';

const COLLAPSED_SIZE = 3;

@Component({
  selector: 'app-campaigns-card',
  templateUrl: './campaigns-card.component.html',
  styleUrls: ['./campaigns-card.component.scss'],
  standalone: false,
})
export class CampaignsCardComponent {
  @Output() createNewCampaign: EventEmitter<boolean> = new EventEmitter();

  readonly loading$: Observable<boolean>;
  readonly error$: Observable<boolean>;
  readonly recipientCampaigns$: Observable<UiRecipientCampaign[]>;
  _businessId: string;
  readonly hasMany$: Observable<boolean>;
  private readonly showMore$$ = new BehaviorSubject(false);
  readonly showingMore$ = this.showMore$$.asObservable();

  constructor(
    private readonly service: CampaignsCardUiService,
    @Inject(ADD_TO_CAMPAIGNS_TOKEN) public readonly canSendCampaigns$: Observable<boolean>,
  ) {
    this.loading$ = service.isLoading$;
    this.error$ = service.isFailed$;

    this.recipientCampaigns$ = combineLatest([service.recipientCampaigns$, this.showMore$$]).pipe(
      map(([allCampaigns, showMore]) => {
        // TODO: Toggling show more causes us to reload unnecessarily
        if (showMore) {
          return allCampaigns;
        }
        return allCampaigns.slice(0, COLLAPSED_SIZE);
      }),
    );
    this.hasMany$ = service.recipientCampaigns.workResults$.pipe(map((c) => c.length > COLLAPSED_SIZE));
  }

  @Input()
  set businessId(businessId: string) {
    this._businessId = businessId;
    this.service.initializeForBusiness(businessId);
  }

  get businessId(): string {
    return this._businessId;
  }

  start(): void {
    this.createNewCampaign.emit(true);
  }

  showMore(): void {
    this.showMore$$.next(true);
  }

  showLess(): void {
    this.showMore$$.next(false);
  }

  handleStatusChange($event: StatusChangeEvent): void {
    this.service.setStatus($event);
  }
}
