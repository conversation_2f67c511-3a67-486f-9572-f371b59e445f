<uikit-async-ui [data]="campaigns$ | async" [loading]="loading$ | async" [error]="error$ | async">
  <ng-container *successData="let campaigns">
    <app-campaigns-list [campaigns]="campaigns" (statusChanged)="handleStatusChanged($event)"></app-campaigns-list>
  </ng-container>
  <ng-container loading>
    <app-campaign-loading
      [recipientCampaigns]="recipientCampaigns"
      (statusChanged)="handleStatusChanged($event)"
    ></app-campaign-loading>
  </ng-container>
</uikit-async-ui>
