import { schedule } from '@vendasta/rx-utils';
import { Observable, ReplaySubject, of, throwError } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { TestScheduler } from 'rxjs/testing';
import {
  CampaignService,
  CampaignStatus,
  RecipientCampaign,
  StatusChangeEvent,
  UiRecipientCampaign,
} from '../../campaigns';
import { CampaignPreviewResponse, ListCampaignsResponse, SetStatusResponse } from '../../campaigns/vbc-campaign-apis';
import { CampaignsCardUiService } from './campaigns-card.ui-service';

class MockCampaignService implements CampaignService {
  readonly statusRequests$$ = new ReplaySubject<StatusChangeEvent>(10);
  failAllRequests = false;

  public listResponse: () => RecipientCampaign[] = () => [];

  getMultiCampaigns = () => throwError('unmocked');

  listCampaigns(): Observable<ListCampaignsResponse> {
    return of(<ListCampaignsResponse>{
      recipient_campaigns: this.listResponse(),
    });
  }

  setStatus(recipientCampaignId: string, active: boolean): Observable<SetStatusResponse> {
    if (this.failAllRequests) {
      return throwError('failed as requested by test');
    }
    this.statusRequests$$.next(<StatusChangeEvent>{
      recipientCampaignId: recipientCampaignId,
      active: active,
    });
    return of(<SetStatusResponse>{
      recipientCampaignId: recipientCampaignId,
      status: active ? 'active' : 'stopped',
    });
  }

  getCampaignPreview(): Observable<CampaignPreviewResponse> {
    return undefined;
  }
}

describe('CampaignsCardUiService', () => {
  function statusFor(id: string, service: CampaignsCardUiService): Observable<CampaignStatus> {
    return service.recipientCampaigns$.pipe(
      switchMap((rcs: UiRecipientCampaign[]) => {
        return rcs.find((rc) => rc.recipientCampaignId === id).statusState.workResults$;
      }),
    );
  }

  let sched: TestScheduler;
  beforeEach(() => {
    sched = new TestScheduler((a, b) => expect(a).toEqual(b));
  });
  afterEach(() => {
    sched.flush();
  });
  describe('setStatus', () => {
    it('should update the status on the recipientCampaigns in the stream', () => {
      const api = new MockCampaignService();
      api.listResponse = () => [
        <RecipientCampaign>{
          recipient_campaign_id: 'RC-1',
          status: 'stopped',
        },
      ];
      const service = new CampaignsCardUiService(api);
      schedule(sched, '-|', () => service.initializeForBusiness('AG-123'));
      schedule(sched, '---|', () => service.setStatus({ recipientCampaignId: 'RC-1', active: true }));

      const idAndStatus$ = statusFor('RC-1', service);
      sched.expectObservable(idAndStatus$).toBe('-x-y', {
        x: 'stopped',
        y: 'active',
      });
    });
    it('should prefer status from list request over status from setter', () => {
      const api = new MockCampaignService();
      api.listResponse = () => [
        <RecipientCampaign>{
          recipient_campaign_id: 'RC-1',
          status: 'stopped',
        },
      ];
      const service = new CampaignsCardUiService(api);
      schedule(sched, '-|', () => service.initializeForBusiness('AG-123'));
      schedule(sched, '---|', () => service.setStatus({ recipientCampaignId: 'RC-1', active: true }));
      schedule(sched, '-----|', () => service.initializeForBusiness('AG-123'));

      const status$ = statusFor('RC-1', service);
      sched.expectObservable(status$).toBe('-x-y-z', {
        x: 'stopped',
        y: 'active',
        z: 'stopped',
      });
    });
    it('should use the latest status', () => {
      const api = new MockCampaignService();
      api.listResponse = () => [
        <RecipientCampaign>{
          recipient_campaign_id: 'RC-1',
          status: 'stopped',
        },
      ];
      const service = new CampaignsCardUiService(api);
      schedule(sched, '-|', () => service.initializeForBusiness('AG-123'));
      schedule(sched, '---|', () => service.setStatus({ recipientCampaignId: 'RC-1', active: true }));
      schedule(sched, '-----|', () => service.setStatus({ recipientCampaignId: 'RC-1', active: false }));

      const status$ = statusFor('RC-1', service);
      sched.expectObservable(status$).toBe('-x-y-z', {
        x: 'stopped',
        y: 'active',
        z: 'stopped',
      });
    });
    it('should use the status from the API', () => {
      const api = new MockCampaignService();
      api.listResponse = () => [
        <RecipientCampaign>{
          recipient_campaign_id: 'RC-1',
          status: 'stopped',
        },
      ];
      const service = new CampaignsCardUiService(api);
      schedule(sched, '-|', () => service.initializeForBusiness('AG-123'));
      schedule(sched, '---|', () => {
        api.setStatus = () =>
          of(<SetStatusResponse>{
            recipientCampaignId: 'RC-1',
            status: 'contrived status for test' as CampaignStatus,
          });
        service.setStatus({ recipientCampaignId: 'RC-1', active: true });
      });
      const status$ = statusFor('RC-1', service);
      sched.expectObservable(status$).toBe('-x-y', {
        x: 'stopped',
        y: 'contrived status for test',
      });
    });
    it('should call the campaigns service to set the status', () => {
      const api = new MockCampaignService();
      const service = new CampaignsCardUiService(api);
      const changes = { recipientCampaignId: 'RC-1', active: true };
      service.setStatus(changes);
      sched.expectObservable(api.statusRequests$$).toBe('x', { x: changes });
    });
    it('should not update the recipient campaigns if the set status request fails', () => {
      const api = new MockCampaignService();
      const service = new CampaignsCardUiService(api);
      schedule(sched, '-|', () => service.initializeForBusiness('AG-123'));
      schedule(sched, '---|', () => {
        api.failAllRequests = true;
        service.setStatus({ recipientCampaignId: 'RC-1', active: true });
      });

      const idAndStatus$ = service.recipientCampaigns$;
      sched.expectObservable(idAndStatus$).toBe('-x--', { x: expect.anything() });
    });
  });
});
