import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CampaignsCardComponent } from './campaigns-card.component';
import { AsyncUiModule } from '@vendasta/uikit';
import { CampaignsModule } from '../../campaigns';
import { CampaignsCardListComponent } from './campaigns-card-list.component';
import { VaStencilsModule } from '@vendasta/uikit';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { CampaignsCardUiService } from './campaigns-card.ui-service';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@NgModule({
  declarations: [CampaignsCardComponent, CampaignsCardListComponent],
  providers: [CampaignsCardUiService],
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatDividerModule,
    MatButtonModule,
    MatTooltipModule,
    AsyncUiModule,
    CampaignsModule,
    VaStencilsModule,
    GalaxyEmptyStateModule,
    TranslateModule,
    MatProgressSpinnerModule,
  ],
  exports: [CampaignsCardComponent],
})
export class CampaignsCardModule {}
