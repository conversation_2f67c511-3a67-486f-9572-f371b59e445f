@use 'design-tokens' as *;

glxy-empty-state {
  margin: $spacing-2;
}

p.empty-state__description {
  color: $gray;
  margin-bottom: $spacing-2;
}

$icon-size: 36px;
mat-icon.empty-state__icon {
  color: $dark-gray;
  width: $icon-size;
  height: $icon-size;
  font-size: $icon-size;
}

glxy-empty-state-hero.set-padding {
  margin-bottom: $spacing-3;
}

:host {
  mat-card-title {
    display: flex;
    align-items: center;
  }
}
