import { combineLatest, Observable, ReplaySubject } from 'rxjs';
import { RecipientCampaign } from '../../campaigns';
import { map, scan, share, startWith } from 'rxjs/operators';

export class RecipientCampaignsUiModel {
  private readonly delegate$: Observable<RecipientCampaign[]>;
  private readonly createdCampaignEvents$$ = new ReplaySubject<RecipientCampaign>(1);

  constructor(apiCampaigns$: Observable<RecipientCampaign[]>) {
    const allCreatedCampaigns$: Observable<RecipientCampaign[]> = this.createdCampaignEvents$$.pipe(
      scan((acc: RecipientCampaign[], val: RecipientCampaign) => [val].concat(acc), []),
      startWith([]),
    );
    const joinedCampaigns$ = combineLatest([apiCampaigns$, allCreatedCampaigns$]).pipe(
      map(([fromApi, created]) => created.concat(fromApi)),
    );
    this.delegate$ = joinedCampaigns$.pipe(share());
  }

  asObservable(): Observable<RecipientCampaign[]> {
    return this.delegate$;
  }

  addNewCampaign(event: RecipientCampaign): void {
    this.createdCampaignEvents$$.next(event);
  }
}
