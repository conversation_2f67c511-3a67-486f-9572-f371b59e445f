import { ChangeDetectionStrategy, Component, EventEmitter, Inject, Input, OnChanges, Output } from '@angular/core';
import { Observable } from 'rxjs';
import { CAMPAIGN_SERVICE_TOKEN, CampaignService } from '../../campaigns/vbc-campaign-apis';
import { ObservableWorkState } from '@vendasta/rx-utils/work-state';
import { isFailed$ } from '@vendasta/rx-utils/work-state';
import { map } from 'rxjs/operators';
import { StatusChangeEvent, UiCampaign } from '../../campaigns';
import { UiRecipientCampaign } from '../../campaigns';
import { MarketingCampaign } from '@vendasta/sales';

@Component({
  selector: 'app-campaigns-card-list',
  templateUrl: 'campaigns-card-list.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class CampaignsCardListComponent implements OnChanges {
  @Input() businessId: string;
  @Input() recipientCampaigns: UiRecipientCampaign[];
  readonly campaigns$: Observable<UiCampaign[]>;
  readonly error$: Observable<boolean>;
  readonly loading$: Observable<boolean>;
  @Output()
  readonly statusChanged = new EventEmitter<StatusChangeEvent>();
  private readonly state = new ObservableWorkState<UiCampaign[]>();

  constructor(@Inject(CAMPAIGN_SERVICE_TOKEN) private readonly service: CampaignService) {
    this.campaigns$ = this.state.workResults$;
    this.error$ = isFailed$(this.state);
    this.loading$ = this.state.isLoading$;
  }

  ngOnChanges(): void {
    if (this.businessId && this.recipientCampaigns) {
      const campaignIds = [...new Set(this.recipientCampaigns.map((c) => c.campaignId))];
      this.state.startWork(
        this.service
          .getMultiCampaigns(this.businessId, campaignIds)
          .pipe(map((response) => UiCampaignsFromGetMultiResponse(this.recipientCampaigns, response))),
      );
    }
  }

  handleStatusChanged($event: StatusChangeEvent): void {
    this.statusChanged.emit($event);
  }
}

function UiCampaignsFromGetMultiResponse(
  recipientCampaigns: UiRecipientCampaign[],
  campaigns: MarketingCampaign[],
): UiCampaign[] {
  return recipientCampaigns.map(
    (c) =>
      <UiCampaign>{
        name: campaigns.find((cam) => cam?.campaignId === c.campaignId)?.name,
        statusState: c.statusState,
        recipientCampaignId: c.recipientCampaignId,
      },
  );
}
