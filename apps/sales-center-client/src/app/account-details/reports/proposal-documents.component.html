<ng-container [ngSwitch]="state">
  <ng-container *ngSwitchCase="'empty'">
    <a
      mat-button
      color="primary"
      data-testid="create-proposal-link"
      [routerLink]="[proposalsUrl]"
      [queryParams]="{ organizationId: businessId }"
    >
      <mat-icon matPrefix>note_add</mat-icon>
      {{ 'ACCOUNT_DETAILS.REPORTS.PROPOSAL_CREATE' | translate }}
    </a>
  </ng-container>
  <ng-container *ngSwitchCase="'hasProposals'">
    <a
      mat-button
      color="primary"
      data-testid="edit-proposal-link"
      [routerLink]="document.url"
      *ngFor="let document of documents"
    >
      <mat-icon matPrefix>description</mat-icon>
      {{ document.title }}
    </a>
  </ng-container>
  <ng-container *ngSwitchCase="'loading'">
    <a mat-button>
      <mat-icon matPrefix>
        <mat-spinner [diameter]="20"></mat-spinner>
      </mat-icon>
      {{ 'ACCOUNT_DETAILS.REPORTS.PROPOSAL_LOADING' | translate }}
    </a>
  </ng-container>
  <ng-container *ngSwitchCase="'error'">
    <p class="error-block error-message">
      <mat-icon class="error-message" matInline>error</mat-icon>
      {{ 'ACCOUNT_DETAILS.REPORTS.PROPOSAL_ERROR' | translate }}
    </p>
  </ng-container>
</ng-container>
