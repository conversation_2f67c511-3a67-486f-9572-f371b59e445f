<ng-container *ngIf="emptyState(); then empty; else normal"></ng-container>

<ng-template #normal>
  <ng-container>
    <div *ngIf="snapshotReport" class="list-item-container--with-secondary">
      <a mat-button color="primary" (click)="openViewScorecard()" class="report-action">
        <mat-icon matPrefix color="primary">description</mat-icon>
        {{ 'SNAPSHOT_REPORT.VIEW_SNAPSHOT_SUMMARY' | whitelabelTranslate | async }}
      </a>
      <button
        mat-icon-button
        (click)="snapshotRenewClickEvent()"
        color="primary"
        [disabled]="!isSnapshotExpired(snapshotReport?.expired)"
        matTooltip="{{ 'SNAPSHOT_REPORT.REFRESH_SNAPSHOT' | whitelabelTranslate | async }}"
        [ngClass]="{
          'disable-color': !isSnapshotExpired(snapshotReport?.expired)
        }"
      >
        <mat-icon>refresh</mat-icon>
      </button>
    </div>
    <span class="item-date">
      {{ snapshotReport?.expired | date : 'mediumDate' }}
    </span>
  </ng-container>
  <ng-container *ngIf="!snapshotReport">
    <ng-container *ngIf="exceedLimit; then disableCreate; else enableCreate"></ng-container>
    <ng-template #disableCreate>
      <a mat-button disabled matTooltip="{{ 'SNAPSHOT_REPORT.DISABLE_CREATE_SNAPSHOT' | translate }}">
        <mat-icon matPrefix>note_add</mat-icon>
        {{ 'SNAPSHOT_REPORT.CREATE_SNAPSHOT' | whitelabelTranslate | async }}
      </a>
    </ng-template>
    <ng-template #enableCreate>
      <div *ngIf="snapshotCreationInProgress$ | async; else createSnapshot" class="snapshot-generating">
        <a mat-button color="primary">
          <mat-icon matPrefix>
            <mat-spinner [diameter]="20"></mat-spinner>
          </mat-icon>
          {{ 'SNAPSHOT_REPORT.GENERATING_SHORT_MESSAGE' | whitelabelTranslate | async }}
        </a>
      </div>
      <ng-template #createSnapshot>
        <a mat-button color="primary" (click)="snapshotCreateClickEvent()">
          <mat-icon matPrefix>note_add</mat-icon>
          {{ 'SNAPSHOT_REPORT.CREATE_SNAPSHOT' | whitelabelTranslate | async }}
        </a>
      </ng-template>
    </ng-template>
  </ng-container>
  <ng-container *ngIf="(displaySnapshotListingScan$ | async) && snapshotLiteState()">
    <a mat-button color="primary" [href]="liteReportUrl">
      <mat-icon matprefix svgIcon="listing-scan" class="listing-scan-icon"></mat-icon>
      {{ 'SNAPSHOT_REPORT.VIEW_LISTING_SCAN' | translate }}
    </a>
  </ng-container>
</ng-template>

<ng-template #empty>
  <div class="list-item no-border">
    <app-async-text [loadFailed]="!success" [loading]="loading" (retry)="retryLoading()"></app-async-text>
  </div>
</ng-template>

<ng-template #openScorecard>
  <app-snapshot-scorecard
    [businessId]="business.accountGroupId"
    [snapshotName]="snapshotName"
    [trigger]="triggerUpdate$ | async"
    [trackingCategory]="'account-details-snapshot'"
    (refreshReportClickedEvent)="snapshotRenewClickEvent()"
    (closeSlideOutPanel)="closeDrawer()"
  ></app-snapshot-scorecard>
</ng-template>
