import { NgModule } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { CommonModule } from '@angular/common';
import { ReportsCardComponent } from './reports-card.component';
import { SnapshotReportContentComponent } from './snapshot-report-content.component';
import { ReportsCardService } from './reports-card.service';
import { AsyncTextModule } from '../async-text/async-text.module';
import { TranslateModule } from '@ngx-translate/core';
import { ExecutiveReportContentComponent } from './executive-report-content.component';
import { WhitelabelTranslationModule } from '@galaxy/snapshot';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';
import { ClipboardModule } from '@angular/cdk/clipboard';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { VaBadgeModule } from '@vendasta/uikit';
import { RouterModule } from '@angular/router';
import { DynamicOpenCloseTemplateRefService } from '../../common/side-drawer/dynamic-open-close-template-ref.service';
import { SnapshotScorecardModule } from '../../snapshot-scorecard/snapshot-scorecard.module';

import { CurrentSnapshotService } from '@vendasta/snapshot';
import { ProposalDocumentsComponent } from './proposal-documents.component';
import { ProductAnalyticsModule } from '@vendasta/product-analytics';

@NgModule({
  imports: [
    CommonModule,
    MatCardModule,
    MatInputModule,
    MatDividerModule,
    MatListModule,
    MatIconModule,
    AsyncTextModule,
    MatDialogModule,
    TranslateModule,
    MatButtonModule,
    MatTooltipModule,
    WhitelabelTranslationModule,
    MatMenuModule,
    ClipboardModule,
    MatProgressSpinnerModule,
    VaBadgeModule,
    RouterModule,
    SnapshotScorecardModule,

    ProductAnalyticsModule,
  ],
  declarations: [
    ReportsCardComponent,
    SnapshotReportContentComponent,
    ExecutiveReportContentComponent,
    ProposalDocumentsComponent,
  ],
  exports: [ReportsCardComponent, SnapshotReportContentComponent, ExecutiveReportContentComponent],
  providers: [ReportsCardService, DynamicOpenCloseTemplateRefService, CurrentSnapshotService],
})
export class ReportsCardModule {}
