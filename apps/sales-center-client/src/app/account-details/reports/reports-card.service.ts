import { Injectable } from '@angular/core';
import { ManualStartWorkStateMap } from '@vendasta/rx-utils/work-state';
import { CurrentSnapshotService } from '@vendasta/snapshot';
import { SSOService, newPartnerServiceContext } from '@vendasta/sso';
import { Observable, ReplaySubject, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { SnapshotReport } from './snapshot-report';

@Injectable()
export class ReportsCardService {
  private readonly isLoading$$: ReplaySubject<boolean> = new ReplaySubject<boolean>(1);
  readonly isLoading$: Observable<boolean>;
  private readonly showError$$: ReplaySubject<boolean> = new ReplaySubject<boolean>(1);
  readonly showError$: Observable<boolean>;
  private readonly snapshotReportWorkStateMap = new ManualStartWorkStateMap<string, SnapshotReport>();

  constructor(
    private readonly currentSnapshotService: CurrentSnapshotService,
    private readonly ssoService: SSOService,
  ) {
    this.isLoading$ = this.isLoading$$.asObservable();
    this.showError$ = this.showError$$.asObservable();
  }

  loadSnapshotReport(partnerId: string, marketId: string, accountGroupId: string): void {
    this.snapshotReportWorkStateMap.startWork(accountGroupId, (success, fail) => {
      this.currentSnapshotService
        .get(accountGroupId)
        .pipe(
          map((response) => {
            return new SnapshotReport(response.snapshot);
          }),
          catchError((err) => {
            if (err.status === 404) {
              return of(null);
            }
            throw err;
          }),
        )
        .subscribe(success, () => fail());
    });
  }

  isLoadingSnapshotReport(accountGroupId: string): Observable<boolean> {
    return this.snapshotReportWorkStateMap.isLoading$(accountGroupId);
  }

  isSuccessSnapshotReport(accountGroupId: string): Observable<boolean> {
    return this.snapshotReportWorkStateMap.isSuccess$(accountGroupId);
  }

  snapshotReport$(accountGroupId: string): Observable<SnapshotReport> {
    return this.snapshotReportWorkStateMap.getWorkResults$(accountGroupId);
  }

  getExecutiveReportLink(partnerId: string, accountGroupId: string): Observable<string> {
    const serviceContext = newPartnerServiceContext(partnerId);
    const destination = `/account/location/${accountGroupId}/executive-report/weekly/`;
    return this.ssoService.getEntryUrl('VBC', serviceContext, { path: destination });
  }
}
