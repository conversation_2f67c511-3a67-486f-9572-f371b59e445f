import { Component, Input, OnInit } from '@angular/core';
import { ReportsCardService } from './reports-card.service';
import { Observable } from 'rxjs';
import { publish, refCount } from 'rxjs/operators';

@Component({
  selector: 'app-executive-report-content',
  templateUrl: './executive-report-content.component.html',
  styleUrls: ['./reports-card-common.scss'],
  standalone: false,
})
export class ExecutiveReportContentComponent implements OnInit {
  @Input() partnerId: string;
  @Input() accountGroupId: string;

  public executiveReportLink: Observable<string>;

  constructor(private readonly reportsCardService: ReportsCardService) {}

  ngOnInit(): void {
    this.executiveReportLink = this.reportsCardService
      .getExecutiveReportLink(this.partnerId, this.accountGroupId)
      .pipe(publish(), refCount());
  }
}
