import { ChangeDetectorRef, Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { DocumentBuilderService, Document } from '@galaxy/document-builder/core';
import { map, shareReplay } from 'rxjs/operators';
import { PROPOSALS_ROOT } from '../../urls';

type DocumentWithUrl = Document & { url: string };

@Component({
  selector: 'app-proposal-documents',
  templateUrl: './proposal-documents.component.html',
  styleUrls: ['./reports-card-common.scss', './proposal-documents.component.scss'],
  standalone: false,
})
export class ProposalDocumentsComponent implements OnInit, OnDestroy {
  @Input() businessId: string;

  documents: DocumentWithUrl[];

  state: 'loading' | 'hasProposals' | 'empty' | 'error' = 'loading';

  readonly proposalsUrl = `/${PROPOSALS_ROOT}`;
  private readonly _subscriptions: Subscription[] = [];

  constructor(
    private readonly cd: ChangeDetectorRef,
    private readonly documentBuilderService: DocumentBuilderService,
  ) {}

  ngOnInit(): void {
    this._subscriptions.push(
      this.documentBuilderService
        .searchDocuments({ recipientOrganizationId: this.businessId })
        .pipe(
          map((results) => {
            return (results.results || []).map((document) => ({
              ...document,
              url: getUrlFromDocument(document),
            }));
          }),
          shareReplay(1),
        )
        .subscribe({
          next: (documents: DocumentWithUrl[]) => {
            this.state = documents.length === 0 ? 'empty' : 'hasProposals';
            this.documents = documents;
            this.cd.detectChanges();
          },
          error: (error: Error) => {
            this.state = 'error';
            console.error(error);
            this.cd.detectChanges();
          },
        }),
    );
  }

  ngOnDestroy(): void {
    this._subscriptions.forEach((s) => s.unsubscribe());
  }
}

function getUrlFromDocument(document: Document): string {
  return `/${PROPOSALS_ROOT}/edit/${document.documentId}`;
}
