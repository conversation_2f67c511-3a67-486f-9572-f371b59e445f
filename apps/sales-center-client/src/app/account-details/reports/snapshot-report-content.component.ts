import {
  Component,
  EventEmitter,
  Inject,
  Input,
  OnC<PERSON><PERSON>,
  On<PERSON><PERSON>roy,
  OnInit,
  Output,
  QueryList,
  SimpleChanges,
  TemplateRef,
  ViewChildren,
} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { CreateRefreshDialogData, SnapshotAction, SnapshotCheckoutComponent } from '@galaxy/snapshot';
import { SubscriptionList } from '@vendasta/rx-utils';
import moment from 'moment';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { startWith, take } from 'rxjs/operators';
import { DynamicOpenCloseTemplateRefService } from '../../common/side-drawer/dynamic-open-close-template-ref.service';
import { ACCESS_SNAPSHOT_LISTING_SCAN_PCC_SCC_TOKEN } from '../../features';
import { ViewBusiness } from '../account-info-store.service';
import { SnapshotReport } from './snapshot-report';

export interface SnapshotInfo {
  hasActiveSnapshot: boolean;
}

const SNAPSHOT_ORIGIN = 'account-details';

export interface SnapshotReportChanges {
  lastestExpiryTime: string;
}

@Component({
  selector: 'app-snapshot-report-content',
  templateUrl: './snapshot-report-content.component.html',
  styleUrls: ['./reports-card-common.scss'],
  standalone: false,
})
export class SnapshotReportContentComponent implements OnInit, OnChanges, OnDestroy {
  @Input() snapshotReport: SnapshotReport = null;
  @Input() loading = true;
  @Input() success = false;
  @Input() business: ViewBusiness = null;
  @Input() exceedLimit = false;
  @Input() snapshotName: string;
  @Output() retry = new EventEmitter<null>();

  @ViewChildren('openScorecard') openScorecardTemplate: QueryList<TemplateRef<any>>;

  triggerUpdate$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  triggerUpdate$: Observable<boolean> = this.triggerUpdate$$.asObservable();

  private snapshotCreationInProgress$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  snapshotCreationInProgress$: Observable<boolean> = this.snapshotCreationInProgress$$.asObservable();

  private readonly dialogRefreshedSnapshot$$ = new Subject<boolean>();

  liteReportUrl: string;
  reportUrl: string;

  private readonly subscriptions = SubscriptionList.new();

  constructor(
    private readonly dialog: MatDialog,
    private readonly dynamicOpenCloseService: DynamicOpenCloseTemplateRefService,
    @Inject(ACCESS_SNAPSHOT_LISTING_SCAN_PCC_SCC_TOKEN) readonly displaySnapshotListingScan$: Observable<boolean>,
  ) {}

  ngOnInit(): void {
    this.liteReportUrl = `/snapshot/lite/${this.business.businessId}`;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (!!changes.snapshotReport && !!changes.snapshotReport.currentValue) {
      this.snapshotCreationInProgress$$.next(false);
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }

  retryLoading(): void {
    this.retry.emit();
  }

  snapshotProvisionClickEvent(action: SnapshotAction): void {
    const dialogData: CreateRefreshDialogData = {
      accountGroupId: this.business.businessId,
      accountGroupName: this.business.name,
      formattedExpiry: moment(this.snapshotReport?.expired).fromNow(),
      action: action,
      snapshotOriginDetails: SNAPSHOT_ORIGIN,
    };
    const ref = this.dialog.open(SnapshotCheckoutComponent, { width: '480px', data: dialogData });
    this.subscriptions.add(ref.afterClosed(), (successful) => {
      if (successful) {
        this.dialogRefreshedSnapshot$$.next(true);
        this.snapshotCreationInProgress$$.next(true);
        window.setTimeout(() => {
          this.retryLoading();
        }, 4000);
      }
    });
  }

  snapshotRefreshClickEvent(): void {
    this.snapshotProvisionClickEvent(SnapshotAction.Refresh);
  }

  snapshotCreateClickEvent(): void {
    this.snapshotProvisionClickEvent(SnapshotAction.Create);
  }

  emptyState(): boolean {
    return this.loading || !this.success;
  }

  snapshotLiteState(): boolean {
    return this.business.countryCode === 'US';
  }

  openViewScorecard(): void {
    this.triggerUpdate$.pipe(take(1)).subscribe((trigger) => this.triggerUpdate$$.next(!trigger));

    const openScorecard$ = this.openScorecardTemplate.changes.pipe(startWith(this.openScorecardTemplate));

    this.subscriptions.add(openScorecard$, (ref) => {
      this.dynamicOpenCloseService.registerTemplate('openScorecard', ref.first);
      this.dynamicOpenCloseService.open('openScorecard');
    });
  }

  closeDrawer(): void {
    this.dynamicOpenCloseService.close();
  }

  snapshotRenewClickEvent(): void {
    this.dynamicOpenCloseService.close();
    this.snapshotRefreshClickEvent();
  }

  isSnapshotExpired(expiry: Date): boolean {
    return expiry < new Date();
  }
}
