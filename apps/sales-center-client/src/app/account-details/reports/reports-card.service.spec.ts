import { TestScheduler } from 'rxjs/testing';
import { ReportsCardService } from './reports-card.service';
import { SnapshotReport } from './snapshot-report';
import { GetCurrentResponseInterface } from '@vendasta/snapshot/lib/_internal';

let scheduler: TestScheduler;

const MockGetSnapshotReportResponse: GetCurrentResponseInterface = {
  snapshot: {
    snapshotId: '0d7ad166-8bca-46f6-9e7f-485f45f6c940',
    expired: new Date(2021, 4, 10),
    created: new Date(2021, 4, 2),
    path: '/snapshot/0d7ad166-8bca-46f6-9e7f-485f45f6c940',
  },
};

const expectSnapshotReport: SnapshotReport = {
  snapshotId: '0d7ad166-8bca-46f6-9e7f-485f45f6c940',
  expired: new Date(2021, 4, 10),
  created: new Date(2021, 4, 2),
  path: '/snapshot/0d7ad166-8bca-46f6-9e7f-485f45f6c940',
};

class MockApiService {
  happy: boolean;
  hasResults: boolean;

  constructor(happy = true) {
    this.happy = happy;
  }
  get = jest.fn(() => {
    if (this.happy) {
      return scheduler.createColdObservable('-x', { x: MockGetSnapshotReportResponse });
    } else {
      return scheduler.createColdObservable('-#', null, Error('Error!'));
    }
  });
}

describe('ReportsCardService', () => {
  let service: ReportsCardService;
  let mockApiService: MockApiService;

  beforeEach(() => {
    scheduler = new TestScheduler((a, b) => expect(a).toEqual(b));
  });

  afterEach(() => {
    scheduler.flush();
  });

  describe('loadSnapShotReport', () => {
    test('should get and convert correct snapshot report', () => {
      mockApiService = new MockApiService(true);
      service = new ReportsCardService(mockApiService as any, null);
      service.loadSnapshotReport('VUNI', 'default', 'AG-GX6GDC7K55');
      scheduler.expectObservable(service.snapshotReport$('AG-GX6GDC7K55')).toBe('-x', { x: expectSnapshotReport });
    });

    test('isSuccessSnapshotReport$ should emit false if http request failed', () => {
      mockApiService = new MockApiService(false);
      service = new ReportsCardService(mockApiService as any, null);
      service.loadSnapshotReport('VUNI', 'default', 'AG-GX6GDC7K55');

      scheduler.expectObservable(service.isSuccessSnapshotReport('AG-GX6GDC7K55')).toBe('-x', { x: false });
    });
  });
});
