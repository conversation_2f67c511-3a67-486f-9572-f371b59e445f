<mat-card appearance="outlined">
  <mat-card-header>
    <mat-card-title>
      <span>{{ 'ACCOUNT_DETAILS.REPORTS.TITLE' | translate }}</span>
      <ng-content select="[afterTitle]"></ng-content>
    </mat-card-title>
  </mat-card-header>
  <mat-card-content>
    <app-snapshot-report-content
      [snapshotReport]="snapshotReport$ | async"
      [business]="business"
      [loading]="loading$ | async"
      [success]="success$ | async"
      [exceedLimit]="exceedLimit$ | async"
      [snapshotName]="snapshotName"
      (retry)="loading()"
    ></app-snapshot-report-content>

    <!-- TODO: Add executive report access controls -->
    <app-executive-report-content
      [partnerId]="partnerId"
      [accountGroupId]="business.businessId"
    ></app-executive-report-content>

    <div class="card-subtitle">
      {{ 'ACCOUNT_DETAILS.REPORTS.PROPOSALS' | translate }}
    </div>
    <app-proposal-documents
      *ngIf="business?.businessId && (proposalBuilderDisabled$ | async) === false"
      [businessId]="business.businessId"
    ></app-proposal-documents>
  </mat-card-content>
</mat-card>
