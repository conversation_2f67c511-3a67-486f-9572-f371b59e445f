@use 'design-tokens' as *;

:host {
  display: block;
}

.list-item {
  font-size: 16px;
  padding: 16px 0;
}

.list-item:last-child {
  padding-bottom: 0;
}

.no-border {
  border-bottom: none;
}

.list-item-container--with-secondary {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
  width: 100%;
}

.report-action {
  white-space: nowrap;
}

.report-title {
  font-size: 16px;
}

.primary-list-action {
  flex-basis: 70%;
}

.listing-scan-icon {
  margin-left: 1px;
}

.list-item-container--with-secondary > .icon-disabled {
  color: $secondary-font-color;
  cursor: default;
}

.disable-color {
  color: $gray;
}

::ng-deep .card-subtitle {
  @include text-preset-4--bold;

  &:not(:first-child) {
    margin-top: $spacing-4;
  }
}

.item-date {
  font-size: $font-preset-5-size;
  color: $secondary-font-color;
}

.snapshot-generating {
  .mat-mdc-list-base,
  .mat-mdc-list-item,
  mat-icon {
    color: $black;
    pointer-events: none;
  }
}
