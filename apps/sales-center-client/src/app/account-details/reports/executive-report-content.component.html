<div class="list-item-container--with-secondary">
  <a mat-button color="primary" href="{{ executiveReportLink | async }}" target="_blank">
    <mat-icon matPrefix>store</mat-icon>
    {{ 'ACCOUNT_DETAILS.REPORTS.EXECUTIVE_REPORT' | whitelabelTranslate | async }}
  </a>
  <button mat-icon-button color="primary" [matMenuTriggerFor]="snapshotMenu">
    <mat-icon>more_vert</mat-icon>
  </button>
  <mat-menu #snapshotMenu="matMenu">
    <a class="primary-list-action" mat-menu-item href="{{ executiveReportLink | async }}" target="_blank">
      <span mat-line>
        {{ 'ACCOUNT_DETAILS.REPORTS.VIEW_EXECUTIVE_REPORT' | translate }}
      </span>
    </a>
    <button mat-menu-item *ngIf="executiveReportLink | async as url" [cdkCopyToClipboard]="url">
      {{ 'ACCOUNT_DETAILS.REPORTS.COPY_URL' | whitelabelTranslate | async }}
    </button>
  </mat-menu>
</div>
<span class="item-date">
  {{ 'ACCOUNT_DETAILS.REPORTS.UP_TO_DATE' | translate }}
</span>
