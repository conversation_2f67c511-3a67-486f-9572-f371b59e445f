import { Component, Inject, Input, OnInit } from '@angular/core';
import { DocumentBuilderService } from '@galaxy/document-builder/core';
import { TranslateService } from '@ngx-translate/core';
import { Observable } from 'rxjs';
import { FeatureFlagService } from '../../core';
import {
  SNAPSHOT_LIMIT_SERVICE_TOKEN,
  SnapshotCountSDKWrapperService,
} from '../../easy-account-create/business/providers/snapshot-limit.service';
import { ViewBusiness } from '../account-info-store.service';
import { ReportsCardService } from './reports-card.service';
import { SnapshotReport } from './snapshot-report';

@Component({
  selector: 'app-reports-card',
  templateUrl: './reports-card.component.html',
  styleUrls: ['./reports-card.component.scss'],
  standalone: false,
})
export class ReportsCardComponent implements OnInit {
  @Input() business: ViewBusiness = null;
  @Input() partnerId = '';
  @Input() marketId = '';
  @Input() showExecReportLink = false;
  @Input() snapshotName: string;
  snapshotReport$: Observable<SnapshotReport>;
  loading$: Observable<boolean>;
  success$: Observable<boolean>;
  exceedLimit$: Observable<boolean>;
  title$: Observable<string>;

  proposalBuilderDisabled$ = this.documentBuilderService.isProposalBuilderDisabled();

  constructor(
    private readonly snapshotReportCardService: ReportsCardService,
    @Inject(SNAPSHOT_LIMIT_SERVICE_TOKEN) readonly snapshotLimitService: SnapshotCountSDKWrapperService,
    private readonly featureAccess: FeatureFlagService,
    private readonly translate: TranslateService,
    private readonly documentBuilderService: DocumentBuilderService,
  ) {}

  ngOnInit(): void {
    this.snapshotReportCardService.loadSnapshotReport(this.partnerId, this.marketId, this.business.businessId);
    this.loading$ = this.snapshotReportCardService.isLoadingSnapshotReport(this.business.businessId);
    this.success$ = this.snapshotReportCardService.isSuccessSnapshotReport(this.business.businessId);
    this.snapshotReport$ = this.snapshotReportCardService.snapshotReport$(this.business.businessId);
    this.exceedLimit$ = this.snapshotLimitService.hasExceededSnapshotLimit$;
  }

  loading(): void {
    this.snapshotReportCardService.loadSnapshotReport(this.partnerId, this.marketId, this.business.businessId);
  }
}
