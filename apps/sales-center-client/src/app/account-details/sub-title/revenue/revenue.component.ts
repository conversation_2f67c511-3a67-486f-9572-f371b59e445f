import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-revenue-title',
  styleUrls: ['../sub-title.component.scss'],
  templateUrl: 'revenue.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class RevenueComponent {
  @Input() revenue: number;
  @Input() currency: string;
  @Output() retry = new EventEmitter<null>();

  retryLoading(): void {
    this.retry.emit(null);
  }
}
