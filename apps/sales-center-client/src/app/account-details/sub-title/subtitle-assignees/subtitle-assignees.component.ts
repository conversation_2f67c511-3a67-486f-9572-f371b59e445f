import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { BehaviorSubject, combineLatest, Observable, Subscription } from 'rxjs';
import { Salesperson } from '@galaxy/types';
import { map, take, tap } from 'rxjs/operators';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { AssigneeEditorComponent } from '../../assignee-editor/assignee-editor.component';
import { ViewBusiness } from '../../account-info-store.service';
import { SubtitleAssigneesService } from './subtitle-assignees.service';

interface AssigneeTemplateData {
  assignee: Salesperson;
  additionalAssignees: Salesperson[];
}

@Component({
  selector: 'app-subtitle-assignees',
  templateUrl: './subtitle-assignees.component.html',
  styleUrls: ['./subtitle-assignees.component.scss'],
  standalone: false,
})
export class SubtitleAssigneesComponent implements OnInit, OnDestroy {
  @Input() business: ViewBusiness;
  additionalAssignees$: Observable<Salesperson[]>;
  additionalAssigneeLoading$: Observable<boolean>;
  additionalAssigneeSuccess$: Observable<boolean>;
  assignee$: Observable<Salesperson>;
  assigneesData$: Observable<AssigneeTemplateData>;
  assigneeLoading$: Observable<boolean>;
  assigneeSuccess$: Observable<boolean>;
  assigneeUpdated = false;
  tempAssignee: Salesperson = null;
  tempAssignees: Salesperson[] = [];
  public assigneeId: string;

  private readonly subscriptions: Subscription[] = [];

  constructor(
    private readonly dialog: MatDialog,
    private readonly subtitleAssigneeService: SubtitleAssigneesService,
  ) {}

  ngOnInit(): void {
    this.assigneeId = this.business.assigneeId;
    const partnerId = this.business.partnerId;
    const marketId = this.business.marketId;
    const additionalAssigneeIds = this.business.additionalAssigneeIds;

    if (this.assigneeId) {
      this.subtitleAssigneeService.loadAssignee(partnerId, marketId, this.assigneeId);
      this.subtitleAssigneeService.loadAdditionalAssignees(partnerId, marketId, additionalAssigneeIds);

      this.assignee$ = this.subtitleAssigneeService.assignee$(this.assigneeId);
      this.additionalAssignees$ = this.subtitleAssigneeService.additionalAssignees$(additionalAssigneeIds);
    } else {
      this.assignee$ = new BehaviorSubject<Salesperson>(null);
      this.additionalAssignees$ = new BehaviorSubject<Salesperson[]>(null);
    }

    this.assigneesData$ = combineLatest([this.assignee$, this.additionalAssignees$]).pipe(
      map(
        ([assignee, additionalAssignees]: [Salesperson, Salesperson[]]): AssigneeTemplateData => ({
          assignee: assignee,
          additionalAssignees: additionalAssignees,
        }),
      ),
    );
    this.assigneeLoading$ = this.subtitleAssigneeService.isLoadingAssignee$(this.assigneeId);
    this.assigneeSuccess$ = this.subtitleAssigneeService.isSuccessAssignee$(this.assigneeId);
    this.additionalAssigneeLoading$ = this.subtitleAssigneeService.isLoadingAdditionalAssignees$(additionalAssigneeIds);
    this.additionalAssigneeSuccess$ = this.subtitleAssigneeService.isSuccessAdditionalAssignees$(additionalAssigneeIds);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
  }

  openEditAssigneesDialog(): void {
    this.subscriptions.push(
      combineLatest([this.assignee$, this.additionalAssignees$])
        .pipe(
          tap(([assignee, additionalAssignees]) => {
            const config: MatDialogConfig = new MatDialogConfig();

            // ignore observables if we have already edited the subtitle-assignees once.
            if (this.assigneeUpdated) {
              config.data = {
                business: this.business,
                assignee: this.tempAssignee,
                additionalAssignees: this.tempAssignees,
              };
            } else {
              config.data = {
                business: this.business,
                assignee: assignee,
                additionalAssignees: additionalAssignees,
              };
            }
            config.minWidth = '200px';

            const assigneeEditDialogRef = this.dialog.open(AssigneeEditorComponent, config);
            this.subscriptions.push(
              assigneeEditDialogRef.afterClosed().subscribe((results) => {
                if (results && results.changesMade) {
                  this.assigneeUpdated = true;
                  this.assigneeId = results.primaryAssignee.id;
                  this.tempAssignee = results.primaryAssignee;
                  this.tempAssignees = results.additionalAssignees;
                }
              }),
            );
          }),
          take(1),
        )
        .subscribe(),
    );
  }

  assigneeRetryLoading(): void {
    this.subtitleAssigneeService.loadAssignee(
      this.business.partnerId,
      this.business.marketId,
      this.business.assigneeId,
    );
    this.subtitleAssigneeService.loadAdditionalAssignees(
      this.business.partnerId,
      this.business.marketId,
      this.business.additionalAssigneeIds,
    );
  }
}
