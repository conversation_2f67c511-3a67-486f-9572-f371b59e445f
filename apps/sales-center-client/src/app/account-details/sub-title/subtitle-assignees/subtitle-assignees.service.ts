import { Injectable } from '@angular/core';
import { Salesperson } from '@galaxy/types';
import { IAMService, UserIdentifierInterface } from '@vendasta/iam';
import { ObservableWorkStateMap } from '@vendasta/rx-utils/work-state';
import { Observable, of } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { SalespeopleService } from '../../../salespeople/salespeople.service';

@Injectable()
export class SubtitleAssigneesService {
  private readonly assigneeState = new ObservableWorkStateMap<string, Salesperson>();
  private readonly additionalAssigneesState = new ObservableWorkStateMap<string[], Salesperson[]>();

  constructor(
    private readonly salespeopleService: SalespeopleService,
    private readonly iam: IAMService,
  ) {}

  loadAssignee(partnerId: string, marketId: string, salespersonId: string): void {
    const userId$ = this.getUserIdFromSalespersonId$(salespersonId, partnerId);
    const work = userId$.pipe(
      switchMap((userId) => {
        if (!userId) {
          return of(null);
        }
        return this.salespeopleService.getSalesPersonByUserId$(userId, partnerId);
      }),
    );

    this.assigneeState.startWork(salespersonId, work);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private getUserIdFromSalespersonId$(salespersonId: string, partnerId: string): Observable<string> {
    const identifiers = <UserIdentifierInterface>{
      subjectId: salespersonId,
    };

    return this.iam.getMultiUsers([identifiers]).pipe(map((users) => users?.[0]?.userId || ''));
  }

  assignee$(salespersonId: string): Observable<Salesperson> {
    return this.assigneeState.getWorkResults$(salespersonId);
  }

  isLoadingAssignee$(salespersonId: string): Observable<boolean> {
    return this.assigneeState.isLoading$(salespersonId);
  }

  isSuccessAssignee$(salespersonId: string): Observable<boolean> {
    return this.assigneeState.isSuccess$(salespersonId);
  }

  loadAdditionalAssignees(partnerId: string, marketId: string, salespersonIds: string[]): void {
    this.additionalAssigneesState.startWork(
      salespersonIds,
      this.salespeopleService.getSalespeople$(partnerId, marketId, salespersonIds),
    );
  }

  additionalAssignees$(salespersonIds: string[]): Observable<Salesperson[]> {
    return this.additionalAssigneesState.getWorkResults$(salespersonIds);
  }

  isLoadingAdditionalAssignees$(salespersonIds: string[]): Observable<boolean> {
    return this.additionalAssigneesState.isLoading$(salespersonIds);
  }

  isSuccessAdditionalAssignees$(salespersonIds: string[]): Observable<boolean> {
    return this.additionalAssigneesState.isSuccess$(salespersonIds);
  }
}
