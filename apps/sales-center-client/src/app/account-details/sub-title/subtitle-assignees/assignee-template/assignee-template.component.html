<ng-container *ngIf="assignee; else noAssignees">
  <span *ngIf="assignees.length === 0">{{ 'COMMON.ASSIGNEE_SINGULAR' | translate }}: {{ assignee.fullName }}</span>
  <span *ngIf="assignees.length !== 0">{{ 'COMMON.ASSIGNEE_PLURAL' | translate }}: {{ assignee.fullName }}</span>
  <span *ngIf="assignee.jobTitle">- {{ assignee?.jobTitle }}</span>
  <span *ngIf="assignees.length !== 0">
    <a color="primary" [matMenuTriggerFor]="actions">
      &nbsp;+&nbsp;{{ assignees.length }}&nbsp;{{ 'COMMON.MORE_LABEL_LOWER' | translate }}
      <mat-icon class="drop-down-icon">arrow_drop_down</mat-icon>
    </a>
    <mat-menu #actions="matMenu">
      <div class="list-item" *ngFor="let additionalAssignee of assignees">
        <span>
          {{ additionalAssignee.fullName }}
          <span *ngIf="additionalAssignee.jobTitle">- {{ additionalAssignee.jobTitle }}</span>
        </span>
      </div>
    </mat-menu>
  </span>
</ng-container>

<ng-template #noAssignees>
  <span>{{ 'ASSIGNEE_EDITOR.NO_ASSIGNED_SALESPEOPLE' | translate }}</span>
</ng-template>
