import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { AssigneeTemplateComponent } from './assignee-template.component';
import { MatMenuModule } from '@angular/material/menu';
import { SubtitleAssigneesComponent } from '../subtitle-assignees.component';
import { CommonModule } from '@angular/common';
import { AsyncTextModule } from '../../../async-text/async-text.module';
import { MatIconModule } from '@angular/material/icon';

@NgModule({
  imports: [CommonModule, TranslateModule, MatMenuModule, AsyncTextModule, MatIconModule],
  exports: [SubtitleAssigneesComponent],
  declarations: [AssigneeTemplateComponent, SubtitleAssigneesComponent],
})
export class AssigneeTemplateModule {}
