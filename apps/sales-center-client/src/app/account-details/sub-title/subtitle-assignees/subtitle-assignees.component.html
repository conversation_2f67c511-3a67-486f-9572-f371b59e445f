<span>
  <ng-container *ngIf="!!this.assigneeId; then isAssignees; else noAssignees"></ng-container>
</span>

<ng-template #noAssignees>
  <app-assignee-template [assignee]="null" [assignees]="null"></app-assignee-template>
  <a (click)="openEditAssigneesDialog()" class="edit-assignees-button">
    <mat-icon class="edit-button-icon">edit</mat-icon>
  </a>
</ng-template>

<ng-template #isAssignees>
  <ng-container *ngIf="assigneesData$ | async as data; else assigneeLoadingError">
    <ng-container *ngIf="!assigneeUpdated">
      <app-assignee-template
        [assignee]="assignee$ | async"
        [assignees]="data.additionalAssignees"
      ></app-assignee-template>
    </ng-container>
    <ng-container *ngIf="assigneeUpdated">
      <app-assignee-template [assignee]="tempAssignee" [assignees]="tempAssignees"></app-assignee-template>
    </ng-container>
  </ng-container>
  <a (click)="openEditAssigneesDialog()">
    <mat-icon class="edit-button-icon">edit</mat-icon>
  </a>
</ng-template>

<ng-template #assigneeLoadingError>
  <app-async-text
    [loadFailed]="(assigneeSuccess$ | async) === false || (additionalAssigneeSuccess$ | async) === false"
    [loading]="(assigneeLoading$ | async) || (additionalAssigneeLoading$ | async)"
    (retry)="assigneeRetryLoading()"
  ></app-async-text>
</ng-template>
