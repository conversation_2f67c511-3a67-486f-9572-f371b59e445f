import { Component, Inject, Input, On<PERSON><PERSON>roy, OnInit, Optional } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { CustomField } from '@galaxy/account-group';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { ParticipantService, SubjectParticipant } from '@galaxy/conversation/core';
import { TranslateService } from '@ngx-translate/core';
import { ConversationChannel, GlobalParticipantType, Participant } from '@vendasta/conversation';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { SubscriptionList } from '@vendasta/rx-utils';
import { EMPTY, Observable, ReplaySubject, combineLatest } from 'rxjs';
import { catchError, map, startWith } from 'rxjs/operators';
import { AccessChecker, SSCAccessService } from '../../access';
import { AsteriskService } from '../../astbe_sdk';
import { FeatureFlagService, HostProvider, PartnerCenterHostService } from '../../core';
import { Features } from '../../features';
import { LoggedInUserInfoService } from '../../logged-in-user-info';
import { ACCESS_ASSOCIATED_PARTNER_INFO_TOKEN } from '../../partner/partner-overrides';
import { ScreenshareService } from '../../screenshare';
import { ScreenShareBusinessInfo } from '../../screenshare/screenshare.component';
import { ViewBusiness } from '../account-info-store.service';
import { CustomerIdentifiers } from './customer-id/customer-id.service';
import { SubTitleService } from './sub-title.service';

interface CallNowData {
  extension: string;
  phoneNumber: string;
}

interface RevenueData {
  currency: string;
  revenue: number;
}

interface SupportFeaturesEnabled {
  zendesk: boolean;
  freshdesk: boolean;
}

@Component({
  selector: 'app-sub-title',
  styleUrls: ['sub-title.component.scss'],
  templateUrl: 'sub-title.component.html',
  standalone: false,
})
export class SubTitleComponent implements OnInit, OnDestroy {
  @Input() customerIdentifiers: Observable<CustomerIdentifiers>;

  @Input() set businessInfo(biz: ViewBusiness) {
    this.business = biz;
    const screenshareInfo = <ScreenShareBusinessInfo>{
      partnerId: biz.partnerId,
      businessId: biz.accountGroupId,
      countryCode: biz.countryCode,
    };
    this.screenShareBusinessInfo$$.next(screenshareInfo);
  }

  @Input() associatedPartnerId$: Observable<string>;

  screenShareBusinessInfo$$ = new ReplaySubject<ScreenShareBusinessInfo>(1);
  screenShareBusinessInfo$ = this.screenShareBusinessInfo$$.asObservable();
  business: ViewBusiness;
  businessId: string;
  partnerCenterHost: string;
  revenue$: Observable<number>;
  revenueLoading$: Observable<boolean>;
  revenueSuccess$: Observable<boolean>;
  customFields$: Observable<CustomField[]>;
  callNowMetaData$: Observable<CallNowData>;
  currency$: Observable<string>;
  revenueTemplateData$: Observable<RevenueData>;
  freshDeskSupportEnabled$: Observable<boolean>;

  supportFeaturesEnabled$: Observable<SupportFeaturesEnabled>;

  private readonly subscriptions = SubscriptionList.new();

  readonly customFieldFeature$: Observable<boolean>;
  readonly zendeskSupportEnabled$: Observable<boolean>;
  readonly phoneFeature$: Observable<boolean>;
  readonly canEditCustomerId$: Observable<boolean>;

  readonly isPlatformSupportedForSharingScreen: boolean;
  readonly isDeviceSupportedForSharingScreen: boolean;

  subjectParticipants: SubjectParticipant[];
  currentIAMParticipant$ = this.participantService.buildIAMUserParticipant() as Observable<Participant>;
  readonly conversationChannel = ConversationChannel.CONVERSATION_CHANNEL_INTERNAL;

  showInbox$: Observable<boolean> = this.access.hasAccessToInbox(this.inboxFeatureFlag$);

  constructor(
    private readonly subTitleService: SubTitleService,
    // tslint:disable-next-line:no-unused-variable
    private readonly featureService: FeatureFlagService,
    private readonly astbeService: AsteriskService,
    private readonly currentUser: LoggedInUserInfoService,
    @Optional() private readonly alerts: SnackbarService,
    @Inject(PartnerCenterHostService) private readonly partnerCenterHostService: HostProvider,
    @Inject(ACCESS_ASSOCIATED_PARTNER_INFO_TOKEN) public readonly accessAssociatedPartnerInfo$: Observable<boolean>,
    @Inject(Features.Inbox) public readonly inboxFeatureFlag$: Observable<boolean>,
    @Inject(SSCAccessService) private readonly access: AccessChecker,
    private readonly screenshareService: ScreenshareService,
    private readonly dialog: MatDialog,
    private readonly translate: TranslateService,
    private participantService: ParticipantService,
  ) {
    this.customFieldFeature$ = featureService.featureFlagEnabled$(Features.CustomFields);
    this.zendeskSupportEnabled$ = featureService.featureFlagEnabled$(Features.SupportTickets);
    this.phoneFeature$ = featureService.featureFlagEnabled$(Features.PhoneService);
    this.freshDeskSupportEnabled$ = featureService.featureFlagEnabled$(Features.FreshdeskIntegration);
    this.canEditCustomerId$ = featureService.featureFlagEnabled$(Features.SalespersonCanEditCustomerID);
    this.isPlatformSupportedForSharingScreen = this.screenshareService.isPlatformSupportedForSharingScreen;
    this.isDeviceSupportedForSharingScreen = this.screenshareService.isDeviceSupportedForSharingScreen;
    this.supportFeaturesEnabled$ = combineLatest([this.zendeskSupportEnabled$, this.freshDeskSupportEnabled$]).pipe(
      map(
        ([zendeskEnabled, freshdeskEnabled]) =>
          <SupportFeaturesEnabled>{ zendesk: zendeskEnabled, freshdesk: freshdeskEnabled },
      ),
    );
  }

  ngOnInit(): void {
    this.businessId = this.business.businessId;
    const partnerId = this.business.partnerId;
    const marketId = this.business.marketId;

    this.subTitleService.loadRevenue(partnerId, marketId, this.business.businessId);
    this.customFields$ = this.subTitleService.fetchCustomFields(this.business.businessId);
    const extension$ = this.currentUser.phoneExtension$;
    this.callNowMetaData$ = extension$.pipe(
      map((ext: string): CallNowData => ({ extension: ext, phoneNumber: this.business.primaryNumber })),
    );

    this.revenueSuccess$ = this.subTitleService.isSuccessRevenue$(this.business.businessId);
    this.revenue$ = this.subTitleService.revenue$(this.business.businessId);
    this.currency$ = this.subTitleService.currency$;
    this.revenueLoading$ = this.subTitleService.isLoadingRevenue$(this.business.businessId).pipe(startWith(true));
    this.revenueTemplateData$ = combineLatest([this.currency$, this.revenue$]).pipe(
      map(([currency, revenue]): RevenueData => {
        return { currency: currency, revenue: revenue };
      }),
    );

    this.partnerCenterHost = this.partnerCenterHostService.host();

    this.subjectParticipants = [
      new SubjectParticipant({
        internalParticipantId: this.business.partnerId,
        participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_PARTNER,
      }),
      new SubjectParticipant({
        internalParticipantId: this.business.businessId,
        participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_ACCOUNT_GROUP,
      }),
    ];
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }

  retryLoading(): void {
    this.revenueRetryLoading();
  }

  revenueRetryLoading(): void {
    this.subTitleService.loadRevenue(this.business.partnerId, this.business.marketId, this.business.businessId);
  }

  public callNumber(extension: string, primaryPhoneNumber: string, phoneNumber?: string): void {
    if (!primaryPhoneNumber || !extension) {
      this.alerts.openErrorSnack('CONTACTS.CALL_NOW_MISSING_NUMBER_OR_EXTENSION_MESSAGE');
      return;
    }

    const mainDialablePhoneNumber = primaryPhoneNumber.replace(/ -+/g, '');
    const number = phoneNumber || mainDialablePhoneNumber;

    this.subscriptions.add(
      this.astbeService.call(number, extension, false).pipe(
        catchError(() => {
          this.alerts.openErrorSnack('ERRORS.UNABLE_TO_CALL_ACCOUNT_CONTACT');
          return EMPTY;
        }),
      ),
    );
  }
}
