import {
  CreateOpportunityResponse,
  GetTagsResponse,
  Opportunity,
  SalesOpportunitiesApi,
  UpdateOpportunityTagsResponse,
} from '@vendasta/sales-ui';
import { Observable, of } from 'rxjs';
import { TestScheduler } from 'rxjs/testing';
import { Configuration } from '../../partner';
import { SubTitleService } from './sub-title.service';

let scheduler: TestScheduler;

const mockOpportunity = {
  accountGroupId: 'AG-F4LVKRBN35',
  actualClosedDate: new Date('2019-01-17T20:51:27.764652676Z'),
  created: new Date('2019-01-17T20:51:03.508975953Z'),
  expectedCloseDate: new Date('2019-02-18T02:51:03.318Z'),
  isClosed: true,
  lastActivityDate: new Date('2019-01-17T20:51:28.165569Z'),
  lastConnectedDate: new Date('0001-01-01T00:00:00Z'),
  name: 'My amazing product',
  opportunityId: 'OPPORTUNITY-0555dc46-a362-4a30-8eed-ea8c835efc55',
  pipelineStage: 'closed-won',
  probability: 0.9,
  projectedFirstYearValue: '10000',
  salesPersonId: 'UID-ca03fafd-fd84-4c60-887f-829d3a36339a',
  updated: new Date('2019-01-17T20:51:28.451313371Z'),
  packages: [],
};

class MockSalesOpportunityApiSDK implements SalesOpportunitiesApi {
  loadOpportunities = jest.fn(() => {
    return scheduler.createColdObservable('-x', { x: [Opportunity.createNewOpportunity(mockOpportunity)] });
  });

  create(): Observable<CreateOpportunityResponse> {
    throw new Error('Method not implemented.');
  }

  getTags(): Observable<GetTagsResponse> {
    throw new Error('Method not implemented.');
  }

  updateTags(): Observable<UpdateOpportunityTagsResponse> {
    throw new Error('Method not implemented.');
  }
}

describe('SubTitleService', () => {
  let service: SubTitleService;
  let mockSalesOpportunityApiSDK: MockSalesOpportunityApiSDK;
  let mockConfig: Observable<Configuration>;

  beforeEach(() => {
    scheduler = new TestScheduler((a, b) => expect(a).toEqual(b));
    mockSalesOpportunityApiSDK = new MockSalesOpportunityApiSDK();
    mockConfig = of({
      defaultDisplayCurrency: 'USD',
    });

    service = new SubTitleService(mockSalesOpportunityApiSDK as any, undefined, undefined, mockConfig);
  });

  afterEach(() => {
    scheduler.flush();
  });

  describe('loadRevenue', () => {
    beforeEach(() => {
      service.loadRevenue('ABC', 'default', mockOpportunity.accountGroupId);
    });

    test('should load correct revenue', () => {
      const revenue = service.revenue$(mockOpportunity.accountGroupId);

      scheduler.expectObservable(revenue).toBe('-x', { x: 10000 });
    });

    test('should return 0 if opportunity have no projectedFirstYearValue field', () => {
      mockOpportunity.projectedFirstYearValue = null;
      service = new SubTitleService(mockSalesOpportunityApiSDK as any, undefined, undefined, mockConfig);
      service.loadRevenue('ABC', 'default', mockOpportunity.accountGroupId);
      const revenue = service.revenue$(mockOpportunity.accountGroupId);

      scheduler.expectObservable(revenue).toBe('-x', { x: 0 });
    });
  });
});
