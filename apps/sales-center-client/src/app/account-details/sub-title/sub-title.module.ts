import { NgModule } from '@angular/core';
import { SubTitleComponent } from './sub-title.component';
import { SubTitleService } from './sub-title.service';
import { SubtitleAssigneesService } from './subtitle-assignees/subtitle-assignees.service';
import { CustomerIdModule } from './customer-id/customer-id.module';
import { AssigneeTemplateModule } from './subtitle-assignees/assignee-template/assignee-template.module';
import { RevenueModule } from './revenue/revenue.module';
import { CommonModule } from '@angular/common';
import { AsyncTextModule } from '../async-text/async-text.module';
import { TranslateModule } from '@ngx-translate/core';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';
import { ScreenshareModule } from '../../screenshare';
import { FormsModule } from '@angular/forms';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { InboxButtonModule } from '@galaxy/conversation/button';
import { InboxModalGuard } from '../../guards/inbox-modal.guard';
import { canAccessInboxModal, Features } from '../../features';
import { FeatureFlagService } from '../../core';

@NgModule({
  imports: [
    AssigneeTemplateModule,
    RevenueModule,
    CustomerIdModule,
    CommonModule,
    AsyncTextModule,
    TranslateModule,
    FormsModule,
    MatTooltipModule,
    MatIconModule,
    RouterModule,
    ScreenshareModule,
    MatProgressSpinnerModule,
    InboxButtonModule,
  ],
  declarations: [SubTitleComponent],
  exports: [SubTitleComponent],
  providers: [
    SubTitleService,
    SubtitleAssigneesService,
    InboxModalGuard,
    {
      provide: Features.Inbox,
      useFactory: canAccessInboxModal,
      deps: [FeatureFlagService, 'PARTNER_ID'],
    },
  ],
})
export class SubTitleModule {}
