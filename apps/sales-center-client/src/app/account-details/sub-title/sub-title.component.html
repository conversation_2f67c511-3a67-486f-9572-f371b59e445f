<div class="sub-title">
  <span class="sub-title__item">
    <div>
      <app-subtitle-assignees [business]="business"></app-subtitle-assignees>
    </div>
    <div>
      <app-customer-id
        [canEditFeature]="canEditCustomerId$ | async"
        [customerIdentifiers]="customerIdentifiers | async"
      ></app-customer-id>
    </div>
  </span>
  <span class="revenue-text">
    <ng-container *ngIf="revenueLoading$ | async; then loading; else loaded"></ng-container>
    <ng-template #loaded>
      <ng-container *ngIf="revenueSuccess$ | async; else error">
        <app-revenue-title
          *ngIf="revenueTemplateData$ | async as revenueTemplateData"
          [revenue]="revenueTemplateData.revenue"
          [currency]="revenueTemplateData.currency"
          (retry)="revenueRetryLoading()"
        ></app-revenue-title>
      </ng-container>
      <ng-template #error>
        <app-async-text
          [loadFailed]="true"
          [loading]="false"
          (retry)="retryLoading()"
          failMessage="{{ 'ERRORS.UNABLE_TO_LOAD_TOTAL_REVENUE' | translate }}"
        ></app-async-text>
      </ng-template>
    </ng-template>
    <ng-template #loading>
      <mat-spinner [diameter]="16" [strokeWidth]="3"></mat-spinner>
    </ng-template>
  </span>
</div>
<div class="sub-title">
  <span class="sub-title__item">
    <span class="sub-title__quick-links">
      <ng-container class="custom-fields" *ngIf="customFieldFeature$ | async">
        <ng-container *ngFor="let field of customFields$ | async">
          <ng-container *ngIf="field.name === 'pardot_prospect_url'; else otherField">
            <a [attr.href]="field.value" target="_blank">
              {{ 'ACCOUNT_DETAILS.PARDOT' | translate }}
            </a>
            &nbsp;|&nbsp;
          </ng-container>
          <ng-template #otherField></ng-template>
        </ng-container>
      </ng-container>
      <ng-container *ngIf="business">
        <ng-container *ngIf="supportFeaturesEnabled$ | async as featureEnabled">
          <ng-container *ngIf="featureEnabled.zendesk">
            <a routerLink="/support/{{ business.businessId }}">
              {{ 'ACCOUNT_DETAILS.QUICK_LINKS.SUPPORT_LABEL' | translate }}
            </a>
          </ng-container>
          <ng-container *ngIf="featureEnabled.freshdesk && featureEnabled.zendesk">&nbsp;|&nbsp;</ng-container>
          <ng-container *ngIf="featureEnabled.freshdesk">
            <a routerLink="/ticketing/freshdesk/{{ business.businessId }}">
              {{ 'TICKETING.FRESHDESK.SUPPORT' | translate }}
            </a>
          </ng-container>
          <ng-container *ngIf="featureEnabled.freshdesk || featureEnabled.zendesk">&nbsp;|&nbsp;</ng-container>
        </ng-container>
        <ng-container *ngIf="(phoneFeature$ | async) && callNowMetaData$ | async as data">
          <span class="link" (click)="callNumber(data.extension, data.phoneNumber)">
            {{ 'CONTACTS.CALL_NOW' | translate }}
          </span>
        </ng-container>
      </ng-container>
      <ng-container *ngIf="(accessAssociatedPartnerInfo$ | async) && (associatedPartnerId$ | async) === ''">
        &nbsp;|&nbsp;
        <a
          target="_blank"
          href="{{ partnerCenterHost }}/superadmin/partner/create?fromAccountGroup={{ business.businessId }}"
        >
          {{ 'ACCOUNT_DETAILS.QUICK_LINKS.CREATE_PARTNER' | translate }}
        </a>
      </ng-container>
      <div class="center-inline-flex" interactable *ngIf="showInbox$ | async">
        &nbsp;|&nbsp;
        <inbox-button-send-message
          [config]="{
            currentParticipant: currentIAMParticipant$ | async,
            subjectParticipants: subjectParticipants,
            channel: conversationChannel
          }"
          type="link"
          [primaryIcon]="true"
        ></inbox-button-send-message>
      </div>
    </span>
  </span>
</div>
