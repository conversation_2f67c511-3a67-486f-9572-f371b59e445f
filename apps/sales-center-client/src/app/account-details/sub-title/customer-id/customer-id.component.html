<div class="container">
  <ng-container *ngIf="canEditFeature; then canEdit; else cannotEdit"></ng-container>
</div>

<ng-template #cannotEdit>
  <span *ngIf="customerIdentifiers?.customerId" class="cannot-edit">
    {{ 'ACCOUNT_DETAILS.QUICK_LINKS.CUSTOMER_ID' | translate }}:
    {{ customerIdentifiers?.customerId }}
  </span>
</ng-template>

<ng-template #canEdit>
  <form (ngSubmit)="submit()">
    {{ 'ACCOUNT_DETAILS.QUICK_LINKS.CUSTOMER_ID' | translate }}:
    <ng-container *ngIf="formControl.disabled">
      <span>{{ formControl.value }}</span>
      <button
        type="button"
        mat-icon-button
        *ngIf="(loading$$ | async) === false"
        class="toggle-button"
        (onfocus)="submit()"
        (click)="toggleForm()"
      >
        <mat-icon>{{ icon }}</mat-icon>
      </button>
    </ng-container>
    <mat-form-field *ngIf="formControl.enabled">
      <mat-label>{{ 'ACCOUNT_DETAILS.QUICK_LINKS.CUSTOMER_ID' | translate }}</mat-label>
      <input
        matInput
        [formControl]="formControl"
        type="text"
        placeholder="{{
          customerIdentifiers?.customerId
            ? ('ACCOUNT_DETAILS.CUSTOMER_ID.EDIT_CUSTOMER_ID' | translate)
            : ('ACCOUNT_DETAILS.CUSTOMER_ID.ADD_CUSTOMER_ID' | translate)
        }}"
        value="{{ customerIdentifiers?.customerId }}"
        (keydown)="onKeyDown($event)"
      />
      <button
        type="submit"
        mat-icon-button
        *ngIf="(loading$$ | async) === false"
        class="edit-customerid-button"
        matSuffix
      >
        <mat-icon>done</mat-icon>
      </button>
      <mat-progress-spinner
        *ngIf="loading$$ | async"
        matSuffix
        mode="indeterminate"
        diameter="16"
      ></mat-progress-spinner>
    </mat-form-field>
    <button *ngIf="formControl.enabled" mat-flat-button type="button" (click)="toggleForm()">
      {{ 'COMMON.ACTION_LABELS.CANCEL' | translate }}
    </button>
  </form>
  <glxy-alert type="info" *ngIf="formControl.enabled">
    <strong>{{ 'COMMON.NOTE' | translate }}</strong>
    &mdash; {{ 'ACCOUNT_DETAILS.CUSTOMER_ID.CUSTOMER_ID_ALERT' | translate }}
  </glxy-alert>
</ng-template>
