import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CustomerIdComponent } from './customer-id.component';
import { CustomerIdService } from './customer-id.service';
import { TranslateModule } from '@ngx-translate/core';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    FormsModule,
    MatInputModule,
    MatIconModule,
    MatProgressSpinnerModule,
    ReactiveFormsModule,
    GalaxyAlertModule,
    MatButtonModule,
  ],
  exports: [CustomerIdComponent],
  declarations: [CustomerIdComponent],
  providers: [CustomerIdService],
})
export class CustomerIdModule {}
