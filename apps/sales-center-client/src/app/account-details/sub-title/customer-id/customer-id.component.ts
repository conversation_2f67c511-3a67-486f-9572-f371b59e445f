import { Component, Input } from '@angular/core';
import { FormControl } from '@angular/forms';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { BehaviorSubject, ReplaySubject, firstValueFrom } from 'rxjs';
import { CustomerIdService, CustomerIdentifiers } from './customer-id.service';

@Component({
  selector: 'app-customer-id',
  templateUrl: './customer-id.component.html',
  styleUrls: ['./customer-id.component.scss', '../sub-title.component.scss'],
  standalone: false,
})
export class CustomerIdComponent {
  private readonly editIcon: string = 'edit';
  private currentIdentifiers: CustomerIdentifiers;

  private readonly failedMessage$$ = new ReplaySubject<string>(1);

  formControl = new FormControl<string>('');
  icon: string = this.editIcon;
  loading$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  @Input() readonly canEditFeature: boolean;
  @Input() set customerIdentifiers(ids: CustomerIdentifiers) {
    this.currentIdentifiers = ids;
    this.formControl.setValue(ids?.customerId);
  }

  constructor(
    private readonly alerts: SnackbarService,
    private readonly customerIdService: CustomerIdService,
  ) {
    this.formControl.disable();
  }

  async submit(): Promise<void> {
    if (this.formControl.value === this.currentIdentifiers?.customerId) {
      this.alerts.openSnackBar(this.customerIdService.noChangeSnackMessage);
      this.toggleForm();
      return;
    }

    this.loading$$.next(true);
    this.formControl.disable();

    await firstValueFrom(this.customerIdService.setCustomerId(this.currentIdentifiers, this.formControl.value))
      .then(() => {
        this.alerts.openSuccessSnack(this.customerIdService.successSnackMessage);
        this.currentIdentifiers.customerId = this.formControl.value;
      })
      .catch(() => {
        this.alerts.openErrorSnack(this.customerIdService.errorSnackMessage);
      });

    this.toggleForm();
    this.loading$$.next(false);
  }

  onKeyDown($event: KeyboardEvent): void {
    if ($event.key === 'Escape') {
      this.toggleForm();
    }
  }

  toggleForm(): void {
    if (this.icon === this.editIcon) {
      this.icon = 'done';
      this.formControl.enable();
    } else {
      this.formControl.setValue(this.currentIdentifiers.customerId);
      this.icon = this.editIcon;
      this.formControl.disable();
    }
  }
}
