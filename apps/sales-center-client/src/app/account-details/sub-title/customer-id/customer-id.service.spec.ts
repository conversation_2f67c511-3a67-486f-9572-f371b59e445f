import { HttpResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import { AccountGroupExternalIdentifiersUpdateOperation, UpdateOperations } from '@galaxy/account-group';
import { Observable } from 'rxjs';

import { CustomerIdentifiers, CustomerIdService } from './customer-id.service';

describe('CustomerIdService', () => {
  let service: CustomerIdService;
  const accountGroupSDKMock = {
    bulkUpdate: jest.fn(),
  } as any;
  const translateMock = {
    stream: jest.fn(),
  } as any;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    translateMock.stream = jest.fn(() => new Observable<string>());
    service = new CustomerIdService(accountGroupSDKMock, translateMock);
  });

  it('calls accountgroup with correct values to update customerId', () => {
    const operations = new UpdateOperations();

    const expectedPartner = 'exPartId';
    const expectedCustomerId = 'exCId';
    const expectedBusinessId = 'exBId';

    operations.addUpdateOperation(
      new AccountGroupExternalIdentifiersUpdateOperation({
        partnerId: expectedPartner,
        customerIdentifier: expectedCustomerId,
      }),
    );

    const currentIdentifiers: CustomerIdentifiers = {
      businessId: expectedBusinessId,
      partnerId: expectedPartner,
      customerId: expectedCustomerId,
    };

    const expectedReturn = new Observable<HttpResponse<null>>();

    accountGroupSDKMock.bulkUpdate = jest.fn(() => expectedReturn);
    expect(service.setCustomerId(currentIdentifiers, expectedCustomerId)).toEqual(expectedReturn);
    expect(accountGroupSDKMock.bulkUpdate).toHaveBeenCalledWith(expectedBusinessId, operations);
  });
});
