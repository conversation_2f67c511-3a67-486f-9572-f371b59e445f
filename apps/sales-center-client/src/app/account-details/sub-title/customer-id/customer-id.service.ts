import { HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import {
  AccountGroupExternalIdentifiersUpdateOperation,
  AccountGroupService,
  UpdateOperations,
} from '@galaxy/account-group';
import { Observable } from 'rxjs';

export interface CustomerIdentifiers {
  businessId: string;
  partnerId: string;
  customerId: string;
  taxIds?: string[];
  vCategoryIds?: string[];
}

@Injectable({
  providedIn: 'root',
})
export class CustomerIdService {
  private readonly errorMessage$: Observable<string> = this.translate.stream(
    'ACCOUNT_DETAILS.SNACKBAR_MESSAGES.CUSTOMER_ID_FAILED',
  );
  private readonly successMessage$: Observable<string> = this.translate.stream(
    'ACCOUNT_DETAILS.SNACKBAR_MESSAGES.CUSTOMER_ID_CHANGED',
  );
  private readonly noChangeMessage$: Observable<string> = this.translate.stream(
    'ACCOUNT_DETAILS.SNACKBAR_MESSAGES.CUSTOMER_ID_NOCHANGE',
  );

  private errorMessage: string;
  private successMessage: string;
  private noChangeMessage: string;

  constructor(private readonly accountGroupService: AccountGroupService, private readonly translate: TranslateService) {
    this.errorMessage = this.getMessage(this.errorMessage$);
    this.successMessage = this.getMessage(this.successMessage$);
    this.noChangeMessage = this.getMessage(this.noChangeMessage$);
  }

  public get errorSnackMessage(): string {
    return this.errorMessage;
  }

  public get successSnackMessage(): string {
    return this.successMessage;
  }

  public get noChangeSnackMessage(): string {
    return this.noChangeMessage;
  }

  setCustomerId(customerIdentifiers: CustomerIdentifiers, newCustomerId: string): Observable<HttpResponse<null>> {
    const operations = new UpdateOperations();

    operations.addUpdateOperation(
      new AccountGroupExternalIdentifiersUpdateOperation({
        partnerId: customerIdentifiers.partnerId,
        customerIdentifier: newCustomerId,
      }),
    );

    return this.accountGroupService.bulkUpdate(customerIdentifiers.businessId, operations);
  }

  private getMessage(message$: Observable<string>): string {
    let m: string;
    message$.subscribe((s) => (m = s));
    return m;
  }
}
