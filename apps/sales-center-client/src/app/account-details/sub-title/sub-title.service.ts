import { Inject, Injectable } from '@angular/core';
import { CustomField } from '@galaxy/account-group';
import { ManualStartWorkStateMap, ObservableWorkStateMap } from '@vendasta/rx-utils/work-state';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { AjaxBusinessApiService, BusinessApiService } from '../../business';
import { Configuration, PARTNER_CONFIG_TOKEN } from '../../partner';
import { SalesOpportunitiesService } from '../../sales-opportunities/sales-opportunities.service';
import { SalespeopleService } from '../../salespeople/salespeople.service';

@Injectable()
export class SubTitleService {
  private readonly opportunityState = new ManualStartWorkStateMap<string, number>();
  private readonly customFieldsState = new ObservableWorkStateMap<string, CustomField[]>();
  readonly currency$: Observable<string> = this.configuration$.pipe(map((c) => c.defaultDisplayCurrency));

  constructor(
    private readonly salesOpportunitiesService: SalesOpportunitiesService,
    private readonly salespeopleService: SalespeopleService,
    @Inject(AjaxBusinessApiService) private readonly businessApi: BusinessApiService,
    @Inject(PARTNER_CONFIG_TOKEN) public readonly configuration$: Observable<Configuration>,
  ) {}

  loadRevenue(partnerId: string, marketId: string, businessId: string): void {
    this.opportunityState.startWork(businessId, (success, fail) => {
      return this.salesOpportunitiesService
        .loadOpportunities(partnerId, marketId, businessId, SalesOpportunitiesService.sortByCreatedDescending(), [
          'closed-won',
          'open',
        ])
        .pipe(
          map((opps) => {
            let totalRevenue = 0;
            opps.forEach((opp) => {
              if (opp.projectedFirstYearValue) {
                totalRevenue = totalRevenue + Number(opp.projectedFirstYearValue);
              }
            });
            return totalRevenue;
          }),
        )
        .subscribe(success, fail);
    });
  }

  revenue$(businessId: string): Observable<number> {
    return this.opportunityState.getWorkResults$(businessId);
  }

  isLoadingRevenue$(businessId: string): Observable<boolean> {
    return this.opportunityState.isLoading$(businessId);
  }

  isSuccessRevenue$(businessId: string): Observable<boolean> {
    return this.opportunityState.isSuccess$(businessId);
  }

  // TODO: move custom fields to central place when continuing to work on custom fields card
  public fetchCustomFields(businessId: string): Observable<CustomField[]> {
    this.customFieldsState.startWork(businessId, this.businessApi.getCustomFields(businessId));
    return this.customFields$(businessId);
  }

  customFields$(businessId: string): Observable<CustomField[]> {
    return this.customFieldsState.getWorkResults$(businessId);
  }

  public isLoadingCustomFields$(businessId: string): Observable<boolean> {
    return this.customFieldsState.isLoading$(businessId);
  }

  public customFieldsFailed$(businessId: string): Observable<boolean> {
    return this.customFieldsState.isSuccess$(businessId).pipe(map((v) => !v));
  }
}
