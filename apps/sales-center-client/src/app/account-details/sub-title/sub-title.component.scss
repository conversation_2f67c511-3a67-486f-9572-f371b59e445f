@use 'design-tokens' as *;

.sub-title {
  font-size: 16px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.sub-title__item {
  flex-direction: row;
  color: $dark-gray;
}

.sub-title__value {
  color: $darker-gray;
  font-weight: bold;
}

.revenue-text {
  color: $green;
  vertical-align: middle;
  margin-bottom: 16px;
}

.more-align {
  vertical-align: middle;
}

.sub-title__quick-links {
  display: inline-flex;
  align-items: center;

  .center-inline-flex {
    display: inline-flex;
    align-items: center;
  }

  .disabled {
    color: $gray;
  }
}
