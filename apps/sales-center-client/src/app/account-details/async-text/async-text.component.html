<ng-container *ngIf="loading; else content">
  <div class="spinner-container">
    <mat-spinner [diameter]="16"></mat-spinner>
  </div>
</ng-container>
<ng-template #content>
  <ng-container *ngIf="loadFailed; else text">
    <button mat-button (click)="handleRetry()">
      <div class="button-contents">
        <mat-icon>error_outline</mat-icon>
        <span class="message">{{ failMessage }} -</span>
        <span class="retry-text">
          {{ 'COMMON.ACTION_LABELS.CLICK_TO_REFRESH' | translate }}
        </span>
      </div>
    </button>
  </ng-container>
</ng-template>
<ng-template #text>
  <div>
    <ng-content></ng-content>
  </div>
</ng-template>
