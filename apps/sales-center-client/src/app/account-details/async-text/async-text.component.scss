@use 'design-tokens' as *;

:host {
  span.message {
    flex-shrink: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    &::after {
      content: ' '; // space after the dash
      white-space: pre;
    }
  }
  span.retry-text {
    flex-shrink: 0;
    color: $blue;
  }
  button {
    width: 100%;
  }
  .button-contents {
    width: 100%;
    display: flex;
    flex-flow: row nowrap;
    justify-content: flex-start;
    align-items: center;

    mat-icon,
    mat-spinner {
      font-size: 16px;
      height: 16px;
      width: 16px;
      margin-right: 8px;
    }
  }
  .spinner-container {
    padding-left: 16px;
    height: 36px;
    display: flex;
    justify-content: space-around;
    align-content: space-around;
    width: max-content;
    mat-spinner {
      margin: auto;
    }
  }
}
