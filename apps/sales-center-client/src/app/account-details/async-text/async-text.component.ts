import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-async-text',
  templateUrl: './async-text.component.html',
  styleUrls: ['./async-text.component.scss'],
  standalone: false,
})
export class AsyncTextComponent {
  @Input() loading = false;
  @Input() loadFailed = false;
  @Input() failTooltipText: string;
  @Input() failMessage = 'Unable to load data';
  @Output() retry = new EventEmitter<null>();

  handleRetry(): void {
    this.retry.emit(null);
  }
}
