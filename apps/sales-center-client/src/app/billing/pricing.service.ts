import { Injectable } from '@angular/core';
import { BillingService, ProductPricing } from '@galaxy/billing';
import { Observable } from 'rxjs';
import { map, shareReplay } from 'rxjs/operators';

@Injectable()
export class PricingService {
  constructor(private readonly billingService: BillingService) {}

  getProductPricing(merchantId: string, sku: string): Observable<ProductPricing> {
    return this.billingService.getMultiProductPricing(merchantId, [sku]).pipe(
      map((pricings) => {
        return pricings[sku];
      }),
      shareReplay(1),
    );
  }
}
