import { BillingStrategy, Currency, Frequency, ProductPricingType } from '@galaxy/billing';
import { BillingModel, MarketplaceBillingFrequency } from '@vendasta/shared';
import { mergePricing, pricingRulesToTiers } from './pricing';

describe('pricingRulesToTiers', () => {
  it('should convert list of pricing rules to a list of pricing tiers', () => {
    const pricingRules = [
      { price: 40, minUnits: 1, maxUnits: 10, isStartingPrice: false },
      { price: 200, minUnits: 0, maxUnits: 5, isStartingPrice: false },
    ];
    const expectedPricingTiers = [
      { price: 40, rangeMin: 1, rangeMax: 10, isStartingPrice: false },
      { price: 200, rangeMin: 0, rangeMax: 5, isStartingPrice: false },
    ];

    const result = pricingRulesToTiers(pricingRules, false);
    expect(result).toEqual(expectedPricingTiers);
  });
  it('should return an empty list if no pricing rules are passed in', () => {
    const pricingRules = [];
    const expectedPricingTiers = [];

    const result = pricingRulesToTiers(pricingRules, false);
    expect(result).toEqual(expectedPricingTiers);
  });
});

describe('mergePricing', () => {
  it('should overwrite billedProducts pricing values with converted values from productPricing', () => {
    const billedProductMock = {
      productName: undefined,
      productId: 'RM',
      billingModel: BillingModel.Standard,
      price: 100,
      notes: undefined,
      iconURL: undefined,
      currency: Currency.CAD,
      billingFrequency: MarketplaceBillingFrequency.Monthly,
      pricingTiers: [{ rangeMin: 1, rangeMax: 10, price: 100 }],
      productCategory: undefined,
    };
    const productPricingMock = {
      strategy: BillingStrategy.Instantly,
      pricingType: ProductPricingType.Stairstep,
      currency: Currency.USD,
      frequency: Frequency.OneTime,
      pricingRules: [{ price: 300, minUnits: 2, maxUnits: 5 }],
    };

    const result = mergePricing(billedProductMock as any, productPricingMock as any);
    expect(result.billingModel).toEqual(BillingModel.Stairstep);
    expect(result.currency).toEqual(Currency.USD);
    expect(result.billingFrequency).toEqual(MarketplaceBillingFrequency.OneTime);
    expect(result.pricingTiers).toEqual([{ price: 300, rangeMin: 2, rangeMax: 5 }]);
  });
});
