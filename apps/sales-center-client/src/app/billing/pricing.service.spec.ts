import { TestScheduler } from 'rxjs/testing';
import { BillingStrategy, ProductPricingType, Currency, Frequency } from '@galaxy/billing';
import { PricingService } from './pricing.service';

let scheduler: TestScheduler;

const happyProductPricingMock = {
  strategy: BillingStrategy.Instantly,
  pricingType: ProductPricingType.Stairstep,
  currency: Currency.CAD,
  frequency: Frequency.OneTime,
  pricingRules: [
    {
      price: 300,
      minUnits: 2,
      maxUnits: 5,
    },
  ],
};

const happyGetMultiPricingMock = {
  ['RM']: happyProductPricingMock,
};

describe('PricingService', () => {
  let billingServiceSpy: any;

  beforeEach(() => {
    scheduler = new TestScheduler((a, b) => expect(a).toEqual(b));
  });

  describe('getProductPricing', () => {
    beforeEach(() => {
      billingServiceSpy = {
        getMultiProductPricing: jest.fn(),
      };
    });

    it('should call through to getMultiProductPricing', () => {
      billingServiceSpy.getMultiProductPricing = jest.fn(() =>
        scheduler.createColdObservable('a', { a: happyGetMultiPricingMock }),
      );
      const pricingService = new PricingService(billingServiceSpy);

      scheduler.schedule(() => pricingService.getProductPricing('ABC', 'RM'));
      scheduler.flush();
      expect(billingServiceSpy.getMultiProductPricing).toHaveBeenCalledTimes(1);
    });

    it('should only return a single product pricing specific to the sku', () => {
      const productPricingMock = {
        ['RM']: happyProductPricingMock,
        ['SM']: null,
        ['LSP']: null,
      };
      billingServiceSpy.getMultiProductPricing = jest.fn(() =>
        scheduler.createColdObservable('a', { a: productPricingMock }),
      );
      const pricingService = new PricingService(billingServiceSpy);

      const result = pricingService.getProductPricing('ABC', 'RM');
      scheduler.expectObservable(result).toBe('a', {
        a: happyProductPricingMock,
      });
      scheduler.flush();
    });
  });
});
