import { Frequency, ProductPricing, ProductPricingRules, ProductPricingType } from '@galaxy/billing';
import { BilledProduct, BillingModel, MarketplaceBillingFrequency, PricingTier } from '@vendasta/shared';

function mapPricingTypeToBillingModel(pricingType: ProductPricingType): BillingModel {
  switch (pricingType) {
    case ProductPricingType.Standard:
      return BillingModel.Standard;
    case ProductPricingType.Stairstep:
      return BillingModel.Stairstep;
    case ProductPricingType.Tiered:
      return BillingModel.Tiered;
  }
}

function mapFrequency(frequency: Frequency): MarketplaceBillingFrequency {
  switch (frequency) {
    case Frequency.OneTime:
      return MarketplaceBillingFrequency.OneTime;
    case Frequency.Monthly:
      return MarketplaceBillingFrequency.Monthly;
    case Frequency.Yearly:
      return MarketplaceBillingFrequency.Yearly;
  }
}

export function pricingRulesToTiers(rules: ProductPricingRules[], isStartingPrice: boolean): PricingTier[] {
  return rules.map((r) => {
    return {
      rangeMin: r.minUnits,
      rangeMax: r.maxUnits,
      price: r.price,
      isStartingPrice: isStartingPrice,
    };
  });
}

export function mergePricing(billedProduct: BilledProduct, pricing: ProductPricing): BilledProduct {
  billedProduct.billingModel = mapPricingTypeToBillingModel(pricing.pricingType);
  billedProduct.currency = pricing.currency;
  billedProduct.billingFrequency = mapFrequency(pricing.frequency);
  billedProduct.pricingTiers = pricingRulesToTiers(pricing.pricingRules, pricing.isStartingPrice);
  billedProduct.commitment = pricing.commitment;
  return billedProduct;
}

export function pricingOnlyBilledProduct(sku: string, pricing: ProductPricing): BilledProduct {
  const undefinedBilledProduct: BilledProduct = {
    productId: sku,
    productName: undefined,
    billingModel: undefined,
    price: undefined,
    notes: undefined,
    iconURL: undefined,
    currency: undefined,
    billingFrequency: undefined,
    pricingTiers: undefined,
    productCategory: undefined,
    setupFee: undefined,
  };
  return mergePricing(undefinedBilledProduct, pricing);
}
