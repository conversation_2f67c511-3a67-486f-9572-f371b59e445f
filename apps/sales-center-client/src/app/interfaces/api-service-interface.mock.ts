import { Observable, throwError } from 'rxjs';
import { TestScheduler } from 'rxjs/testing';
import { ApiServiceInterface } from './api-service-interface';

export class ApiServiceMock implements ApiServiceInterface {
  constructor(private readonly scheduler: TestScheduler) {}

  get(url: string): Observable<any> {
    if (url !== null) {
      return this.scheduler.createColdObservable('--x', {
        x: {
          salespersonId: 'numberOneNoodle',
          isImpersonation: false,
          partnerId: 'ABC',
          marketId: 'scrummi',
          isSalesManager: true,
        },
      });
    }
    return throwError('ApiServiceMock error');
  }

  post(): Observable<any> {
    return undefined;
  }
}
