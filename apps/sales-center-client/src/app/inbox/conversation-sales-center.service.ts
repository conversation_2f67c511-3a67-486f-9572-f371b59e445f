import { Inject, Injectable } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { Router } from '@angular/router';
// eslint-disable-next-line @nx/enforce-module-boundaries
import {
  addNextUrl,
  AppOptionsKeys,
  ConversationDetail,
  ConversationMessage,
  ConversationTitleInfo,
  getSubjectParticipantByType,
  HostAppInterface,
  InboxNamespace,
  KabobDynamicButton,
  ParticipantService,
  SendMessage,
  SubjectParticipant,
} from '@galaxy/conversation/core';
import { FeatureFlagService, PartnerAccountGroupService } from '@galaxy/partner';
import { PaymentLinkItem } from '@vendasta/billing';
import {
  ConversationChannel,
  GlobalParticipantType,
  Participant,
  ParticipantType,
  PlatformLocation,
} from '@vendasta/conversation';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { newPartnerServiceContext, SSOService } from '@vendasta/sso';
import { Owner, UrlShortenerService } from '@vendasta/url-shortener';
import { catchError, firstValueFrom, forkJoin, map, Observable, of, switchMap, take } from 'rxjs';
import { PARTNER_ID_TOKEN } from '../common/providers/tokens';
import { AssociatedUserInterface } from '@vendasta/business-center/lib/_internal/interfaces/api.interface';

@Injectable()
export class ConversationSalesCenterService implements HostAppInterface {
  private partnerId = toSignal(this.partnerId$);

  constructor(
    private router: Router,
    private featureFlagService: FeatureFlagService,
    private snackbarService: SnackbarService,
    private participantService: ParticipantService,
    private ssoService: SSOService,
    private urlShortenerService: UrlShortenerService,
    private partnerAccountGroupService: PartnerAccountGroupService,
    @Inject(PARTNER_ID_TOKEN) private partnerId$: Observable<string>,
  ) {}

  getConversationTitleInfo(conversationDetail: ConversationDetail): ConversationTitleInfo {
    const recipients = this.getRecipient(conversationDetail);
    const conversationTitleInfo = {} as ConversationTitleInfo;
    const title: string[] = [];
    const secondarySubtitle: string[] = [];
    let subtitle = '';

    recipients.map((recipient) => {
      if (
        recipient.participantType == ParticipantType.PARTICIPANT_TYPE_CUSTOMER &&
        recipient.isParticipantInternalInfoDeleted
      ) {
        title.push('INBOX.ERROR.DELETED_CUSTOMER');
      } else {
        title.push(recipient.name || recipient.internalParticipantId);
        secondarySubtitle.push(recipient.internalParticipantId);
      }
    });
    conversationTitleInfo.title = title.filter((item) => item).join(', ');
    conversationTitleInfo.secondarySubtitle = secondarySubtitle.filter((item) => item).join(', ');

    // only returns a subtitle when there's only an accountGroup as recipient
    if (recipients.length === 1) {
      const address = recipients.find(
        (recipient) => recipient.participantType === ParticipantType.PARTICIPANT_TYPE_ACCOUNT_GROUP,
      )?.address;
      if (address) {
        subtitle = [address.firstLineAddress, address.secondLineAddress, address.city, address.state, address.country]
          .filter((item) => item)
          .join(', ');
      }
    }
    conversationTitleInfo.subtitle = subtitle;
    // Don't show the subtitle for openAI channel conversations
    if (conversationDetail.conversation.channel === ConversationChannel.CONVERSATION_CHANNEL_OPENAI) {
      conversationTitleInfo.subtitle = '';
      conversationTitleInfo.secondarySubtitle = '';
    }
    return conversationTitleInfo;
  }

  getRecipient(conversationDetail: ConversationDetail): Participant[] | null {
    const subjectParticipants = conversationDetail?.participants?.filter(
      (participant) => participant.isSubjectParticipant,
    );

    let recipientResult = subjectParticipants.filter(
      (recipient) => recipient.internalParticipantId !== this.partnerId(),
    );

    if (!recipientResult?.length) {
      recipientResult = subjectParticipants.map((subjectParticipant) => {
        let result = subjectParticipant;
        if (subjectParticipant.participantType !== ParticipantType.PARTICIPANT_TYPE_PARTNER) {
          result = new Participant({
            internalParticipantId: result.internalParticipantId,
            participantType: ParticipantType.PARTICIPANT_TYPE_PARTNER,
            isSubjectParticipant: true,
          });
        }
        return result;
      });
    }

    return recipientResult;
  }

  isSenderFromOrganization(message: ConversationMessage): boolean {
    if (
      message.sender?.participantType === ParticipantType.PARTICIPANT_TYPE_IAM_USER ||
      message.sender?.participantType === ParticipantType.PARTICIPANT_TYPE_PARTNER
    ) {
      return message.sender?.partnerId === this.partnerId() && !message.sender?.accountGroupId;
    }
    return (
      message.sender?.participantType === ParticipantType.PARTICIPANT_TYPE_DIGITAL_AGENT ||
      (message.sender?.participantType === ParticipantType.PARTICIPANT_TYPE_OPENAI_BOT &&
        message.channel === ConversationChannel.CONVERSATION_CHANNEL_WEB_CHAT)
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  isYourExpert(_conversationDetail: ConversationDetail): boolean {
    return false;
  }

  // this method only returns isInfoDeleted when the there's only an accountGroup as recipient
  isRecipientInternalInfoDeleted(conversationDetail: ConversationDetail): boolean {
    const recipient = this.getRecipient(conversationDetail) || [];
    if (recipient.length == 1) {
      return recipient.find(
        (subjectParticipant) => subjectParticipant?.participantType === ParticipantType.PARTICIPANT_TYPE_ACCOUNT_GROUP,
      )?.isParticipantInternalInfoDeleted;
    }
    return false;
  }

  private buildSubjectParticipant(partnerId: string, subjectParticipant: SubjectParticipant): Participant {
    switch (subjectParticipant?.participantType) {
      case GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_ACCOUNT_GROUP:
        return this.participantService.buildAccountGroup(partnerId, subjectParticipant?.internalParticipantId);
      case GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_PARTNER:
        return this.participantService.buildPartner(subjectParticipant?.internalParticipantId);
      case GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_VENDOR:
        return this.participantService.buildVendor(subjectParticipant?.internalParticipantId);
      default:
        return {} as Participant;
    }
  }

  buildParticipant(partnerId: string, recipient: SubjectParticipant): Observable<Participant> {
    if (
      partnerId === 'VMF' &&
      recipient?.participantType === GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_ACCOUNT_GROUP
    ) {
      return this.partnerAccountGroupService.getPartnerAccountGroupMapping('', recipient?.internalParticipantId).pipe(
        catchError((err) => {
          console.error(err);
          if (err.status === 404) {
            this.snackbarService.openErrorSnack('Account is not associated with a Partner');
            throw new Error('Account is not associated with a Partner');
          }
          this.snackbarService.openErrorSnack('An error occurred, please try again');
          throw err;
        }),
        switchMap((partnerFromAccountGroup) => {
          return this.featureFlagService.batchGetStatus(partnerFromAccountGroup.partnerId, '', ['inbox_tab_pcc']).pipe(
            map((featureFlagsStatus) => ({
              featureFlags: featureFlagsStatus,
              partnerFromAccountGroup: partnerFromAccountGroup,
            })),
          );
        }),
        map((result) => {
          if (result.featureFlags['inbox_tab_pcc']) {
            return this.participantService.buildPartner(result.partnerFromAccountGroup.partnerId);
          }
          this.snackbarService.openErrorSnack('Partner does not have the "inbox_tab_pcc" feature flag enabled');
          throw new Error('Partner does not have the "inbox_tab_pcc" feature flag enabled');
        }),
      );
    }
    return of(this.buildSubjectParticipant(partnerId, recipient));
  }

  buildParticipants(currentIAMParticipant: Participant, participants: SubjectParticipant[]): Observable<Participant[]> {
    const participantList$: Observable<Participant>[] = [];

    participants.forEach((participant) => {
      participantList$.push(this.buildParticipant(currentIAMParticipant?.partnerId, participant));
    });
    return forkJoin(participantList$).pipe(
      map((participants) => {
        participants.push(currentIAMParticipant);
        return participants;
      }),
    );
  }

  buildSendMessageParams(
    currentIAMParticipant: Participant,
    subjectParticipants: SubjectParticipant[],
    channel: ConversationChannel,
  ): Observable<SendMessage> {
    return this.buildParticipants(currentIAMParticipant, subjectParticipants).pipe(
      map((participants) => ({
        participants: participants,
        location: PlatformLocation.PLATFORM_LOCATION_PARTNER_CENTER,
        channel: channel,
      })),
    );
  }

  redirectToInternalConversation(
    currentIAMParticipant: Participant,
    subjectParticipants: SubjectParticipant[],
    channel: ConversationChannel,
  ): void {
    this.buildSendMessageParams(currentIAMParticipant, subjectParticipants, channel)
      .pipe(take(1))
      .subscribe((params) => this.router.navigate(['/inbox/send-message/'], { state: params }));
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  createPaymentLink(_conversationDetail: ConversationDetail, _paymentLinkItems: PaymentLinkItem[]): Observable<string> {
    throw new Error('not implemented');
  }

  shortenLink(link: string, accountGroupId = ''): Observable<string> {
    link = !link.match(/^http/) ? 'https://' + link : link;
    // TODO: Add new owner type in urlshortener service
    return this.urlShortenerService.createLink(accountGroupId, link, Owner.OWNER_UNTRACKED, '', false).pipe(
      catchError(() => of(null)),
      map((result) => {
        if (!result) {
          return link;
        }
        return `https://${result.domain}${result.shortCode}`;
      }),
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  calculatePaymentLinkTax(_conversationDetail: ConversationDetail, _amount: number): Observable<number> {
    throw new Error('not implemented');
  }

  buildKabobActions(accountGroupId: string): KabobDynamicButton[] {
    const goToAccountAction: KabobDynamicButton = {
      title: 'INBOX.SENDER.GO_TO_ACCOUNT',
      action: () => this.router.navigate([`/info/${accountGroupId}`]),
      posthogLabel: 'clicked-go-to-account',
    };
    const openBusinessAppButton: KabobDynamicButton = {
      title: 'INBOX.SENDER.OPEN_BUSINESS_APP',
      action: () => this.openBusinessApp(accountGroupId),
      posthogLabel: 'clicked-open-business-app',
    };
    return [goToAccountAction, openBusinessAppButton];
  }

  getKabobAvailableActions(conversationDetail: ConversationDetail): Observable<KabobDynamicButton[]> {
    const accountGroupId = getSubjectParticipantByType(
      conversationDetail.participants,
      ParticipantType.PARTICIPANT_TYPE_ACCOUNT_GROUP,
    )?.internalParticipantId;
    if (accountGroupId) {
      const isRecipientDeleted = this.isRecipientInternalInfoDeleted(conversationDetail);
      if (!isRecipientDeleted) {
        return of(this.buildKabobActions(accountGroupId));
      }
    }
    if (this.partnerId() === 'VMF') {
      const participantPartnerId = conversationDetail?.participants.find(
        (participant) =>
          participant?.isSubjectParticipant &&
          participant?.participantType === ParticipantType.PARTICIPANT_TYPE_PARTNER &&
          participant?.internalParticipantId !== 'VMF',
      )?.internalParticipantId;
      return this.partnerAccountGroupService.getPartnerAccountGroupMapping(participantPartnerId, null).pipe(
        catchError((err) => {
          console.error(err);
          // Will not be able to go to account if an error occurs
          return [];
        }),
        map((accountGroupFromPartner) => {
          return this.buildKabobActions(accountGroupFromPartner.accountGroupId);
        }),
      );
    }
    return of([]);
  }

  getAppOptions(): { [key in AppOptionsKeys]?: boolean } {
    return {};
  }

  openBusinessApp(accountGroupId: string): void {
    const entryUrl$ = this.ssoService.getEntryUrl('VBC', newPartnerServiceContext(this.partnerId()));
    firstValueFrom(entryUrl$).then((entryUrl) => {
      if (entryUrl) {
        window.open(addNextUrl(entryUrl, `/account/location/${accountGroupId}/dashboard`), '_blank');
      }
    });
  }

  navigateToCreateContact() {
    throw new Error('not implemented');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  getUsers(accountGroupId: string): Observable<AssociatedUserInterface[]> {
    return;
  }

  getNamespace(): Observable<InboxNamespace> {
    return this.partnerId$.pipe(
      map(
        (partnerId): InboxNamespace => ({
          id: partnerId,
          namespaceType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_PARTNER,
        }),
      ),
    );
  }

  viewContact(_conversationDetail: ConversationDetail): void {
    // Sales Center does not support viewing contacts
    return;
  }
}
