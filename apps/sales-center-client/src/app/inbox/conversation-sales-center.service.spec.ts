// eslint-disable-next-line @nx/enforce-module-boundaries
import { ConversationDetail, ConversationMessage, ParticipantService } from '@galaxy/conversation/core';
import { FeatureFlagService, PartnerAccountGroupService } from '@galaxy/partner';
import { SpectatorService, createServiceFactory } from '@ngneat/spectator/jest';
import { Conversation, Message, Participant, ParticipantType, ConversationChannel } from '@vendasta/conversation';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { SSOService } from '@vendasta/sso';
import { UrlShortenerService } from '@vendasta/url-shortener';
import { of } from 'rxjs';
import { ConversationSalesCenterService } from './conversation-sales-center.service';

describe('ConversationSalesCenterService', () => {
  let spectator: SpectatorService<ConversationSalesCenterService>;
  const createService = createServiceFactory({
    service: ConversationSalesCenterService,
    providers: [
      { provide: 'PARTNER_ID_TOKEN', useValue: of('VUNI') },
      {
        provide: ParticipantService,
        useValue: new ParticipantService(null, null, null, null, null, null, null, null, null),
      },
    ],
    mocks: [FeatureFlagService, SnackbarService, UrlShortenerService, SSOService, PartnerAccountGroupService],
  });

  beforeEach(() => {
    spectator = createService();
  });

  it('should give deleted contact as title if contact deleted', () => {
    const conversationDetailDeleted = {
      conversation: {
        conversationId: 'vstore-123',
      } as Conversation,
      participants: [
        new Participant({
          internalParticipantId: 'Contact-123',
          participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
          isSubjectParticipant: true,
          isParticipantInternalInfoDeleted: true,
        }),
        new Participant({
          internalParticipantId: 'VUNI',
          participantType: ParticipantType.PARTICIPANT_TYPE_PARTNER,
          isSubjectParticipant: true,
        }),
      ],
      latestMessage: {} as Message,
    } as ConversationDetail;

    const get = spectator.service.getConversationTitleInfo(conversationDetailDeleted);
    expect(get).toEqual({ title: 'INBOX.ERROR.DELETED_CUSTOMER', subtitle: '', secondarySubtitle: '' });
  });
  it('should return true when it is from OPENAI BOT and channel is WEBCHAT', () => {
    const firestoreMessage = {
      sender: new Participant({
        partnerId: '',
        participantType: ParticipantType.PARTICIPANT_TYPE_OPENAI_BOT,
      }),
      channel: ConversationChannel.CONVERSATION_CHANNEL_WEB_CHAT,
      body: 'message',
    } as ConversationMessage;

    const isFromOrg = spectator.service.isSenderFromOrganization(firestoreMessage);
    expect(isFromOrg).toEqual(true);
  });

  it('should return false when it is from OPENAI BOT and channel is not WEBCHAT', () => {
    const firestoreMessage = {
      sender: new Participant({
        partnerId: '',
        participantType: ParticipantType.PARTICIPANT_TYPE_OPENAI_BOT,
      }),
      channel: ConversationChannel.CONVERSATION_CHANNEL_UNDEFINED,
      body: 'message',
    } as ConversationMessage;

    const isFromOrg = spectator.service.isSenderFromOrganization(firestoreMessage);
    expect(isFromOrg).toEqual(false);
  });
});
