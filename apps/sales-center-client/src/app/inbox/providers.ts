import { inject, InjectionToken } from '@angular/core';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { ConversationConfig, RouteConfig } from '@galaxy/conversation/core';
import { of } from 'rxjs';
import { distinctUntilChanged, filter, map, shareReplay, switchMap, take } from 'rxjs/operators';
import { PartnerService } from '@galaxy/partner';
import { WhitelabelService } from '@galaxy/partner';
import { FeatureFlagService } from '@galaxy/partner';
import { LoggedInUserInfoService } from '../logged-in-user-info';
import { AtlasDataService } from '@galaxy/atlas/core';
import { ConversationChannel, PlatformLocation } from '@vendasta/conversation';

export const INBOX_CONFIG_FOR_CONVERSATION = new InjectionToken<ConversationConfig>(
  '[Sales Center Client]: Token for Conversation config',
  {
    providedIn: 'root',
    factory: function (): ConversationConfig {
      const userService = inject(LoggedInUserInfoService);
      const partnerService = inject(PartnerService);
      const whitelabelService = inject(WhitelabelService);
      const featureFlagService = inject(FeatureFlagService);
      const atlasDataService = inject(AtlasDataService);

      const userId$ = userService.unifiedUserId$;
      const accountGroupId$ = of('');

      const partnerId$ = partnerService.getPartnerId().pipe(
        map((partnerId) => partnerId),
        filter<string>(Boolean),
        distinctUntilChanged(),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );

      const country$ = partnerId$.pipe(
        take(1),
        switchMap((partnerId) => {
          return whitelabelService
            .getConfiguration(partnerId)
            .pipe(map((config) => config.mailingConfiguration.mailingCountry));
        }),
        distinctUntilChanged(),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );

      const geographicalState$ = of('');

      const partnerBrandName$ = of('');

      const companyName$ = of('');

      const featureFlag$ = partnerId$.pipe(
        take(1),
        switchMap((partnerId) => {
          return featureFlagService.batchGetStatus(partnerId, 'default', ['inbox_tab_pcc']);
        }),
        map((featureFlags) => {
          return featureFlags['inbox_tab_pcc'];
        }),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );

      const platformLocation = PlatformLocation.PLATFORM_LOCATION_SALES_CENTER;
      const conversationChannelsEnabled$ = of([ConversationChannel.CONVERSATION_CHANNEL_INTERNAL]);

      const routes$ = of({
        root: '',
        sendNewMessage: 'manage-accounts',
        useModal: true,
      } as RouteConfig);

      const marketId$ = of('');

      const isImpersonating$ = atlasDataService.impersonateeUsername$.pipe(
        map((impersonateeUsername) => Boolean(impersonateeUsername)),
      );

      return {
        userId$,
        accountGroupId$,
        country$,
        geographicalState$,
        partnerId$,
        partnerBrandName$,
        companyName$,
        featureFlag$,
        platformLocation,
        conversationChannelsEnabled$,
        routes$,
        marketId$,
        isImpersonating$,
      };
    },
  },
);
