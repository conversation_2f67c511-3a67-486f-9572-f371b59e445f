import { animate, animation, stagger, style } from '@angular/animations';

export const slideRightIntoViewAnimation = animation([
  style([{ transform: 'translateX(-100%)' }]),
  animate('300ms cubic-bezier(0.35, 0, 0.25, 1)', style([{ transform: 'translateX(0%)' }])),
]);

export const slideRightOutOfViewAnimation = animation([
  style([
    { transform: 'translateX(0%)' },
    {
      position: 'absolute',
      top: '2%',
      left: 0,
      width: '100%',
    },
  ]),
  animate('300ms cubic-bezier(0.35, 0, 0.25, 1)', style([{ transform: 'translateX(+100%)' }])),
]);

export const slideLeftOutOfViewAnimation = animation([
  style([
    { transform: 'translateX(0%)' },
    {
      position: 'absolute',
      top: '2%',
      left: 0,
      width: '100%',
    },
  ]),
  animate('300ms cubic-bezier(0.35, 0, 0.25, 1)', style([{ transform: 'translateX(-100%)' }])),
]);

export const slideLeftIntoViewAnimation = animation([
  style([{ transform: 'translateX(100%)' }]),
  animate('300ms cubic-bezier(0.35, 0, 0.25, 1)', style([{ transform: 'translateX(0%)' }])),
]);

export const fadeDownIntoView = animation([
  style({ opacity: 0, transform: 'translateY(-15px)' }),
  stagger('50ms', animate('550ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))),
]);
