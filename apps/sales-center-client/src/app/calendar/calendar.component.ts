import { MediaMatcher } from '@angular/cdk/layout';
import { ChangeDetectorRef, Component, OnInit, TemplateRef, ViewChild, ViewContainerRef } from '@angular/core';
import { MatSidenav } from '@angular/material/sidenav';
import { TranslateService } from '@ngx-translate/core';
import { CalendarEvent, CalendarView } from 'angular-calendar';
import { BehaviorSubject, firstValueFrom, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { BreadCrumb } from '../navigation';
import { CalendarSidePanelService } from './calendar-side-panel.service';
import { CALENDAR, EventType } from './constants';
import { TaskService } from './task.service';
import {
  TaskCreateDialogComponent,
  TaskCreateDialogData,
} from '../task-overview/create-dialog/task-create-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { LoggedInUserInfoService } from '../logged-in-user-info';

const DESKTOP_LARGE_MINIMUM_BREAKPOINT = '1440px';

@Component({
  selector: 'app-calendar',
  templateUrl: './calendar.component.html',
  styleUrls: ['./calendar.component.scss'],
  standalone: false,
})
export class CalendarComponent implements OnInit {
  @ViewChild('panel', { static: true }) private readonly sidePanel: MatSidenav;
  @ViewChild('content', { static: true, read: ViewContainerRef }) private readonly vcf: ViewContainerRef;
  @ViewChild('sidePanelContent', { static: false }) private readonly sidePanelContent: TemplateRef<any>;
  CalendarView = CalendarView;
  displayedView$$: BehaviorSubject<CalendarView> = new BehaviorSubject<CalendarView>(CalendarView.Month);
  viewDate: Date = new Date();
  events$: Observable<CalendarEvent[]>;
  calendarTaskListDate$$: BehaviorSubject<Date> = new BehaviorSubject<Date>(this.viewDate);
  calendarEventType$$: BehaviorSubject<EventType> = new BehaviorSubject<EventType>(EventType.Task);
  widthQuery: MediaQueryList;
  EventType = EventType;

  private readonly _widthQueryListener: () => void;
  readonly breadCrumbs$: Observable<BreadCrumb[]>;

  constructor(
    private readonly taskService: TaskService,
    public readonly sidenavService: CalendarSidePanelService,
    private readonly media: MediaMatcher,
    private readonly changeDetectorRef: ChangeDetectorRef,
    private readonly translate: TranslateService,
    private readonly dialog: MatDialog,
    private readonly loggedInUserInfoService: LoggedInUserInfoService,
  ) {
    this.widthQuery = media.matchMedia(`(max-width: ${DESKTOP_LARGE_MINIMUM_BREAKPOINT})`);
    this._widthQueryListener = () => changeDetectorRef.detectChanges();
    this.widthQuery.addListener(this._widthQueryListener);
    this.breadCrumbs$ = this.translate.stream(CALENDAR).pipe(map((translation) => [{ label: translation }]));
  }

  ngOnInit(): void {
    this.events$ = this.taskService.calendarEvents$;
    this.sidenavService.setPanel(this.sidePanel);
    this.sidenavService.setContentVcf(this.vcf);
    this.taskService.refreshCalendar();
  }

  handleDateClicked(date: Date, eventType: EventType): void {
    this.calendarTaskListDate$$.next(date);
    this.calendarEventType$$.next(eventType);
    this.sidenavService.open(this.sidePanelContent);
  }

  toggleView(view: CalendarView): void {
    this.displayedView$$.next(view);
  }

  isTodayDate(event: CalendarEvent): boolean {
    return event && this.viewDate.getDate() === event.start.getDate();
  }

  closeSideNav(): void {
    this.taskService.refreshCalendar();
    this.sidenavService.close();
  }

  viewDateChange(newDate: Date): void {
    this.taskService.setDateRange(newDate);
  }

  async createTask(): Promise<void> {
    return firstValueFrom(this.loggedInUserInfoService.loggedInUserInfo$)
      .then((l) => {
        const data: TaskCreateDialogData = {
          partnerId: l.partnerId,
          marketId: l.marketId,
          salespersonId: l.salespersonId,
        };
        const dialogRef = this.dialog.open(TaskCreateDialogComponent, { data: data });
        return firstValueFrom(dialogRef.afterClosed());
      })
      .then((hasTasksCreated) => {
        if (hasTasksCreated) {
          this.taskService.refreshCalendar();
        }
      });
  }
}
