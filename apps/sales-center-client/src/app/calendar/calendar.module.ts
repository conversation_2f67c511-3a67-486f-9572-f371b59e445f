import { DragDropModule } from '@angular/cdk/drag-drop';
import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { RouterModule, Routes } from '@angular/router';
import { CalendarComponent } from './calendar.component';
import { NavigationModule } from '../navigation/navigation.module';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { CalendarModule as AngularCalendarModule, DateAdapter } from 'angular-calendar';
import { adapterFactory } from 'angular-calendar/date-adapters/date-fns';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatSidenavModule } from '@angular/material/sidenav';
import { CalendarTaskListComponent } from './calendar-task-list/calendar-task-list.component';
import { MatTabsModule } from '@angular/material/tabs';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import {
  CalendarTaskTableComponent,
  PrimaryContactPipe,
} from './calendar-task-list/calendar-task-table/calendar-task-table.component';
import { InlineEditModule } from './inline-edit/inline-edit.module';
import { CommonPipesModule, EmptyStateModule, VaStatusBannerModule } from '@vendasta/uikit';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatCardModule } from '@angular/material/card';
import { MatSelectModule } from '@angular/material/select';
import { CalendarSidePanelService } from './calendar-side-panel.service';
import { ClipboardModule } from '@angular/cdk/clipboard';
import { TaskService } from './task.service';
import { MeetingTaskListComponent } from './meeting-task-list/meeting-task-list.component';
import { MeetingTaskTableComponent } from './meeting-task-list/meeting-task-table/meeting-task-table.component';
import { MatStepperModule } from '@angular/material/stepper';
import { ConnectionStatusModule, ConnectToServiceModule, CustomElementsModule } from '@vendasta/integrations';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatDividerModule } from '@angular/material/divider';
import { MatDialogModule } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
// eslint-disable-next-line @nx/enforce-module-boundaries
import {
  CancelMeetingButtonModule,
  RescheduleMeetingButtonModule,
  MeetingTypeModule,
  MeetingIntegrationsModule,
  MeetingSchedulerSharedModule,
} from '@galaxy/meeting-scheduler';
import { MatMenuModule } from '@angular/material/menu';
import { VaFormsModule } from '@vendasta/forms';
import { MatChipsModule } from '@angular/material/chips';
import { LexiconModule } from '@galaxy/lexicon';
import baseTranslation from '../../assets/i18n/en_devel.json';
import { WEBLATE_COMPONENT_NAME } from '../constants';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { ContactFormsModule } from '../common/contacts/contact-forms.module';
import { ContactPipeModule } from '../common/contacts/contact-pipe/contact-pipe.module';
import { TaskCreateDialogModule } from '../task-overview/create-dialog/task-create-dialog.module';
import { ScrollingModule } from '@angular/cdk/scrolling';

const routes: Routes = [
  {
    path: 'calendar',
    component: CalendarComponent,
  },

  // Exact empty match will match the CalendarComponent. Otherwise, we match MeetingScheduler
  {
    path: '',
    component: CalendarComponent,
    pathMatch: 'full',
  },
  {
    path: '',
    loadChildren: () => import('@galaxy/meeting-scheduler').then((m) => m.MeetingSchedulerModule),
    pathMatch: 'prefix',
  },
];

@NgModule({
  imports: [
    RouterModule.forChild(routes),
    TranslateModule,
    LexiconModule.forChild({
      componentName: WEBLATE_COMPONENT_NAME,
      baseTranslation: baseTranslation,
    }),
    NavigationModule,
    AngularCalendarModule.forRoot({
      provide: DateAdapter,
      useFactory: adapterFactory,
    }),
    MatButtonToggleModule,
    CommonModule,
    MatIconModule,
    MatSidenavModule,
    MatTabsModule,
    MatButtonModule,
    MatTableModule,
    InlineEditModule,
    CommonPipesModule,
    MatFormFieldModule,
    MatInputModule,
    MatDatepickerModule,
    ReactiveFormsModule,
    MatAutocompleteModule,
    MatProgressSpinnerModule,
    MatCheckboxModule,
    MatCardModule,
    MatSelectModule,
    FormsModule,
    ClipboardModule,
    VaStatusBannerModule,
    EmptyStateModule,
    MatStepperModule,
    MatExpansionModule,
    MatDividerModule,
    ConnectionStatusModule,
    ConnectToServiceModule,
    CustomElementsModule,
    MatDialogModule,
    MatTooltipModule,
    MatMenuModule,
    MeetingTypeModule,
    CancelMeetingButtonModule,
    RescheduleMeetingButtonModule,
    MeetingIntegrationsModule,
    MeetingSchedulerSharedModule,
    NavigationModule,
    VaFormsModule,
    MatChipsModule,
    DragDropModule,
    GalaxyPageModule,
    ContactFormsModule,
    ContactPipeModule,
    TaskCreateDialogModule,
    ScrollingModule,
  ],
  declarations: [
    CalendarComponent,
    CalendarTaskListComponent,
    CalendarTaskTableComponent,
    MeetingTaskListComponent,
    MeetingTaskTableComponent,
    PrimaryContactPipe,
  ],
  exports: [RouterModule],
  providers: [CalendarSidePanelService, TaskService],
})
export class CalendarModule {}
