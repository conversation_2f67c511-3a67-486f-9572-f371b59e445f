import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Status } from '@vendasta/task';
import { Observable, Subscription } from 'rxjs';
import { take } from 'rxjs/operators';
import { TasksService } from '../../tasks';
import { DueDateChangedEvent, SalesTask, StatusChangedEvent, TitleChangedEvent } from '../constants';
import { CalendarTaskListService } from './calendar-task-list.service';

@Component({
  selector: 'app-calendar-task-list',
  templateUrl: './calendar-task-list.component.html',
  styleUrls: ['./calendar-task-list.component.scss'],
  providers: [CalendarTaskListService, TasksService],
  standalone: false,
})
export class CalendarTaskListComponent implements OnInit, OnDestroy {
  @Input() date$: Observable<Date>;
  @Output() closed: EventEmitter<void> = new EventEmitter<void>();
  taskStatuses = Status;
  activeTab: string = Status.InProgress;
  inProgressTasks$: Observable<SalesTask[]>;
  completedTasks$: Observable<SalesTask[]>;
  inProgressTotalTasks$: Observable<number>;
  completedTotalTasks$: Observable<number>;
  inProgressHasMore$: Observable<boolean>;
  completedHasMore$: Observable<boolean>;
  inProgressLoading$: Observable<boolean>;
  completedLoading$: Observable<boolean>;

  private readonly subscriptions: Subscription[] = [];

  constructor(
    private readonly calendarTaskListService: CalendarTaskListService,
    private readonly alertService: SnackbarService,
  ) {}

  ngOnInit(): void {
    this.inProgressTasks$ = this.calendarTaskListService.inProgressTasks$;
    this.completedTasks$ = this.calendarTaskListService.completedTasks$;
    this.inProgressTotalTasks$ = this.calendarTaskListService.inProgressTotalTasks$;
    this.completedTotalTasks$ = this.calendarTaskListService.completedTotalTasks$;
    this.inProgressHasMore$ = this.calendarTaskListService.inProgressHasMore$;
    this.completedHasMore$ = this.calendarTaskListService.completedHasMore$;
    this.inProgressLoading$ = this.calendarTaskListService.inProgressLoading$;
    this.completedLoading$ = this.calendarTaskListService.completedLoading$;
    this.subscriptions.push(this.date$.subscribe((date) => (this.calendarTaskListService.date = date)));
  }

  closeSidePanel(): void {
    this.closed.emit();
  }

  dueDateChanged(event: DueDateChangedEvent, status: Status): void {
    this.subscriptions.push(
      this.calendarTaskListService.handleDueDateChanged(event, status).subscribe(
        () => this.alertService.openSuccessSnack('CALENDAR.TASK_LIST.DUE_DATE_CHANGE_SUCCESS'),
        () => this.alertService.openErrorSnack('CALENDAR.TASK_LIST.DUE_DATE_CHANGE_ERROR'),
      ),
    );
  }

  titleChanged(event: TitleChangedEvent, status: Status): void {
    this.subscriptions.push(
      this.calendarTaskListService.handleTitleChanged(event, status).subscribe(
        () => this.alertService.openSuccessSnack('CALENDAR.TASK_LIST.TITLE_CHANGE_SUCCESS'),
        () => this.alertService.openErrorSnack('CALENDAR.TASK_LIST.TITLE_CHANGE_ERROR'),
      ),
    );
  }

  statusChanged(event: StatusChangedEvent): void {
    this.subscriptions.push(
      this.calendarTaskListService.handleStatusChanged(event).subscribe(
        () => this.alertService.openSuccessSnack('CALENDAR.TASK_LIST.STATUS_CHANGE_SUCCESS'),
        () => this.alertService.openErrorSnack('CALENDAR.TASK_LIST.STATUS_CHANGE_ERROR'),
      ),
    );
  }

  loadMore(status: Status): void {
    if (status === Status.InProgress) {
      this.calendarTaskListService.loadMoreInProgressTasks(status).pipe(take(1)).subscribe();
    } else {
      this.calendarTaskListService.loadMoreCompletedTasks(status).pipe(take(1)).subscribe();
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }
}
