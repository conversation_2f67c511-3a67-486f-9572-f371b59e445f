<div class="task-list-container">
  <div class="row">
    <div class="col col-xs-12">
      <nav mat-tab-nav-bar>
        <a mat-tab-link (click)="activeTab = taskStatuses.InProgress" [active]="activeTab === taskStatuses.InProgress">
          <span>
            {{ (inProgressTotalTasks$ | async) || 0 }}
            {{ ((inProgressTotalTasks$ | async) === 1 ? 'CALENDAR.TASK' : 'CALENDAR.TASK_PLURAL') | translate }}
            {{ 'CALENDAR.TASK_LIST.IN_PROGRESS' | translate }}
          </span>
        </a>
        <a mat-tab-link (click)="activeTab = taskStatuses.Completed" [active]="activeTab === taskStatuses.Completed">
          <span>
            {{ (completedTotalTasks$ | async) || 0 }}
            {{ ((completedTotalTasks$ | async) === 1 ? 'CALENDAR.TASK' : 'CALENDAR.TASK_PLURAL') | translate }}
            {{ 'CALENDAR.TASK_LIST.COMPLETED' | translate }}
          </span>
        </a>
        <div class="nav-actions">
          <mat-icon class="close-icon" (click)="closeSidePanel()">close</mat-icon>
        </div>
      </nav>
    </div>
  </div>
  <div [ngSwitch]="activeTab">
    <ng-container *ngSwitchCase="taskStatuses.InProgress">
      <app-calendar-task-table
        [tasks]="inProgressTasks$ | async"
        (dueDateChanged)="dueDateChanged($event, taskStatuses.InProgress)"
        [date]="date$ | async"
        (titleChanged)="titleChanged($event, taskStatuses.InProgress)"
        (statusChanged)="statusChanged($event)"
        (loadMore)="loadMore(taskStatuses.InProgress)"
        [hasMore]="inProgressHasMore$ | async"
        [loadingTasks]="inProgressLoading$ | async"
      ></app-calendar-task-table>
    </ng-container>
    <ng-container *ngSwitchCase="taskStatuses.Completed">
      <app-calendar-task-table
        [tasks]="completedTasks$ | async"
        (dueDateChanged)="dueDateChanged($event, taskStatuses.Completed)"
        [date]="date$ | async"
        (titleChanged)="titleChanged($event, taskStatuses.Completed)"
        (statusChanged)="statusChanged($event)"
        (loadMore)="loadMore(taskStatuses.Completed)"
        [hasMore]="completedHasMore$ | async"
        [loadingTasks]="completedLoading$ | async"
      ></app-calendar-task-table>
    </ng-container>
  </div>
</div>
