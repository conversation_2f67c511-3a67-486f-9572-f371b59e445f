import { Status, TaskInterface } from '@vendasta/task';
import { of } from 'rxjs';
import { LoggedInUserInfo } from '../../logged-in-user-info';
import { SalesTask } from '../constants';
import { TaskService } from '../task.service';
import { CalendarTaskListService } from './calendar-task-list.service';

const userInfo: LoggedInUserInfo = new LoggedInUserInfo(
  'UID-123',
  false,
  true,
  'ABC',
  'market',
  '123',
  null,
  null,
  null,
  null,
  null,
  null,
  null,
);

const taskInProgressWithAccount: TaskInterface = {
  status: Status.InProgress,
  identity: {
    namespace: 'partner/ABC/account-group/AG-123',
    parentPath: '/',
    taskId: 'TK-123',
  },
  title: 'Task In Progress',
  dueDate: new Date(2020, 1, 1),
  assignees: [userInfo.salespersonId],
};
const taskCompletedNoAccount: TaskInterface = {
  status: Status.Completed,
  identity: {
    namespace: 'partner/ABC',
    parentPath: '/',
    taskId: 'TK-456',
  },
  title: 'Task Completed',
  dueDate: new Date(2020, 1, 1),
  assignees: [userInfo.salespersonId],
};

describe('CalendarTaskListService', () => {
  let service: CalendarTaskListService;
  let taskService: TaskService;
  beforeEach(() => {
    taskService = {
      loadTasks: jest.fn(() => of([taskInProgressWithAccount, taskCompletedNoAccount])),
      updateDueDate: jest.fn(() => of(1)),
      updateTitle: jest.fn(() => of(1)),
      updateStatus: jest.fn(() => of(1)),
      createTask: jest.fn(() => of(taskInProgressWithAccount)),
    } as unknown as TaskService;
    service = new CalendarTaskListService(taskService, of('ABC'), of(userInfo));
  });

  describe('removeTaskFromTaskList', () => {
    it('should remove In Progress task from correct list and update total', async () => {
      service.inProgressTasks = [taskInProgressWithAccount];
      service.inProgressTaskSearchResponse = {
        total: 1,
        cursor: '1',
        hasMore: false,
      };
      await service.removeTaskFromTaskList(taskInProgressWithAccount, Status.InProgress);
      expect(service.inProgressTasks.length).toEqual(0);
      expect(service.inProgressTotalTasks).toEqual(0);
    });
    it('should remove Completed task from correct list and update total', async () => {
      service.completedTasks = [taskCompletedNoAccount];
      service.completedTaskSearchResponse = {
        total: 1,
        cursor: '1',
        hasMore: false,
      };
      await service.removeTaskFromTaskList(taskCompletedNoAccount, Status.Completed);
      expect(service.completedTasks.length).toEqual(0);
      expect(service.completedTotalTasks).toEqual(0);
    });
  });

  describe('addTaskToTaskList', () => {
    it('should add In Progress task from correct list and update total', async () => {
      service.inProgressTasks = [taskInProgressWithAccount];
      service.inProgressTaskSearchResponse = {
        total: 1,
        cursor: '1',
        hasMore: false,
      };
      const newTask: SalesTask = {};
      await service.addTaskToTaskList(newTask, Status.InProgress);
      expect(service.inProgressTasks.length).toEqual(2);
      expect(service.inProgressTasks.findIndex((t) => t === newTask)).toEqual(0);
      expect(service.inProgressTotalTasks).toEqual(2);
    });
    it('should add Completed task from correct list and update total', async () => {
      service.completedTasks = [taskCompletedNoAccount];
      service.completedTaskSearchResponse = {
        total: 1,
        cursor: '1',
        hasMore: false,
      };
      const newTask: SalesTask = {};
      await service.addTaskToTaskList(newTask, Status.Completed);
      expect(service.completedTasks.length).toEqual(2);
      expect(service.completedTasks.findIndex((t) => t === newTask)).toEqual(0);
      expect(service.completedTotalTasks).toEqual(2);
    });
  });
  describe('handleTitleChanged', () => {
    it('should call updateTitle with expected paprameters', async () => {
      const titleRequest = {
        title: 'New Title',
        task: taskInProgressWithAccount,
      };
      await service.handleTitleChanged(titleRequest, Status.InProgress);
      expect(taskService.updateTitle).toBeCalledWith(titleRequest);
    });
  });
  describe('handleStatusChanged', () => {
    it('should call updateStatus with expected paprameters', async () => {
      const statusRequest = {
        newStatus: Status.InProgress,
        oldStatus: Status.Open,
        task: taskInProgressWithAccount,
      };
      await service.handleStatusChanged(statusRequest);
      expect(taskService.updateStatus).toBeCalledWith(statusRequest);
    });
  });
  describe('handleDueDateChanged', () => {
    it('should call updateDueDate with expected paprameters', async () => {
      const dueDateRequest = {
        newDate: new Date(2020, 1, 1),
        task: taskInProgressWithAccount,
      };
      await service.handleDueDateChanged(dueDateRequest, Status.InProgress);
      expect(taskService.updateDueDate).toBeCalledWith(dueDateRequest.newDate, dueDateRequest.task);
    });
  });
});
