<div class="table-container">
  <ng-container *ngIf="tasks">
    <ng-container *ngIf="!loadingTasks || tasks.length > 0; else shimmer">
      <ng-container *ngIf="tasks.length > 0">
        <table mat-table [dataSource]="tasks">
          <ng-container matColumnDef="status">
            <td mat-cell *matCellDef="let task">
              <mat-checkbox
                [checked]="task.status === 'Completed'"
                (change)="handleStatusChange($event, task)"
              ></mat-checkbox>
            </td>
          </ng-container>
          <ng-container matColumnDef="title">
            <td mat-cell *matCellDef="let task">
              <app-display-input
                [value]="task.title"
                (valueChanged)="handleTitleChanged($event, task)"
                class="task-title"
              ></app-display-input>
            </td>
          </ng-container>
          <ng-container matColumnDef="account">
            <td mat-cell *matCellDef="let task">
              <div *ngIf="task.account; else noAccount" class="account-details-container">
                <a class="account-title" [routerLink]="'/info/' + task.accountGroupId">
                  {{ task.account.napData.companyName }}
                </a>
                <div class="account-subtitle">
                  {{ task.account.napData.address }}
                </div>
              </div>
            </td>
          </ng-container>
          <ng-container matColumnDef="primaryContact">
            <td mat-cell *matCellDef="let task">
              <ng-container
                *ngIf="task.account && (task | primaryContact) as pc; else noAccount"
                class="contact-container"
              >
                <span>{{ pc | contactFullName }}</span>
                <ng-container *ngIf="task.account.napData && task.account.napData.country">
                  <a class="phone-number" [href]="'tel:' + pc.phoneNumber">
                    {{ pc.phoneNumber || '' | phone : task.account.napData.country }}
                    <span *ngIf="pc.phoneExtension">
                      {{ ' x' + pc.phoneExtension }}
                    </span>
                  </a>
                </ng-container>
              </ng-container>
            </td>
          </ng-container>
          <ng-container matColumnDef="dueDate">
            <td mat-cell *matCellDef="let task">
              <app-display-date
                [initialValue]="task.dueDate"
                (dateChange)="handleDueDateChanged($event, task)"
              ></app-display-date>
            </td>
          </ng-container>
          <tr mat-row *matRowDef="let row; columns: displayedColumns" class="row table-row"></tr>
        </table>
        <div *ngIf="hasMore" class="load-more-button-container">
          <button class="load-more-button" mat-raised-button color="primary" (click)="loadMoreTasks()">
            {{ 'CALENDAR.TASK_LIST.LOAD_MORE' | translate }}
          </button>
        </div>
      </ng-container>
    </ng-container>
  </ng-container>
  <ng-template #noAccount>
    <span>-</span>
  </ng-template>
</div>
<ng-template #shimmer>
  <div class="stencil-shimmer shimmer"></div>
</ng-template>
