import { Component, EventEmitter, Input, Output, Pipe, PipeTransform } from '@angular/core';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { Status, TaskInterface } from '@vendasta/task';
import { ContactShadow } from '../../../common/contacts';
import { DueDateChangedEvent, SalesTask, StatusChangedEvent, TitleChangedEvent } from '../../constants';

@Component({
  selector: 'app-calendar-task-table',
  templateUrl: './calendar-task-table.component.html',
  styleUrls: ['./calendar-task-table.component.scss'],
  standalone: false,
})
export class CalendarTaskTableComponent {
  @Input() tasks: SalesTask[];
  @Input() loadingTasks = false;
  @Input() hasMore = false;
  @Input() date: Date;
  // eslint-disable-next-line @angular-eslint/no-output-native
  @Output() cancel: EventEmitter<any> = new EventEmitter<any>();
  @Output() dueDateChanged: EventEmitter<DueDateChangedEvent> = new EventEmitter<DueDateChangedEvent>();
  @Output() titleChanged: EventEmitter<TitleChangedEvent> = new EventEmitter<TitleChangedEvent>();
  @Output() statusChanged: EventEmitter<StatusChangedEvent> = new EventEmitter<StatusChangedEvent>();
  @Output() loadMore: EventEmitter<any> = new EventEmitter<any>();
  displayedColumns: string[] = ['status', 'title', 'account', 'primaryContact', 'dueDate'];

  handleDueDateChanged(newDate: Date, task: TaskInterface): void {
    this.dueDateChanged.emit({ newDate: newDate, task: task });
  }

  handleTitleChanged(title: string, task: TaskInterface): void {
    this.titleChanged.emit({ title: title, task: task });
  }

  handleStatusChange(event: MatCheckboxChange, task: TaskInterface): void {
    event.checked
      ? this.statusChanged.emit({ newStatus: Status.Completed, oldStatus: Status.InProgress, task: task })
      : this.statusChanged.emit({ newStatus: Status.InProgress, oldStatus: Status.Completed, task: task });
  }

  loadMoreTasks(): void {
    this.loadMore.emit();
  }
}

@Pipe({
  name: 'primaryContact',
  standalone: false,
})
export class PrimaryContactPipe implements PipeTransform {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  transform(task: SalesTask, ..._args: any[]): ContactShadow {
    if (!task.contacts) return undefined;
    if (!task.primaryContact) return task.contacts?.[0];

    const primaryContact = task.contacts.find((c) => c.contactId === task.primaryContact);

    return primaryContact ?? task.contacts[0];
  }
}
