@use 'design-tokens' as dt;

.mat-column-dueDate {
  width: 160px;
  padding: auto;
}

.mat-column-status {
  width: 50px;
  overflow: initial;
}

.mat-column-title {
  width: 100px;
  overflow: hidden;
  padding: dt.$spacing-1;
}

.mat-column-account {
  width: 120px;
  overflow: hidden;
}

.mat-column-primaryContact {
  width: 120px;
  overflow: hidden;
  font-size: dt.$font-preset-5-size;
}

.row {
  height: 80px;
  margin: dt.$spacing-2 0;
}

table {
  margin-top: dt.$spacing-3;
  width: 100%;

  mat-cell {
    vertical-align: bottom;
  }
}

.account-subtitle {
  color: dt.$tertiary-font-color;
  font-size: dt.$font-preset-5-size;
}
.account-title {
  font-size: dt.$font-preset-4-size;
}

.contact-container {
  font-size: dt.$font-preset-5-size;
  margin-bottom: dt.$spacing-2;
  .phone-number {
    color: dt.$primary-font-color;
  }
  .phone-number:hover {
    background-color: dt.$lighter-gray;
  }
}

.shimmer {
  margin-top: dt.$spacing-3;
  width: 100%;
  height: 500px;
}
