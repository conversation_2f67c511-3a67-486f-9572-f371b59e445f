import { Inject, Injectable, OnDestroy } from '@angular/core';
import { AccountGroup } from '@galaxy/account-group';
import { SearchTaskResponseInterface, Status, TaskInterface } from '@vendasta/task';
import { BehaviorSubject, combineLatest, EMPTY, iif, Observable, of, Subscription } from 'rxjs';
import { map, switchMap, take, tap } from 'rxjs/operators';
import { PARTNER_ID_TOKEN } from '../../common/providers';
import { USER_INFO_TOKEN } from '../../core/feature-flag.service';
import { LoggedInUserInfo } from '../../logged-in-user-info';
import {
  DueDateChangedEvent,
  EXCLUDE_MEETINGS,
  SalesTask,
  StatusChangedEvent,
  TaskSearchResponse,
  TitleChangedEvent,
} from '../constants';
import { TaskService } from '../task.service';

@Injectable()
export class CalendarTaskListService implements OnDestroy {
  private readonly date$$: BehaviorSubject<Date> = new BehaviorSubject<Date>(null);
  private readonly inProgressTasks$$: BehaviorSubject<SalesTask[]> = new BehaviorSubject<SalesTask[]>([]);
  private readonly completedTasks$$: BehaviorSubject<SalesTask[]> = new BehaviorSubject<SalesTask[]>([]);
  private readonly inProgressTaskSearchResponse$$: BehaviorSubject<TaskSearchResponse> =
    new BehaviorSubject<TaskSearchResponse>(null);
  private readonly completedTaskSearchResponse$$: BehaviorSubject<TaskSearchResponse> =
    new BehaviorSubject<TaskSearchResponse>(null);
  private readonly inProgressLoading$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  private readonly completedLoading$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  private readonly subscriptions: Subscription[] = [];

  constructor(
    private readonly taskService: TaskService,
    @Inject(PARTNER_ID_TOKEN) private readonly partnerId$: Observable<string>,
    @Inject(USER_INFO_TOKEN) private readonly userInfo$: Observable<LoggedInUserInfo>,
  ) {
    this.subscriptions.push(
      this.date$$
        .pipe(switchMap((date) => iif(() => !!date, this.getTasks(date, Status.InProgress, EXCLUDE_MEETINGS), EMPTY)))
        .subscribe(this.inProgressTasks$$),
      this.date$$
        .pipe(switchMap((date) => iif(() => !!date, this.getTasks(date, Status.Completed, EXCLUDE_MEETINGS), EMPTY)))
        .subscribe(this.completedTasks$$),
    );
  }

  set date(date: Date) {
    this.date$$.next(date);
  }

  get inProgressTasks$(): Observable<SalesTask[]> {
    return this.inProgressTasks$$.asObservable();
  }

  get inProgressTasks(): SalesTask[] {
    return this.inProgressTasks$$.getValue();
  }

  set inProgressTasks(salesTasks: SalesTask[]) {
    this.inProgressTasks$$.next(salesTasks);
  }

  get completedTasks$(): Observable<SalesTask[]> {
    return this.completedTasks$$.asObservable();
  }

  get completedTasks(): SalesTask[] {
    return this.completedTasks$$.getValue();
  }

  set completedTasks(salesTasks: SalesTask[]) {
    this.completedTasks$$.next(salesTasks);
  }

  get inProgressTotalTasks$(): Observable<number> {
    return this.inProgressTaskSearchResponse$$.asObservable().pipe(map((response) => (response ? response.total : 0)));
  }

  get inProgressTotalTasks(): number {
    return this.inProgressTaskSearchResponse$$.getValue().total;
  }

  set inProgressTaskSearchResponse(taskSearchResponse: TaskSearchResponse) {
    this.inProgressTaskSearchResponse$$.next(taskSearchResponse);
  }

  get completedTotalTasks$(): Observable<number> {
    return this.completedTaskSearchResponse$$.asObservable().pipe(map((response) => (response ? response.total : 0)));
  }

  get completedTotalTasks(): number {
    return this.completedTaskSearchResponse$$.getValue().total;
  }

  set completedTaskSearchResponse(taskSearchResponse: TaskSearchResponse) {
    this.completedTaskSearchResponse$$.next(taskSearchResponse);
  }

  get inProgressHasMore$(): Observable<boolean> {
    return this.inProgressTaskSearchResponse$$
      .asObservable()
      .pipe(map((response) => (response ? response.hasMore : false)));
  }

  get completedHasMore$(): Observable<boolean> {
    return this.completedTaskSearchResponse$$
      .asObservable()
      .pipe(map((response) => (response ? response.hasMore : false)));
  }

  get inProgressLoading$(): Observable<boolean> {
    return this.inProgressLoading$$.asObservable();
  }

  get completedLoading$(): Observable<boolean> {
    return this.completedLoading$$.asObservable();
  }

  private getTasks(date: Date, status: Status, type: string, cursor?: string): Observable<SalesTask[]> {
    status === Status.InProgress ? this.inProgressLoading$$.next(true) : this.completedLoading$$.next(true);
    return combineLatest([this.partnerId$, this.userInfo$]).pipe(
      switchMap(([partnerId, userInfo]) => {
        return this.taskService.loadTasks(partnerId, userInfo.salespersonId, date, status, type, cursor).pipe(
          map((searchResponse: SearchTaskResponseInterface) => {
            const taskSearchResponse: TaskSearchResponse = {
              total: searchResponse.hits || 0,
              cursor: searchResponse.nextCursor,
              hasMore: searchResponse.hasMore,
            };
            if (status === Status.InProgress) {
              this.inProgressTaskSearchResponse$$.next(taskSearchResponse);
            } else {
              this.completedTaskSearchResponse$$.next(taskSearchResponse);
            }
            return searchResponse.tasks;
          }),
        );
      }),
      map((tasks) => this.taskService.addAccountGroupFieldToTasks(tasks)),
      switchMap((tasks) => this.taskService.fetchAccountGroupsAndAddToTasks(tasks)),
      switchMap((tasks) => this.taskService.fetchContactsAndAddToTasks(tasks)),
      switchMap((tasks) => this.taskService.fetchPrimaryContactAndAddToTasks(tasks)),
      tap(() =>
        status === Status.InProgress ? this.inProgressLoading$$.next(false) : this.completedLoading$$.next(false),
      ),
    );
  }

  loadMoreInProgressTasks(status: Status): Observable<any> {
    return this.getTasks(
      this.date$$.getValue(),
      status,
      EXCLUDE_MEETINGS,
      this.inProgressTaskSearchResponse$$.getValue().cursor,
    ).pipe(
      tap((newTasks) => {
        let existingTasks: SalesTask[] = this.inProgressTasks$$.getValue();
        existingTasks = existingTasks.concat(newTasks);
        this.inProgressTasks$$.next(existingTasks);
      }),
    );
  }

  loadMoreCompletedTasks(status: Status): Observable<any> {
    return this.getTasks(
      this.date$$.getValue(),
      status,
      EXCLUDE_MEETINGS,
      this.completedTaskSearchResponse$$.getValue().cursor,
    ).pipe(
      tap((newTasks) => {
        let existingTasks: SalesTask[] = this.completedTasks$$.getValue();
        existingTasks = existingTasks.concat(newTasks);
        this.completedTasks$$.next(existingTasks);
      }),
    );
  }

  handleDueDateChanged(event: DueDateChangedEvent, status: Status): Observable<number> {
    return this.taskService.updateDueDate(event.newDate, event.task).pipe(
      take(1),
      tap(() => this.removeTaskFromTaskList(event.task, status)),
    );
  }

  removeTaskFromTaskList(task: TaskInterface, status: Status): void {
    if (status === Status.InProgress) {
      let tasks: SalesTask[] = this.inProgressTasks$$.getValue();
      tasks = tasks.filter((t) => t.identity.taskId !== task.identity.taskId);
      this.inProgressTasks$$.next(tasks);
      const searchResponse: TaskSearchResponse = this.inProgressTaskSearchResponse$$.getValue();
      searchResponse.total -= 1;
      this.inProgressTaskSearchResponse$$.next(searchResponse);
    } else if (status === Status.Completed) {
      let tasks: SalesTask[] = this.completedTasks$$.getValue();
      tasks = tasks.filter((t) => t.identity.taskId !== task.identity.taskId);
      this.completedTasks$$.next(tasks);
      const searchResponse: TaskSearchResponse = this.completedTaskSearchResponse$$.getValue();
      searchResponse.total -= 1;
      this.completedTaskSearchResponse$$.next(searchResponse);
    }
  }

  handleStatusChanged(event: StatusChangedEvent): Observable<number> {
    return this.taskService.updateStatus(event).pipe(
      take(1),
      tap(() => {
        event.task.status = event.newStatus;
        this.removeTaskFromTaskList(event.task, event.oldStatus);
        this.addTaskToTaskList(event.task, event.newStatus);
      }),
    );
  }

  addTaskToTaskList(task: TaskInterface, status: Status): void {
    if (status === Status.InProgress) {
      const tasks: SalesTask[] = this.inProgressTasks$$.getValue() || [];
      tasks.unshift(task);
      this.inProgressTasks$$.next(tasks);
      const searchResponse: TaskSearchResponse = this.inProgressTaskSearchResponse$$.getValue();
      searchResponse.total += 1;
      this.inProgressTaskSearchResponse$$.next(searchResponse);
    } else if (status === Status.Completed) {
      const tasks: SalesTask[] = this.completedTasks$$.getValue() || [];
      tasks.unshift(task);
      this.completedTasks$$.next(tasks);
      const searchResponse: TaskSearchResponse = this.completedTaskSearchResponse$$.getValue();
      searchResponse.total += 1;
      this.completedTaskSearchResponse$$.next(searchResponse);
    }
  }

  createTask(title: string, accountGroup: AccountGroup, dueDate: Date): Observable<any> {
    return this.taskService.createTask(title, accountGroup, dueDate).pipe(
      switchMap((newTask) => {
        if (newTask.dueDate.getTime() === this.date$$.getValue()?.getTime()) {
          const newTaskList: SalesTask[] = this.taskService.addAccountGroupFieldToTasks([newTask]);
          return this.taskService.fetchAccountGroupsAndAddToTasks(newTaskList).pipe(
            switchMap((ts) => this.taskService.fetchContactsAndAddToTasks(ts)),
            tap((salesTasks) => {
              let tasks: SalesTask[] = this.inProgressTasks$$.getValue() || [];
              tasks = salesTasks.concat(tasks);
              this.inProgressTasks$$.next(tasks);
              const searchResponse: TaskSearchResponse = this.inProgressTaskSearchResponse$$.getValue();
              searchResponse.total += 1;
              this.inProgressTaskSearchResponse$$.next(searchResponse);
            }),
          );
        }
        return of(null);
      }),
    );
  }

  handleTitleChanged(event: TitleChangedEvent, status: Status): Observable<number> {
    return this.taskService.updateTitle(event).pipe(
      tap(() => {
        if (status === Status.InProgress) {
          const tasks: SalesTask[] = this.inProgressTasks$$.getValue();
          tasks.forEach((task) => {
            if (task.identity.taskId === event.task.identity.taskId) {
              task.title = event.title;
            }
          });
          this.inProgressTasks$$.next(tasks);
        } else if (status === Status.Completed) {
          const tasks: SalesTask[] = this.completedTasks$$.getValue();
          tasks.forEach((task) => {
            if (task.identity.taskId === event.task.identity.taskId) {
              task.title = event.title;
            }
          });
          this.completedTasks$$.next(tasks);
        }
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }
}
