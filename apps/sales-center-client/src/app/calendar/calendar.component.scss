@use 'design-tokens' as *;

.calendar-container {
  padding: 20px;
  .top-section {
    div {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .calendar-section {
    max-width: 1200px;
    margin: 0 auto;
  }
  .current-view-title {
    margin: 14px 0;
    mat-icon {
      vertical-align: middle;
      cursor: pointer;
    }
    mat-icon:hover {
      background: $light-gray;
    }
  }
}

.month-view-cell {
  .month-view-date {
    font-size: 1.2em;
    font-weight: 400;
    opacity: 0.5;
    margin: 15px 15px 10px 0;
    text-align: right;
  }
  .task-count {
    border: 1px solid $blue;
    border-radius: 15px;
    font-size: 12px;
    padding: 2px 0 2px 8px;
    margin-top: 4px;
  }
  .task-count:hover {
    background: $light-gray;
  }
}

.calendar-shimmer {
  width: 100%;
  height: 500px;
}

.month-cell-today {
  background: $lighter-gray;
  height: 100%;
}

.calendar-month-view {
  ::ng-deep mwl-calendar-month-cell {
    cursor: pointer;
  }
}

.title-link-container {
  display: flex;
  justify-content: space-between;
}

.link {
  color: $blue;
  cursor: pointer;
  font-weight: 400;
  font-size: 14px;
  margin-top: 16px;

  mat-icon {
    vertical-align: middle;
  }
}

.switch-view-link {
  display: block;
  text-align: right;
  font-size: $font-preset-5-size;
  color: $tertiary-font-color;
}

.mat-drawer-container {
  background-color: unset;
  height: 100%;
  width: 100%;
}

.sidepanel-content-container {
  min-width: 400px;
  max-width: 100vw;
  padding: 24px;
  padding-top: 0;
  min-height: 100%;
  background-color: $white !important;
}
