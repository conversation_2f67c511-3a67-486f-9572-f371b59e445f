import { NgModule } from '@angular/core';
import { DisplayDateComponent } from './display-date/display-date.component';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { DisplayInputComponent } from './display-input/display-input.component';
import { GalaxyWrapModule } from '@vendasta/galaxy/galaxy-wrap';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  imports: [
    CommonModule,
    MatFormFieldModule,
    MatDatepickerModule,
    MatIconModule,
    MatInputModule,
    GalaxyWrapModule,
    TranslateModule,
  ],
  declarations: [DisplayDateComponent, DisplayInputComponent],
  exports: [DisplayDateComponent, DisplayInputComponent],
  providers: [],
})
export class InlineEditModule {}
