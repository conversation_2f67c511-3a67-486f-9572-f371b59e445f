import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-display-input',
  templateUrl: './display-input.component.html',
  styleUrls: ['./display-input.component.scss'],
  standalone: false,
})
export class DisplayInputComponent {
  @Input() value: string;
  @Output() valueChanged: EventEmitter<string> = new EventEmitter<string>();
  editing = false;

  handleValueChanged(newValue: string): void {
    this.editing = false;
    if (this.value !== newValue) {
      this.value = newValue;
      this.valueChanged.emit(newValue);
    }
  }
}
