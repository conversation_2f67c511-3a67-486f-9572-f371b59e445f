import moment from 'moment';

export interface DateModelInterface {
  date: {
    year: number;
    month: number;
    day: number;
  };
}

export function formatDateString(date: Date, locale = 'default'): string {
  if (!date) {
    return '';
  }
  let timeZone = 'UTC';
  if (Intl.DateTimeFormat().resolvedOptions().timeZone) {
    timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  }
  if (date.getFullYear() !== new Date().getFullYear()) {
    return date.toLocaleString(locale, {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      timeZone: timeZone,
    });
  }
  return date.toLocaleString(locale, {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
    timeZone: timeZone,
  });
}

export function adjustBackToUTCTime(date: Date): Date {
  const offsetHours = date.getTimezoneOffset() / 60;
  date = moment(date).add(offsetHours, 'h').toDate();
  return date;
}
