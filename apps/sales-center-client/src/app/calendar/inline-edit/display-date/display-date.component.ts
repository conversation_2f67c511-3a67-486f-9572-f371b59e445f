import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { DateModelInterface } from './DisplayDate';
import { Observable, ReplaySubject } from 'rxjs';
import { MatDatepickerInputEvent } from '@angular/material/datepicker';

@Component({
  selector: 'app-display-date',
  templateUrl: './display-date.component.html',
  styleUrls: ['./display-date.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class DisplayDateComponent {
  private selectedDate$$ = new ReplaySubject<Date>(1);
  selectedDate$: Observable<Date> = this.selectedDate$$;

  @Input() set initialValue(initialValue: Date) {
    this.selectedDate$$.next(initialValue);
  }
  @Output() dateChange: EventEmitter<Date> = new EventEmitter();
  dateModel: DateModelInterface;

  dateChanged(event: MatDatepickerInputEvent<Date>): void {
    const newDate = event.value;
    this.selectedDate$$.next(newDate);
    this.dateChange.emit(newDate);
  }
}
