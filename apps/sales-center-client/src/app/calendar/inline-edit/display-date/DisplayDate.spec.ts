import { adjustBackToUTCTime, formatDateString } from './DisplayDate';

describe('DisplayDate', () => {
  describe('getTimezoneOffset', () => {
    it('should always be 0', () => {
      expect(new Date().getTimezoneOffset()).toBe(0);
    });
  });
  describe('adjustBackToUTCTime', () => {
    it('should convert CST date to UTC and return date as same day', () => {
      const initialDate = new Date('2020-01-01 00:00:00 CST');
      const expected = new Date('2020-01-01 6:00:00 UTC');
      const result = adjustBackToUTCTime(initialDate);
      expect(result).toEqual(expected);
    });
    it('should convert CDT date to UTC and return date as same day', () => {
      const initialDate = new Date('2020-01-01 00:00:00 CDT');
      const expected = new Date('2020-01-01 5:00:00 UTC');
      const result = adjustBackToUTCTime(initialDate);
      expect(result).toEqual(expected);
    });
    it('should convert CST date to UTC and return date as next day', () => {
      const initialDate = new Date('2020-01-01 20:00:00 CST');
      const expected = new Date('2020-01-02 02:00:00 UTC');
      const result = adjustBackToUTCTime(initialDate);
      expect(result).toEqual(expected);
    });
    it('should return date as UTC date if input date is UTC', () => {
      const initialDate = new Date('2020-01-01 08:00:00 UTC');
      const expected = new Date('2020-01-01 08:00:00 UTC');
      const result = adjustBackToUTCTime(initialDate);
      expect(result).toEqual(expected);
    });
    it('should return date as UTC previous day if input date is ACT', () => {
      const initialDate = new Date('2020-01-01 08:00:00 GMT+11');
      const expected = new Date('2019-12-31 21::00:00 UTC');
      const result = adjustBackToUTCTime(initialDate);
      expect(result).toEqual(expected);
    });
  });
  describe('formatDateString (en-US)', () => {
    it('should show date string with expected value if same day', () => {
      const initialDate = new Date('2020-01-01 00:00:00 CST');
      const expected = 'Wed, Jan 1, 2020';
      const result = formatDateString(initialDate, 'en-US');
      expect(result).toEqual(expected);
    });
    it('should show date string with expected date value if same year', () => {
      const initialDate = new Date('2020-01-01 22:00:00 CDT');
      const expected = 'Wed, Jan 1, 2020';
      const result = formatDateString(initialDate, 'en-US');
      const result2 = formatDateString(adjustBackToUTCTime(initialDate), 'en-US');
      expect(result).toEqual(expected);
      expect(result2).toEqual(expected);
    });
    it('should show date string with expected date value if different year', () => {
      const initialDateUTC = new Date('2019-01-04 00:00:00 UTC');
      const expectedUTC = 'Thu, Jan 3, 2019';
      const resultUTC = formatDateString(initialDateUTC, 'en-US');
      expect(resultUTC).toEqual(expectedUTC);
    });
  });
});
