@use 'design-tokens' as *;

$tiny-text: 12px;

.meetings-container {
  color: $secondary-font-color;
  .header {
    width: 100%;
    align-items: center;
    .time {
      margin-right: 2px;
      font-size: $tiny-text;
    }
    .disabled {
      color: $tertiary-font-color;
    }
    .provider-parent {
      margin-right: 22px;
      .provider {
        font-weight: bold;
        display: flex;
        align-items: center;
        mat-icon {
          margin-right: 4px;
        }
      }
    }
    .user {
      margin-right: 4px;
      font-size: $tiny-text;
    }
  }
  .comment-section {
    margin-bottom: 22px;
    .title {
      font-weight: bold;
      margin-bottom: 4px;
      color: $secondary-font-color;
    }
    .comments {
      color: $tertiary-font-color;
      margin-bottom: 22px;
    }
    .meeting-link {
      color: $tertiary-font-color;
      font-size: $tiny-text;
      .m-title {
        margin-right: 8px;
      }
    }
  }

  .contact-section {
    margin-bottom: 24px;

    .title {
      font-weight: bold;
      margin-bottom: 8px;
      color: $secondary-font-color;
    }
  }

  .copy-section {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    mat-icon {
      margin-right: 2px;
    }
  }

  .shimmer {
    margin-top: 16px;
    width: 100%;
    height: 500px;
  }
}

#copy-button {
  color: $blue;
}
