import { Component, EventEmitter, Input, Output } from '@angular/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { TaskInterface } from '@vendasta/task';
import { openSecureNewTab } from '../../../common/secure';
import { DescriptionChangedEvent, SalesTask } from '../../constants';

@Component({
  selector: 'app-meeting-task-table',
  templateUrl: './meeting-task-table.component.html',
  styleUrls: ['./meeting-task-table.component.scss'],
  standalone: false,
})
export class MeetingTaskTableComponent {
  @Input() tasks: SalesTask[];
  @Input() loadingTasks = false;
  @Input() hasMore = false;
  @Output() descriptionChanged: EventEmitter<DescriptionChangedEvent> = new EventEmitter<DescriptionChangedEvent>();
  @Output() loadMore: EventEmitter<any> = new EventEmitter<any>();

  constructor(private readonly alertService: SnackbarService) {}

  loadMoreTasks(): void {
    this.loadMore.emit();
  }

  handleDescriptionChanged(newDescription: string, task: TaskInterface): void {
    this.descriptionChanged.emit({ description: newDescription, task: task });
  }

  handleMeetingLinkClicked(event: MouseEvent, link: string): void {
    if (event.ctrlKey || event.metaKey) {
      this.openMeetingLink(link);
      this.alertService.openSuccessSnack('MEETINGS_SHARED.ALERTS.LINK_OPENED');
    } else {
      this.alertService.openSuccessSnack('MEETINGS_SHARED.ALERTS.LINK_COPIED');
    }
  }

  openMeetingLink(link: string): void {
    const w = openSecureNewTab(link);
    if (w && w.focus) {
      w.focus();
    }
  }
}
