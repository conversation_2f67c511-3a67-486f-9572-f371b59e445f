<div class="meetings-container">
  <ng-container *ngIf="tasks">
    <ng-container *ngIf="!loadingTasks || tasks.length > 0; else shimmer">
      <ng-container *ngIf="tasks.length > 0">
        <mat-accordion>
          <div *ngFor="let task of tasks">
            <mat-expansion-panel class="mat-elevation-z0">
              <mat-expansion-panel-header>
                <mat-panel-title>
                  <div class="header row">
                    <div class="time col col-xs-3">
                      {{ task.dueDate | date: 'shortTime' }}
                      <span *ngIf="task?.meeting?.endDate">- {{ task?.meeting?.endDate | date: 'shortTime' }}</span>
                    </div>
                    <div class="provider-parent col col-xs-6" (click)="$event.stopPropagation()">
                      <a
                        *ngIf="task?.meeting?.meetingURL"
                        class="provider"
                        (click)="(!!task?.meeting?.meetingURL)"
                        href="{{ task?.meeting?.meetingURL || '#' }}"
                        target="_blank"
                      >
                        <mat-icon
                          svgIcon="{{
                            !!task?.meeting?.meetingURL ? (task?.meeting?.meetingURL | meetingProvider).icon : ''
                          }}"
                        >
                          {{ !!task?.meeting?.meetingURL ? '' : (task?.meeting?.meetingURL | meetingProvider).icon }}
                        </mat-icon>
                        {{ (task?.meeting?.meetingURL | meetingProvider).label }}
                      </a>
                    </div>
                    <div class="user col col-xs-3">
                      {{ task?.meeting?.contactName || '-' }}
                    </div>
                  </div>
                </mat-panel-title>
              </mat-expansion-panel-header>
              <div class="contact-section">
                <div class="title">{{ 'COMMON.CONTACT' | translate }}</div>
                <div class="email">
                  <a href="{{ 'mailto:' + task?.meeting?.contactEmail }}">
                    {{ task?.meeting?.contactEmail }}
                  </a>
                </div>
                <div class="phone">
                  <a href="{{ 'tel:+' + task?.meeting?.contactPhone }}">
                    {{ task?.meeting?.contactPhone }}
                  </a>
                </div>
              </div>
              <div class="comment-section">
                <div class="title">
                  {{ 'CALENDAR.MEETING_LIST.COMMENTS' | translate }}
                </div>
                <div class="comments">{{ task.notes || '—' }}</div>
                <div *ngIf="task?.meeting?.meetingURL" class="meeting-link">
                  <span class="m-title">{{ 'CALENDAR.MEETING_LIST.MEETING_LINK' | translate }}:</span>
                  <a href="{{ task?.meeting?.meetingURL || '#' }}" target="_blank">
                    {{ task?.meeting?.meetingURL || '-' }}
                  </a>
                  <button
                    id="copy-button"
                    mat-button
                    color="primary"
                    [cdkCopyToClipboard]="task?.meeting?.meetingURL"
                    (click)="handleMeetingLinkClicked($event, task?.meeting?.meetingURL)"
                  >
                    <mat-icon class="material-icons-outlined">file_copy</mat-icon>
                    <span>{{ 'COMMON.ACTION_LABELS.COPY' | translate }}</span>
                  </button>
                </div>
              </div>
              <mat-form-field appearance="outline" class="description col col-xs-12">
                <mat-label>{{ 'COMMON.NOTES_LABEL' | translate }}</mat-label>
                <textarea
                  matInput
                  cdkTextareaAutosize
                  #description
                  cdkAutosizeMinRows="3"
                  cdkAutosizeMaxRows="8"
                  (change)="handleDescriptionChanged(description.value, task)"
                  matInput
                  [value]="task.description || ''"
                ></textarea>
              </mat-form-field>
              <a
                *ngIf="task?.meeting?.meetingURL"
                href="{{ task?.meeting?.meetingURL }}"
                class="copy-section"
                target="_blank"
                rel="noreferrer"
              >
                <button id="meeting-task-table__join-meet" mat-stroked-button color="primary">
                  <mat-icon
                    svgIcon="{{
                      !!task?.meeting?.meetingURL ? (task?.meeting?.meetingURL | meetingProvider).icon : ''
                    }}"
                  >
                    {{ !!task?.meeting?.meetingURL ? '' : (task?.meeting?.meetingURL | meetingProvider).icon }}
                  </mat-icon>
                  {{ (task?.meeting?.meetingURL | meetingProvider).label }}
                </button>
              </a>
            </mat-expansion-panel>
          </div>
        </mat-accordion>
        <div *ngIf="hasMore" class="load-more-button-container">
          <button class="load-more-button" mat-raised-button color="primary" (click)="loadMoreTasks()">
            {{ 'CALENDAR.TASK_LIST.LOAD_MORE' | translate }}
          </button>
        </div>
      </ng-container>
    </ng-container>
  </ng-container>
</div>
<ng-template #shimmer>
  <div class="stencil-shimmer shimmer"></div>
</ng-template>
