import { DomainKeyValue, KeyValueTypes, Status } from '@vendasta/task';
import { of } from 'rxjs';
import { LoggedInUserInfo } from '../../logged-in-user-info';
import { MeetingStatus, SalesTask } from '../constants';
import { TaskService } from '../task.service';
import { MeetingTaskListService } from './meeting-task-list.service';

const userInfo: LoggedInUserInfo = new LoggedInUserInfo(
  'UID-123',
  false,
  true,
  'ABC',
  'market',
  '123',
  null,
  null,
  null,
  null,
  null,
  null,
  null,
);
const data = new DomainKeyValue();
data.set('endDate', [new Date(2020, 1, 2)], KeyValueTypes.TIME);
data.set('meetingURL', ['meet.google.com'], KeyValueTypes.STRING);
data.set('contactName', ['Demo Guy'], KeyValueTypes.STRING);
data.set('contactPhone', ['***********'], KeyValueTypes.STRING);
data.set('contactEmail', ['<EMAIL>'], KeyValueTypes.STRING);

const upcomingMeetingTask: SalesTask = {
  status: Status.InProgress,
  identity: {
    namespace: 'partner/ABC/account-group/AG-123',
    parentPath: '/',
    taskId: 'TK-123',
  },
  title: 'Task In Progress',
  data: data.toKeyValue(),
  dueDate: new Date(2020, 1, 2),
  assignees: [userInfo.salespersonId],
};

const previousMeetingTask: SalesTask = {
  status: Status.Completed,
  identity: {
    namespace: 'partner/ABC',
    parentPath: '/',
    taskId: 'TK-456',
  },
  title: 'Task Completed',
  dueDate: new Date(2020, 1, 1),
  data: data.toKeyValue(),
  assignees: [userInfo.salespersonId],
};

describe('MeetingTaskListService', () => {
  let service: MeetingTaskListService;
  let taskService: TaskService;
  beforeEach(() => {
    taskService = {
      loadTasks: jest.fn(() => of([upcomingMeetingTask, previousMeetingTask])),
      updateDescription: jest.fn(() => of(1)),
    } as unknown as TaskService;
    service = new MeetingTaskListService(taskService, of('ABC'), of(userInfo));
  });

  describe('handleDescriptionChanged', () => {
    it('should call updateDescription with expected paprameters', async () => {
      const descriotionRequest = {
        description: 'New Description',
        task: upcomingMeetingTask,
      };
      await service.handleDescriptionChanged(descriotionRequest, MeetingStatus.Upcoming);
      expect(taskService.updateDescription).toBeCalledWith(descriotionRequest);
    });
  });
  describe('fetchMeetingData', () => {
    it('should return results with expected paprameters', async () => {
      const expected = [
        {
          ...upcomingMeetingTask,
          meeting: {
            contactName: 'Demo Guy',
            meetingURL: 'meet.google.com',
            endDate: new Date(2020, 1, 2),
            contactPhone: '***********',
            contactEmail: '<EMAIL>',
          },
        },
      ];
      service.fetchMeetingData([upcomingMeetingTask]);
      expect(service.fetchMeetingData([upcomingMeetingTask])).toEqual(expected);
    });
  });
});
