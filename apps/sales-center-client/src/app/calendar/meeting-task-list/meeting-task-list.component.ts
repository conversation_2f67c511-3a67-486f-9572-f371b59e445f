import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Observable, Subscription } from 'rxjs';
import { take } from 'rxjs/operators';
import { TasksService } from '../../tasks';
import { DescriptionChangedEvent, MeetingStatus, SalesTask } from '../constants';
import { MeetingTaskListService } from './meeting-task-list.service';

@Component({
  selector: 'app-meeting-task-list',
  templateUrl: './meeting-task-list.component.html',
  styleUrls: ['./meeting-task-list.component.scss'],
  providers: [MeetingTaskListService, TasksService],
  standalone: false,
})
export class MeetingTaskListComponent implements OnInit, OnDestroy {
  @Input() date$: Observable<Date>;
  // TODO: Rename as close conflicts with a native output
  // tslint:disable-next-line:no-output-native
  @Output() closed: EventEmitter<null> = new EventEmitter<null>();
  meetingStatuses = MeetingStatus;
  activeTab: string = MeetingStatus.Upcoming;
  upcomingMeetingTasks$: Observable<SalesTask[]>;
  previousMeetingTasks$: Observable<SalesTask[]>;
  totalUpcomingMeetings$: Observable<number>;
  totalPreviousMeetings$: Observable<number>;
  upcomingHasMore$: Observable<boolean>;
  previousHasMore$: Observable<boolean>;
  upcomingLoading$: Observable<boolean>;
  previousLoading$: Observable<boolean>;

  private readonly subscriptions: Subscription[] = [];

  constructor(
    private readonly meetingTaskListService: MeetingTaskListService,
    private readonly alertService: SnackbarService,
    private readonly translateService: TranslateService,
  ) {
    this.upcomingMeetingTasks$ = this.meetingTaskListService.upcomingMeetingTasks$;
    this.previousMeetingTasks$ = this.meetingTaskListService.previousMeetingTasks$;
    this.totalUpcomingMeetings$ = this.meetingTaskListService.totalUpcomingMeetings$;
    this.totalPreviousMeetings$ = this.meetingTaskListService.totalPreviousMeetings$;
    this.upcomingHasMore$ = this.meetingTaskListService.upcomingHasMore$;
    this.previousHasMore$ = this.meetingTaskListService.previousHasMore$;
    this.upcomingLoading$ = this.meetingTaskListService.upcomingLoading$;
    this.previousLoading$ = this.meetingTaskListService.previousLoading$;
  }

  ngOnInit(): void {
    this.subscriptions.push(this.date$.subscribe((date) => (this.meetingTaskListService.date = date)));
  }

  closeSidePanel(): void {
    this.closed.emit();
  }

  loadMore(status: MeetingStatus): void {
    if (status === MeetingStatus.Upcoming) {
      this.meetingTaskListService.loadMoreUpcomingMeetings().pipe(take(1)).subscribe();
    } else {
      this.meetingTaskListService.loadMorePreviousMeetings().pipe(take(1)).subscribe();
    }
  }

  descriptionChanged(event: DescriptionChangedEvent, meetingStatus: MeetingStatus): void {
    this.subscriptions.push(
      this.meetingTaskListService.handleDescriptionChanged(event, meetingStatus).subscribe({
        next: () => this.alertService.openSuccessSnack('CALENDAR.MEETING_LIST.DESCRIPTION_CHANGE_SUCCESS'),
        error: () => this.alertService.openErrorSnack('CALENDAR.MEETING_LIST.DESCRIPTION_CHANGE_ERROR'),
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }
}
