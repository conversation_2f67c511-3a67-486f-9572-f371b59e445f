@use 'design-tokens' as *;

.task-list-container {
  width: 600px;
  mat-icon {
    vertical-align: middle;
  }
  .nav-actions {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    button {
      color: $blue;
      padding: 0 $spacing-2 0 0;
    }
    .nav-actions-item {
      padding-right: $spacing-2;
    }
  }
}

.close-icon {
  color: $primary-font-color;
  vertical-align: middle;
  cursor: pointer;
}
.close-icon:hover {
  background: $light-grey;
}

.top-row {
  margin-top: 16px;
  .date-header {
    color: $black;
    font-size: $font-preset-2-size;
  }
  .close-drawer-button {
    text-align: end;
    align-self: center;
  }
}
