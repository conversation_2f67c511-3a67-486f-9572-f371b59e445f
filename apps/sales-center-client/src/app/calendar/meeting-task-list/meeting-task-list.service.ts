import { Inject, Injectable, OnDestroy } from '@angular/core';
import { DomainKeyValue, KeyValueTypes, SearchTaskResponseInterface, Status } from '@vendasta/task';
import moment from 'moment';
import { BehaviorSubject, EMPTY, Observable, Subscription, combineLatest, iif } from 'rxjs';
import { map, switchMap, tap } from 'rxjs/operators';
import { PARTNER_ID_TOKEN } from '../../common/providers';
import { USER_INFO_TOKEN } from '../../core/feature-flag.service';
import { LoggedInUserInfo } from '../../logged-in-user-info';
import {
  DescriptionChangedEvent,
  EXCLUDE_COMPLETED,
  EventType,
  MeetingInfo,
  MeetingStatus,
  SalesTask,
  TaskSearchResponse,
} from '../constants';
import { TaskService } from '../task.service';

const MEETING_SORT = 'due_date';

@Injectable()
export class MeetingTaskListService implements OnD<PERSON>roy {
  private readonly date$$: BehaviorSubject<Date | null> = new BehaviorSubject<Date | null>(null);
  private readonly upcomingMeetingTasks$$: BehaviorSubject<SalesTask[]> = new BehaviorSubject<SalesTask[]>([]);
  private readonly previousMeetingTasks$$: BehaviorSubject<SalesTask[]> = new BehaviorSubject<SalesTask[]>([]);
  private readonly upcomingSearchResponse$$: BehaviorSubject<TaskSearchResponse | null> =
    new BehaviorSubject<TaskSearchResponse | null>(null);
  private readonly previousSearchResponse$$: BehaviorSubject<TaskSearchResponse | null> =
    new BehaviorSubject<TaskSearchResponse | null>(null);
  private readonly upcomingLoading$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  private readonly previousLoading$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  private readonly subscriptions: Subscription[] = [];

  constructor(
    private readonly taskService: TaskService,
    @Inject(PARTNER_ID_TOKEN) private readonly partnerId$: Observable<string>,
    @Inject(USER_INFO_TOKEN) private readonly userInfo$: Observable<LoggedInUserInfo>,
  ) {
    this.subscriptions.push(
      this.date$$
        .pipe(switchMap((date) => iif(() => !!date, this.getUpcomingTasks(date, EventType.Meeting), EMPTY)))
        .subscribe(this.upcomingMeetingTasks$$),
      this.date$$
        .pipe(switchMap((date) => iif(() => !!date, this.getPreviousTasks(date, EventType.Meeting), EMPTY)))
        .subscribe(this.previousMeetingTasks$$),
    );
  }

  set date(date: Date) {
    this.date$$.next(date);
  }

  get upcomingMeetingTasks$(): Observable<SalesTask[]> {
    return this.upcomingMeetingTasks$$.asObservable();
  }

  get previousMeetingTasks$(): Observable<SalesTask[]> {
    return this.previousMeetingTasks$$.asObservable();
  }

  get totalUpcomingMeetings$(): Observable<number> {
    return this.upcomingSearchResponse$$.asObservable().pipe(map((response) => (response ? response.total : 0)));
  }

  get totalPreviousMeetings$(): Observable<number> {
    return this.previousSearchResponse$$.asObservable().pipe(map((response) => (response ? response.total : 0)));
  }

  get upcomingHasMore$(): Observable<boolean> {
    return this.upcomingSearchResponse$$.asObservable().pipe(map((response) => (response ? response.hasMore : false)));
  }

  get previousHasMore$(): Observable<boolean> {
    return this.previousSearchResponse$$.asObservable().pipe(map((response) => (response ? response.hasMore : false)));
  }

  get upcomingLoading$(): Observable<boolean> {
    return this.upcomingLoading$$.asObservable();
  }

  get previousLoading$(): Observable<boolean> {
    return this.previousLoading$$.asObservable();
  }

  private getPreviousTasks(date: Date, type: string, cursor?: string): Observable<SalesTask[]> {
    this.previousLoading$$.next(true);
    let customEnd: Date = null;
    if (moment(date).isSame(moment(), 'day')) {
      customEnd = new Date();
    }
    return combineLatest([this.partnerId$, this.userInfo$]).pipe(
      switchMap(([partnerId, userInfo]) => {
        if (moment(date).isAfter(moment(), 'day')) {
          this.previousLoading$$.next(false);
          return [];
        }
        return this.taskService
          .loadTasks(
            partnerId,
            userInfo.salespersonId,
            date,
            EXCLUDE_COMPLETED as Status,
            type,
            cursor,
            MEETING_SORT,
            null,
            customEnd,
          )
          .pipe(
            map((searchResponse: SearchTaskResponseInterface) => {
              const taskSearchResponse: TaskSearchResponse = {
                total: searchResponse.hits || 0,
                cursor: searchResponse.nextCursor,
                hasMore: searchResponse.hasMore,
              };
              this.previousSearchResponse$$.next(taskSearchResponse);
              return searchResponse.tasks;
            }),
          );
      }),
      map(this.fetchMeetingData),
      tap(() => this.previousLoading$$.next(false)),
    );
  }

  private getUpcomingTasks(date: Date, type: string, cursor?: string): Observable<SalesTask[]> {
    this.upcomingLoading$$.next(true);
    let customStart: Date = null;
    if (moment(date).isSame(moment(), 'day')) {
      customStart = new Date();
    }
    return combineLatest([this.partnerId$, this.userInfo$]).pipe(
      switchMap(([partnerId, userInfo]) => {
        if (moment(date).isBefore(moment(), 'day')) {
          this.upcomingLoading$$.next(false);
          return [];
        }
        return this.taskService
          .loadTasks(
            partnerId,
            userInfo.salespersonId,
            date,
            EXCLUDE_COMPLETED as Status,
            type,
            cursor,
            MEETING_SORT,
            customStart,
          )
          .pipe(
            map((searchResponse: SearchTaskResponseInterface) => {
              const taskSearchResponse: TaskSearchResponse = {
                total: searchResponse.hits || 0,
                cursor: searchResponse.nextCursor,
                hasMore: searchResponse.hasMore,
              };
              this.upcomingSearchResponse$$.next(taskSearchResponse);
              return searchResponse.tasks;
            }),
          );
      }),
      map(this.fetchMeetingData),
      tap(() => this.upcomingLoading$$.next(false)),
    );
  }

  fetchMeetingData(tasks: SalesTask[]): SalesTask[] {
    if (!tasks) {
      return tasks;
    }
    return tasks.map((task) => {
      const domainKeyValue: DomainKeyValue = DomainKeyValue.fromKeyValue(task.data);
      const contactName: string = (domainKeyValue.get('contactName', KeyValueTypes.STRING)[0] as string) || null;
      const endDate: Date = (domainKeyValue.get('endDate', KeyValueTypes.TIME)[0] as Date) || null;
      const meetingURL: string = (domainKeyValue.get('meetingURL', KeyValueTypes.STRING)[0] as string) || null;
      const contactPhone: string = (domainKeyValue.get('contactPhone', KeyValueTypes.STRING)[0] as string) || null;
      const contactEmail: string = (domainKeyValue.get('contactEmail', KeyValueTypes.STRING)[0] as string) || null;
      const meetingInfo: MeetingInfo = {
        contactName,
        endDate,
        meetingURL,
        contactPhone,
        contactEmail,
      };
      return {
        ...task,
        meeting: meetingInfo,
      };
    });
  }

  loadMoreUpcomingMeetings(): Observable<any> {
    return this.getUpcomingTasks(
      this.date$$.getValue(),
      EventType.Meeting,
      this.upcomingSearchResponse$$.getValue().cursor,
    ).pipe(
      tap((newTasks) => {
        let existingTasks: SalesTask[] = this.upcomingMeetingTasks$$.getValue();
        existingTasks = existingTasks.concat(newTasks);
        this.upcomingMeetingTasks$$.next(existingTasks);
      }),
    );
  }

  loadMorePreviousMeetings(): Observable<any> {
    return this.getPreviousTasks(
      this.date$$.getValue(),
      EventType.Meeting,
      this.previousSearchResponse$$.getValue().cursor,
    ).pipe(
      tap((newTasks) => {
        let existingTasks: SalesTask[] = this.previousMeetingTasks$$.getValue();
        existingTasks = existingTasks.concat(newTasks);
        this.previousMeetingTasks$$.next(existingTasks);
      }),
    );
  }

  handleDescriptionChanged(event: DescriptionChangedEvent, meetingStatus: MeetingStatus): Observable<number> {
    return this.taskService.updateDescription(event).pipe(
      tap(() => {
        if (meetingStatus === MeetingStatus.Upcoming) {
          const tasks: SalesTask[] = this.upcomingMeetingTasks$$.getValue();
          tasks.forEach((task) => {
            if (task.identity.taskId === event.task.identity.taskId) {
              task.description = event.description;
            }
          });
          this.upcomingMeetingTasks$$.next(tasks);
        } else if (meetingStatus === MeetingStatus.Previous) {
          const tasks: SalesTask[] = this.previousMeetingTasks$$.getValue();
          tasks.forEach((task) => {
            if (task.identity.taskId === event.task.identity.taskId) {
              task.description = event.description;
            }
          });
          this.previousMeetingTasks$$.next(tasks);
        }
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }
}
