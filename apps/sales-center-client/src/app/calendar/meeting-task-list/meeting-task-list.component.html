<div class="task-list-container">
  <div class="row">
    <div class="col col-xs-12">
      <div class="row top-row">
        <div class="col col-xs-10">
          <div class="date-header" *ngIf="date$ | async as date">
            {{ date | date : 'EEEE, MMM d, y' }}
          </div>
        </div>
        <div class="col col-xs-2 close-drawer-button">
          <mat-icon class="close-icon" (click)="closeSidePanel()">close</mat-icon>
        </div>
      </div>
    </div>
    <div class="col col-xs-12">
      <nav mat-tab-nav-bar>
        <a
          mat-tab-link
          (click)="activeTab = meetingStatuses.Upcoming"
          [active]="activeTab === meetingStatuses.Upcoming"
        >
          <span>
            {{ (totalUpcomingMeetings$ | async) || 0 }}
            {{ 'CALENDAR.MEETING_LIST.UPCOMING' | translate }}
            {{ ((totalUpcomingMeetings$ | async) === 1 ? 'CALENDAR.MEETING' : 'CALENDAR.MEETING_PLURAL') | translate }}
          </span>
        </a>
        <a
          mat-tab-link
          (click)="activeTab = meetingStatuses.Previous"
          [active]="activeTab === meetingStatuses.Previous"
        >
          <span>
            {{ (totalPreviousMeetings$ | async) || 0 }}
            {{ 'CALENDAR.MEETING_LIST.PREVIOUS' | translate }}
            {{ ((totalPreviousMeetings$ | async) === 1 ? 'CALENDAR.MEETING' : 'CALENDAR.MEETING_PLURAL') | translate }}
          </span>
        </a>
        <div class="nav-actions">
          <meeting-scheduler-copy-personal-booking-link-dropdown></meeting-scheduler-copy-personal-booking-link-dropdown>
        </div>
      </nav>
    </div>
  </div>
  <div [ngSwitch]="activeTab">
    <ng-container *ngSwitchCase="meetingStatuses.Upcoming">
      <app-meeting-task-table
        [tasks]="upcomingMeetingTasks$ | async"
        (loadMore)="loadMore(meetingStatuses.Upcoming)"
        (descriptionChanged)="descriptionChanged($event, meetingStatuses.Upcoming)"
        [hasMore]="upcomingHasMore$ | async"
        [loadingTasks]="upcomingLoading$ | async"
      ></app-meeting-task-table>
    </ng-container>
    <ng-container *ngSwitchCase="meetingStatuses.Previous">
      <app-meeting-task-table
        [tasks]="previousMeetingTasks$ | async"
        (loadMore)="loadMore(meetingStatuses.Previous)"
        (descriptionChanged)="descriptionChanged($event, meetingStatuses.Previous)"
        [hasMore]="previousHasMore$ | async"
        [loadingTasks]="previousLoading$ | async"
      ></app-meeting-task-table>
    </ng-container>
  </div>
</div>
