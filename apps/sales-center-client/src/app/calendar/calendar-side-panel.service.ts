import { Injectable, TemplateRef, ViewContainerRef } from '@angular/core';
import { MatSidenav, MatDrawerToggleResult } from '@angular/material/sidenav';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable()
export class CalendarSidePanelService {
  private panel: MatSidenav;
  private vcf: ViewContainerRef;
  private readonly isOpen$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  get isNowOpened$(): Observable<boolean> {
    return this.isOpen$$.asObservable();
  }

  setPanel(sidenav: MatSidenav): void {
    this.panel = sidenav;
  }

  setContentVcf(viewContainerRef: ViewContainerRef): void {
    this.vcf = viewContainerRef;
  }

  private createView(template: TemplateRef<any>): void {
    this.vcf.clear();
    this.vcf.createEmbeddedView(template);
  }

  open(template: TemplateRef<any>): Promise<MatDrawerToggleResult> {
    this.createView(template);
    this.isOpen$$.next(true);
    return this.panel.open();
  }

  close(): Promise<MatDrawerToggleResult> {
    this.vcf.clear();
    this.isOpen$$.next(false);
    return this.panel.close();
  }

  toggle(): Promise<MatDrawerToggleResult> {
    return this.panel.toggle();
  }
}
