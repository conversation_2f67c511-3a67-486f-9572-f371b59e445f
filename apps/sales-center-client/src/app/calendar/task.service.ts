import { Inject, Injectable, OnDestroy } from '@angular/core';
import { AccountGroup, AccountGroupService, ProjectionFilter } from '@galaxy/account-group';
import { PartnerMarket } from '@galaxy/types';
import { TranslateService } from '@ngx-translate/core';
import { PersonaType } from '@vendasta/iam';
import { BusinessService } from '@vendasta/sales-v2';
import {
  GenerateTaskRequestPersonaInterface,
  GetMetricsFiltersInterface,
  Metric,
  MetricResponsesMetricResponse,
  Persona,
  RangeField,
  SearchTaskRequestInterface,
  SearchTaskResponseInterface,
  SortDirection,
  Status,
  TaskInterface,
  TaskSdkService,
} from '@vendasta/task';
import { CalendarEvent } from 'angular-calendar';
import { endOfDay, startOfDay } from 'date-fns';
import { BehaviorSubject, Observable, Subscription, combineLatest, of, zip } from 'rxjs';
import { catchError, combineAll, map, shareReplay, switchMap, take } from 'rxjs/operators';
import { AllContactFields } from '../common/contacts/contact-field.enum';
import { ContactsV2Service } from '../common/contacts/contacts-v2.service';
import { USER_INFO_TOKEN, USER_PARTNER_MARKET_TOKEN } from '../core/feature-flag.service';
import { LoggedInUserInfo } from '../logged-in-user-info';
import { TasksService } from '../tasks/tasks.service';
import {
  AccountGroupAndContacts,
  DescriptionChangedEvent,
  EXCLUDE_MEETINGS,
  EventType,
  SalesTask,
  StatusChangedEvent,
  TitleChangedEvent,
  calendarColors,
} from './constants';

const DATE_TODAY = new Date();
const DEFAULT_DATE_RANGE = {
  start: new Date(DATE_TODAY.getFullYear(), DATE_TODAY.getMonth(), 1),
  end: new Date(DATE_TODAY.getFullYear(), DATE_TODAY.getMonth() + 1, 0),
};
const ACCESS = [PersonaType.sales_person];
const PAGE_SIZE = 25;

@Injectable()
export class TaskService implements OnDestroy {
  private readonly dateRange$$: BehaviorSubject<{ start: Date; end: Date }> = new BehaviorSubject<{
    start: Date;
    end: Date;
  }>(DEFAULT_DATE_RANGE);
  private readonly calendarEvents$$: BehaviorSubject<CalendarEvent[]> = new BehaviorSubject<CalendarEvent[]>([]);
  readonly calendarEvents$: Observable<CalendarEvent[]> = this.calendarEvents$$;

  private readonly subscriptions: Subscription[] = [];

  private partnerId$: Observable<string>;

  constructor(
    private readonly taskSdk: TaskSdkService,
    @Inject(USER_INFO_TOKEN) private readonly userInfo$: Observable<LoggedInUserInfo>,
    private readonly translateService: TranslateService,
    private readonly contactsService: ContactsV2Service,
    private readonly accountGroupService: AccountGroupService,
    private readonly businessService: BusinessService,
    @Inject(USER_PARTNER_MARKET_TOKEN) private readonly partnerMarket$: Observable<PartnerMarket>,
  ) {
    this.partnerId$ = partnerMarket$.pipe(map((pm) => pm.partnerId));
    this.buildCalendarEvents();
  }

  buildCalendarEvents(): void {
    const fetchTaskMetrics$ = combineLatest([this.partnerId$, this.userInfo$, this.dateRange$$]).pipe(
      switchMap(([partnerId, user, dateRange]) =>
        this.getTaskMetrics(partnerId, user, EXCLUDE_MEETINGS, dateRange.start, dateRange.end),
      ),
      map((metricsResponse) => this.convertToCalendarEvent(metricsResponse, EventType.Task)),
      shareReplay(1),
    );
    const fetchMeetingsMetrics$ = combineLatest([this.partnerId$, this.userInfo$, this.dateRange$$]).pipe(
      switchMap(([partnerId, user, dateRange]) =>
        this.getTaskMetrics(partnerId, user, EventType.Meeting, dateRange.start, dateRange.end),
      ),
      map((metricsResponse) => this.convertToCalendarEvent(metricsResponse, EventType.Meeting)),
      shareReplay(1),
    );
    this.subscriptions.push(
      zip(fetchTaskMetrics$, fetchMeetingsMetrics$)
        .pipe(map((res) => [].concat(...res)))
        .subscribe(this.calendarEvents$$),
    );
  }

  get dateRange(): { start: Date; end: Date } {
    return this.dateRange$$.getValue();
  }

  setDateRange(date: Date): void {
    this.dateRange$$.next({
      start: new Date(date.getFullYear(), date.getMonth(), 1),
      end: new Date(date.getFullYear(), date.getMonth() + 1, 0),
    });
  }

  refreshCalendar(): void {
    // kick off combineLatest in constructor to fetch updated calendar events
    this.dateRange$$.next(this.dateRange);
  }

  convertToCalendarEvent(responses: MetricResponsesMetricResponse[], type: EventType): CalendarEvent[] {
    if (!responses) {
      return [];
    }
    responses = responses.filter((response) => response.count.count);
    return responses.map((response) => {
      const taskDateString = response.count.key.replace('Z', '');
      const startDateTime: Date = new Date(taskDateString);
      const endDateTime: Date = new Date(taskDateString);
      endDateTime.setHours(23, 59, 59);
      const count: number = response.count.count;
      const taskText =
        count === 1
          ? this.translateService.instant(`CALENDAR.${type.toUpperCase()}`)
          : this.translateService.instant(`CALENDAR.${type.toUpperCase()}_PLURAL`);
      const event: CalendarEvent = {
        title: `${count.toString()} ${taskText}`,
        color: calendarColors[type],
        start: startDateTime,
        end: endDateTime,
        meta: { type: type },
      };
      return event;
    });
  }

  getTaskMetrics(partnerId: string, user: LoggedInUserInfo, types: string, start: Date, end: Date): Observable<any> {
    const startDate: Date = startOfDay(start);
    const endDate: Date = endOfDay(end);
    const filters: GetMetricsFiltersInterface = {
      metadata: { keyValues: [{ key: 'partner', values: [{ stringValue: partnerId }] }] },
      rangeStart: startDate,
      rangeEnd: endDate,
      assignees: [user.salespersonId],
      types: [types],
      dateRangeField: RangeField.RANGE_FIELD_DUE_DATE,
      access: ACCESS,
    };
    return this.taskSdk.getMetric(Metric.METRIC_PER_DAY, filters);
  }

  updateDueDate(newDate: Date, task: TaskInterface): Observable<number> {
    return this.taskSdk.setDueDate(
      task.identity.namespace,
      task.identity.parentPath,
      task.identity.taskId,
      newDate,
      task.version,
    );
  }

  loadTasks(
    partnerId: string,
    userId: string,
    date: Date,
    status: Status,
    type: string,
    cursor?: string,
    sortField = 'title',
    customStartDate?: Date,
    customEndDate?: Date,
  ): Observable<SearchTaskResponseInterface> {
    const rangeStart: Date = customStartDate ?? startOfDay(date);
    const rangeEnd: Date = customEndDate ?? endOfDay(date);
    const request: SearchTaskRequestInterface = {
      pageSize: PAGE_SIZE,
      metadata: { keyValues: [{ key: 'partner', values: [{ stringValue: partnerId }] }] },
      assignees: [userId],
      rangeStart: rangeStart,
      rangeEnd: rangeEnd,
      dateRangeField: RangeField.RANGE_FIELD_DUE_DATE,
      status: [status],
      types: type ? [type] : null,
      cursor: cursor,
      sorts: [
        {
          field: sortField,
          direction: SortDirection.SORT_DIRECTION_ASCENDING,
        },
      ],
    };
    return this.taskSdk.search(request);
  }

  createTask(title: string, accountGroup: AccountGroup, dueDate: Date): Observable<SalesTask> {
    return combineLatest([this.partnerId$, this.userInfo$]).pipe(
      switchMap(([partnerId, userInfo]) => {
        const namespace = accountGroup
          ? TaskSdkService.buildAccountGroupNamespace(partnerId, accountGroup.accountGroupId)
          : TaskSdkService.buildPartnerNamespace(partnerId);
        const taskId = TaskSdkService.generateTaskId();
        const generateTaskRequest: GenerateTaskRequestPersonaInterface = {
          namespace: namespace,
          taskId: taskId,
          title: title,
          status: Status.InProgress,
          taskType: '',
          access: Persona.salesPerson,
          dueDate: dueDate,
          assignees: [userInfo.salespersonId],
        };
        return this.taskSdk.generate(generateTaskRequest).pipe(
          map(() => {
            return {
              identity: {
                namespace: namespace,
                parentPath: '/',
                taskId: taskId,
              },
              taskId: taskId,
              title: title,
              status: Status.InProgress,
              taskType: '',
              dueDate: dueDate,
              assignees: [userInfo.salespersonId],
            } as SalesTask;
          }),
        );
      }),
      take(1),
    );
  }

  fetchContactsAndAddToTasks(tasks: SalesTask[]): Observable<SalesTask[]> {
    if (!tasks) {
      return of(tasks);
    }

    const accountGroupIds = tasks.map((t) => t.accountGroupId);
    const uniqueAccountGroupIds: string[] = [...new Set(accountGroupIds)];
    if (uniqueAccountGroupIds.length === 0) {
      return of(tasks);
    }

    const contacts$List: Observable<AccountGroupAndContacts | null>[] = uniqueAccountGroupIds.map((accountGroupId) => {
      if (accountGroupId) {
        return this.contactsService.list$(accountGroupId, ...AllContactFields).pipe(
          catchError((err) => {
            console.warn('failed to get contacts, not adding all contacts data to task', err);
            return of(null);
          }),
          map((agContactsOrNull) => ({ accountGroupId: accountGroupId, contacts: agContactsOrNull })),
        );
      } else {
        return of(null);
      }
    });
    return combineLatest(contacts$List).pipe(
      map((contactsByAccountGroup: AccountGroupAndContacts[]) =>
        tasks.map((task) => {
          task.contacts = contactsByAccountGroup.find((a) => a?.accountGroupId === task.accountGroupId)?.contacts;

          return task;
        }),
      ),
      take(1),
    );
  }

  fetchPrimaryContactAndAddToTasks(tasks: SalesTask[]): Observable<SalesTask[]> {
    if (!tasks) return of(tasks);

    return this.partnerMarket$.pipe(
      take(1),
      switchMap((pm) =>
        (tasks ?? []).map((task) => {
          if (!task.accountGroupId) return of(task);

          return this.businessService.getPrimaryContact(task.accountGroupId, pm.partnerId, pm.marketId).pipe(
            map((primaryContact) => {
              task.primaryContact = primaryContact;
              return task;
            }),
            catchError(() => of(task)),
          );
        }),
      ),
      combineAll(),
    );
  }

  fetchAccountGroupsAndAddToTasks(tasks: SalesTask[]): Observable<SalesTask[]> {
    if (!tasks) {
      return of(tasks);
    }
    const accountGroupIds: string[] = tasks.map((task) => task.accountGroupId);
    const uniqueAccountGroupIds: string[] = [...new Set(accountGroupIds.filter((a) => !!a))];
    if (uniqueAccountGroupIds.length === 0) {
      return of(tasks);
    }
    return this.accountGroupService
      .getMulti(
        uniqueAccountGroupIds,
        new ProjectionFilter({
          napData: true,
          contactDetails: true,
        }),
      )
      .pipe(
        map((accountGroups) => accountGroups.filter(Boolean)),
        map((accountGroups: AccountGroup[]) =>
          tasks.map((task) => ({
            ...task,
            account: accountGroups.find((ag) => ag?.accountGroupId === task?.accountGroupId),
          })),
        ),
        catchError((err) => {
          console.warn('failed to get account groups, not adding account data to tasks', err.message);
          return of(tasks);
        }),
      );
  }

  addAccountGroupFieldToTasks(tasks: TaskInterface[]): SalesTask[] {
    if (!tasks) {
      return tasks;
    }
    return tasks.map((task) => ({ ...task, accountGroupId: TasksService.getAccountGroupFromNamespace(task) }));
  }

  updateTitle(event: TitleChangedEvent): Observable<number> {
    return this.taskSdk.setTitle(
      event.task.identity.namespace,
      event.task.identity.parentPath,
      event.task.identity.taskId,
      event.title,
      event.task.version,
    );
  }

  updateDescription(event: DescriptionChangedEvent): Observable<number> {
    return this.taskSdk.setDescription(
      event.task.identity.namespace,
      event.task.identity.parentPath,
      event.task.identity.taskId,
      event.description,
      event.task.version,
    );
  }

  updateStatus(event: StatusChangedEvent): Observable<number> {
    return this.taskSdk.setStatus(
      event.task.identity.namespace,
      event.task.identity.parentPath,
      event.task.identity.taskId,
      event.newStatus,
      event.task.version,
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }
}
