import { AccountGroup, AccountGroupService } from '@galaxy/account-group';
import { TranslateService } from '@ngx-translate/core';
import { PersonaType } from '@vendasta/iam';
import { BusinessService, Contact } from '@vendasta/sales-v2';
import {
  GetMetricsFiltersInterface,
  Metric,
  RangeField,
  SortDirection,
  Status,
  TaskInterface,
  TaskSdkService,
} from '@vendasta/task';
import { MetricResponsesMetricResponse } from '@vendasta/task/lib/_internal';
import { CalendarEvent } from 'angular-calendar';
import { endOfDay, startOfDay } from 'date-fns';
import { Observable, firstValueFrom, of, throwError } from 'rxjs';
import { ContactShadow, ContactsV2Service } from '../common/contacts';
import { AllContactFields } from '../common/contacts/contact-field.enum';
import { LoggedInUserInfo } from '../logged-in-user-info';
import { EXCLUDE_MEETINGS, EventType, SalesTask, calendarColors } from './constants';
import { TaskService } from './task.service';

const partnerId = 'ABC';
const userInfo: LoggedInUserInfo = new LoggedInUserInfo(
  'UID-123',
  false,
  true,
  partnerId,
  'market',
  '123',
  null,
  null,
  null,
  null,
  null,
  null,
  null,
);
const metricResponse: MetricResponsesMetricResponse[] = [
  {
    count: {
      key: '2020-03-30T00:00:00Z',
      count: 15,
    },
  },
];
const taskInProgressWithAccount: TaskInterface = {
  status: Status.InProgress,
  identity: {
    namespace: 'partner/ABC/account-group/AG-123',
    parentPath: '/',
    taskId: 'TK-123',
  },
  title: 'Task In Progress',
  dueDate: new Date(2020, 1, 1),
  assignees: [userInfo.salespersonId],
};
const taskCompletedNoAccount: TaskInterface = {
  status: Status.Completed,
  identity: {
    namespace: 'partner/ABC',
    parentPath: '/',
    taskId: 'TK-456',
  },
  title: 'Task Completed',
  dueDate: new Date(2020, 1, 1),
  assignees: [userInfo.salespersonId],
};
const contacts: ContactShadow[] = [
  new ContactShadow(
    new Contact({
      contactId: 'CO-1',
      contactEmail: '<EMAIL>',
      firstName: 'First',
      lastName: 'Last',
      notes: '',
      phoneExtension: '',
      phoneNumber: '**********',
      title: 'title',
    }),
    AllContactFields,
    ['AG-123'],
    'UID-456',
  ),
];
describe('TaskService tests', () => {
  let service: TaskService;
  let taskSdk: TaskSdkService;
  let translateService: TranslateService;
  let businessService: BusinessService;
  const accountGroupService = {
    getMulti: jest.fn(),
  };
  let contactsService: ContactsV2Service;
  beforeEach(() => {
    taskSdk = {
      getMetric: jest.fn(() => of(metricResponse)),
      setDueDate: jest.fn(() => of(1)),
      search: jest.fn(() => of(null)),
    } as unknown as TaskSdkService;
    translateService = {
      instant: jest.fn((): string => 'tasks'),
    } as unknown as TranslateService;
    contactsService = {
      list$: jest.fn(() => of(contacts[0])),
    } as unknown as ContactsV2Service;
    businessService = {
      getPrimaryContact: () => of('CO-1'),
    } as unknown as BusinessService;

    service = new TaskService(
      taskSdk,
      of(userInfo),
      translateService,
      contactsService,
      accountGroupService as unknown as AccountGroupService,
      businessService,
      of({ partnerId: partnerId, marketId: 'mid' }),
    );
  });

  describe('convertToCalendarEvent', () => {
    it('should return empty list if no responses', async () => {
      const result = await service.convertToCalendarEvent(null, null);
      expect(result).toEqual([]);
    });
    it('should return list of Calendar Events', async () => {
      const result = await service.convertToCalendarEvent(metricResponse, EventType.Task);
      const expected: CalendarEvent[] = [
        {
          title: '15 tasks',
          start: new Date(2020, 2, 30, 0, 0, 0),
          end: new Date(2020, 2, 30, 23, 59, 59),
          color: calendarColors[EventType.Task],
          meta: { type: EventType.Task },
        },
      ];
      expect(result).toEqual(expected);
    });
  });

  describe('getTaskMetrics', () => {
    beforeEach(() => {
      jest.spyOn(taskSdk, 'getMetric');
    });
    it('should call taskSdk getMetric with correct metric and filters', async () => {
      const dateRange = service.dateRange;
      await service.getTaskMetrics(partnerId, userInfo, EXCLUDE_MEETINGS, dateRange.start, dateRange.end);
      dateRange.end.setUTCHours(23, 59, 59);
      const expectedFilters: GetMetricsFiltersInterface = {
        metadata: { keyValues: [{ key: 'partner', values: [{ stringValue: partnerId }] }] },
        rangeStart: startOfDay(dateRange.start),
        rangeEnd: endOfDay(dateRange.end),
        assignees: [userInfo.salespersonId],
        types: [EXCLUDE_MEETINGS],
        dateRangeField: RangeField.RANGE_FIELD_DUE_DATE,
        access: [PersonaType.sales_person],
      };
      expect(taskSdk.getMetric).toBeCalledWith(Metric.METRIC_PER_DAY, expectedFilters);
    });
  });

  describe('setDateRange', () => {
    const today: Date = new Date();
    it('should set date range properly when new date is in the same month', async () => {
      await service.setDateRange(today);
      const todayMonthStart: Date = new Date(today.getFullYear(), today.getMonth(), 1);
      const todayMonthEnd: Date = new Date(today.getFullYear(), today.getMonth() + 1, 0);
      expect(service.dateRange).toEqual({
        start: todayMonthStart,
        end: todayMonthEnd,
      });
    });
    it('should set date range properly when new date is in a different month', async () => {
      const nextMonth: Date = new Date(today.getFullYear(), today.getMonth() + 1, 1);
      await service.setDateRange(nextMonth);
      const nextMonthStart: Date = new Date(nextMonth.getFullYear(), nextMonth.getMonth(), 1);
      const nextMonthEnd: Date = new Date(nextMonth.getFullYear(), nextMonth.getMonth() + 1, 0);
      expect(service.dateRange).toEqual({
        start: nextMonthStart,
        end: nextMonthEnd,
      });
    });
  });

  describe('updateDueDate', () => {
    beforeEach(() => {
      jest.spyOn(taskSdk, 'setDueDate');
    });
    it('should call taskSdk with correct params', async () => {
      const newDate: Date = new Date();
      const task: TaskInterface = {
        identity: {
          namespace: 'partner/ABC',
          parentPath: '/',
          taskId: 'TK-123',
        },
      };
      await service.updateDueDate(newDate, task);
      const expected = [task.identity.namespace, task.identity.parentPath, task.identity.taskId, newDate, task.version];
      expect(taskSdk.setDueDate).toBeCalledWith(...expected);
    });
  });

  describe('loadTasks', () => {
    it('should call taskSdk search with correct request parameters', async () => {
      jest.spyOn(taskSdk, 'search');
      const date: Date = new Date(2020, 2, 2);
      await service.loadTasks(partnerId, userInfo.salespersonId, date, Status.InProgress, EXCLUDE_MEETINGS);
      const rangeStart = startOfDay(new Date(date));
      const rangeEnd = endOfDay(new Date(date));
      const expectedRequest = {
        pageSize: 25,
        metadata: { keyValues: [{ key: 'partner', values: [{ stringValue: partnerId }] }] },
        assignees: [userInfo.salespersonId],
        rangeStart: rangeStart,
        rangeEnd: rangeEnd,
        types: [EXCLUDE_MEETINGS],
        dateRangeField: RangeField.RANGE_FIELD_DUE_DATE,
        status: [Status.InProgress],
        cursor: undefined,
        sorts: [
          {
            field: 'title',
            direction: SortDirection.SORT_DIRECTION_ASCENDING,
          },
        ],
      };
      expect(taskSdk.search).toHaveBeenCalledWith(expectedRequest);
    });
  });

  describe('addAccountGroupFieldToTasks', () => {
    it('should not perform anything if tasks are falsy', async () => {
      const result = await service.addAccountGroupFieldToTasks(null);
      expect(result).toEqual(null);
    });
    it('should add accountGroupField onto task where it exists', async () => {
      const result = await service.addAccountGroupFieldToTasks([taskInProgressWithAccount]);
      expect(result[0].accountGroupId).toEqual('AG-123');
    });
    it('should not add accountGroupField onto task where it does not exist', async () => {
      const result = await service.addAccountGroupFieldToTasks([taskCompletedNoAccount]);
      expect(result[0].accountGroupId).toBeFalsy();
    });
  });

  describe('fetchAccountGroupsAndAddToTasks', () => {
    it('should not make call to fetch accountGroups if null parameters', (done) => {
      const result$ = service.fetchAccountGroupsAndAddToTasks(null);
      result$.subscribe(() => {
        expect(accountGroupService.getMulti).not.toHaveBeenCalled();
        done();
      });
    });
    it('should not make call to fetch accountGroups if accountGroup not in namespace', (done) => {
      const taskWithoutAccount: SalesTask = {};
      const result$ = service.fetchAccountGroupsAndAddToTasks([taskWithoutAccount]);
      result$.subscribe(() => {
        expect(accountGroupService.getMulti).not.toHaveBeenCalled();
        done();
      });
    });
    it('should add any existing account groups to their tasks when account groups for other tasks have been deleted', async () => {
      const existingAccountGroup = new AccountGroup({
        accountGroupId: 'AG-NTPJXB2ZNX',
        napData: {
          companyName: 'Frothy Monkey',
          address: '2509 12th Avenue South',
        },
      });

      const getMultiResult: Observable<AccountGroup[]> = of([null, existingAccountGroup]);

      const tasks: SalesTask[] = [
        {
          version: 1000,
          title: 'This tasks account is deleted',
          status: 'In Progress',
          accountGroupId: 'AG-NXKZ22DZMF',
        },
        {
          version: 1000,
          title: 'This tasks account is not deleted',
          status: 'In Progress',
          accountGroupId: 'AG-NTPJXB2ZNX',
        },
      ];

      const expectedTasks = [
        {
          version: 1000,
          title: 'This tasks account is deleted',
          status: 'In Progress',
          accountGroupId: 'AG-NXKZ22DZMF',
        },
        {
          version: 1000,
          title: 'This tasks account is not deleted',
          status: 'In Progress',
          accountGroupId: 'AG-NTPJXB2ZNX',
          account: existingAccountGroup,
        },
      ];

      accountGroupService.getMulti.mockReturnValueOnce(getMultiResult);
      const result$ = service.fetchAccountGroupsAndAddToTasks(tasks);
      return firstValueFrom(result$).then((result) => {
        expect(accountGroupService.getMulti).toHaveBeenCalled();
        expect(result).toEqual(expectedTasks);
      });
    });
  });

  describe('fetchContactsAndAddToTasks', () => {
    beforeEach(() => jest.spyOn(contactsService, 'list$'));
    it('should not make call to fetch contacts if null parameters', (done) => {
      const result$ = service.fetchContactsAndAddToTasks(null);
      result$.subscribe(() => {
        expect(contactsService.list$).not.toHaveBeenCalled();
        done();
      });
    });
    it('should not make call to fetch contacts if accountGroup not in namespace', (done) => {
      const taskWithoutAccount: SalesTask = {};
      const result$ = service.fetchContactsAndAddToTasks([taskWithoutAccount]);
      result$.subscribe(() => {
        expect(contactsService.list$).not.toHaveBeenCalled();
        done();
      });
    });
  });

  describe('fetchPrimaryContactAndAddToTasks', () => {
    beforeEach(() => jest.spyOn(contactsService, 'list$'));
    it('should not make call to fetch primary contact if tasks param is null', (done) => {
      jest.spyOn(businessService, 'getPrimaryContact').mockReturnValueOnce(of('PC-1'));

      const result$ = service.fetchPrimaryContactAndAddToTasks(null);
      result$.subscribe(() => {
        expect(businessService.getPrimaryContact).not.toHaveBeenCalled();
        expect(contactsService.list$).not.toHaveBeenCalled();
        done();
      });
    });

    it('assigns the correct primaryContact to the correct task', (done) => {
      const ag1 = 'AG-1';
      const ag2 = 'AG-2';
      const pc1 = 'PC-1';
      const pc2 = 'PC-2';

      const tasks = [
        { ...taskInProgressWithAccount, accountGroupId: ag1 },
        { ...taskInProgressWithAccount, accountGroupId: ag2 },
      ];

      jest.spyOn(businessService, 'getPrimaryContact').mockImplementation((agId: string) => {
        if (agId === ag1) return of(pc1);
        if (agId === ag2) return of(pc2);
        return of('');
      });

      const result$ = service.fetchPrimaryContactAndAddToTasks(tasks);
      result$.subscribe((result) => {
        expect(result[0].primaryContact).toEqual(pc1);
        expect(result[1].primaryContact).toEqual(pc2);
        expect(contactsService.list$).not.toHaveBeenCalled();
        done();
      });
    });

    it('tasks without account group are included in returned tasks', (done) => {
      const ag1 = 'AG-1';
      const pc1 = 'PC-1';

      const tasks = [{ ...taskInProgressWithAccount, accountGroupId: ag1 }, { ...taskInProgressWithAccount }];

      jest.spyOn(businessService, 'getPrimaryContact').mockReturnValueOnce(of(pc1));

      const result$ = service.fetchPrimaryContactAndAddToTasks(tasks);
      result$.subscribe((result) => {
        expect(result[0].primaryContact).toEqual(pc1);
        expect(result[1]).toEqual(tasks[1]);
        expect(contactsService.list$).not.toHaveBeenCalled();
        done();
      });
    });

    it('returns an empty string if primary contact call returns error', (done) => {
      const task = { ...taskInProgressWithAccount, accountGroupId: 'AG-1' };
      const error = new Error('error');
      jest.spyOn(businessService, 'getPrimaryContact').mockReturnValue(throwError(error));
      const result$ = service.fetchPrimaryContactAndAddToTasks([task]);
      result$
        .subscribe((result) => {
          expect(result[0].primaryContact).toBeUndefined();
          done();
        })
        .unsubscribe();
    });
  });
});
