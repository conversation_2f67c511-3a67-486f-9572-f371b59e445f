<mat-drawer-container [hasBackdrop]="widthQuery.matches ? true : false" (backdropClick)="closeSideNav()">
  <mat-drawer-content>
    <glxy-page [pagePadding]="false">
      <glxy-page-toolbar>
        <glxy-page-title>
          {{ 'CALENDAR.BREADCRUMB' | translate }}
        </glxy-page-title>

        <glxy-page-actions>
          <button mat-raised-button color="primary" (click)="createTask()">
            {{ 'TASK_OVERVIEW.CREATE_TASK_LABEL' | translate }}
          </button>
        </glxy-page-actions>
      </glxy-page-toolbar>

      <div class="calendar-container" *ngIf="displayedView$$ | async as displayedView">
        <div class="top-section">
          <div class="row">
            <div class="col col-xs-7"></div>
            <div class="col col-xs-5">
              <mat-button-toggle-group>
                <mat-button-toggle
                  value="month"
                  [checked]="displayedView === CalendarView.Month"
                  (click)="toggleView(CalendarView.Month)"
                >
                  {{ 'CALENDAR.MONTH' | translate }}
                </mat-button-toggle>
                <mat-button-toggle
                  value="week"
                  [checked]="displayedView === CalendarView.Week"
                  (click)="toggleView(CalendarView.Week)"
                >
                  {{ 'CALENDAR.WEEK' | translate }}
                </mat-button-toggle>
                <mat-button-toggle
                  value="day"
                  [checked]="displayedView === CalendarView.Day"
                  (click)="toggleView(CalendarView.Day)"
                >
                  {{ 'CALENDAR.DAY' | translate }}
                </mat-button-toggle>
              </mat-button-toggle-group>
            </div>
          </div>
          <div class="row">
            <div class="col col-xs-12 current-view-title">
              <div
                mwlCalendarPreviousView
                [view]="displayedView"
                [(viewDate)]="viewDate"
                (viewDateChange)="viewDateChange($event)"
              >
                <mat-icon>chevron_left</mat-icon>
              </div>
              <div>
                {{ viewDate | calendarDate: displayedView + 'ViewTitle':'en' }}
              </div>
              <div
                mwlCalendarNextView
                [view]="displayedView"
                [(viewDate)]="viewDate"
                (viewDateChange)="viewDateChange($event)"
              >
                <mat-icon>chevron_right</mat-icon>
              </div>
            </div>
          </div>
        </div>
        <div class="calendar-section" [ngSwitch]="displayedView" *ngIf="events$ | async as events; else loading">
          <mwl-calendar-month-view
            class="calendar-month-view"
            *ngSwitchCase="CalendarView.Month"
            [viewDate]="viewDate"
            [events]="events"
            [cellTemplate]="monthlyCellTemplate"
          ></mwl-calendar-month-view>
          <mwl-calendar-week-view
            *ngSwitchCase="CalendarView.Week"
            [viewDate]="viewDate"
            [events]="events"
            (eventClicked)="handleDateClicked($event.event.start, $event.event.meta.type)"
            [dayStartHour]="8"
            [dayEndHour]="17"
          ></mwl-calendar-week-view>
          <mwl-calendar-day-view
            *ngSwitchCase="CalendarView.Day"
            [viewDate]="viewDate"
            [events]="events"
            (eventClicked)="handleDateClicked($event.event.start, $event.event.meta.type)"
            [dayStartHour]="8"
            [dayEndHour]="17"
          ></mwl-calendar-day-view>
        </div>
        <ng-template #loading>
          <div class="stencil-shimmer calendar-shimmer"></div>
        </ng-template>
      </div>

      <ng-template #monthlyCellTemplate let-day="day" let-locale="locale">
        <div
          class="month-view-cell"
          [class.month-cell-today]="isTodayDate(day.events.length > 0 && day.events[0].title ? day.events[0] : null)"
        >
          <div class="month-view-date">
            {{ day.date | calendarDate: 'monthViewDayNumber':locale }}
          </div>
          <div *ngIf="day.events && day.events.length > 0">
            <div *ngFor="let event of day.events">
              <div
                class="task-count"
                [ngStyle]="{
                  'border-color': event.color.primary,
                  color: event.color.primary,
                  'background-color': event.color.secondary
                }"
                *ngIf="event.title"
                (click)="handleDateClicked(event.start, event.meta.type)"
              >
                {{ event.title }}
              </div>
            </div>
          </div>
        </div>
      </ng-template>

      <ng-template #sidePanelContent>
        <div class="sidepanel-content-container">
          <app-calendar-task-list
            *ngIf="(calendarEventType$$ | async) === EventType.Task"
            [date$]="calendarTaskListDate$$.asObservable()"
            (closed)="closeSideNav()"
          ></app-calendar-task-list>
          <app-meeting-task-list
            *ngIf="(calendarEventType$$ | async) === EventType.Meeting"
            [date$]="calendarTaskListDate$$.asObservable()"
            (closed)="closeSideNav()"
          ></app-meeting-task-list>
        </div>
      </ng-template>
    </glxy-page>
  </mat-drawer-content>

  <mat-drawer #panel [mode]="widthQuery.matches ? 'over' : 'side'" position="end">
    <ng-container #content></ng-container>
  </mat-drawer>
</mat-drawer-container>
