import { Status, TaskInterface } from '@vendasta/task';
import { AccountGroup } from '@galaxy/account-group';
import { ContactShadow } from '../common/contacts';

export const MEETINGS_LIST = 'MEETING_LIST.BREADCRUMB';
export const MEETINGS_SETTINGS = 'SCHEDULE_SETTINGS.BREADCRUMB';
export const CALENDAR = 'CALENDAR.BREADCRUMB';
export const BOOKING_LINK = 'BOOKING_LINK.BREADCRUMB';

export interface SalesTask extends TaskInterface {
  accountGroupId?: string;
  account?: AccountGroup;
  contacts?: ContactShadow[];
  primaryContact?: string;
  meeting?: MeetingInfo;
}

export interface MeetingInfo {
  contactName: string;
  endDate: Date;
  meetingURL: string;
  contactPhone: string;
  contactEmail: string;
}

export interface DueDateChangedEvent {
  newDate: Date;
  task: TaskInterface;
}

export interface TitleChangedEvent {
  title: string;
  task: TaskInterface;
}

export interface StatusChangedEvent {
  newStatus: Status;
  oldStatus: Status;
  task: TaskInterface;
}

export interface AccountGroupAndContacts {
  accountGroupId: string;
  contacts: ContactShadow[];
}

export interface TaskSearchResponse {
  total: number;
  cursor: string;
  hasMore: boolean;
}

export enum EventType {
  Meeting = 'Meeting',
  Task = 'Task',
}

export enum MeetingStatus {
  Upcoming = 'Upcoming',
  Previous = 'Previous',
}

export interface DescriptionChangedEvent {
  description: string;
  task: TaskInterface;
}

export const EXCLUDE_MEETINGS = '-Meeting';
export const EXCLUDE_COMPLETED = '-Completed';

// Defined colors for identifying event types in calendar
export const calendarColors: any = {
  Meeting: {
    primary: '#7e57c2',
    secondary: '#e9dcff',
  },
  Task: {
    primary: '#1e90ff',
    secondary: '#D1E8FF',
  },
};
