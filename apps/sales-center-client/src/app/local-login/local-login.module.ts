import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LoginModule } from '@vendasta/devtools';
// import { LocalLoginRoutingModule } from './local-login-routing.module';
import { LocalLoginComponent } from './local-login.component';

@NgModule({
  declarations: [LocalLoginComponent],
  // imports: [CommonModule, LocalLoginRoutingModule, LoginModule] Put the routing module back in when we want this module lazy loaded
  imports: [CommonModule, LoginModule],
})
export class LocalLoginModule {}
