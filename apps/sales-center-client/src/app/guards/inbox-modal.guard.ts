import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot } from '@angular/router';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { InboxNavigationService } from '@galaxy/conversation/core';

@Injectable()
export class InboxModalGuard {
  constructor(private router: Router, private inboxNav: InboxNavigationService) {}

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Promise<boolean> {
    this.router.navigate(['dashboard']).then(() => this.inboxNav.gotoConversationID(route.params['id']));
    return false;
  }
}
