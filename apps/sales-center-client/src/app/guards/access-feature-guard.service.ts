import { Inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Observable, throwError } from 'rxjs';
import { catchError, timeout } from 'rxjs/operators';
import { AccessChecker, SSCAccessService } from '../access';

@Injectable()
export class AccessFeatureGuard {
  constructor(
    @Inject(SSCAccessService) private readonly access: AccessChecker,
    private readonly alert: SnackbarService,
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    state: RouterStateSnapshot,
  ): Observable<boolean> | Promise<boolean> | boolean {
    const feature = route.data.feature;
    return this.access.hasAccessToFeature(feature).pipe(
      timeout(10000),
      catchError((e) => {
        this.alert.openErrorSnack('An error occurred while attempting to load the page.');
        return throwError(e);
      }),
    );
  }
}
