import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot, UrlTree } from '@angular/router';
import { FeatureFlagService } from '../core/feature-flag.service';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';

const retirementDate = new Date(2025, 0, 15);

@Injectable({ providedIn: 'root' })
export class SunsetGuard implements CanActivate {
  constructor(
    private readonly router: Router,
    private readonly featureFlagService: FeatureFlagService,
  ) {}
  canActivate(_: ActivatedRouteSnapshot, __: RouterStateSnapshot): Observable<boolean | UrlTree> {
    const now = new Date();
    if (now < retirementDate) {
      return of(true);
    }

    return this.featureFlagService.featureFlagEnabled$('ssc_retirement_exemption').pipe(
      map((isExempt) => {
        if (isExempt) {
          return true;
        }

        return this.router.createUrlTree(['/retired']);
      }),
    );
  }
}
