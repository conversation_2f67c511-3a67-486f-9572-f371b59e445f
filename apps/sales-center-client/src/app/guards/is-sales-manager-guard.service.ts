import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { LoggedInUserInfoService } from '../logged-in-user-info';

@Injectable()
export class IsSalesManagerGuard {
  constructor(private readonly loggedInUser: LoggedInUserInfoService) {}

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    return this.loggedInUser.isSalesManager$;
  }
}
