import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { take } from 'rxjs/operators';
import { LoggedInUserInfoService } from '../logged-in-user-info';

@Injectable()
export class IsSalespersonGuard {
  constructor(private readonly loggedInUser: LoggedInUserInfoService, private readonly router: Router) {}

  canActivate(): Observable<boolean> {
    this.loggedInUser.isSalesperson$.pipe(take(1)).subscribe((isSalesperson) => {
      if (isSalesperson === false) {
        this.router.navigateByUrl('/non-salesperson', { skipLocationChange: true });
      }
    });

    return this.loggedInUser.isSalesperson$;
  }
}
