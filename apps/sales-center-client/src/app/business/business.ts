export class Business {
  public name: string;
  public accountGroupId: string;
  public partnerId: string;
  public marketId: string;
  public address: string;
  public city: string;
  public state: string;
  public country: string;
  public zip: string;
  public phoneNumbers: string[];
  public contactFirstName: string;
  public contactLastName: string;
  public contactEmail: string;
  public contactPhoneNumber: string;
  public assigneeId: string;
  public additionalAssigneeIds: string[];
  public website: string;

  public static createBusiness(rawBusiness: any): Business {
    const business = new Business();
    business.name = rawBusiness.name;
    business.accountGroupId = rawBusiness.account_group_id;
    business.partnerId = rawBusiness.pid;
    business.marketId = rawBusiness.market_id;
    business.address = rawBusiness.address;
    business.city = rawBusiness.city;
    business.state = rawBusiness.state;
    business.country = rawBusiness.country;
    business.zip = rawBusiness.zip;
    business.phoneNumbers = rawBusiness.phone_numbers;
    business.contactFirstName = rawBusiness.contact_first_name;
    business.contactLastName = rawBusiness.contact_last_name;
    business.contactEmail = rawBusiness.contact_email;
    business.assigneeId = rawBusiness.assignee_id;
    business.additionalAssigneeIds = rawBusiness.additional_assignee_ids;
    business.website = rawBusiness.website;

    if (rawBusiness.account_group_vobject) {
      business.contactPhoneNumber = rawBusiness.account_group_vobject.contact_phone_number;
    }
    return business;
  }

  public get fullAddress(): string {
    const parts = [this.address, this.city, this.state, this.country, this.zip].filter((part) => !!part);
    return parts.join(', ');
  }

  public get contactFullName(): string {
    const parts = [this.contactFirstName, this.contactLastName].filter((part) => !!part);
    return parts.join(' ');
  }
}
