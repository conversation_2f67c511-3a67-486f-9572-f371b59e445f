import { BusinessService } from './business.service';
import { AccountGroup, AccountGroupService, ProjectionFilter } from '@galaxy/account-group';
import { Observable, of } from 'rxjs';
import { TestScheduler } from 'rxjs/testing';

const scheduler = new TestScheduler((actual, expected) => expect(actual).toEqual(expected));

class AccountGroupSDKMock {
  lookup = jest.fn(() => {
    throw new Error('unmocked lookup response; mock me');
  });
}

describe('BusinessService', () => {
  let agSDKMock: AccountGroupService;
  let allAccess$: Observable<boolean>;
  let loggedInUsers$: Observable<string>;
  let marketId$: Observable<string[]>;

  afterEach(() => {
    scheduler.flush();
  });

  beforeEach(() => {
    agSDKMock = new AccountGroupSDKMock() as any;
    allAccess$ = of(true);
    loggedInUsers$ = of('UID-123');
    marketId$ = of(['market-id']);
  });

  describe('search', () => {
    const ag1 = new AccountGroup({ accountGroupId: 'AG-123' });
    const ag2 = new AccountGroup({ accountGroupId: 'AG-321' });
    const currentStreamResponseFromSDKLookup = of(null, [ag1, ag2]); // Note could change at anytime
    const hypotheticalStreamResponseFromSDKLookupWithAllFalseyValues = of(...[null, undefined, [ag1, ag2], null]);

    it('skips first falsey values but returns sequential falsey values', () => {
      const mockLookup = jest.fn();
      mockLookup.mockReturnValue(hypotheticalStreamResponseFromSDKLookupWithAllFalseyValues);
      agSDKMock.lookup = mockLookup;
      const service = new BusinessService(agSDKMock, allAccess$, loggedInUsers$, marketId$);

      const result$ = service.search(new ProjectionFilter());

      scheduler.expectObservable(result$).toBe('(xy|)', { x: [ag1, ag2], y: null });
    });

    it('returns second non null value', () => {
      const mockLookup = jest.fn();
      mockLookup.mockReturnValue(currentStreamResponseFromSDKLookup);
      agSDKMock.lookup = mockLookup;
      const service = new BusinessService(agSDKMock, allAccess$, loggedInUsers$, marketId$);

      const result$ = service.search(new ProjectionFilter());

      scheduler.expectObservable(result$).toBe('(x|)', { x: [ag1, ag2] });
    });
  });
});
