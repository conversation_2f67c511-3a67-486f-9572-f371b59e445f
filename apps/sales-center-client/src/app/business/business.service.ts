import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import {
  CustomField as AGCustomField,
  AccountGroup,
  AccountGroupService,
  LookupFilter,
  ProjectionFilter,
  ReadFilter,
  RichDataUpdateOperation,
  UpdateOperations,
} from '@galaxy/account-group';
import { BehaviorSubject, Observable, combineLatest, of, throwError } from 'rxjs';
import { catchError, map, mapTo, skipWhile, switchMap, tap } from 'rxjs/operators';
import { ACCESS_ALL_MARKET_TOKEN, MARKET_ID_TOKEN, SALESPERSON_ID_TOKEN } from '../common/providers';
import { HostProvider, SalesToolHostService } from '../core';

const BASE_URL = '/api/v1/business/';
const GET_BUSINESS_URL = BASE_URL + 'get/';
const UPDATE_NOTES_URL = BASE_URL + 'notes/update/';
const UPDATE_IS_READ_URL = BASE_URL + 'is-read/update/';

export interface BasicBusinessInfo {
  name: string;
  accountGroupId: string;
  streetAddress: string;
  city: string;
  state: string;
  countryCode: string;
  zip: string;
  website: string;
  hotness: number;
  businessCategory: string;
  serviceCategories: string[];
  marketId: string;
  partnerId: string;
  notes: string;
  assigneeId: string;
  additionalAssigneeIds: string[];
  isArchived: boolean;
  vtaxCategory: string;
}

export interface BusinessResponse extends BasicBusinessInfo {
  primaryNumber: string;
  phoneNumbers: string[];
  assignee_id: string;
  additional_assignee_ids: string[];
}

export type UpdateNotesResponse = null;
export type UpdateIsReadResponse = null;

export interface BusinessApiService {
  getBusiness(businessId: string): Observable<BusinessResponse>;

  updateNotes(businessId: string, notes: string): Observable<UpdateNotesResponse>;

  updateIsRead(accountGroupId: string, isRead: boolean): Observable<UpdateIsReadResponse>;

  getCustomFields(businessId: string): Observable<AGCustomField[]>;

  updateCustomFields(businessId: string, customFields: AGCustomField[]): Observable<null>;
}

/**
 * Uses IAM auth to interact with business APIs.
 */
@Injectable()
export class AjaxBusinessApiService implements BusinessApiService {
  constructor(
    private readonly http: HttpClient,
    private readonly accountGroupService: AccountGroupService,
    @Inject(SalesToolHostService) private readonly host: HostProvider,
  ) {}

  getBusiness(businessId: string): Observable<BusinessResponse> {
    const host = this.host.host();
    const url = `${host}${GET_BUSINESS_URL}`;
    const body = {
      accountGroupId: businessId,
    };
    return this.http
      .post(url, body, {
        withCredentials: true,
      })
      .pipe(
        map<any, BusinessResponse>((r) => r.data),
        map((br) => {
          // The api doesn't return accountGroupId
          br.accountGroupId = businessId;
          return br;
        }),
        catchError(() => {
          return of({}) as Observable<BusinessResponse>;
        }),
      );
  }

  // FIXME: need to accept empty notes to overwrite exiting notes.
  updateNotes(businessId: string, notes: string): Observable<UpdateNotesResponse> {
    const host = this.host.host();
    const url = `${host}${UPDATE_NOTES_URL}`;
    const body = {
      accountGroupId: businessId,
      notes: notes,
    };
    return this.http.post(url, body, { withCredentials: true }).pipe(map<any, UpdateNotesResponse>((r) => r.data));
  }

  updateIsRead(accountGroupId: string, isRead: boolean): Observable<UpdateIsReadResponse> {
    const host = this.host.host();
    const url = `${host}${UPDATE_IS_READ_URL}`;
    const body = {
      accountGroupId: accountGroupId,
      isRead: isRead,
    };

    return this.http.post(url, body, { withCredentials: true }).pipe(map<any, UpdateIsReadResponse>((r) => r.data));
  }

  getCustomFields(businessId: string): Observable<AGCustomField[]> {
    const projectionFilter = new ProjectionFilter({ richData: true });
    return this.accountGroupService.get(businessId, projectionFilter).pipe(
      catchError((err) => {
        err.message = 'Failed to load custom fields';
        return throwError(err);
      }),
      map((ag) => {
        return ag.richData.customFields || [];
      }),
    );
  }

  updateCustomFields(businessId: string, customFields: AGCustomField[]): Observable<null> {
    const customFieldsOp = new RichDataUpdateOperation({ customFields: customFields });
    const operations = new UpdateOperations();
    operations.addUpdateOperation(customFieldsOp);
    return this.accountGroupService.bulkUpdate(businessId, operations).pipe(
      catchError((err) => {
        err.message = 'Failed to update custom fields';
        return throwError(err);
      }),
      mapTo(null),
    );
  }
}

@Injectable({
  providedIn: 'root',
})
export class BusinessService {
  private readonly business$$: BehaviorSubject<AccountGroup> = new BehaviorSubject<AccountGroup>(undefined);
  readonly business$: Observable<AccountGroup> = this.business$$.asObservable();

  constructor(
    private readonly accountGroupService: AccountGroupService,
    @Inject(ACCESS_ALL_MARKET_TOKEN) readonly hasAccessToAllAccountsInMarket$: Observable<boolean>,
    @Inject(SALESPERSON_ID_TOKEN) readonly salespersonId$: Observable<string>,
    @Inject(MARKET_ID_TOKEN) readonly marketIds$: Observable<string[]>,
  ) {}

  get(accountGroupId: string, projectionFilter: ProjectionFilter, readFilter?: ReadFilter): Observable<AccountGroup> {
    if (readFilter) {
      return this.accountGroupService.getMulti([accountGroupId], projectionFilter, readFilter).pipe(
        map((results) => {
          if (results.length !== 1) {
            throwError(new Error('Business not found'));
          }
          return results[0];
        }),
        tap((business) => this.business$$.next(business)),
      );
    }

    return this.accountGroupService
      .get(accountGroupId, projectionFilter)
      .pipe(tap((business) => this.business$$.next(business)));
  }

  search(projectionFilter: ProjectionFilter, searchTerm?: string, pageSize = 500): Observable<AccountGroup[]> {
    return combineLatest([this.hasAccessToAllAccountsInMarket$, this.salespersonId$, this.marketIds$]).pipe(
      switchMap(([hasAllMarketAccess, salespersonId, marketIds]) => {
        const filters = !hasAllMarketAccess ? new LookupFilter(salespersonId) : undefined;
        return this.accountGroupService.lookup(projectionFilter, marketIds, searchTerm, filters, pageSize).pipe(
          skipWhile((r) => r == null), // skip the first null values (and undefined)
        );
      }),
    );
  }
}
