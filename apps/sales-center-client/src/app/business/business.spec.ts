import { Business } from './business';

describe('Business Model', () => {
  describe('createBusiness', () => {
    it('should return a correct business model', () => {
      const rawData = {
        name: 'Scrummibears',
        account_group_id: 'AG-123',
        pid: 'WWF',
        market_id: 'raw',
        address: '1510 Texas Ave',
        city: 'Houston',
        state: 'TX',
        country: 'US',
        zip: 'TX5967',
        phone_numbers: ['123123'],
        contact_first_name: '<PERSON>',
        contact_last_name: '<PERSON>',
        contact_email: '<EMAIL>',
        account_group_vobject: {
          contact_phone_number: '345678',
        },
      };
      const expectedBusiness = new Business();
      expectedBusiness.name = 'Scrummibears';
      expectedBusiness.accountGroupId = 'AG-123';
      expectedBusiness.partnerId = 'WWF';
      expectedBusiness.marketId = 'raw';
      expectedBusiness.address = '1510 Texas Ave';
      expectedBusiness.city = 'Houston';
      expectedBusiness.state = 'TX';
      expectedBusiness.country = 'US';
      expectedBusiness.zip = 'TX5967';
      expectedBusiness.phoneNumbers = ['123123'];
      expectedBusiness.contactFirstName = 'Jim';
      expectedBusiness.contactLastName = 'Green';
      expectedBusiness.contactEmail = '<EMAIL>';
      expectedBusiness.contactPhoneNumber = '345678';
      expect(expectedBusiness).toEqual(Business.createBusiness(rawData));
    });
  });

  describe('fullAddress', () => {
    it('should return a full address', () => {
      const rawData = {
        name: 'Scrummibears',
        account_group_id: 'AG-123',
        pid: 'WWF',
        market_id: 'raw',
        address: '1510 Texas Ave',
        city: 'Houston',
        state: 'TX',
        country: 'US',
        zip: 'TX5967',
      };
      const expectedFullAddress = '1510 Texas Ave, Houston, TX, US, TX5967';
      expect(expectedFullAddress).toEqual(Business.createBusiness(rawData).fullAddress);
    });

    it('should ignore missing part of a full address', () => {
      const rawData = {
        name: 'Scrummibears',
        account_group_id: 'AG-123',
        pid: 'WWF',
        market_id: 'raw',
        address: '1510 Texas Ave',
        city: 'Houston',
        country: 'US',
      };
      const expectedFullAddress = '1510 Texas Ave, Houston, US';
      expect(expectedFullAddress).toEqual(Business.createBusiness(rawData).fullAddress);
    });
  });

  describe('contactFullName', () => {
    it('should return a full name', () => {
      const rawData = {
        name: 'Scrummibears',
        account_group_id: 'AG-123',
        pid: 'WWF',
        market_id: 'raw',
        contact_first_name: 'Jim',
        contact_last_name: 'Green',
        contact_email: '<EMAIL>',
      };
      const expectedFullName = 'Jim Green';
      expect(expectedFullName).toEqual(Business.createBusiness(rawData).contactFullName);
    });

    it('should ignore missing part of a name', () => {
      const rawData = {
        name: 'Scrummibears',
        account_group_id: 'AG-123',
        pid: 'WWF',
        market_id: 'raw',
        contact_first_name: 'Jim',
        contact_email: '<EMAIL>',
      };
      const expectedFullName = 'Jim';
      expect(expectedFullName).toEqual(Business.createBusiness(rawData).contactFullName);
    });
  });
});
