import { NgModule } from '@angular/core';
import { AjaxBusinessApiService } from './business.service';
import { PARTNER_ID_TOKEN as EAC_PARTNER_ID_TOKEN } from '@galaxy/easy-account-create';
import { LoggedInUserInfoService } from '../logged-in-user-info';

@NgModule({
  imports: [],
  declarations: [],
  providers: [
    AjaxBusinessApiService,
    {
      provide: EAC_PARTNER_ID_TOKEN,
      useFactory: (loggedInUser: LoggedInUserInfoService) => {
        return loggedInUser.getPartnerId();
      },
      deps: [LoggedInUserInfoService],
    },
  ],
})
export class BusinessModule {}
