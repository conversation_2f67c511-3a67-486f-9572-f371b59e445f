import { Inject, Injectable } from '@angular/core';
import { Salesperson } from '@galaxy/types';
import { Observable, ReplaySubject, merge, race } from 'rxjs';
import { map, switchMap, take, tap, withLatestFrom } from 'rxjs/operators';
import { LoggedInUserInfoService } from '../../logged-in-user-info';
import { SalespersonCacheEntry, SalespersonCacheService } from '../../manage-accounts/cache/salesperson-cache.service';
import {
  CurrentSalespersonProvider,
  SalespeopleService,
  SalespersonService,
} from '../../salespeople/salespeople.service';
import { ALL_SALESPEOPLE } from './all-salespeople';

export const ALL_SALESPEOPLE_STARTING_WITH_CURRENT_TOKEN = 'ALL_SALESPEOPLE_STARTING_WITH_CURRENT';

export function getAllSalespeopleStartingWithCurrent$(
  accessAllAccounts: Observable<boolean>,
  svs: SalespersonService,
  cp: CurrentSalespersonProvider,
): Observable<Salesperson[]> {
  const allSalespeople$: Observable<Salesperson[]> = accessAllAccounts.pipe(
    switchMap((hasAccess) => svs.loadSalespeople(hasAccess)),
  );
  const currentSalesperson$ = cp.getCurrentSalesPerson().pipe(map((sp) => [sp]));
  return race(allSalespeople$, merge(currentSalesperson$, allSalespeople$));
}

@Injectable()
export class SalespeopleStartingWithCurrent {
  private readonly salespeople$$ = new ReplaySubject<Salesperson[]>();
  public readonly salespeople$: Observable<Salesperson[]>;
  constructor(
    private readonly service: SalespeopleService,
    private readonly cacheService: SalespersonCacheService,
    private readonly userInfoService: LoggedInUserInfoService,
    @Inject(ALL_SALESPEOPLE) private readonly allSalespeople$: Observable<Salesperson[]>,
  ) {
    this.salespeople$ = this.salespeople$$.asObservable();
    const salesPersonId$ = userInfoService.salesPersonId$.pipe(take(1));
    salesPersonId$
      .pipe(
        tap((id) => {
          const cacheResponse: SalespersonCacheEntry = this.cacheService.getCachedSalespeople(id);
          if (!cacheResponse) {
            return;
          }
          this.salespeople$$.next(cacheResponse.salespeople);
        }),
      )
      .subscribe();

    getAllSalespeopleStartingWithCurrent$(service.hasAccessToAllAccountsInMarket$, service, service)
      .pipe(
        withLatestFrom(salesPersonId$),
        tap(([ppl, id]) => {
          const entry = <SalespersonCacheEntry>{
            salespeople: ppl,
            ownerId: id,
          };
          this.cacheService.add(entry);
          this.salespeople$$.next(ppl);
        }),
      )
      .subscribe();
  }
}
