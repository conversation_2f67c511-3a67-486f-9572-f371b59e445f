import { Injectable } from '@angular/core';
import { Salesperson } from '@galaxy/types';
import { Observable, ReplaySubject } from 'rxjs';
import { switchMap, take, tap, withLatestFrom } from 'rxjs/operators';
import { LoggedInUserInfoService } from '../../logged-in-user-info';
import { SalespersonCacheEntry, SalespersonCacheService } from '../../manage-accounts/cache/salesperson-cache.service';
import { SalespeopleService, SalespersonService } from '../../salespeople/salespeople.service';

export const ALL_SALESPEOPLE = 'ALL_SALESPEOPLE';

export function getAllSalespeople$(
  accessAllAccounts: Observable<boolean>,
  svs: SalespersonService,
): Observable<Salesperson[]> {
  return accessAllAccounts.pipe(switchMap((hasAccess) => svs.loadSalespeople(hasAccess)));
}

@Injectable()
export class AllSalespeople {
  private readonly salespeople$$ = new ReplaySubject<Salesperson[]>();
  public readonly salespeople$: Observable<Salesperson[]>;

  constructor(
    private readonly service: SalespeopleService,
    private readonly cacheService: SalespersonCacheService,
    private readonly userInfoService: LoggedInUserInfoService,
  ) {
    this.salespeople$ = this.salespeople$$.asObservable();
    const salesPersonId$ = userInfoService.salesPersonId$.pipe(take(1));
    salesPersonId$
      .pipe(
        tap((id) => {
          const cacheResponse: SalespersonCacheEntry = this.cacheService.getCachedSalespeople(id);
          if (!cacheResponse) {
            return;
          }
          this.salespeople$$.next(cacheResponse.salespeople);
        }),
      )
      .subscribe();

    getAllSalespeople$(service.hasAccessToAllAccountsInMarket$, service)
      .pipe(
        withLatestFrom(salesPersonId$),
        tap(([ppl, id]) => {
          const entry = <SalespersonCacheEntry>{
            salespeople: ppl,
            ownerId: id,
          };
          this.cacheService.add(entry);
          this.salespeople$$.next(ppl);
        }),
      )
      .subscribe();
  }
}
