import { Salesperson } from '@galaxy/types';
import { EMPTY, Observable, of } from 'rxjs';
import { TestScheduler } from 'rxjs/testing';
import { EMPTY_STATE } from '../../rx-utils/state-maps';
import { JestScheduler } from '../../rx-utils/testing/jest-scheduler';
import { CurrentSalespersonProvider, SalespersonService } from '../../salespeople/salespeople.service';
import { getAllSalespeopleStartingWithCurrent$ } from './salespeople-starting-with-current';

class SalespersonServiceMock implements SalespersonService {
  spSharedAccountState = EMPTY_STATE;
  spState = EMPTY_STATE;
  spWithTeamState = EMPTY_STATE;

  constructor(public loadSalespeople: () => Observable<Salesperson[]>) {}

  getSalesPersonBySubjectId$ = () => EMPTY;

  getSalesPersonByUserId$ = () => EMPTY;

  getSalespeople$ = () => EMPTY;

  getSalespeopleWithTeams$ = () => EMPTY;
}

describe('doGetAllSalespeopleStartingWithCurrent$', () => {
  let scheduler: TestScheduler;
  beforeEach(() => (scheduler = new JestScheduler()));
  afterEach(() => scheduler.flush());
  const accessAll = of(false);
  const allSalespeople: Salesperson[] = [{ id: 'A' } as Salesperson, { id: 'B' } as Salesperson];
  const currentSalesperson: Salesperson = { id: 'C' } as Salesperson;
  it('should emit the salespeople once if the salespeople are fetched before the current salesperson', () => {
    const salespeople = new SalespersonServiceMock(() => scheduler.createColdObservable('-x', { x: allSalespeople }));
    const current: CurrentSalespersonProvider = {
      getCurrentSalesPerson: () => scheduler.createColdObservable('-----x', { x: currentSalesperson }),
    };
    const actual = getAllSalespeopleStartingWithCurrent$(accessAll, salespeople, current);
    scheduler.expectObservable(actual).toBe('-x--------', { x: allSalespeople });
  });
  it(
    'should emit the current salesperson followed by all the salespeople if the current salesperson is fetched before ' +
      'the other salespeople',
    () => {
      const salespeople = new SalespersonServiceMock(() =>
        scheduler.createColdObservable('---a', { a: allSalespeople }),
      );
      const current: CurrentSalespersonProvider = {
        getCurrentSalesPerson: () => scheduler.createColdObservable('-c', { c: currentSalesperson }),
      };
      const actual = getAllSalespeopleStartingWithCurrent$(accessAll, salespeople, current);
      scheduler.expectObservable(actual).toBe('-c-a----', { c: [currentSalesperson], a: allSalespeople });
    },
  );
  it('should emit the current salesperson if the other salespeople never load', () => {
    const salespeople = new SalespersonServiceMock(() => scheduler.createColdObservable('---------'));
    const current: CurrentSalespersonProvider = {
      getCurrentSalesPerson: () => scheduler.createColdObservable('-c', { c: currentSalesperson }),
    };
    const actual = getAllSalespeopleStartingWithCurrent$(accessAll, salespeople, current);
    scheduler.expectObservable(actual).toBe('-c------', { c: [currentSalesperson], a: allSalespeople });
  });
  it('should emit all the salespeople if the current salesperson never loads', () => {
    const salespeople = new SalespersonServiceMock(() => scheduler.createColdObservable('-a', { a: allSalespeople }));
    const current: CurrentSalespersonProvider = {
      getCurrentSalesPerson: () => scheduler.createColdObservable('--------'),
    };
    const actual = getAllSalespeopleStartingWithCurrent$(accessAll, salespeople, current);
    scheduler.expectObservable(actual).toBe('-a------', { c: [currentSalesperson], a: allSalespeople });
  });
});
