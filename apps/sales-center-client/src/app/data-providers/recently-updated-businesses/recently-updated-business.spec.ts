import { AccountGroup } from '@galaxy/account-group';
import { BusinessSearchResult } from '@vendasta/sales-v2';
import { Observable } from 'rxjs';
import { TestScheduler } from 'rxjs/testing';
import { convert, mapRecentlyUpdatedBusinessInfo, RecentlyUpdatedBusiness } from './source';
const scheduler: TestScheduler = new TestScheduler((a, b) => expect(a).toEqual(b));

const agIds = ['AG-1', 'AG-2', 'AG-3', 'AG-4'];
const businessesUpdatedFromApi = agIds.map((agId) => <BusinessSearchResult>{ accountGroupId: agId });
const accountGroupInfo = agIds.map((agId) => <AccountGroup>{ accountGroupId: agId, napData: { companyName: agId } });
const mappedResults = agIds.map((agId) => <RecentlyUpdatedBusiness>{ url: '/info/' + agId, name: agId });

class MockAccountGroupService {
  getMulti(): Observable<AccountGroup[]> {
    return scheduler.createColdObservable('x', {
      x: accountGroupInfo,
    });
  }
}

describe('convert', () => {
  it('should map all results from account group service without error', () => {
    const mockAccountGroupService = new MockAccountGroupService();
    const apiResults = agIds.map((agId) => <BusinessSearchResult>{ accountGroupId: agId });

    scheduler.expectObservable(convert(mockAccountGroupService as any, apiResults)).toBe('x', { x: mappedResults });
    scheduler.flush();
  });
});

describe('mapRecentlyUpdatedBusinessInfo', () => {
  it('should map updated businesses while missing some retrieved account group details', () => {
    const missingAccountGroupId = agIds[0];
    const allAccountGroupDetailsExceptOne = accountGroupInfo.filter(
      (ag) => ag.accountGroupId !== missingAccountGroupId,
    );

    const mappedResultsWithMissingAccount = [...mappedResults];
    const missingEntry = mappedResultsWithMissingAccount.find((ag) => ag.name === missingAccountGroupId);
    missingEntry.name = undefined;

    expect(mapRecentlyUpdatedBusinessInfo(allAccountGroupDetailsExceptOne, businessesUpdatedFromApi)).toEqual(
      mappedResultsWithMissingAccount,
    );
  });
  it('should succeed in mapping an empty array without error', () => {
    const noAccountsRetrieved = [];

    expect(mapRecentlyUpdatedBusinessInfo(noAccountsRetrieved, businessesUpdatedFromApi)).toHaveLength(agIds.length);
  });
  it('should return all businesses despite undefined account group details', () => {
    const accountsUndefined = undefined;
    expect(mapRecentlyUpdatedBusinessInfo(accountsUndefined, businessesUpdatedFromApi)).toHaveLength(agIds.length);
  });
  it('should return all businesses if there is a null in the returned account groups', () => {
    const businessesUpdatedFromApiWithANull = [...businessesUpdatedFromApi, null];
    expect(mapRecentlyUpdatedBusinessInfo(accountGroupInfo, businessesUpdatedFromApiWithANull)).toHaveLength(
      agIds.length,
    );
  });
});
