import { Inject, Injectable, Optional } from '@angular/core';
import { AccountGroup, AccountGroupService, ProjectionFilter } from '@galaxy/account-group';
import { PartnerMarket } from '@galaxy/types';
import { DataProviderConfig, KeyedDataProvider } from '@vendasta/data-providers';
import { WorkStates } from '@vendasta/rx-utils/work-state';
import {
  BusinessSearchResult,
  BusinessService,
  BusinessServiceInterface,
  Sort,
  SortDirection,
} from '@vendasta/sales-v2';
import { EMPTY, Observable, SchedulerLike, firstValueFrom } from 'rxjs';
import { map, shareReplay, switchMap } from 'rxjs/operators';
import { USER_PARTNER_MARKET_TOKEN } from '../../core/feature-flag.service';
import { ACCOUNT_INFO } from '../../urls';

export interface RecentlyUpdatedBusiness {
  name: string;
  url: string;
}

export type APIResults = BusinessSearchResult[];
export type UIResults = RecentlyUpdatedBusiness[];

function fetch(svc: BusinessServiceInterface, i: PartnerMarket): Observable<BusinessSearchResult[]> {
  return svc.search(i.partnerId, i.marketId, Sort.Updated, SortDirection.Descending, 5);
}

export function mapRecentlyUpdatedBusinessInfo(
  accountGroups: AccountGroup[],
  apiResults: APIResults,
): RecentlyUpdatedBusiness[] {
  const businessesWithNames = [];
  for (let i = 0; i < apiResults.length; i++) {
    const agId = apiResults[i];
    if (agId) {
      businessesWithNames.push(<RecentlyUpdatedBusiness>{
        name: accountGroups?.find((accountGroup) => accountGroup.accountGroupId === agId.accountGroupId)?.napData
          .companyName,
        url: ACCOUNT_INFO(agId.accountGroupId),
      });
    }
  }
  return businessesWithNames;
}

export function convert(accountGroupService: AccountGroupService, res: APIResults): Observable<UIResults> {
  const filter = new ProjectionFilter({ napData: true });
  return accountGroupService
    .getMulti(
      res.map((apiResult) => apiResult.accountGroupId),
      filter,
    )
    .pipe(map((accountGroups) => mapRecentlyUpdatedBusinessInfo(accountGroups, res)));
}

function keyFunc(i: PartnerMarket): string {
  return `PARTNER(${i.partnerId})-MARKET(${i.marketId})`;
}

@Injectable()
export class RecentlyUpdatedBusinessesSource {
  private readonly delegate: KeyedDataProvider<PartnerMarket, APIResults, UIResults>;

  constructor(
    @Inject(USER_PARTNER_MARKET_TOKEN) private readonly partnerMarket: Observable<PartnerMarket>,
    @Inject(BusinessService) svc: BusinessServiceInterface,
    @Inject(AccountGroupService) accountGroupService: AccountGroupService,
    @Inject('TestScheduler') @Optional() scheduler?: SchedulerLike,
  ) {
    const c: DataProviderConfig<PartnerMarket, APIResults, UIResults> = {
      externalSource: {
        fetchFn: (i: PartnerMarket) => fetch(svc, i),
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        uploadFn: (i: PartnerMarket, d: RecentlyUpdatedBusiness[]) => EMPTY,
      },
      convert: {
        apiDataToAppData: (res: APIResults) => convert(accountGroupService, res),
        inputsToStringKey: (i: PartnerMarket) => keyFunc(i),
      },
      testing: { scheduler: scheduler },
    };
    this.delegate = new KeyedDataProvider(c);
  }

  getCachedStateOrLoad$(): WorkStates<RecentlyUpdatedBusiness[]> {
    const pm$ = this.partnerMarket.pipe(map(keyFunc), shareReplay(1));
    return {
      isLoading$: pm$.pipe(switchMap((pm) => this.delegate.state.isLoading$(pm))),
      isSuccess$: pm$.pipe(switchMap((pm) => this.delegate.state.isSuccess$(pm))),
      successEvents$: pm$.pipe(switchMap((pm) => this.delegate.state.successEvent$(pm))),
      workResults$: this.partnerMarket.pipe(switchMap((pm) => this.delegate.getCachedOrFetch$(pm))),
    };
  }

  async refresh(): Promise<void> {
    const pm = await firstValueFrom(this.partnerMarket);
    this.delegate.requestRefresh(pm);
  }
}
