import { inject, InjectionToken } from '@angular/core';
import { EnvironmentService } from '@galaxy/core';
import { DocumentBuilderLinkService, DocumentBuilderOrganizationService } from '@galaxy/document-builder/adapters';
import { DocumentBuilderConfiguration } from '@galaxy/document-builder/core';
import { LineItemsAdapterService, LineItemsSelectorService } from '@vendasta/orders';
import { Document } from '@vendasta/sales-orders';
import { Observable, of } from 'rxjs';
import { PARTNER_ID_TOKEN } from '../../common/providers';
import { LoggedInUserInfoService } from '../../logged-in-user-info';
import { filter, map, switchMap } from 'rxjs/operators';
import { IAMApiService, PersonaType } from '@vendasta/iam';

const ATLAS_HEIGHT = 40;

export const SALES_CENTER_CONFIG_FOR_DOCUMENT_BUILDER = new InjectionToken<DocumentBuilderConfiguration>(
  '[Sales Center Client]: Token for DocumentBuilder config',
  {
    providedIn: 'any',
    factory: function (): DocumentBuilderConfiguration {
      const loggedInUserService = inject(LoggedInUserInfoService);
      const iamService = inject(IAMApiService);
      return {
        // Cast PARTNER_ID_TOKEN as any because it is actually a string idToken, but Angular is not recognizing it.
        organizationId$: inject<Observable<string>>(PARTNER_ID_TOKEN as any),
        proposalBuilderDisabled$: inject(LoggedInUserInfoService).loggedInUserInfo$.pipe(
          filter(Boolean),
          switchMap((user) => {
            if (user.partnerId !== 'BRF2') {
              return of(false);
            }
            if (user.isSalesManager) {
              return of(false);
            }
            return iamService.getMultiUsers({ userIdentifiers: [{ userId: user.unifiedUserId }] }).pipe(
              map((response) => {
                const roles = response?.users?.[0]?.user?.roles;
                return roles.includes(PersonaType.partner);
              }),
            );
          }),
        ),
        offsetHeight: ATLAS_HEIGHT,
        environment: inject(EnvironmentService).getEnvironment(),
        userId$: loggedInUserService.unifiedUserId$,
        organizationService: inject(DocumentBuilderOrganizationService),
        orderService: {
          generateOrderUrl(document: Document): string {
            if (!document.recipientOrganizationId || !document.orderId) {
              return '';
            }
            return `sales-orders/${document.recipientOrganizationId}/order/${document.orderId}`;
          },
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          getOrderApprovalUrl(document: Document): string {
            return '/';
          },
        },
        lineItemAdapterService: inject(LineItemsAdapterService),
        lineItemSelectorService: inject(LineItemsSelectorService),
        linkService: inject(DocumentBuilderLinkService),
        orderFormFileUploadUrl: '/ajax/v1/marketplace/file/upload',
      };
    },
  },
);
