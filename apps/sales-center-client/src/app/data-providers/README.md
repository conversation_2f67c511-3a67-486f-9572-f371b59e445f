# Data Providers

This folder contains the source-of-truth for various sets of data in the SSC 
app.

## Does a Data Provider suit my use case?
Is it expensive to acquire your data?  For example, does it require an HTTP 
request or a non-trivial procedure?
- [Yes](quiz/EXPENSIVE.md) 
- [No](quiz/INEXPENSIVE.md)

## Expectations

Data providers should attempt to be thin wrappers around the `KeyedDataProvider`
class, as it handles caching and fetching data for you. 

Providers should be the only things that actually manipulate the data in their 
internal subjects and streams.

Providers should expose intentful functions for manipulating the data.

E.g.

```
Opportunities:
    - opportunities$ => (stream containing all opportunities)
    - opportunitiesForStage$(stageId) => (stream of all opportunities for stage)
    - moveOpportunityToStage(oppId, fromStageId, toStageId)
    - etc.
``` 
