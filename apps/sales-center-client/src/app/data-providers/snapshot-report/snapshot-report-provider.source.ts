import { Inject, Injectable } from '@angular/core';
import { DataProviderConfig, KeyedDataProvider } from '@vendasta/data-providers';
import { CurrentInterface, CurrentSnapshotService, GetCurrentResponseInterface } from '@vendasta/snapshot';
import { Observable, firstValueFrom, of, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { RetriesExhaustedError } from './error';

function fetch(svc: CurrentSnapshotService, ag: string): Observable<GetCurrentResponseInterface> {
  return svc.get(ag).pipe(
    catchError((err) => {
      if (err.status === 404) {
        return of(null);
      }
      throw err;
    }),
  );
}

@Injectable({
  providedIn: 'root',
})
export class SnapshotReportSource {
  private readonly delegate: KeyedDataProvider<string, GetCurrentResponseInterface, CurrentInterface>;

  constructor(@Inject(CurrentSnapshotService) svc: CurrentSnapshotService) {
    const config: DataProviderConfig<string, GetCurrentResponseInterface, CurrentInterface> = {
      externalSource: {
        fetchFn: (ag: string) => fetch(svc, ag),
        uploadFn: () => throwError('Upload not supported'),
      },
      convert: {
        apiDataToAppData: (data: GetCurrentResponseInterface) => of(data?.snapshot),
        inputsToStringKey: (ag: string) => ag,
      },
    };

    this.delegate = new KeyedDataProvider(config);
  }

  public get$(accountGroupId: string): Observable<CurrentInterface> {
    return this.delegate.getCachedOrFetch$(accountGroupId);
  }

  public refresh(accountGroupId: string): void {
    this.delegate.requestRefresh(accountGroupId);
  }

  public async getWithRetry(accountGroupId: string, retries = 10): Promise<CurrentInterface> {
    return firstValueFrom(this.get$(accountGroupId)).then((snapshot) =>
      this.recursiveFetch(accountGroupId, snapshot?.snapshotId, retries),
    );
  }

  private async recursiveFetch(
    accountGroupId: string,
    initialSnapshotId: string,
    retries: number,
  ): Promise<CurrentInterface> {
    if (retries === 0) {
      return Promise.reject(RetriesExhaustedError);
    }

    this.delegate.requestRefresh(accountGroupId);

    const next = await firstValueFrom(this.get$(accountGroupId));
    if (next?.snapshotId === initialSnapshotId) {
      return new Promise((dc) => window.setTimeout(dc, 500)).then(() =>
        this.recursiveFetch(accountGroupId, initialSnapshotId, --retries),
      );
    }

    return next;
  }
}
