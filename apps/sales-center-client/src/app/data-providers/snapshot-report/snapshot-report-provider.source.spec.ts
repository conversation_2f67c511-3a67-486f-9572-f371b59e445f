import { HttpErrorResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import { CurrentSnapshotService, GetCurrentResponseInterface } from '@vendasta/snapshot';
import { firstValueFrom, of, throwError } from 'rxjs';
import { SnapshotReportSource } from './snapshot-report-provider.source';

const MockCurrentSnapshotService = {
  get: jest.fn(),
};

describe('SnapshotReportProviderService', () => {
  let service: SnapshotReportSource;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [{ provide: CurrentSnapshotService, useValue: MockCurrentSnapshotService }],
    });
    service = TestBed.inject(SnapshotReportSource);

    MockCurrentSnapshotService.get = jest.fn();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('returns the expected snapshot', async () => {
    const expectedSnapshot = {
      snapshotId: 'Snappy-1 to command, where am I?',
      created: new Date(),
      expired: new Date(),
      path: 'command to Snappy-1, you exist in my heart',
    };

    const defaultGetSnapshotResponse = <GetCurrentResponseInterface>{
      snapshot: expectedSnapshot,
    };

    MockCurrentSnapshotService.get.mockReturnValue(of(defaultGetSnapshotResponse));

    return firstValueFrom(service.get$('Snappy-1')).then((snapshot) => expect(snapshot).toEqual(expectedSnapshot));
  });

  it('returns a null observable if no snapshot found.', () => {
    const error = {
      status: 404,
      message: 'Snappy-1 to command, Where am I? Please come in. Command? I feel so cold.',
    } as HttpErrorResponse;

    MockCurrentSnapshotService.get.mockReturnValue(throwError(error));

    return firstValueFrom(service.get$('Snappy-1')).then((snapshot) => expect(snapshot).toBeUndefined());
  });

  describe('getWithRetry', () => {
    it('rejects promise if newly created snapshot not found and retries exhausted', async () => {
      const error = {
        status: 404,
        message: 'Snappy-1 to command, Where am I? Please come in. Command? I feel so cold.',
      } as HttpErrorResponse;

      MockCurrentSnapshotService.get.mockReturnValue(throwError(error));

      return service.getWithRetry('Snappy-1', 2).then(
        () => fail('Promise Should be rejected'),
        // Called 3 times because 2 retries + 1 call to get current snapshot id
        () => expect(MockCurrentSnapshotService.get).toHaveBeenCalledTimes(3),
      );
    });

    it('rejects promise if refreshed snapshot not found and retries exhausted', async () => {
      const expectedSnapshot = {
        snapshotId: 'Snappy-1 to command, where am I?',
        created: new Date(),
        expired: new Date(),
        path: 'command to Snappy-1, you exist in my heart',
      };

      const defaultGetSnapshotResponse = <GetCurrentResponseInterface>{
        snapshot: expectedSnapshot,
      };

      MockCurrentSnapshotService.get = jest.fn(() => of(defaultGetSnapshotResponse));

      return service.getWithRetry('ag-1', 2).then(
        () => fail('Promise Should be rejected'),
        () => expect(MockCurrentSnapshotService.get).toHaveBeenCalledTimes(3),
      );
    });

    it('returns updated snapshot', async () => {
      const expectedSnapshot = {
        snapshotId: 'Snappy-1 to command, where am I?',
        created: new Date(),
        expired: new Date(),
        path: 'command to Snappy-1, you exist in my heart',
      };

      const defaultGetSnapshotResponse = <GetCurrentResponseInterface>{
        snapshot: expectedSnapshot,
      };

      MockCurrentSnapshotService.get.mockReturnValueOnce(of(defaultGetSnapshotResponse));

      // ag-1's initial snapshot loaded ahead of time
      await firstValueFrom(service.get$('ag-1'));

      const newSnapshot = {
        snapshotId: "Snappy-1 to base, I'm bringing home the bird",
        created: new Date(),
        expired: new Date(),
        path: 'the way home',
      };
      MockCurrentSnapshotService.get.mockReturnValue(of({ snapshot: newSnapshot }));

      const snapshot = await service.getWithRetry('ag-1', 2);
      // expect two calls = 1 to set the initial snapshot + 1 to get the refreshed snapshot
      expect(MockCurrentSnapshotService.get).toHaveBeenCalledTimes(2);
      expect(snapshot.snapshotId).toEqual(newSnapshot.snapshotId);
    });
  });
});
