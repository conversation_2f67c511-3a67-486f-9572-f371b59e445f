# Data Provider Quiz

## Frequently Loaded Expensive Contextual Data 

You want to take advantage of caching. 

You want to avoid wasting processor cycles or network bandwidth by caching your
data in memory.

You want to acquire the data more than one time during the lifespan of the app.

The data you acquire will be scoped to different contexts. 

For example, you might fetch the data for each unique Business (account group 
ID) that the user encounters.

--- 

## Quiz (Continued):

When you acquire new data, how do you want to store it?
- [I want to discard any previously loaded data in favour of the new context](SHORT_TERM.md)
- [I want the data from the previous context to be saved so I can get it back instantly when I come back](DATA_PROVIDER.md)
