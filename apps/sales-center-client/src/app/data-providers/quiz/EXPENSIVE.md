# Data Provider Quiz

## Expensive Data

You want to take advantage of caching. 

You want to avoid wasting processor cycles or network bandwidth by caching your
data in memory.

--- 

## Quiz (Continued):

How many times will you need to acquire a fresh version of your data?
- [Exactly once for the entire lifetime of the app](ONETIME.md)
- [Frequently. For example, every time a page is loaded](FREQUENT.md)
