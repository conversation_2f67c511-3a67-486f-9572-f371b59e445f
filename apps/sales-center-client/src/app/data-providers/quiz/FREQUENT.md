# Data Provider Quiz

## Frequently Loaded Expensive Data

You want to take advantage of caching. 

You want to avoid wasting processor cycles or network bandwidth by caching your
data in memory.

You want to acquire the data more than one time during the lifespan of the app.

--- 

## Quiz (Continued):

Which one of these statements is most true?

Every time I acquire/compute a new copy of the data:
- [My function/request parameters will be identical every time](DATA_PROVIDER.md)
- [My function/request parameters will change based on what the user is doing](FREQUENT_CONTEXTUAL.md)
- [I will use a cursor or offset so that I can load different pages of data](PAGINATION.md)
