# Static Provider

Using the Data Provider helper classes will probably **complicate your 
use-case**. 

Write a service to produce this data once. Remember to write unit tests!

```
@Injectable()
export class MyService {
    readonly myStaticData$: Observable<MyStaticData>;

    constructor(dep: MyDependency) {
        this.myStaticData$ = dep.someRequiredValue$.pipe(
          switchMap(v => this.myExpensiveWork(v)),
          // 👇 Avoids re-executing the expensive work for each new subscriber
          shareReplay(1),
        )
    }
}
```

You can then provide your service in the main `AppModule` like:

```
@NgModule({
  ...
  providers: [
    ...
    MyService,
    ...
  ],
  ...
})
export class AppModule {}
```
