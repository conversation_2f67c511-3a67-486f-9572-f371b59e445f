# Data Provider Quiz

## Frequently Loaded Expensive Paginated Data

The Data Provider helper classes **will likely compicate your use case**.

## Recommendation

Build a service to acquire this data just-in-time. Don't cache the data because 
paging and caching are often incompatible.

## Advanced

There is one exception. You *could* use a data provider to keep a copy of "page 1"
of your paged data cached and ready for the user at all times. However, this is 
an advanced technique we cannot recommend unless its absolutely necessary.
