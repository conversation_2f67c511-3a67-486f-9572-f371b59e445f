# It's a Data Provider!

Based on your answers, the Data Provider helper classes will probably:
- Make your development task easier
- Make the application perform better

The easiest way to make a DataProvider is by building a thin wrapper around the
`KeyedDataProvider`.

You will need to create a `DataProviderConfig`[(source)](../base.ts) to define
how the `KeyedDataProvider` should interact with your data source and how it 
should surface your data when it is ready.

Here is an [example](../recently-updated-businesses/source.ts).
