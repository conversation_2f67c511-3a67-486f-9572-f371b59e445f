import { Inject, Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
// eslint-disable-next-line @nx/enforce-module-boundaries
import {
  MeetingDetailsActionOrLink,
  MeetingNavigationLink,
  MeetingNavigationLinkService,
} from '@galaxy/meeting-scheduler';
import { TranslateService } from '@ngx-translate/core';
import { ProjectionFilter } from '@galaxy/account-group';
import { Meeting, WellKnownMeetingMetadataKeys } from '@vendasta/meetings';
import { Observable, of } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { AccountGroupStoreService } from '../../account-group/account-group.service';
import {
  AccountGroupMeetingLinkDialogComponent,
  AccountGroupMeetingLinkDialogData,
} from './account-group-meeting-link-dialog/account-group-meeting-link-dialog.component';
import { AccountGroupMeetingUnLinkDialogComponent } from './account-group-meeting-unlink-dialog/account-group-meeting-un-link-dialog.component';

const ACCOUNT_DETAIL_URL = `/info/`;
export type MeetingDetails = Pick<Meeting, 'metadata' | 'id' | 'attendees' | 'start' | 'end'>;

@Injectable()
export class SSCMeetingDetailsActionService implements MeetingNavigationLinkService {
  accountGroupNameCache: { [accountGroupId: string]: string } = {};

  constructor(
    @Inject(AccountGroupStoreService) private readonly accountGroup: Pick<AccountGroupStoreService, 'getMulti'>,
    @Inject(TranslateService) private readonly translate: Pick<TranslateService, 'instant'>,
    private readonly dialog: MatDialog,
  ) {}

  getPrimaryMeetingDetailsAction(meetings: MeetingDetails[]): Observable<{ [p: string]: MeetingDetailsActionOrLink }> {
    const connectAccountGroupActions = this.getConnectAccountGroupActions(meetings);
    return this.getAccountGroupLinks(meetings).pipe(
      map((accountGroupLinks) => {
        return { ...connectAccountGroupActions, ...accountGroupLinks };
      }),
    );
  }

  getSecondaryMeetingDetailsAction(
    meetings: Meeting[],
  ): Observable<{ [meetingId: string]: MeetingDetailsActionOrLink[] }> {
    const meetingsWithBusinessAssociations = meetings.filter(
      (m) => m?.metadata?.[WellKnownMeetingMetadataKeys.BUSINESS],
    );
    return of(
      meetingsWithBusinessAssociations.reduce((actions, meeting) => {
        return {
          ...actions,
          [meeting.id]: [
            {
              label: this.translate.instant('DATA_PROVIDERS.MEETING_SCHEDULER.ACCOUNT_LINK.UNLINK_ACCOUNT'),
              matIcon: 'highlight_off',
              onClick: async () => {
                this.dialog.open<AccountGroupMeetingUnLinkDialogComponent, AccountGroupMeetingLinkDialogData>(
                  AccountGroupMeetingUnLinkDialogComponent,
                  {
                    width: '800px',
                    data: { meeting },
                  },
                );
              },
            },
          ],
        };
      }, {}),
    );
  }

  connectAccountGroupForMeeting(meeting: MeetingDetails): void {
    this.dialog.open<AccountGroupMeetingLinkDialogComponent, AccountGroupMeetingLinkDialogData>(
      AccountGroupMeetingLinkDialogComponent,
      {
        width: '800px',
        data: { meeting },
      },
    );
  }

  private getConnectAccountGroupActions(meetings: MeetingDetails[]): { [meetingId: string]: MeetingNavigationLink } {
    const meetingsWithoutBusinessId = meetings.filter((m) => !m?.metadata?.[WellKnownMeetingMetadataKeys.BUSINESS]);
    return meetingsWithoutBusinessId.reduce((meetingMap, m) => {
      return {
        ...meetingMap,
        [m.id]: {
          label: this.translate.instant('DATA_PROVIDERS.MEETING_SCHEDULER.ACCOUNT_LINK.LINK_ACCOUNT'),
          matIcon: 'add',
          onClick: (clickedMeeting: Meeting) => this.connectAccountGroupForMeeting(clickedMeeting),
        },
      };
    }, {});
  }

  private getAccountGroupLinks(meetings: MeetingDetails[]): Observable<{ [meetingId: string]: MeetingNavigationLink }> {
    const meetingsWithBusinessAssociations = meetings.filter(
      (m) => m?.metadata?.[WellKnownMeetingMetadataKeys.BUSINESS],
    );
    const businessIdsNotInCache = meetingsWithBusinessAssociations
      .map((m) => m?.metadata?.[WellKnownMeetingMetadataKeys.BUSINESS])
      .filter((businessId) => !this?.accountGroupNameCache?.[businessId]);
    if (businessIdsNotInCache.length > 0) {
      return this.accountGroup.getMulti(businessIdsNotInCache, new ProjectionFilter({ napData: true })).pipe(
        tap((accountGroups) => {
          this.accountGroupNameCache = this.accountGroupNameCache || {};
          (accountGroups || []).forEach(
            (ag) => (this.accountGroupNameCache[ag.accountGroupId] = ag.napData.companyName),
          );
        }),
        map(() => this.getAccountGroupLinksFromCache(meetingsWithBusinessAssociations)),
      );
    }
    return of(this.getAccountGroupLinksFromCache(meetingsWithBusinessAssociations));
  }

  private getAccountGroupLinksFromCache(meetings: MeetingDetails[]): { [meetingId: string]: MeetingNavigationLink } {
    return meetings
      .filter((m) => Boolean(this.accountGroupNameCache[m?.metadata?.[WellKnownMeetingMetadataKeys.BUSINESS]]))
      .reduce((linkMap, meeting) => {
        const businessId = meeting?.metadata?.[WellKnownMeetingMetadataKeys.BUSINESS];
        return {
          ...linkMap,
          [meeting.id]: {
            label: this.accountGroupNameCache[businessId] || '',
            link: `${ACCOUNT_DETAIL_URL}${businessId}`,
          },
        };
      }, {});
  }
}
