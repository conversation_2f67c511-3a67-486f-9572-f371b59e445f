<h2 mat-dialog-title>
  {{ 'DATA_PROVIDERS.MEETING_SCHEDULER.ACCOUNT_LINK.TITLE' | translate }}
</h2>
<mat-dialog-content>
  <div class="row row-gutters details-container">
    <div class="col col-xs-12 col-md-4">
      <div class="details-inner-container">
        <mat-icon>calendar_today</mat-icon>
        <div class="details-section">
          <h4 class="subtitle">
            {{ 'DATA_PROVIDERS.MEETING_SCHEDULER.ACCOUNT_LINK.MEETING_DETAILS' | translate }}
          </h4>
          <p class="details highlighted">
            {{ dialogData.meeting.start | date: 'longDate' }}
          </p>
          <p class="details">
            {{ dialogData.meeting.start | date: 'shortTime' }} -
            {{ dialogData.meeting.end | date: 'shortTime' }}
          </p>
        </div>
      </div>
    </div>
    <ng-container *ngIf="accountSelected$ | async; else guests">
      <div class="col col-xs-12 col-md-8">
        <div class="details-inner-container contact-details">
          <mat-icon>person</mat-icon>
          <div class="details-section">
            <h4 class="subtitle contact-create-subtitle">
              {{ 'DATA_PROVIDERS.MEETING_SCHEDULER.ACCOUNT_LINK.CREATE_CONTACT' | translate }}
              <mat-icon
                class="info"
                color="primary"
                [matTooltip]="'DATA_PROVIDERS.MEETING_SCHEDULER.ACCOUNT_LINK.INFO_TOOLTIP' | translate"
              >
                info
              </mat-icon>
            </h4>
            <div *ngFor="let attendee of dialogData.meeting.attendees">
              <!-- TODO rlaforge MEET-281 allow for more than just primary contact when sales supports it better (not showing undefined on login, not returning error) -->
              <ng-container *ngIf="attendee.isPrimary">
                <mat-checkbox
                  *ngIf="!attendeeAlreadyAContact(attendee, contactsOnAccountGroup$ | async); else disabledCheckbox"
                  [(ngModel)]="shouldCreateContact"
                >
                  <ng-container
                    [ngTemplateOutlet]="attendeeDisplay"
                    [ngTemplateOutletContext]="{ attendee: attendee }"
                  ></ng-container>
                </mat-checkbox>
                <ng-template #disabledCheckbox>
                  <mat-checkbox
                    [matTooltip]="
                      'DATA_PROVIDERS.MEETING_SCHEDULER.ACCOUNT_LINK.ALREADY_A_CONTACT'
                        | translate: { contact: attendee.email }
                    "
                    [disabled]="true"
                  >
                    <ng-container
                      [ngTemplateOutlet]="attendeeDisplay"
                      [ngTemplateOutletContext]="{ attendee: attendee }"
                    ></ng-container>
                  </mat-checkbox>
                </ng-template>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
    </ng-container>
    <ng-template #guests>
      <div class="col col-xs-12 col-md-8">
        <div class="details-inner-container contact-details">
          <mat-icon>person</mat-icon>
          <div class="details-section">
            <h4 class="subtitle">
              {{ 'DATA_PROVIDERS.MEETING_SCHEDULER.ACCOUNT_LINK.GUESTS' | translate }}
            </h4>
            <div *ngFor="let attendee of dialogData.meeting.attendees">
              <ng-container
                [ngTemplateOutlet]="attendeeDisplay"
                [ngTemplateOutletContext]="{ attendee: attendee }"
              ></ng-container>
            </div>
          </div>
        </div>
      </div>
    </ng-template>

    <ng-template #attendeeDisplay let-attendee="attendee">
      <span class="details" [ngClass]="{ highlighted: attendee.isPrimary }">
        <ng-container *ngIf="attendee.firstName || attendee.lastName">
          {{ attendee.firstName }} {{ attendee.lastName }}
        </ng-container>
        <ng-container *ngIf="!(attendee.firstName || attendee.lastName)">
          {{ attendee.email }}
        </ng-container>
      </span>
      <ng-container *ngIf="attendee.firstName || attendee.lastName">
        <br *ngIf="onMobile$ | async" />
        <span class="details email">({{ attendee.email }})</span>
      </ng-container>
    </ng-template>
  </div>
  <app-account-group-search (accountGroupSelected)="onAccountGroupSelected($event)"></app-account-group-search>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-dialog-close mat-button color="primary">
    {{ 'DATA_PROVIDERS.MEETING_SCHEDULER.ACCOUNT_LINK.CANCEL' | translate }}
  </button>
  <button
    mat-raised-button
    color="primary"
    [disabled]="(accountSelected$ | async) === false"
    (click)="linkAccountToMeeting()"
  >
    {{ 'DATA_PROVIDERS.MEETING_SCHEDULER.ACCOUNT_LINK.LINK_ACCOUNT' | translate }}
  </button>
</mat-dialog-actions>
