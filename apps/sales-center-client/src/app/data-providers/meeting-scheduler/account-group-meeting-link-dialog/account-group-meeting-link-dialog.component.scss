@use 'design-tokens' as *;
@import 'utilities';

.title {
  font-weight: normal;
  margin: 0;
}

.subtitle {
  font-weight: bold;
  margin: 0 0 $spacing-1;
}

.details-inner-container {
  display: flex;
  flex-direction: row;

  @include phone() {
    justify-content: flex-start;
  }
}

.contact-details {
  justify-content: flex-end;
  @include phone() {
    justify-content: flex-start;
  }
}

.details {
  margin: 0;
  color: $secondary-font-color;

  &.highlighted {
    color: $primary-font-color;
  }
}

.email {
  font-size: $font-preset-5-size;
  @include tablet() {
    font-size: $font-preset-4-size;
  }
}

.details-section {
  margin-left: $spacing-3;
}

.details-container {
  margin-bottom: $spacing-3;
}

.info {
  cursor: pointer;
  margin-left: $spacing-1;
}

.contact-create-subtitle {
  display: flex;
}

.contact-shimmer {
  width: 50%;
  height: $spacing-3;
}
