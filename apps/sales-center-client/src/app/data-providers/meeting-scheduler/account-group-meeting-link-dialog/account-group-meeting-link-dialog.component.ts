import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { MeetingSchedulerStoreService } from '@galaxy/meeting-scheduler';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Attendee, Meeting } from '@vendasta/meetings';
import { ContactCreateAttributesInterface } from '@vendasta/sales-v2';
import { BehaviorSubject, concat, EMPTY, Observable, of } from 'rxjs';
import { catchError, first, last, map, shareReplay, switchMap } from 'rxjs/operators';
import { ContactShadow, ContactsV2Service } from '../../../common/contacts';
import { AllContactFields } from '../../../common/contacts/contact-field.enum';

export interface AccountGroupMeetingLinkDialogData {
  meeting: Pick<Meeting, 'metadata' | 'id' | 'attendees' | 'start' | 'end'>;
}

@Component({
  selector: 'app-account-group-meeting-link-dialog',
  templateUrl: './account-group-meeting-link-dialog.component.html',
  styleUrls: ['./account-group-meeting-link-dialog.component.scss'],
  standalone: false,
})
export class AccountGroupMeetingLinkDialogComponent implements OnInit {
  contactsOnAccountGroup$: Observable<ContactShadow[]>;

  accountSelected$: Observable<boolean>;

  shouldCreateContact = true;
  linkingAccount = false;
  private readonly selectedAccountGroupId$$: BehaviorSubject<string | undefined> = new BehaviorSubject(undefined);
  onMobile$: Observable<boolean>;

  constructor(
    @Inject(MAT_DIALOG_DATA) public readonly dialogData: AccountGroupMeetingLinkDialogData,
    private readonly dialogRef: MatDialogRef<AccountGroupMeetingLinkDialogData>,
    @Inject(ContactsV2Service) private readonly contactService: ContactsV2Service,
    @Inject(MeetingSchedulerStoreService)
    private readonly meetingSchedulerStoreService: Pick<MeetingSchedulerStoreService, 'linkBusinessToMeeting'>,
    private readonly alertService: SnackbarService,
    private readonly breakpointObserver: BreakpointObserver,
  ) {
    this.onMobile$ = this.breakpointObserver
      .observe([Breakpoints.Handset, Breakpoints.Small, Breakpoints.XSmall])
      .pipe(map((result) => result.matches));
  }

  ngOnInit(): void {
    const accountGroupId$ = this.selectedAccountGroupId$$.pipe(map((accountGroupId) => accountGroupId || ''));
    this.accountSelected$ = this.selectedAccountGroupId$$.pipe(map((agid) => Boolean(agid)));

    this.contactsOnAccountGroup$ = accountGroupId$.pipe(
      switchMap((accountGroupId) => {
        if (!accountGroupId) {
          return of([]);
        }
        return this.contactService.list$(accountGroupId, ...AllContactFields).pipe(
          catchError(() => {
            this.alertService.openErrorSnack('DATA_PROVIDERS.MEETING_SCHEDULER.ACCOUNT_LINK.LOAD_CONTACT_ERROR');
            return of([]);
          }),
        );
      }),
      shareReplay(1),
    );
  }

  onAccountGroupSelected(accountGroupId: string): void {
    this.selectedAccountGroupId$$.next(accountGroupId || '');
  }

  attendeeAlreadyAContact(attendee: Attendee, contacts: ContactShadow[]): boolean {
    return Boolean((contacts || []).find((c) => c.contactEmail === attendee.email));
  }

  linkAccountToMeeting(): void {
    this.linkingAccount = true;
    let work$: Observable<void | ContactShadow> = this.meetingSchedulerStoreService
      .linkBusinessToMeeting({
        businessId: this.selectedAccountGroupId$$.getValue(),
        meetingId: this.dialogData.meeting.id,
      })
      .pipe(
        first(),
        catchError(() => {
          this.alertService.openErrorSnack('DATA_PROVIDERS.MEETING_SCHEDULER.ACCOUNT_LINK.ERROR');
          return EMPTY;
        }),
      );

    if (this.shouldCreateContact) {
      work$ = concat(work$, this.createContactForPrimaryAttendee$()).pipe(last());
    }

    work$.subscribe(() => {
      this.linkingAccount = false;
      this.alertService.openSuccessSnack('DATA_PROVIDERS.MEETING_SCHEDULER.ACCOUNT_LINK.SUCCESS');
      this.dialogRef.close();
    });
  }

  createContactForPrimaryAttendee$(): Observable<ContactShadow> {
    const primaryAttendee = (this.dialogData.meeting.attendees || []).find((a) => a.isPrimary);

    return this.contactsOnAccountGroup$.pipe(
      first(),
      map((existingContacts) =>
        existingContacts.find(
          (contact) => contact?.contactEmail?.toLowerCase() === primaryAttendee.email.toLowerCase(),
        ),
      ),
      map((contactForAttendee: ContactShadow | undefined) => {
        if (contactForAttendee !== undefined) {
          throw new Error('tried to create a new contact for primary attendee when one already exists');
        }

        return <ContactCreateAttributesInterface>{
          firstName: primaryAttendee.firstName,
          lastName: primaryAttendee.lastName,
          email: primaryAttendee.email,
          phoneNumber: primaryAttendee.phoneNumber ?? '',
        };
      }),
      switchMap((contactAttr) =>
        this.contactService.create$(this.selectedAccountGroupId$$.getValue(), contactAttr).pipe(
          catchError(() => {
            this.alertService.openErrorSnack('CONTACTS.CREATE_ERROR');
            return EMPTY;
          }),
        ),
      ),
    );
  }
}
