<h2 mat-dialog-title>
  {{ 'DATA_PROVIDERS.MEETING_SCHEDULER.ACCOUNT_UNLINK.TITLE' | translate }}
</h2>
<mat-dialog-content>
  <div class="row row-gutters details-container">
    <div class="col col-xs-12 col-md-4">
      <div class="details-inner-container">
        <mat-icon>calendar_today</mat-icon>
        <div class="details-section">
          <h4 class="subtitle">
            {{ 'DATA_PROVIDERS.MEETING_SCHEDULER.ACCOUNT_LINK.MEETING_DETAILS' | translate }}
          </h4>
          <p class="details highlighted">
            {{ dialogData.meeting.start | date: 'longDate' }}
          </p>
          <p class="details">
            {{ dialogData.meeting.start | date: 'shortTime' }} -
            {{ dialogData.meeting.end | date: 'shortTime' }}
          </p>
        </div>
      </div>
    </div>
  </div>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-dialog-close mat-button color="primary">
    {{ 'DATA_PROVIDERS.MEETING_SCHEDULER.ACCOUNT_LINK.CANCEL' | translate }}
  </button>
  <button mat-raised-button color="primary" (click)="unlinkAccountToMeeting()">
    {{ 'DATA_PROVIDERS.MEETING_SCHEDULER.ACCOUNT_LINK.UNLINK_ACCOUNT' | translate }}
  </button>
</mat-dialog-actions>
