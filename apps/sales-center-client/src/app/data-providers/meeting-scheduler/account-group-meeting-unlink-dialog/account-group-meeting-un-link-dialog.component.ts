import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { MeetingSchedulerStoreService } from '@galaxy/meeting-scheduler';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Meeting } from '@vendasta/meetings';

export interface AccountGroupMeetingLinkDialogData {
  meeting: Pick<Meeting, 'metadata' | 'id' | 'attendees' | 'start' | 'end'>;
}

@Component({
  selector: 'app-account-group-meeting-unlink-dialog',
  templateUrl: './account-group-meeting-un-link-dialog.component.html',
  styleUrls: ['./account-group-meeting-un-link-dialog.component.scss'],
  standalone: false,
})
export class AccountGroupMeetingUnLinkDialogComponent {
  constructor(
    @Inject(MAT_DIALOG_DATA) public readonly dialogData: AccountGroupMeetingLinkDialogData,
    private readonly dialogRef: MatDialogRef<AccountGroupMeetingUnLinkDialogComponent>,
    @Inject(MeetingSchedulerStoreService)
    private readonly meetingSchedulerStoreService: Pick<MeetingSchedulerStoreService, 'unlinkBusinessFromMeeting'>,
    private readonly alertService: SnackbarService,
    private readonly translate: TranslateService,
  ) {}

  async unlinkAccountToMeeting(): Promise<void> {
    try {
      await this.meetingSchedulerStoreService.unlinkBusinessFromMeeting({ meetingId: this.dialogData.meeting.id });
      this.alertService.openSuccessSnack('DATA_PROVIDERS.MEETING_SCHEDULER.ACCOUNT_LINK.UNLINK_ACCOUNT_SUCCESS');
      this.dialogRef.close();
    } catch (e) {
      console.error(e);
      this.alertService.openErrorSnack('DATA_PROVIDERS.MEETING_SCHEDULER.ACCOUNT_LINK.UNLINK_ACCOUNT_ERROR');
    }
  }
}
