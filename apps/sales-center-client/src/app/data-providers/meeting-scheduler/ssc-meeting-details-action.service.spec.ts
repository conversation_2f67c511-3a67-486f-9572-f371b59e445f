import { AccountGroupStoreService } from '../../account-group/account-group.service';
import { of } from 'rxjs';
import { MeetingDetails, SSCMeetingDetailsActionService } from './ssc-meeting-details-action.service';
import { TranslateService } from '@ngx-translate/core';
import { TestScheduler } from 'rxjs/testing';
import { JestScheduler } from '@vendasta/rx-utils';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { MeetingDetailsAction, MeetingNavigationLink } from '@galaxy/meeting-scheduler';
import Mock = jest.Mock;
import { ProjectionFilter } from '@galaxy/account-group';
import { WellKnownMeetingMetadataKeys } from '@vendasta/meetings';

function meetingsWithoutBusinessLinksFixture(): MeetingDetails[] {
  return [
    { id: 'without-1', metadata: undefined, attendees: [], start: new Date(), end: new Date() },
    { id: 'without-2', metadata: {}, attendees: [], start: new Date(), end: new Date() },
    { id: 'without-3', metadata: { msm_bus: 'AG-111' }, attendees: [], start: new Date(), end: new Date() },
  ];
}

function meetingsWithBusinessLinksFixture(): MeetingDetails[] {
  const key = WellKnownMeetingMetadataKeys.BUSINESS;
  return [
    { id: 'with-1', metadata: { [key]: 'AG-1' }, attendees: [], start: new Date(), end: new Date() },
    { id: 'with-2', metadata: { [key]: 'AG-2' }, attendees: [], start: new Date(), end: new Date() },
    { id: 'with-3', metadata: { [key]: 'AG-3' }, attendees: [], start: new Date(), end: new Date() },
  ];
}

function expectedAccountLinksFixture(): { [meetingId: string]: MeetingNavigationLink } {
  return {
    'with-1': { label: 'Company 1', link: '/info/AG-1' },
    'with-2': { label: 'Company 2', link: '/info/AG-2' },
    'with-3': { label: 'Company 3', link: '/info/AG-3' },
  };
}

function expectedLinkAccountsFixture(): { [meetingId: string]: MeetingDetailsAction } {
  return {
    'without-1': { label: 'Link account', matIcon: 'add', onClick: expect.any(Function) },
    'without-2': { label: 'Link account', matIcon: 'add', onClick: expect.any(Function) },
    'without-3': { label: 'Link account', matIcon: 'add', onClick: expect.any(Function) },
  };
}

function businessNameCacheFixture(): { [businessId: string]: string } {
  return {
    'AG-1': 'Company 1',
    'AG-2': 'Company 2',
    'AG-3': 'Company 3',
  };
}

describe('SSCMeetingDetailsActionService', () => {
  let accountGroupStub: Pick<AccountGroupStoreService, 'getMulti'>;
  let translateService: Pick<TranslateService, 'instant'>;
  let service: SSCMeetingDetailsActionService;
  let scheduler: TestScheduler;
  beforeEach(() => {
    translateService = {
      instant: jest.fn((translate: string) => {
        return translate === 'DATA_PROVIDERS.MEETING_SCHEDULER.ACCOUNT_LINK.LINK_ACCOUNT' ? 'Link account' : translate;
      }),
    };
    accountGroupStub = { getMulti: jest.fn(() => of(undefined)) };
    service = new SSCMeetingDetailsActionService(accountGroupStub, translateService, undefined);
    scheduler = new JestScheduler();
  });

  it('should not call the AccountGroup service if all business names are already loaded', () => {
    service.accountGroupNameCache = businessNameCacheFixture();
    const result$ = service.getPrimaryMeetingDetailsAction([
      ...meetingsWithBusinessLinksFixture(),
      ...meetingsWithoutBusinessLinksFixture(),
    ]);
    scheduler
      .expectObservable(result$)
      .toBe('(x|)', { x: { ...expectedAccountLinksFixture(), ...expectedLinkAccountsFixture() } });
    expect((accountGroupStub.getMulti as Mock).mock.calls.length).toBe(0);
    scheduler.flush();
  });

  it('should call the AccountGroup service if there is no cache', () => {
    service.accountGroupNameCache = undefined;

    accountGroupStub.getMulti = jest.fn(() =>
      of([
        { accountGroupId: 'AG-1', napData: { companyName: 'Company 1' } },
        { accountGroupId: 'AG-2', napData: { companyName: 'Company 2' } },
        { accountGroupId: 'AG-3', napData: { companyName: 'Company 3' } },
      ]),
    );
    const result$ = service.getPrimaryMeetingDetailsAction([
      ...meetingsWithBusinessLinksFixture(),
      ...meetingsWithoutBusinessLinksFixture(),
    ]);
    scheduler
      .expectObservable(result$)
      .toBe('(x|)', { x: { ...expectedAccountLinksFixture(), ...expectedLinkAccountsFixture() } });
    expect((accountGroupStub.getMulti as Mock).mock.calls.length).toBe(1);
    expect((accountGroupStub.getMulti as Mock).mock.calls[0]).toEqual([
      ['AG-1', 'AG-2', 'AG-3'],
      new ProjectionFilter({ napData: true }),
    ]);
    scheduler.flush();
  });

  it('should return only actions if the AccountGroup service returns undefined and there is no cache', () => {
    service.accountGroupNameCache = undefined;
    accountGroupStub.getMulti = jest.fn(() => of(undefined));
    const result$ = service.getPrimaryMeetingDetailsAction([
      ...meetingsWithBusinessLinksFixture(),
      ...meetingsWithoutBusinessLinksFixture(),
    ]);
    scheduler.expectObservable(result$).toBe('(x|)', { x: expectedLinkAccountsFixture() });
    expect((accountGroupStub.getMulti as Mock).mock.calls.length).toBe(1);
    expect((accountGroupStub.getMulti as Mock).mock.calls[0]).toEqual([
      ['AG-1', 'AG-2', 'AG-3'],
      new ProjectionFilter({ napData: true }),
    ]);
    scheduler.flush();
  });

  it('should return only link accounts if the AccountGroup service returns an empty list', () => {
    service.accountGroupNameCache = undefined;
    accountGroupStub.getMulti = jest.fn(() => of([]));
    const result$ = service.getPrimaryMeetingDetailsAction([
      ...meetingsWithBusinessLinksFixture(),
      ...meetingsWithoutBusinessLinksFixture(),
    ]);
    scheduler.expectObservable(result$).toBe('(x|)', { x: expectedLinkAccountsFixture() });
    expect((accountGroupStub.getMulti as Mock).mock.calls.length).toBe(1);
    expect((accountGroupStub.getMulti as Mock).mock.calls[0]).toEqual([
      ['AG-1', 'AG-2', 'AG-3'],
      new ProjectionFilter({ napData: true }),
    ]);
    scheduler.flush();
  });

  it('should only call AccountGroup with ids not already cached', () => {
    const cache = businessNameCacheFixture();
    delete cache['AG-2'];
    service.accountGroupNameCache = cache;

    accountGroupStub.getMulti = jest.fn(() => of([{ accountGroupId: 'AG-2', napData: { companyName: 'Company 2' } }]));
    const result$ = service.getPrimaryMeetingDetailsAction([
      ...meetingsWithBusinessLinksFixture(),
      ...meetingsWithoutBusinessLinksFixture(),
    ]);
    scheduler
      .expectObservable(result$)
      .toBe('(x|)', { x: { ...expectedAccountLinksFixture(), ...expectedLinkAccountsFixture() } });
    expect((accountGroupStub.getMulti as Mock).mock.calls.length).toBe(1);
    expect((accountGroupStub.getMulti as Mock).mock.calls[0]).toEqual([
      ['AG-2'],
      new ProjectionFilter({ napData: true }),
    ]);
    scheduler.flush();
  });
});
