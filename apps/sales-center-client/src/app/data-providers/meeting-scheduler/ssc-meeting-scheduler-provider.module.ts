import { NgModule } from '@angular/core';
import { AccountGroupMeetingLinkDialogComponent } from './account-group-meeting-link-dialog/account-group-meeting-link-dialog.component';
import { AccountGroupMeetingUnLinkDialogComponent } from './account-group-meeting-unlink-dialog/account-group-meeting-un-link-dialog.component';
import { MatDialogModule } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { AccountGroupSearchModule } from '../../common/search/account-group-search';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';

@NgModule({
  declarations: [AccountGroupMeetingLinkDialogComponent, AccountGroupMeetingUnLinkDialogComponent],
  imports: [
    MatDialogModule,
    CommonModule,
    AccountGroupSearchModule,
    MatCheckboxModule,
    MatTooltipModule,
    MatButtonModule,
    MatIconModule,
    TranslateModule,
    FormsModule,
  ],
})
export class SscMeetingSchedulerProviderModule {}
