import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { LoggedInUserInfoService } from '../logged-in-user-info';
import { map, switchMap, take, shareReplay } from 'rxjs/operators';
import { TaxonomyServiceInterface, TaxonomyOption } from '@vendasta/taxonomy';
import { PartnerCenterHostService } from '../core';

const GET_TAXONOMY_URL = '/api/v1/taxonomy';

export interface TaxonomyService {
  getCategory$(categoryId: string): Observable<string>;
  getTaxonomyIds(): Observable<TaxonomyOption[]>;
}

@Injectable()
export class TaxonomyWrapperService implements TaxonomyService, TaxonomyServiceInterface {
  constructor(
    readonly http: HttpClient,
    readonly loggedInUser: LoggedInUserInfoService,
    readonly parterHost: PartnerCenterHostService,
  ) {}

  getCategory$(businessCategoryId: string): Observable<string> {
    return this.loggedInUser.getPartnerIdAndMarketId$().pipe(
      map((partnerMarket) => {
        return {
          vtaxCategoryId: businessCategoryId.toLowerCase(),
          partnerId: partnerMarket.partnerId,
          marketId: partnerMarket.marketId,
        };
      }),
      switchMap((params) => {
        return this.http.get<{ data: string }>(GET_TAXONOMY_URL, { params: params, withCredentials: true });
      }),
      map((res) => res.data),
      take(1),
    );
  }

  getTaxonomyIds(): Observable<TaxonomyOption[]> {
    const url = '/api/v1/taxonomy/list/';

    return this.loggedInUser.getPartnerIdAndMarketId$().pipe(
      switchMap((partnerMarket) => {
        return this.http.get<{ data: TaxonomyOption[] }>(url, {
          params: { partnerId: partnerMarket.partnerId, marketId: partnerMarket.marketId },
          withCredentials: true,
        });
      }),
      map((res) => res.data),
      shareReplay(),
    );
  }
}
