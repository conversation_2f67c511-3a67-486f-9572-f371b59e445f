<glxy-page [pagePadding]="false">
  <glxy-page-toolbar>
    <glxy-page-title>
      <!-- leaving this here as I don't have time to pull out the business name -->
      <!-- This should turn into using the `<glxy-page-nav-button>` and `<glxy-page-title>` in the futute -->
      <app-nav-breadcrumbs [breadCrumbs]="breadCrumbs$ | async"></app-nav-breadcrumbs>
    </glxy-page-title>
  </glxy-page-toolbar>

  <div class="filter-container">
    <va-filter2-model
      data-cy="history-filter"
      [filterGroup]="filter"
      [defaultSidebarState]="sideBarVisible"
      [toolTip]="toolTipText$ | async"
    >
      <va-filter-content>
        <div class="history-feed" data-cy="history-feed">
          <ng-container *ngIf="historyData$ | async as data; else loading">
            <uikit-async-ui [loading]="data.loading" [error]="!data.success" [data]="data.items">
              <div class="history-feed__item" *ngFor="let history of data.items">
                <app-account-history-card [historyItem]="history"></app-account-history-card>
              </div>
              <ng-container loading [ngTemplateOutlet]="loading"></ng-container>
              <ng-container error [ngTemplateOutlet]="error"></ng-container>
            </uikit-async-ui>
          </ng-container>
          <div class="load-more-container" *ngIf="hasMore$ | async">
            <div class="load-more-artifact">
              <mat-spinner [diameter]="64" [strokeWidth]="8" *ngIf="moreLoading$ | async"></mat-spinner>
            </div>
            <div class="load-more-artifact">
              <button mat-raised-button color="primary" (click)="loadMore()">
                {{ 'COMMON.ACTION_LABELS.LOAD_MORE' | translate }}
              </button>
            </div>
          </div>
        </div>
      </va-filter-content>
    </va-filter2-model>
  </div>

  <ng-template #loading>
    <div class="loading-container">
      <uikit-list-stencil rowHeight="128px" [numRows]="6" [showHeader]="false" [matElevation]="0"></uikit-list-stencil>
    </div>
  </ng-template>

  <ng-template #error>
    <div class="loading-container">
      <uikit-empty-state
        [title]="'ACCOUNT_HISTORY.LOADING_ERROR' | translate"
        [ctaPrimaryText]="'COMMON.ACTION_LABELS.CLICK_TO_RETRY' | translate"
        (ctaPrimaryEvent)="retryLoading()"
      ></uikit-empty-state>
    </div>
  </ng-template>
</glxy-page>
