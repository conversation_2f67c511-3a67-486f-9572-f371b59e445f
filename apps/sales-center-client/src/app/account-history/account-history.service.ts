import { Inject, Injectable, Optional } from '@angular/core';
import { BehaviorSubject, combineLatest, Observable, of, ReplaySubject, SchedulerLike } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ObservableWorkStateMap } from '@vendasta/rx-utils/work-state';
import { map, switchMap } from 'rxjs/operators';
import { doWorkIfInitial } from '../rx-utils/state-maps';
import { ActivityType, SalesPersonAction } from './account-history.component';

const ACCOUNT_HISTORY_URL = '/ajax/history/';

export enum HistoryType {
  ACCOUNT_CREATION = 'account-creation',
  SALES_PERSON_ASSIGNMENT = 'sales-person-assignment',
  SALES_PERSON_REASSIGNMENT = 'sales-person-reassignment',
  CAMPAIGN_INFORMATION = 'campaign-information',
  STATUS_CHANGE = 'status-change',
  SALES_RECORD_DELETE = 'sales-record-delete',
  EMAIL_EVENT = 'email-event',
  IN_APP_ACTIVITY_EVENT = 'in-app-activity-event',
  BUSINESS_ARCHIVE_STATE_CHANGE = 'business-archive-state-change',
  USER_EVENT = 'user-event',
}

interface RawAccountHistory {
  action: string;
  current_salesperson_name: string;
  description: string;
  event_date: string;
  history_id: string;
  history_type: string;
  notes: string;
  salesperson_picture: string;
  vbc_user_id: string;
  activity_detail: string;
}

interface AccountHistoryRawResponse {
  data: RawAccountHistory[];
  totalResults: number;
  nextQueryString: string;
}

interface AccountHistoryResponse {
  data: AccountHistory[];
  totalResults: number;
  nextQueryString: string;
}

export interface AccountHistory {
  action: string;
  currentSalespersonName: string;
  description: string;
  eventDate: Date;
  historyId: string;
  historyType: HistoryType;
  notes: string;
  salespersonPictureUrl: string;
  vbcUserId?: string;
  vbcUserName?: string;
  activityDetail: string;
}

export interface LoadHistoryEvent {
  businessId: string;
  activityTypes: ActivityType[];
  salesPersonAction: SalesPersonAction;
}

export interface LoadMoreHistoryEvent {
  nextQueryString: string;
  businessId: string;
  currentHistory?: [];
}

@Injectable()
export class AccountHistoryService {
  private readonly loadMoreHistoryEvent$$ = new BehaviorSubject<LoadMoreHistoryEvent>(null);
  private readonly loadHistoryEvent$$ = new ReplaySubject<LoadHistoryEvent>(1);

  private readonly accountHistoryWSM = new ObservableWorkStateMap<string, AccountHistoryResponse>();

  history$: Observable<AccountHistoryResponse>;

  constructor(
    private readonly http: HttpClient,
    @Optional() @Inject('Scheduler') private readonly scheduler?: SchedulerLike,
  ) {
    const initialHistory$ = this.loadHistoryEvent$$.pipe(
      switchMap((event) => this.loadHistory(event.businessId, event.activityTypes, event.salesPersonAction)),
    );
    const moreHistory$ = this.loadMoreHistoryEvent$$.pipe(
      switchMap((event) => {
        return event !== null ? this.load(event.nextQueryString, event.businessId, event.currentHistory) : of(null);
      }),
    );
    this.history$ = combineLatest([initialHistory$, moreHistory$]).pipe(
      map(([initial, more]) => {
        return <AccountHistoryResponse>{
          data: more === null ? initial.data : more.data,
          totalResults: initial.totalResults,
          nextQueryString: more === null ? initial.nextQueryString : more.nextQueryString,
        };
      }),
    );
  }

  static rawHistoryToHistory(rawHistory: RawAccountHistory): AccountHistory {
    return <AccountHistory>{
      action: rawHistory.action,
      currentSalespersonName: rawHistory.current_salesperson_name,
      description: rawHistory.description,
      eventDate: new Date(rawHistory.event_date),
      historyId: rawHistory.history_id,
      historyType: rawHistory.history_type,
      notes: rawHistory.notes,
      salespersonPictureUrl: rawHistory.salesperson_picture,
      vbcUserId: rawHistory.vbc_user_id,
      activityDetail: rawHistory.activity_detail,
    };
  }

  static buildFilterFormData(
    history: ActivityType[],
    action: SalesPersonAction,
    businessId: string,
    cursor = 0,
  ): string {
    const historyFilter: string = history.reduce((acc, current) => `${acc}historyTypeFilter=${current}&`, '');
    return `${historyFilter}salesPersonActionFilter=${action}&accountGroupId=${businessId}&cursor=${cursor}`;
  }

  static buildWorkStateKey(businessId: string, query: string): string {
    return `${ACCOUNT_HISTORY_URL}${businessId}/?${query}`;
  }

  loadMoreHistory(historyItems: AccountHistory[], nextQueryString: string, businessId: string): void {
    this.loadMoreHistoryEvent$$.next(<LoadMoreHistoryEvent>{
      nextQueryString: nextQueryString,
      currentHistory: historyItems,
      businessId: businessId,
    });
  }

  loadInitialHistory(businessId: string, historyTypes: ActivityType[], salesPersonAction: SalesPersonAction): void {
    this.loadHistoryEvent$$.next(<LoadHistoryEvent>{
      businessId: businessId,
      activityTypes: historyTypes,
      salesPersonAction: salesPersonAction,
    });
  }

  private loadHistory(
    businessId: string,
    historyTypes: ActivityType[],
    salesPersonAction: SalesPersonAction,
  ): Observable<AccountHistoryResponse> {
    const formData = AccountHistoryService.buildFilterFormData(historyTypes, salesPersonAction, businessId);
    return this.load(formData, businessId);
  }

  private load(
    formData: string,
    businessId: string,
    loadedHistory: AccountHistory[] = [],
  ): Observable<AccountHistoryResponse> {
    const url = `${ACCOUNT_HISTORY_URL}${businessId}/?${formData}`;
    const work = () =>
      this.http.post<AccountHistoryRawResponse>(url, {}, { withCredentials: true }).pipe(
        map((res: AccountHistoryRawResponse) => {
          return {
            data: [...loadedHistory, ...res.data.map(AccountHistoryService.rawHistoryToHistory)],
            totalResults: res.totalResults,
            nextQueryString: res.nextQueryString,
          };
        }),
      );
    return doWorkIfInitial(url, this.accountHistoryWSM, work, this.scheduler || undefined);
  }

  loading$(url): Observable<boolean> {
    return this.accountHistoryWSM.isLoading$(url);
  }

  success$(url): Observable<boolean> {
    return this.accountHistoryWSM.isSuccess$(url);
  }

  resetItems(): void {
    this.loadMoreHistoryEvent$$.next(null);
  }
}
