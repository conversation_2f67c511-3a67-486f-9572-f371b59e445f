<mat-card appearance="outlined">
  <mat-card-header>
    <img mat-card-avatar *ngIf="historyItem.salespersonPictureUrl !== ''" [src]="historyItem.salespersonPictureUrl" />
    <img *ngIf="historyItem.salespersonPictureUrl === ''" src="/static/images/Profile.svg" mat-card-avatar />
    <mat-card-title [innerHTML]="historyItem.vbcUserName || historyItem.currentSalespersonName"></mat-card-title>
    <mat-card-subtitle>{{ historyItem.eventDate }}</mat-card-subtitle>
  </mat-card-header>
  <mat-card-content class="content">
    <p *ngIf="historyItem.action" [innerHTML]="historyItem.action"></p>
    <p>{{ historyItem.description }}</p>
  </mat-card-content>
</mat-card>
