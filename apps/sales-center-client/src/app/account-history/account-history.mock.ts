/** tslint: disable **/
export const mockAccountHistoryResponse = {
  totalResults: 26,
  version: '0.0',
  data: [
    {
      current_salesperson_name: '<PERSON>',
      history_type: 'email-event',
      description: '',
      salesperson_picture: '',
      history_id: 'salestool-demo/123',
      action: '<span class="customer-name"><PERSON><PERSON></span> was delivered email: How to Make Friends',
      event_date: '2019-04-05T22:02:45Z',
      notes: null,
    },
    {
      current_salesperson_name: null,
      history_type: 'campaign-information',
      description: '',
      salesperson_picture: '',
      history_id: 'salestool-demo/456',
      action: 'Business Assigned to Campaign How to Make Friends',
      event_date: '2019-04-05T21:59:43Z',
      notes: null,
    },
    {
      current_salesperson_name: '<PERSON>',
      history_type: 'account-creation',
      description: '',
      salesperson_picture: 'this-persons-face',
      history_id: 'salestool-demo/789',
      action: 'Account Created',
      event_date: '2019-03-27T22:52:21Z',
      notes: null,
    },
  ],
  statusCode: 200,
  nextQueryString: 'next-url',
};

export const mockAccountLoadMoreResponse = {
  totalResults: 26,
  version: '0.0',
  requestId: '5d09588a00ff0b64b5902e16b00001737e73616c6573746f6f6c2d64656d6f0001636f6e74696e756f75730001010a',
  responseTime: 968,
  data: [
    {
      current_salesperson_name: 'Locke Cole',
      history_type: 'account-creation',
      description: '',
      salesperson_picture: 'face-picture',
      history_id: 'salestool-demo/101112',
      action: 'Account Created via Sales & Success Center',
      event_date: '2019-02-13T21:06:39Z',
      notes: null,
    },
  ],
  statusCode: 200,
  nextQueryString: 'next-url2',
};

export const mockAccountLoadEvenMoreResponse = {
  totalResults: 26,
  version: '0.0',
  requestId: '5d09588a00ff0b64b5902e16b00001737e73616c6573746f6f6c2d64656d6f0001636f6e74696e756f75730001010a',
  responseTime: 968,
  data: [
    {
      action: '<span class="customer-name">mr person</span> bounced on email: Snapshot',
      current_salesperson_name: 'Locke Cole',
      description: '',
      event_date: '2019-06-18T19:45:29Z',
      history_id: 'salestool-demo/131415',
      history_type: 'email-event',
      notes: null,
      salesperson_picture: '',
    },
  ],
  statusCode: 200,
  nextQueryString: 'next-url3',
};
