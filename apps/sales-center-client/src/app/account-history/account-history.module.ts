import { NgModule } from '@angular/core';
import { AccountHistoryComponent } from './account-history.component';
import { AccountHistoryRoutingModule } from './account-history.routing.module';
import { NavigationModule } from '../navigation/navigation.module';
import { CommonModule } from '@angular/common';
import { FilterModule } from '@vendasta/va-filter2';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { AccountHistoryService } from './account-history.service';
import { AccountHistoryCardComponent } from './account-history-card/account-history-card.component';
import { EmptyStateModule, VaStencilsModule } from '@vendasta/uikit';
import { SalesToolHostService } from '../core';
import { AsyncUiModule } from '@vendasta/uikit';
import { TranslateModule } from '@ngx-translate/core';
import { UserActivitiesService } from '../account-details/recent-activity/user-activities.service';
import { WhitelabelService } from '@galaxy/partner';
import { LexiconModule } from '@galaxy/lexicon';
import baseTranslation from '../../assets/i18n/en_devel.json';
import { WEBLATE_COMPONENT_NAME } from '../constants';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    AccountHistoryRoutingModule,
    NavigationModule,
    GalaxyPageModule,
    FilterModule,
    VaStencilsModule,
    EmptyStateModule,
    AsyncUiModule,
    MatFormFieldModule,
    MatCheckboxModule,
    MatRadioModule,
    MatInputModule,
    MatCardModule,
    MatButtonModule,
    TranslateModule,
    LexiconModule.forChild({
      componentName: WEBLATE_COMPONENT_NAME,
      baseTranslation: baseTranslation,
    }),
    MatProgressSpinnerModule,
  ],
  declarations: [AccountHistoryComponent, AccountHistoryCardComponent],
  providers: [AccountHistoryService, SalesToolHostService, UserActivitiesService, WhitelabelService],
})
export class AccountHistoryModule {}
