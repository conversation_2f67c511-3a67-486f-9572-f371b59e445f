import { Component, Inject, OnDestroy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { WhitelabelService } from '@galaxy/partner';
import { TranslateService } from '@ngx-translate/core';
import { CheckboxFilterControl, FilterGroup, RadioFilterControl, SidebarState } from '@vendasta/va-filter2';
import { BehaviorSubject, Observable, Subject, Subscription, combineLatest } from 'rxjs';
import { map, shareReplay, startWith, switchMap, withLatestFrom } from 'rxjs/operators';
import { UserActivitiesService } from '../account-details/recent-activity/user-activities.service';
import { AjaxBusinessApiService, BusinessApiService } from '../business';
import { AllContactFields } from '../common/contacts/contact-field.enum';
import { ContactShadow } from '../common/contacts/contact-v2';
import { ContactsV2Service } from '../common/contacts/contacts-v2.service';
import { LoggedInUserInfoService } from '../logged-in-user-info';
import { BreadCrumb } from '../navigation/breadcrumbs.component';
import { AccountHistory, AccountHistoryService, HistoryType } from './account-history.service';

export enum SalesPersonAction {
  ANY = '',
  EMAIL_SENT = 'email-sent',
  EMAIL_RECEIVED = 'email-received',
  INBOUND_CALL = 'inbound-call',
  OUTBOUND_CALL = 'outbound-call',
  MEETING = 'meeting',
  OPPORTUNITY_CREATED = 'opportunity-created',
  OPPORTUNITY_CLOSED_WON = 'opportunity-closed-won',
  OPPORTUNITY_CLOSED_LOST = 'opportunity-closed-lost',
  ARCHIVED_BUSINESS = 'archived-business',
  UNARCHIVED_BUSINESS = 'unarchived-business',
}

export enum ActivityType {
  CUSTOMER_ACTIVITY = 'customer-activity',
  SALES_ACTIVITY = 'sales-activity',
  OTHER = 'other',
}

@Component({
  selector: 'app-account-history',
  templateUrl: './account-history.component.html',
  styleUrls: ['./account-history.component.scss'],
  standalone: false,
})
export class AccountHistoryComponent implements OnDestroy {
  breadCrumbs$: Observable<BreadCrumb[]>;
  filter: FilterGroup;
  businessId$: Observable<string>;
  historyItems$: Observable<AccountHistory[]>;
  salesPersonAction$: Observable<SalesPersonAction>;
  historyData$: Observable<{ loading: boolean; items: AccountHistory[]; success: boolean }>;
  selectedHistoryTypes$: Observable<ActivityType[]>;
  toolTipText$: Observable<string>;
  sideBarVisible = SidebarState.OPEN;
  hasMore$: Observable<boolean>;
  moreLoading$: Observable<boolean>;
  subscriptions = new Array<Subscription>();
  businessCenterName$: Observable<string>;

  private readonly loadTrigger$$ = new BehaviorSubject(null);
  private readonly loadMoreClicked$$ = new Subject<null>();
  private readonly loadMoreQueryString$$ = new Subject<string>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly accountHistory: AccountHistoryService,
    @Inject(AjaxBusinessApiService) private readonly businessApi: BusinessApiService,
    private readonly translate: TranslateService,
    private readonly userActivityService: UserActivitiesService,
    private readonly userInfo: LoggedInUserInfoService,
    private readonly whiteLabelService: WhitelabelService,
    private readonly contactsService: ContactsV2Service,
  ) {
    const translationKeys = [
      'ACCOUNT_HISTORY.FILTER.TYPE',
      'ACCOUNT_HISTORY.FILTER.CUSTOMER_ACTIVITY',
      'ACCOUNT_HISTORY.FILTER.SALES_ACTIVITY',
      'ACCOUNT_HISTORY.FILTER.OTHER',
      'ACCOUNT_HISTORY.FILTER.ACTION',
      'ACCOUNT_HISTORY.FILTER.ANY',
      'ACCOUNT_HISTORY.FILTER.EMAIL_SENT',
      'ACCOUNT_HISTORY.FILTER.EMAIL_RECEIVED',
      'ACCOUNT_HISTORY.FILTER.INBOUND_CALL',
      'ACCOUNT_HISTORY.FILTER.OUTBOUND_CALL',
      'ACCOUNT_HISTORY.FILTER.MEETING',
      'ACCOUNT_HISTORY.FILTER.OPPORTUNITY_CREATED',
      'ACCOUNT_HISTORY.FILTER.OPPORTUNITY_CLOSED_WON',
      'ACCOUNT_HISTORY.FILTER.OPPORTUNITY_CLOSED_LOST',
      'ACCOUNT_HISTORY.FILTER.ARCHIVED_BUSINESS',
      'ACCOUNT_HISTORY.FILTER.UNARCHIVED_BUSINESS',
    ];
    const translationsMap = this.translate.instant(translationKeys);

    this.filter = new FilterGroup('account-history')
      .addSection(this.translate.instant(translationsMap['ACCOUNT_HISTORY.FILTER.TYPE']), [
        new CheckboxFilterControl('customerActivity', translationsMap['ACCOUNT_HISTORY.FILTER.CUSTOMER_ACTIVITY']),
        new CheckboxFilterControl('salesActivity', translationsMap['ACCOUNT_HISTORY.FILTER.SALES_ACTIVITY']),
        new CheckboxFilterControl('other', translationsMap['ACCOUNT_HISTORY.FILTER.OTHER']),
      ])
      .addSection(translationsMap['ACCOUNT_HISTORY.FILTER.ACTION'], [
        new RadioFilterControl('action', '', [
          { label: translationsMap['ACCOUNT_HISTORY.FILTER.ANY'], value: SalesPersonAction.ANY },
          { label: translationsMap['ACCOUNT_HISTORY.FILTER.EMAIL_SENT'], value: SalesPersonAction.EMAIL_SENT },
          { label: translationsMap['ACCOUNT_HISTORY.FILTER.EMAIL_RECEIVED'], value: SalesPersonAction.EMAIL_RECEIVED },
          { label: translationsMap['ACCOUNT_HISTORY.FILTER.INBOUND_CALL'], value: SalesPersonAction.INBOUND_CALL },
          { label: translationsMap['ACCOUNT_HISTORY.FILTER.OUTBOUND_CALL'], value: SalesPersonAction.OUTBOUND_CALL },
          { label: translationsMap['ACCOUNT_HISTORY.FILTER.MEETING'], value: SalesPersonAction.MEETING },
          {
            label: translationsMap['ACCOUNT_HISTORY.FILTER.OPPORTUNITY_CREATED'],
            value: SalesPersonAction.OPPORTUNITY_CREATED,
          },
          {
            label: translationsMap['ACCOUNT_HISTORY.FILTER.OPPORTUNITY_CLOSED_WON'],
            value: SalesPersonAction.OPPORTUNITY_CLOSED_WON,
          },
          {
            label: translationsMap['ACCOUNT_HISTORY.FILTER.OPPORTUNITY_CLOSED_LOST'],
            value: SalesPersonAction.OPPORTUNITY_CLOSED_LOST,
          },
          {
            label: translationsMap['ACCOUNT_HISTORY.FILTER.ARCHIVED_BUSINESS'],
            value: SalesPersonAction.ARCHIVED_BUSINESS,
          },
          {
            label: translationsMap['ACCOUNT_HISTORY.FILTER.UNARCHIVED_BUSINESS'],
            value: SalesPersonAction.UNARCHIVED_BUSINESS,
          },
        ]),
      ]);

    this.businessId$ = this.route.params.pipe(map((params) => params.businessId));

    this.businessCenterName$ = this.userInfo.getPartnerIdAndMarketId$().pipe(
      switchMap((ids) => this.whiteLabelService.getBranding(ids.partnerId)),
      map((branding) => branding?.apps?.VBC?.name || ''),
      shareReplay(1),
    );

    const business$ = this.businessId$.pipe(switchMap((id) => this.businessApi.getBusiness(id)));
    this.breadCrumbs$ = combineLatest([
      this.businessId$,
      business$,
      this.translate.stream('MANAGE_ACCOUNTS.TITLE'),
      this.translate.stream('ACCOUNT_HISTORY.TITLE'),
    ]).pipe(
      map(([id, info, manageAccounts, history]) => {
        return [{ label: manageAccounts, link: '/' }, { label: info.name, link: `/info/${id}/` }, { label: history }];
      }),
    );

    this.selectedHistoryTypes$ = this.filter.valueChanges.pipe(
      startWith([]),
      map((filterState) => {
        const historyTypes: ActivityType[] = [];
        if (filterState.customerActivity) {
          historyTypes.push(ActivityType.CUSTOMER_ACTIVITY);
        }
        if (filterState.salesActivity) {
          historyTypes.push(ActivityType.SALES_ACTIVITY);
        }
        if (filterState.other) {
          historyTypes.push(ActivityType.OTHER);
        }
        return historyTypes;
      }),
    );
    this.salesPersonAction$ = this.filter.valueChanges.pipe(
      startWith(SalesPersonAction.ANY),
      map((f) => (f.action !== undefined && f.action !== null ? f.action : SalesPersonAction.ANY)),
    );

    const history$ = combineLatest([
      this.businessId$,
      this.selectedHistoryTypes$,
      this.salesPersonAction$,
      this.loadTrigger$$,
    ]).pipe(
      switchMap(([bizId, historyTypes, action]) => {
        this.accountHistory.loadInitialHistory(bizId, historyTypes, action);
        return this.accountHistory.history$;
      }),
      shareReplay(1),
    );

    const contacts$: Observable<ContactShadow[]> = this.businessId$.pipe(
      switchMap((id) => this.contactsService.list$(id, ...AllContactFields)),
    );
    this.historyItems$ = combineLatest([history$, this.businessCenterName$, contacts$]).pipe(
      map(([historyItem, businessCenterName, contacts]) => {
        historyItem.data.forEach((item) => {
          item.vbcUserName = '';
          if (item.historyType === HistoryType.USER_EVENT) {
            this.retrieveVBCUserName(item, businessCenterName, contacts);
          }
        });
        return historyItem.data;
      }),
    );

    this.subscriptions.push(
      this.loadMoreClicked$$.pipe(withLatestFrom(history$, this.businessId$)).subscribe(([, history, bizId]) => {
        this.accountHistory.loadMoreHistory(history.data, history.nextQueryString, bizId);
        this.loadMoreQueryString$$.next(history.nextQueryString);
      }),
    );
    this.subscriptions.push(this.filter.valueChanges.subscribe(() => this.accountHistory.resetItems()));

    const filterState$ = combineLatest([this.businessId$, this.selectedHistoryTypes$, this.salesPersonAction$]);
    const loading$ = filterState$.pipe(
      switchMap(([bizId, historyTypes, action]) => {
        const formData = AccountHistoryService.buildFilterFormData(historyTypes, action, bizId);
        return this.accountHistory.loading$(AccountHistoryService.buildWorkStateKey(bizId, formData));
      }),
    );
    const success$ = filterState$.pipe(
      switchMap(([bizId, historyTypes, action]) => {
        const formData = AccountHistoryService.buildFilterFormData(historyTypes, action, bizId);
        return this.accountHistory.success$(AccountHistoryService.buildWorkStateKey(bizId, formData));
      }),
    );

    this.historyData$ = combineLatest([this.historyItems$, loading$, success$]).pipe(
      map(([items, loading, success]) => {
        return { items: items, loading: loading, success: success };
      }),
    );

    this.toolTipText$ = history$.pipe(
      switchMap((history) =>
        this.translate.stream('COMMON.SHOWING_SOME_OF_TOTAL', {
          some: history.data.length,
          total: history.totalResults,
        }),
      ),
    );
    this.hasMore$ = history$.pipe(map((history) => history.data.length < history.totalResults));
    this.moreLoading$ = combineLatest([this.loadMoreQueryString$$, this.businessId$]).pipe(
      switchMap(([query, bizId]) =>
        this.accountHistory.loading$(AccountHistoryService.buildWorkStateKey(bizId, query)),
      ),
    );
  }

  private retrieveContact(vbcUserId: string, contacts: ContactShadow[]): ContactShadow {
    return contacts.find((contact) => contact.userId === vbcUserId);
  }

  retrieveVBCUserName(history: AccountHistory, businessCenterName: string, contacts: ContactShadow[]): void {
    const userEventAction = history.action;
    history.action = this.userActivityService.buildHistoryAction(
      history.action,
      businessCenterName,
      history.activityDetail,
    );
    const userName = history.vbcUserId ? this.retrieveContact(history.vbcUserId, contacts) : undefined;
    if (this.userActivityService.shouldUseGenericContactDisplayName(userEventAction)) {
      history.vbcUserName = userName
        ? [userName.firstName, userName.lastName].filter((v) => !!v).join(' ') + ' ' || ' '
        : this.translate.instant('ACTIVITY_DETAIL.GENERIC_CONTACT') + ' ';
    }
  }

  retryLoading(): void {
    this.loadTrigger$$.next(null);
  }

  loadMore(): void {
    this.loadMoreClicked$$.next(null);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }
}
