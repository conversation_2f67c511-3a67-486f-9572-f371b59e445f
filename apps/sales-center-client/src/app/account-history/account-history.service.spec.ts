import { TestScheduler } from 'rxjs/testing';
import { AccountHistory, AccountHistoryService, HistoryType } from './account-history.service';
import {
  mockAccountHistoryResponse,
  mockAccountLoadEvenMoreResponse,
  mockAccountLoadMoreResponse,
} from './account-history.mock';
import { ActivityType, SalesPersonAction } from './account-history.component';
import { map } from 'rxjs/operators';

let sched: TestScheduler;

class MockHttpClient {
  mockGetResponse: any;
  mockNextResponse: any;
  mockLastResponse: any;

  constructor(mockGetResponse: any, mockNextResponse = {}, mockLastresponse = {}) {
    this.mockGetResponse = mockGetResponse;
    this.mockNextResponse = mockNextResponse;
    this.mockLastResponse = mockLastresponse;
  }

  get = jest.fn(() => sched.createColdObservable('x', { x: this.mockGetResponse }));
  post = jest
    .fn(() => sched.createColdObservable('x', { x: this.mockGetResponse }))
    .mockImplementationOnce(() => sched.createColdObservable('x', { x: this.mockGetResponse }))
    .mockImplementationOnce(() => sched.createColdObservable('x', { x: this.mockNextResponse }))
    .mockImplementationOnce(() => sched.createColdObservable('x', { x: this.mockLastResponse }));
}

const expectedHistory = <AccountHistory[]>[
  {
    currentSalespersonName: 'Jason Dahl',
    historyType: HistoryType.EMAIL_EVENT,
    description: '',
    salespersonPictureUrl: '',
    historyId: 'salestool-demo/123',
    action: '<span class="customer-name">Shuo Yuan</span> was delivered email: How to Make Friends',
    eventDate: new Date('2019-04-05T22:02:45Z'),
    notes: null,
  },
  {
    currentSalespersonName: null,
    historyType: HistoryType.CAMPAIGN_INFORMATION,
    description: '',
    salespersonPictureUrl: '',
    historyId: 'salestool-demo/456',
    action: 'Business Assigned to Campaign How to Make Friends',
    eventDate: new Date('2019-04-05T21:59:43Z'),
    notes: null,
  },
  {
    currentSalespersonName: 'Jason Dahl',
    historyType: HistoryType.ACCOUNT_CREATION,
    description: '',
    salespersonPictureUrl: 'this-persons-face',
    historyId: 'salestool-demo/789',
    action: 'Account Created',
    eventDate: new Date('2019-03-27T22:52:21Z'),
    notes: null,
  },
];

describe('AccountHistoryService', () => {
  beforeEach(() => (sched = new TestScheduler((a, b) => expect(a).toEqual(b))));
  afterEach(() => sched.flush());

  let service: AccountHistoryService;
  let mockHttp: MockHttpClient;

  describe('buildFilterFormData', () => {
    test('single history type', () => {
      const history = ActivityType.CUSTOMER_ACTIVITY;
      const result = AccountHistoryService.buildFilterFormData([history], SalesPersonAction.ANY, 'AG-123');
      expect(result).toBe(
        'historyTypeFilter=customer-activity&salesPersonActionFilter=&accountGroupId=AG-123&cursor=0',
      );
    });
    test('multiple history types', () => {
      const history = [ActivityType.CUSTOMER_ACTIVITY, ActivityType.SALES_ACTIVITY];
      const result = AccountHistoryService.buildFilterFormData(history, SalesPersonAction.ANY, 'AG-123');
      expect(result).toBe(
        'historyTypeFilter=customer-activity&historyTypeFilter=sales-activity&' +
          'salesPersonActionFilter=&accountGroupId=AG-123&cursor=0',
      );
    });
    test('no history types', () => {
      const history = [];
      const result = AccountHistoryService.buildFilterFormData(history, SalesPersonAction.ANY, 'AG-123');
      expect(result).toBe('salesPersonActionFilter=&accountGroupId=AG-123&cursor=0');
    });
    test('with salesPersonAction', () => {
      const history = [];
      const result = AccountHistoryService.buildFilterFormData(history, SalesPersonAction.EMAIL_SENT, 'AG-123');
      expect(result).toBe('salesPersonActionFilter=email-sent&accountGroupId=AG-123&cursor=0');
    });
    test('with cursor', () => {
      const history = [];
      const result = AccountHistoryService.buildFilterFormData(history, SalesPersonAction.ANY, 'AG-123', 25);
      expect(result).toBe('salesPersonActionFilter=&accountGroupId=AG-123&cursor=25');
    });
  });

  describe('buildWorkStateKey', () => {
    test('builds expected key', () => {
      const result = AccountHistoryService.buildWorkStateKey('AG-123', 'some-query');
      expect(result).toBe('/ajax/history/AG-123/?some-query');
    });
  });

  describe('loadHistoryItems', () => {
    beforeEach(() => {
      mockHttp = new MockHttpClient(
        mockAccountHistoryResponse,
        mockAccountLoadMoreResponse,
        mockAccountLoadEvenMoreResponse,
      );
      service = new AccountHistoryService(mockHttp as any, sched);
    });

    test('initial load', () => {
      service.loadInitialHistory('AG-123', [], SalesPersonAction.ANY);
      const history$ = service.history$.pipe(map((history) => history.data));
      sched.expectObservable(history$).toBe('-x', { x: expectedHistory });
    });

    test('initial load has nextUrl', () => {
      service.loadInitialHistory('AG-123', [], SalesPersonAction.ANY);
      const nextUrl$ = service.history$.pipe(map((history) => history.nextQueryString));
      sched.expectObservable(nextUrl$).toBe('-x', { x: 'next-url' });
    });

    test('load more', () => {
      service.loadInitialHistory('AG-123', [], SalesPersonAction.ANY);
      sched.schedule(() => service.loadMoreHistory(expectedHistory, 'AG-123', 'next-url'), sched.createTime('---|'));
      const results$ = service.history$.pipe(map((history) => history.data.map((h) => h.historyId)));
      sched.expectObservable(results$).toBe('-x--y', {
        x: ['salestool-demo/123', 'salestool-demo/456', 'salestool-demo/789'],
        y: ['salestool-demo/123', 'salestool-demo/456', 'salestool-demo/789', 'salestool-demo/101112'],
      });
    });

    test('subsequent loads use latest requests nextUrl', () => {
      service.loadInitialHistory('AG-123', [], SalesPersonAction.ANY);
      sched.schedule(() => service.loadMoreHistory(expectedHistory, 'AG-123', 'next-url'), sched.createTime('---|'));
      const nextUrl$ = service.history$.pipe(map((history) => history.nextQueryString));
      sched.expectObservable(nextUrl$).toBe('-x--y', { x: 'next-url', y: 'next-url2' });
    });

    test('load more twice', () => {
      const newHistory = [
        {
          currentSalespersonName: 'Locke Cole',
          historyType: HistoryType.ACCOUNT_CREATION,
          description: '',
          salespersonPictureUrl: 'face-picture',
          historyId: 'salestool-demo/101112',
          action: 'Account Created via Sales & Success Center',
          eventDate: new Date('2019-02-13T21:06:39Z'),
          notes: null,
        },
      ];

      service.loadInitialHistory('AG-123', [], SalesPersonAction.ANY);
      sched.schedule(() => service.loadMoreHistory(expectedHistory, 'AG-123', 'next-url'), sched.createTime('---|'));
      sched.schedule(
        () =>
          service.loadMoreHistory([...(expectedHistory as any[]), ...(newHistory as any[])], 'AG-123', 'next-next-url'),
        sched.createTime('--------|'),
      );
      const resultHistoryIds$ = service.history$.pipe(map((history) => history.data.map((h) => h.historyId)));
      sched.expectObservable(resultHistoryIds$).toBe('-x--y----z', {
        x: ['salestool-demo/123', 'salestool-demo/456', 'salestool-demo/789'],
        y: ['salestool-demo/123', 'salestool-demo/456', 'salestool-demo/789', 'salestool-demo/101112'],
        z: [
          'salestool-demo/123',
          'salestool-demo/456',
          'salestool-demo/789',
          'salestool-demo/101112',
          'salestool-demo/131415',
        ],
      });
    });
  });

  describe('resetMore', () => {
    beforeEach(() => {
      mockHttp = new MockHttpClient(
        mockAccountHistoryResponse,
        mockAccountLoadMoreResponse,
        mockAccountLoadEvenMoreResponse,
      );
      service = new AccountHistoryService(mockHttp as any, sched);
    });

    test('initial query string used after reset', () => {
      service.loadInitialHistory('AG-123', [], SalesPersonAction.ANY);
      sched.schedule(() => service.loadMoreHistory(expectedHistory, 'AG-123', 'next-url'), sched.createTime('---|'));
      sched.schedule(() => service.resetItems(), sched.createTime('----------|'));

      const queryStrings$ = service.history$.pipe(map((history) => history.nextQueryString));
      sched.expectObservable(queryStrings$).toBe('-x--y-----z', {
        x: 'next-url',
        y: 'next-url2',
        z: 'next-url',
      });
    });
  });
});
