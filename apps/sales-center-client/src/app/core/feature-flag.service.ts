import { Inject, Injectable, InjectionToken, Optional } from '@angular/core';
import { FeatureFlagStatusInterface, FeatureFlagService as PartnerFlagService } from '@galaxy/partner';
import { ObservableWorkStateMap } from '@vendasta/rx-utils/work-state';
import { Observable, ReplaySubject, SchedulerLike } from 'rxjs';
import { auditTime, filter, map, scan, switchMap } from 'rxjs/operators';
import { LoggedInUserInfo, PartnerMarket } from '../logged-in-user-info/logged-in-user-info.service';
import { ListChangeEvent, resetOrAddToList } from '../rx-utils/scan';
import { doWorkIfInitial, startWorkMulti } from '../rx-utils/state-maps';

// TODO: move tokens to src/app/common/providers/tokens
export const USER_PARTNER_MARKET_TOKEN = new InjectionToken<Observable<PartnerMarket>>('USER_PARTNER_MARKET');
export const USER_INFO_TOKEN = new InjectionToken<Observable<LoggedInUserInfo>>('USER_INFO');

const DEFAULT_THROTTLE_MILLISECONDS = 50;

type CheckRequest = ListChangeEvent<string>;

@Injectable()
export class FeatureFlagService {
  private readonly flagCache = new ObservableWorkStateMap<string, boolean>();
  private readonly checkRequests$$ = new ReplaySubject<CheckRequest>(1);

  constructor(
    private readonly flags: PartnerFlagService,
    @Inject(USER_PARTNER_MARKET_TOKEN) private readonly partnerMarket$: Observable<PartnerMarket>,
    @Inject('throttle') @Optional() private readonly throttleMillis?: number,
    @Inject('scheduler') @Optional() private readonly scheduler?: SchedulerLike,
  ) {
    this.scheduler = scheduler || undefined;
    this.throttleMillis = throttleMillis || DEFAULT_THROTTLE_MILLISECONDS;
    this.handleNewRequests();
  }

  private handleNewRequests(): void {
    this.checkRequests$$
      .pipe(
        scan((acc, newRequest) => resetOrAddToList(acc, newRequest), []),
        filter((requests) => requests.length > 0),
        auditTime(this.throttleMillis, this.scheduler),
        switchMap((flagIds) => this.partnerMarket$.pipe(map((pm) => <[string[], PartnerMarket]>[flagIds, pm]))),
      )
      .subscribe(([flagIds, partnerMarket]) => {
        this.fetchFlagStates(flagIds, partnerMarket);
        this.checkRequests$$.next({ eventType: 'reset' });
      });
  }

  private fetchFlagStates(flagIds: string[], partnerMarket: PartnerMarket): void {
    if (flagIds.length === 1) {
      this.fetchSingleFlag(flagIds[0], partnerMarket);
      return;
    }
    const workIds = flagIds.map((id) => this.buildWorkId(partnerMarket, id));
    startWorkMulti(
      this.flagCache,
      workIds,
      this.flags.batchGetStatus(partnerMarket.partnerId, partnerMarket.marketId, flagIds),
      (key: string, res: FeatureFlagStatusInterface) => res[JSON.parse(key)['flagId']],
    );
  }

  private fetchSingleFlag(flagId: string, partnerMarket: PartnerMarket): void {
    const workForOne = () =>
      this.flags
        .batchGetStatus(partnerMarket.partnerId, partnerMarket.marketId, [flagId])
        .pipe(map((res) => res[flagId]));
    const workId = this.buildWorkId(partnerMarket, flagId);
    doWorkIfInitial(workId, this.flagCache, workForOne, this.scheduler).subscribe();
  }

  private buildWorkId(partnerMarket: PartnerMarket, flagId: string): string {
    return JSON.stringify({ partnerMarket: partnerMarket, flagId: flagId });
  }

  featureFlagEnabled$(flagId: string): Observable<boolean> {
    this.checkRequests$$.next({ value: flagId, eventType: 'add' });
    return this.partnerMarket$.pipe(
      switchMap((pm) => {
        const workId = this.buildWorkId(pm, flagId);
        return this.flagCache.getWorkResults$(workId);
      }),
    );
  }
}
