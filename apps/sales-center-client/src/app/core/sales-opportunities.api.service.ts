import { Injectable } from '@angular/core';
import { Environment, EnvironmentService } from '@galaxy/core';
import { Observable } from 'rxjs';
import { map, retry } from 'rxjs/operators';
import { ApiService } from '../common';
import {
  CreateOpportunityRequest,
  CreateOpportunityResponse,
  GetTagsRequest,
  GetTagsResponse,
  SalesOpportunitiesApi,
  UpdateOpportunityTagsRequest,
  UpdateOpportunityTagsResponse,
} from '@vendasta/sales-ui';
import { CREATE_OPPORTUNITY_URL, GET_TAGS_URL, UPDATE_TAGS_URL } from '@vendasta/sales-ui';

@Injectable()
export class SalesOpportunitiesApiService implements SalesOpportunitiesApi {
  constructor(private readonly apiService: ApiService, private readonly environmentService: EnvironmentService) {}

  private host: string;

  private get salesOpportunitiesHost(): string {
    if (this.host) {
      return this.host;
    }
    switch (this.environmentService.getEnvironment()) {
      case Environment.LOCAL:
        this.host = 'http://localhost:23003';
        break;
      case Environment.DEMO:
        this.host = 'https://sales-opportunities-api-demo.vendasta-internal.com';
        break;
      case Environment.PROD:
        this.host = 'https://sales-opportunities-api-prod.vendasta-internal.com';
        break;
    }
    return this.host;
  }

  create(req: CreateOpportunityRequest): Observable<CreateOpportunityResponse> {
    const url = `${this.salesOpportunitiesHost}` + CREATE_OPPORTUNITY_URL;
    return this.apiService.post(url, JSON.stringify(req)).pipe(
      map((resp) => {
        return {
          opportunity: resp.opportunity,
        };
      }),
      retry(2),
    );
  }

  public getTags(req: GetTagsRequest): Observable<GetTagsResponse> {
    const url = `${this.salesOpportunitiesHost}` + GET_TAGS_URL;
    return this.apiService.post(url, JSON.stringify(req)).pipe(
      map((resp) => {
        return {
          tags: resp.tags ? resp.tags : [],
        };
      }),
      retry(2),
    );
  }

  public updateTags(req: UpdateOpportunityTagsRequest): Observable<UpdateOpportunityTagsResponse> {
    const url = `${this.salesOpportunitiesHost}` + UPDATE_TAGS_URL;
    return this.apiService.post(url, JSON.stringify(req)).pipe(
      map((resp) => {
        return {
          opportunity: resp.opportunity,
        };
      }),
      retry(2),
    );
  }
}
