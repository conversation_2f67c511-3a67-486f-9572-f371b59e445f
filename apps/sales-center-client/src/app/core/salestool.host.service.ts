import { Injectable } from '@angular/core';
import { Environment, EnvironmentService } from '@galaxy/core';

export interface HostProvider {
  host(): string;
}

@Injectable()
export class SalesToolHostService implements HostProvider {
  private _host: string;

  constructor(private readonly environmentService: EnvironmentService) {}

  host(): string {
    if (this._host) {
      return this._host;
    }

    switch (this.environmentService.getEnvironment()) {
      case Environment.LOCAL:
        this._host = 'http://localhost:8086';
        break;
      case Environment.TEST:
        this._host = 'https://salestool-test.appspot.com';
        break;
      case Environment.DEMO:
        this._host = 'https://salestool-demo.appspot.com';
        break;
      case Environment.PROD:
        this._host = 'https://salestool-prod.appspot.com';
        break;
    }
    return this._host;
  }
}
