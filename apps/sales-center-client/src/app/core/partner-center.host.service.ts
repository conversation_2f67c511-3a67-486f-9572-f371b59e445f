import { Injectable } from '@angular/core';
import { Environment, EnvironmentService } from '@galaxy/core';
import { HostProvider } from './salestool.host.service';

@Injectable()
export class PartnerCenterHostService implements HostProvider {
  private _host: string;

  constructor(private readonly environmentService: EnvironmentService) {}

  host(): string {
    if (this._host) {
      return this._host;
    }

    switch (this.environmentService.getEnvironment()) {
      case Environment.LOCAL:
        this._host = 'http://localhost:8081';
        break;
      case Environment.DEMO:
        this._host = 'https://partner-central-demo.appspot.com';
        break;
      case Environment.PROD:
        this._host = 'https://partners.vendasta.com';
        break;
    }
    return this._host;
  }
}
