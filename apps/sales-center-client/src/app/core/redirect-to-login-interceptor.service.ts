import { Location } from '@angular/common';
import { HttpEvent, HttpHandler, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { Observable, Subject, throwError } from 'rxjs';
import { catchError, throttleTime } from 'rxjs/operators';
import { PUBLIC_ROUTES } from '../urls';

declare let deployment: string;

@Injectable()
export class RedirectToLoginInterceptor implements HttpInterceptor {
  private readonly redirectEvents$$ = new Subject<void>();

  constructor(
    private readonly router: Router,
    private readonly location: Location,
    private readonly alerts: SnackbarService,
    private readonly i18n: TranslateService,
    private readonly productAnalytics: ProductAnalyticsService,
  ) {
    this.redirectEvents$$
      .pipe(throttleTime(1000))
      .subscribe(() => this.alerts.openErrorSnack(i18n.instant('COMMON.ERRORS.SESSION_EXPIRY_REDIRECT')));
  }

  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return next.handle(request).pipe(
      catchError((err) => {
        if (err.status === 401) {
          this.productAnalytics.trackEvent('redirects-to-login', 'redirect-due-to-401', 'navigation', null, {
            errorMessage: err?.message || '',
            url: err?.url || '',
          });
          const currentLocation = window.location.href;
          const isLocal = typeof deployment === 'undefined';
          this.redirectToLogin(isLocal, currentLocation);
        }
        return throwError(err);
      }),
    );
  }

  private redirectToLogin(isLocal: boolean, currentLocation: string): void {
    const currentRoute = this.location.path();
    const currentRouteWithoutParams = currentRoute.split('?')[0];
    if (PUBLIC_ROUTES.includes(currentRouteWithoutParams)) {
      console.log(currentRouteWithoutParams, 'is a Public Route');
      return; // Public routes don't require login
    }
    this.doRedirectToLogin(isLocal, currentLocation);
  }

  private doRedirectToLogin(isLocal: boolean, currentLocation: string): void {
    this.redirectEvents$$.next();
    if (isLocal) {
      this.router.navigate(['login']);
    } else {
      window.location.href = `/entry?nextUrl=${currentLocation}`;
    }
  }
}
