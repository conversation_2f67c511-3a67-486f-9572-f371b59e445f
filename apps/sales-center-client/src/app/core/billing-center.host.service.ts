import { Injectable } from '@angular/core';
import { Environment, EnvironmentService } from '@galaxy/core';
import { HostProvider } from './salestool.host.service';

@Injectable()
export class BillingCenterHostService implements HostProvider {
  private _host: string;

  constructor(private readonly environmentService: EnvironmentService) {}

  host(): string {
    if (this._host) {
      return this._host;
    }

    switch (this.environmentService.getEnvironment()) {
      case Environment.LOCAL:
      case Environment.DEMO:
        this._host = 'https://billing-demo.apigateway.co/';
        break;
      case Environment.PROD:
        this._host = 'https://billing-prod.apigateway.co/';
        break;
    }
    return this._host;
  }
}
