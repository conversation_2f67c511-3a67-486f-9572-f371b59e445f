import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Route, Router, RouterStateSnapshot, UrlSegment, UrlTree } from '@angular/router';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Observable, throwError } from 'rxjs';
import { catchError, map, timeout } from 'rxjs/operators';
import { FeatureFlagService } from './feature-flag.service';

export interface FeatureFlagGuardData {
  feature: string;
}

@Injectable()
export class FeatureFlagGuard {
  constructor(
    private readonly router: Router,
    private readonly features: FeatureFlagService,
    private readonly alert: SnackbarService,
  ) {}

  canLoad(
    route: Route,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    segments: UrlSegment[],
  ): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    return this.canLoadAndActivate(route.data as FeatureFlagGuardData);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean | UrlTree> {
    return this.canLoadAndActivate(route.data as FeatureFlagGuardData);
  }

  canLoadAndActivate(data: FeatureFlagGuardData): Observable<boolean | UrlTree> {
    const feature = data.feature;
    const flag$ = this.features.featureFlagEnabled$(feature);
    return flag$.pipe(
      timeout(10000),
      map((hasAccessToFeature) => {
        return hasAccessToFeature || this.router.createUrlTree(['']); // take 'em to the home page
      }),
      catchError((e) => {
        this.alert.openErrorSnack('An error occurred while attempting to load the page.');
        return throwError(e);
      }),
    );
  }
}
