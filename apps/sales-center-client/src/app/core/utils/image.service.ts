// Copied from: apps/partner-center-client/src/app/core/utils/image.service.ts
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ImageService {
  getImageSrc(imageName: string): string {
    if (window.location.hostname === 'localhost') {
      return `/assets/images/${imageName}`;
    }
    return `https://vstatic-prod.apigateway.co/partner-center-client/assets/images/${imageName}`;
  }
}
