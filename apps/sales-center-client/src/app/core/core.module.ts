import { SalesOpportunitiesApiService } from './sales-opportunities.api.service';
import { MatSnackBarModule } from '@angular/material/snack-bar';

import { NgModule } from '@angular/core';
import { AppService } from '../app.service';
import { FeatureFlagService } from './feature-flag.service';
import { SalesToolHostService } from './salestool.host.service';
import { PartnerCenterHostService } from './partner-center.host.service';
import { FeatureFlagGuard } from './feature-flag.guard';
import { BillingCenterHostService } from './billing-center.host.service';

const providers: any[] = [
  AppService,
  SalesOpportunitiesApiService,
  FeatureFlagService,
  FeatureFlagGuard,
  SalesToolHostService,
  PartnerCenterHostService,
  BillingCenterHostService,
];

@NgModule({
  imports: [MatSnackBarModule],
  providers: providers,
})
export class CoreModule {}
