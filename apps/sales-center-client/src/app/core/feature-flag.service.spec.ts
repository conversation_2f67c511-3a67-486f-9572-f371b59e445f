import { FeatureFlagStatusInterface, FeatureFlagService as PartnerFlagService } from '@galaxy/partner';
import { schedule } from '@vendasta/rx-utils';
import { Observable, ReplaySubject, Subject, of } from 'rxjs';
import { TestScheduler } from 'rxjs/testing';
import { PartnerMarket } from '../logged-in-user-info/logged-in-user-info.service';
import { FeatureFlagService } from './feature-flag.service';

describe('FeatureFlagService', () => {
  describe('featureFlagEnabled$', () => {
    let sched: TestScheduler;
    let requests$$: Subject<any>;
    let partnerMock: PartnerFlagService;
    const partnerMarket = of(new PartnerMarket('pid', 'mid'));
    beforeEach(() => {
      sched = new TestScheduler((a, b) => expect(a).toEqual(b));
      requests$$ = new ReplaySubject(2);
      partnerMock = {
        batchGetStatus(partnerId: string, marketId: string, featureIds: string[]): Observable<any> {
          requests$$.next(featureIds);
          return of({});
        },
      } as PartnerFlagService;
    });
    afterEach(() => sched.flush());
    it('should make an HTTP call if feature has never been checked', () => {
      const svc = new FeatureFlagService(partnerMock, partnerMarket, 10, sched);
      svc.featureFlagEnabled$('testflag').subscribe();

      sched.expectObservable(requests$$).toBe('--x', {
        x: ['testflag'],
      });
    });
    it('should NOT make an HTTP call if feature has been checked before', () => {
      const svc = new FeatureFlagService(partnerMock, partnerMarket, 10, sched);
      schedule(sched, '|', () => svc.featureFlagEnabled$('testflag').subscribe());
      schedule(sched, '---|', () => svc.featureFlagEnabled$('testflag').subscribe());

      sched.expectObservable(requests$$).toBe('--x--', {
        x: ['testflag'],
      });
    });
    it('should make 2 calls if 2 features are requested more than THROTTLETIME apart', () => {
      const THROTTLETIME = sched.createTime('---|');
      const svc = new FeatureFlagService(partnerMock, partnerMarket, THROTTLETIME, sched);
      schedule(sched, '|', () => svc.featureFlagEnabled$('flag-one'));
      schedule(sched, '------|', () => svc.featureFlagEnabled$('flag-two'));
      sched.expectObservable(requests$$).toBe('----x-----y', {
        x: ['flag-one'],
        y: ['flag-two'],
      });
    });
    it('should make 1 call if 2 features are requested less than THROTTLETIME apart', () => {
      const THROTTLETIME = sched.createTime('---|');
      const svc = new FeatureFlagService(partnerMock, partnerMarket, THROTTLETIME, sched);
      schedule(sched, '|', () => svc.featureFlagEnabled$('flag-one'));
      schedule(sched, '--|', () => svc.featureFlagEnabled$('flag-two'));
      sched.expectObservable(requests$$).toBe('---x-', {
        x: ['flag-one', 'flag-two'],
      });
    });
    it('should return the correct boolean values - multi', () => {
      partnerMock = {
        batchGetStatus(
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          partnerId: string,
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          marketId: string,
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          featureIds: string[],
        ): Observable<FeatureFlagStatusInterface> {
          return of({
            'flag-one': true,
            'flag-two': false,
          });
        },
      } as PartnerFlagService;
      const THROTTLETIME = sched.createTime('--|');
      const svc = new FeatureFlagService(partnerMock, partnerMarket, THROTTLETIME, sched);
      const enabledOne$ = svc.featureFlagEnabled$('flag-one');
      const enabledTwo$ = svc.featureFlagEnabled$('flag-two');
      sched.expectObservable(enabledOne$).toBe('--x', { x: true });
      sched.expectObservable(enabledTwo$).toBe('--x', { x: false });
    });
    it('should return the correct boolean values - single', () => {
      partnerMock = {
        batchGetStatus(
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          partnerId: string,
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          marketId: string,
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          featureIds: string[],
        ): Observable<FeatureFlagStatusInterface> {
          return of({
            'test-flag': true,
          });
        },
      } as PartnerFlagService;
      const THROTTLETIME = sched.createTime('--|');
      const svc = new FeatureFlagService(partnerMock, partnerMarket, THROTTLETIME, sched);
      const enabledOne$ = svc.featureFlagEnabled$('test-flag');
      sched.expectObservable(enabledOne$).toBe('---x', { x: true });
    });
    it('should work if partner market arrives after feature check', () => {
      partnerMock = {
        batchGetStatus(
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          partnerId: string,
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          marketId: string,
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          featureIds: string[],
        ): Observable<FeatureFlagStatusInterface> {
          return of({
            'test-flag': true,
          });
        },
      } as PartnerFlagService;
      const THROTTLETIME = sched.createTime('--|');
      const slowPartnerMarket = sched.createColdObservable('----x', {
        x: <PartnerMarket>{ partnerId: 'pid', marketId: 'mid' },
      });
      const svc = new FeatureFlagService(partnerMock, slowPartnerMarket, THROTTLETIME, sched);
      const enabledOne$ = svc.featureFlagEnabled$('test-flag');
      sched.expectObservable(enabledOne$).toBe('-------x', { x: true });
    });

    it('should emit a value if HTTP request takes more than THROTTLETIME', () => {
      const THROTTLETIME = sched.createTime('---|');
      const SLOW_RESPONSE_MARBLES = '----x';
      partnerMock = {
        batchGetStatus(
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          partnerId: string,
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          marketId: string,
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          featureIds: string[],
        ): Observable<FeatureFlagStatusInterface> {
          return sched.createColdObservable(SLOW_RESPONSE_MARBLES, { x: { 'test-flag': true } });
        },
      } as PartnerFlagService;

      const svc = new FeatureFlagService(partnerMock, partnerMarket, THROTTLETIME, sched);
      const enabledOne$ = svc.featureFlagEnabled$('test-flag');
      sched.expectObservable(enabledOne$).toBe('--------x', { x: true });
    });
  });
});
