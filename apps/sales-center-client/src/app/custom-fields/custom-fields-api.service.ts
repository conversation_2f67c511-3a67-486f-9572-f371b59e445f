import { Inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { HostProvider, SalesToolHostService } from '../core/salestool.host.service';
import { map } from 'rxjs/operators';
import { CustomField } from './custom-field';
import { GET_CUSTOM_FIELDS_URL } from './routes';
import { firstValueFrom } from 'rxjs';

export interface GetResponse {
  customFields: CustomField[];
}
export interface GetRequest {
  partnerId: string;
  marketId: string;
}
export interface CustomFieldsService {
  getPartnerMarketCustomFields(request: GetRequest): Promise<GetResponse>;
}

@Injectable()
export class CustomFieldsApiService implements CustomFieldsService {
  constructor(private readonly http: HttpClient, @Inject(SalesToolHostService) private readonly host: HostProvider) {}

  public getPartnerMarketCustomFields(request: GetRequest): Promise<GetResponse> {
    const host = this.host.host();
    const url = `${host}${GET_CUSTOM_FIELDS_URL}`;
    const body = {
      partnerId: request.partnerId,
      marketId: request.marketId,
    };
    return firstValueFrom(
      this.http.post(url, body, { withCredentials: true }).pipe(
        map<any, GetResponse>((r) => {
          return {
            customFields: (r.data.customFields || []).map(
              (f) => new CustomField(f.name, f.field_type, f.label, f.options),
            ),
          };
        }),
      ),
    );
  }
}
