export class CustomField {
  name: string;
  fieldType: string;
  label: string;
  options?: string[] | undefined;
  value?: string | undefined;

  constructor(name: string, fieldType: string, label: string, options?: string[], value?: string) {
    this.name = name;
    this.value = value;
    this.fieldType = fieldType;
    this.label = label;
    this.options = options;
  }

  setValue(val: string): void {
    this.value = val;
  }
}
