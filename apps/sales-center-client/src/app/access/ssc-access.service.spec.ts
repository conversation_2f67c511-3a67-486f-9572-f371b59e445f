import { SSCAccessService, Feature } from './ssc-access.service';
import { Observable, of } from 'rxjs';
import { ConfigurationInterface } from '@galaxy/partner';
import { TestScheduler } from 'rxjs/testing';
import { JestScheduler } from '@vendasta/rx-utils';
import { expectBool } from '../rx-utils/testing/expect-bool';

describe('AjaxAccessService', () => {
  let sched: TestScheduler;
  beforeEach(() => (sched = new JestScheduler()));
  afterEach(() => sched.flush());
  describe('hasAccessToFeature', () => {
    let mockConfig: ConfigurationInterface;
    describe('when sales orders is enabled', () => {
      beforeEach(() => {
        mockConfig = {
          enabledFeatures: ['sales-orders'],
        };
      });
      it('should emit false if "hideOrders" is enabled on the SalesConfiguration', () => {
        mockConfig.salesConfiguration = {
          hideOrders: true,
        };
        const whitelabelMock = {
          getConfiguration: () => sched.createColdObservable('-x', { x: mockConfig }),
        };
        const s = new SSCAccessService(
          of({ partnerId: 'pid', marketId: '', hasMarket: () => false }),
          whitelabelMock as any,
          null,
        );
        expectBool(sched, s.hasAccessToFeature(Feature.salesOrders)).toBe('-f');
      });
      it('should emit true if "hideOrders" is disabled on the SalesConfiguration', () => {
        mockConfig.salesConfiguration = {
          hideOrders: false,
        };
        const whitelabelMock = {
          getConfiguration: () => sched.createColdObservable('-x', { x: mockConfig }),
        };
        const s = new SSCAccessService(
          of({ partnerId: 'pid', marketId: '', hasMarket: () => false }),
          whitelabelMock as any,
          null,
        );
        expectBool(sched, s.hasAccessToFeature(Feature.salesOrders)).toBe('-t');
      });
      it('should emit true if "hideOrders" is absent on the SalesConfiguration', () => {
        mockConfig.salesConfiguration = {};
        const whitelabelMock = {
          getConfiguration: () => sched.createColdObservable('-x', { x: mockConfig }),
        };
        const s = new SSCAccessService(
          of({ partnerId: 'pid', marketId: '', hasMarket: () => false }),
          whitelabelMock as any,
          null,
        );
        expectBool(sched, s.hasAccessToFeature(Feature.salesOrders)).toBe('-t');
      });
    });
    describe('when sales orders is disabled', () => {
      beforeEach(() => {
        mockConfig = {
          enabledFeatures: ['something-else'],
        };
      });
      it('should emit false if "hideOrders" is enabled on the SalesConfiguration', () => {
        mockConfig.salesConfiguration = {
          hideOrders: true,
        };
        const whitelabelMock = {
          getConfiguration: () => sched.createColdObservable('-x', { x: mockConfig }),
        };
        const s = new SSCAccessService(
          of({ partnerId: 'pid', marketId: '', hasMarket: () => false }),
          whitelabelMock as any,
          null,
        );
        expectBool(sched, s.hasAccessToFeature(Feature.salesOrders)).toBe('-f');
      });
      it('should emit false if "hideOrders" is disabled on the SalesConfiguration', () => {
        mockConfig.salesConfiguration = {
          hideOrders: false,
        };
        const whitelabelMock = {
          getConfiguration: () => sched.createColdObservable('-x', { x: mockConfig }),
        };
        const s = new SSCAccessService(
          of({ partnerId: 'pid', marketId: '', hasMarket: () => false }),
          whitelabelMock as any,
          null,
        );
        expectBool(sched, s.hasAccessToFeature(Feature.salesOrders)).toBe('-f');
      });
      it('should emit false if "hideOrders" is absent on the SalesConfiguration', () => {
        mockConfig.salesConfiguration = {};
        const whitelabelMock = {
          getConfiguration: () => sched.createColdObservable('-x', { x: mockConfig }),
        };
        const s = new SSCAccessService(
          of({ partnerId: 'pid', marketId: '', hasMarket: () => false }),
          whitelabelMock as any,
          null,
        );
        expectBool(sched, s.hasAccessToFeature(Feature.salesOrders)).toBe('-f');
      });
    });
  });
  describe('hasAccessToInbox', () => {
    let mockConfig: ConfigurationInterface;
    let mockInboxFeature: Observable<boolean> = of(true);
    beforeEach(() => {
      mockConfig = {
        salesConfiguration: {},
      };
    });
    it('should emit true if both "stShowInbox" and "InboxFeature" is enabled', () => {
      mockConfig.salesConfiguration = {
        stShowInbox: true,
      };
      const whitelabelMock = {
        getConfiguration: () => sched.createColdObservable('-x', { x: mockConfig }),
      };
      const s = new SSCAccessService(
        of({ partnerId: 'pid', marketId: '', hasMarket: () => false }),
        whitelabelMock as any,
        null,
      );
      expectBool(sched, s.hasAccessToInbox(mockInboxFeature)).toBe('-t');
    });
    it('should emit false if "stShowInbox" is disabled on the SalesConfiguration', () => {
      mockConfig.salesConfiguration = {
        stShowInbox: false,
      };
      const whitelabelMock = {
        getConfiguration: () => sched.createColdObservable('-x', { x: mockConfig }),
      };
      const s = new SSCAccessService(
        of({ partnerId: 'pid', marketId: '', hasMarket: () => false }),
        whitelabelMock as any,
        null,
      );
      expectBool(sched, s.hasAccessToInbox(mockInboxFeature)).toBe('-f');
    });
    it('should emit false if "InboxFeature" is disabled on the Feature flag', () => {
      mockInboxFeature = of(false);
      mockConfig.salesConfiguration = {
        stShowInbox: true,
      };
      const whitelabelMock = {
        getConfiguration: () => sched.createColdObservable('-x', { x: mockConfig }),
      };
      const s = new SSCAccessService(
        of({ partnerId: 'pid', marketId: '', hasMarket: () => false }),
        whitelabelMock as any,
        null,
      );
      expectBool(sched, s.hasAccessToInbox(mockInboxFeature)).toBe('-f');
    });
  });
});
