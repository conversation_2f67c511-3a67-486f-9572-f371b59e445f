import { Inject, Injectable } from '@angular/core';
import { combineLatest, Observable } from 'rxjs';
import { AccessChecker } from './interface';
import { filter, map, shareReplay, switchMap } from 'rxjs/operators';
import { Feature as PartnerCenterFeature } from '@vendasta/businesses/easy-account-create';
import { ConfigurationInterface, WhitelabelService } from '@galaxy/partner';
import { USER_PARTNER_MARKET_TOKEN } from '../core/feature-flag.service';
import { PartnerMarket } from '../logged-in-user-info/logged-in-user-info.service';
import { AppPage } from './page-access/app-page.enum';
import { PageAccessService } from './page-access/page-access.service';

// This Feature enum contains all of the features that sales center client needs to know about
export const enum Feature {
  whitelabel = 'whitelabel',
  emailIntegration = 'email-integration',
  pipeline = 'ssc-pipeline',
  salesOrders = 'sales-orders',
  restrictedCampaigns = 'all-recommended-campaigns',
  recommendedCampaigns = 'marketing-automation',
  customCampaigns = 'custom-campaigns',
  markets = 'markets-access',
  customFields = 'custom-fields',
  managePipeline = 'manage-pipeline',
}

export const ACCESS_PIPELINE = 'ACCESS_PIPELINE';
export const MANAGE_PIPELINE = 'MANAGE_PIPELINE';

export const hasAccessToPipeline = (svc: SSCAccessService): Observable<boolean> =>
  svc.hasAccessToFeature(Feature.pipeline);

export const canManagePipeline = (svc: SSCAccessService): Observable<boolean> =>
  svc.hasAccessToFeature(Feature.managePipeline);

@Injectable()
export class SSCAccessService implements AccessChecker {
  private readonly config$: Observable<ConfigurationInterface>;

  constructor(
    @Inject(USER_PARTNER_MARKET_TOKEN) private readonly partnerMarket$: Observable<PartnerMarket>,
    private readonly whitelabelService: WhitelabelService,
    private readonly pageAccessService: PageAccessService,
  ) {
    this.config$ = this.partnerMarket$.pipe(
      switchMap((pm) => this.whitelabelService.getConfiguration(pm.partnerId, pm.marketId)),
      shareReplay(1),
    );
  }

  hasAccessToFeature(feature: Feature | PartnerCenterFeature): Observable<boolean> {
    return this.config$.pipe(
      filter((config) => Boolean(config) && Boolean(config.enabledFeatures)),
      map((config) => {
        if (feature === Feature.salesOrders && config.salesConfiguration.hideOrders === true) {
          return false;
        }
        return config.enabledFeatures.includes(feature);
      }),
    );
  }

  hasAccessToInbox(inboxFeatureFlag: Observable<boolean>): Observable<boolean> {
    return combineLatest(inboxFeatureFlag, this.config$).pipe(
      map(([featureFlag, config]) => {
        return featureFlag === true && Boolean(config.salesConfiguration.stShowInbox) === true;
      }),
    );
  }

  hasAccessToPage(page: AppPage): Observable<boolean> {
    return this.pageAccessService.hasAccessToPage(page);
  }
}
