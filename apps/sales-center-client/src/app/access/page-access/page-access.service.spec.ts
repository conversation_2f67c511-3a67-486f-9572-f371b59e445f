import { PageAccessService } from './page-access.service';
import { TestScheduler } from 'rxjs/testing';
import { EMPTY } from 'rxjs';
import { AppPage } from './app-page.enum';

describe('PageAccessService', () => {
  let scheduler: TestScheduler;

  beforeEach(() => {
    scheduler = new TestScheduler((a, b) => expect(a).toEqual(b));
  });
  afterEach(() => scheduler.flush());

  describe('Individual Page Access', () => {
    it('Returns true if user has access to the partner prospect table page', () => {
      const partnerTableAccess$ = scheduler.createColdObservable('-x', { x: true });
      const serviceToTest = new PageAccessService(EMPTY, partnerTableAccess$, EMPTY, EMPTY);
      const actualResult$ = serviceToTest.hasAccessToPage(AppPage.PartnerProspectTablePage);
      scheduler.expectObservable(actualResult$).toBe('-x', { x: true });
    });
    it('Returns true if user has access to the products page', () => {
      const canAccessProductsPage$ = scheduler.createColdObservable('-x', { x: true });
      const serviceToTest = new PageAccessService(canAccessProductsPage$, EMPTY, EMPTY, EMPTY);
      const actualResult$ = serviceToTest.hasAccessToPage(AppPage.ProductsPage);
      scheduler.expectObservable(actualResult$).toBe('-x', { x: true });
    });
    it('Returns true if user has access to the leaderboard page', () => {
      const canAccessLeaderboardPage$ = scheduler.createColdObservable('-x', { x: true });
      const serviceToTest = new PageAccessService(EMPTY, EMPTY, canAccessLeaderboardPage$, EMPTY);
      const actualResult$ = serviceToTest.hasAccessToPage(AppPage.LeaderboardPage);
      scheduler.expectObservable(actualResult$).toBe('-x', { x: true });
    });
    it('Returns true if user has access to the dashboard page', () => {
      const canAccessDashboardPage$ = scheduler.createColdObservable('-x', { x: true });
      const serviceToTest = new PageAccessService(EMPTY, EMPTY, EMPTY, canAccessDashboardPage$);
      const actualResult$ = serviceToTest.hasAccessToPage(AppPage.DashboardPage);
      scheduler.expectObservable(actualResult$).toBe('-x', { x: true });
    });
    it('Should return false if the service does not know if the user should have access', (done) => {
      const serviceToTest = new PageAccessService(EMPTY, EMPTY, EMPTY, EMPTY);
      serviceToTest.hasAccessToPage('some-unknown-page' as AppPage).subscribe((canAccess) => {
        expect(canAccess).toBe(false);
        done();
      });
    });
  });
});
