import { Inject, Injectable } from '@angular/core';
import { AppPage, PageAccessTokens } from './app-page.enum';
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class PageAccessService {
  constructor(
    @Inject(PageAccessTokens.PRODUCTS_PAGE_ACCESS_TOKEN) private readonly productsPageAccess$: Observable<boolean>,
    @Inject(PageAccessTokens.ACCESS_PARTNER_PROSPECT_TABLE_TOKEN)
    private readonly canAccessPartnerProspects$: Observable<boolean>,
    @Inject(PageAccessTokens.ACCESS_LEADERBOARD_TOKEN) private readonly canAccessLeaderboard$: Observable<boolean>,
    @Inject(PageAccessTokens.ACCESS_DASHBOARD_TOKEN) private readonly canAccessDashboard$: Observable<boolean>,
  ) {}

  hasAccessToPage(page: AppPage): Observable<boolean> {
    switch (page) {
      case AppPage.ProductsPage:
        return this.productsPageAccess$;
      case AppPage.PartnerProspectTablePage:
        return this.canAccessPartnerProspects$;
      case AppPage.LeaderboardPage:
        return this.canAccessLeaderboard$;
      case AppPage.DashboardPage:
        return this.canAccessDashboard$;
      default:
        console.error('Unknown page access request.  Please add it to the page access service.');
        return of(false);
    }
  }
}
