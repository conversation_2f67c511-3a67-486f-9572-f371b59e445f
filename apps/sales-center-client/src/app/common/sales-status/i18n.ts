import { Status } from '@vendasta/prospect';

export function StatusToTranslationString(s: Status): string {
  const statusMapping = new Map<Status, string>([
    [Status.STATUS_UNREAD_ACTIVITY, 'PROSPECT_COMPONENT.COLUMNS.UNREAD_ACTIVITY'],
    [Status.STATUS_READY_TO_SELL, 'COMMON.SALES_STATUS.READY_TO_SELL'],
    [Status.STATUS_FOLLOW_UP_NEEDED, 'COMMON.SALES_STATUS.FOLLOW_UP_NEEDED'],
    [Status.STATUS_IN_PROGRESS, 'COMMON.SALES_STATUS.IN_PROGRESS'],
    [Status.STATUS_ACCOUNT_CREATED, 'COMMON.SALES_STATUS.ACCOUNT_CREATED'],
    [Status.STATUS_CLOSED_LOST, 'COMMON.SALES_STATUS.CLOSED_LOST'],
    [Status.STATUS_CLOSED_WON, 'COMMON.SALES_STATUS.CLOSED_WON'],
    [Status.STATUS_INVALID, 'PROSPECT_COMPONENT.ENUMS.STATUS.INVALID'],
  ]);
  return statusMapping.get(s.valueOf());
}
