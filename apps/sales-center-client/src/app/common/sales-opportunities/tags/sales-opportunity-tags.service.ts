import { Injectable } from '@angular/core';
import { EMPTY, Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { SalesToolNotification, AppService, SalesToolNotificationType } from '../../../app.service';
import { TranslateService } from '@ngx-translate/core';
import { Opportunity, SalesOpportunitiesService } from '@vendasta/sales-opportunities';

@Injectable()
export class SalesOpportunityTagsService {
  constructor(
    private readonly salesOpportunitiesService: SalesOpportunitiesService,
    private readonly appService: AppService,
    private readonly translate: TranslateService,
  ) {}

  updateTags(
    opportunityId: string,
    accountGroupId: string,
    editSalesPersonId: string,
    tags: string[],
  ): Observable<string[]> {
    return this.salesOpportunitiesService.updateTags(opportunityId, accountGroupId, editSalesPersonId, tags).pipe(
      map((resp: Opportunity) => resp.tags || []),
      catchError((err) => {
        const errorMessage = this.translate.instant('OPPORTUNITY_TAGS.UNABLE_TO_UPDATE_TAGS');
        const notification = new SalesToolNotification(errorMessage, SalesToolNotificationType.ERROR, err.code);
        this.appService.pushNewMessage(notification);
        return EMPTY;
      }),
    );
  }
}
