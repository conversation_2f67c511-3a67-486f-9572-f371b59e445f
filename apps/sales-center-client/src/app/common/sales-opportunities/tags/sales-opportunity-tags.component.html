<div *ngIf="opportunity$ | async as opportunity">
  <ng-container *ngIf="(hasTags$ | async) === false || (tags$ | async)?.length === 0; else tagContent">
    <div class="empty-state">
      <uikit-empty-state iconName="local_offer">
        <span state-description>
          {{ 'OPPORTUNITY_TAGS.NO_TAGS_FOR_OPPORTUNITY' | translate }}
        </span>
      </uikit-empty-state>
    </div>
  </ng-container>
  <ng-template #tagContent>
    <div class="content-state">
      <ng-container *ngFor="let tag of tags$ | async">
        <va-badge color="gray" class="badge-padding">{{ tag }}</va-badge>
      </ng-container>
    </div>
  </ng-template>
</div>
