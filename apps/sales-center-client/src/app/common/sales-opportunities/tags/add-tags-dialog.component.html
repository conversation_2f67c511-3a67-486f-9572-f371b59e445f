<h2 mat-dialog-title>
  {{ 'OPPORTUNITY_TAGS.ADD_OPPORTUNITY_TAGS' | translate }}
</h2>
<mat-dialog-content>
  <form [formGroup]="opportunityTagsForm" novalidate class="add-tag-content">
    <div>
      <forms-va-input-tags
        class="tag-input-field"
        [options]="(tagOptions$ | async) || []"
        [loading]="loadingTags$ | async"
        [formControl]="opportunityTagsForm.controls.tags"
        [placeholder]="'Add Tag'"
        [allowCustomTags]="true"
        [allowDuplicates]="false"
        [required]="required"
      ></forms-va-input-tags>
    </div>
  </form>
</mat-dialog-content>
<mat-dialog-actions>
  <div *ngIf="required" class="dialog-secondary-actions validation-text">* Required fields</div>
  <button mat-stroked-button (click)="cancel()">
    {{ 'COMMON.ACTION_LABELS.CANCEL' | translate }}
  </button>
  <button mat-flat-button type="submit" color="primary" (click)="saveTags()">
    {{ 'COMMON.ACTION_LABELS.SAVE' | translate }}
  </button>
</mat-dialog-actions>
