import { Component, EventEmitter, Input, OnDestroy, Output } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { filter, map, shareReplay, startWith, switchMap, take } from 'rxjs/operators';
import { Observable, ReplaySubject } from 'rxjs';
import { AddTagsDialogComponent, AddTagsDialogData } from './add-tags-dialog.component';
import { Opportunity as AppOpportunity } from '@vendasta/sales-ui';
import { Opportunity } from '@vendasta/sales-opportunities';
import { SubscriptionList } from '@vendasta/rx-utils';
import { AccountTagsApiService } from '../../../account-tags/account-tags.api.service';

@Component({
  selector: 'app-sales-opportunity-tags',
  templateUrl: './sales-opportunity-tags.component.html',
  styleUrls: ['./sales-opportunity-tags.component.scss'],
  standalone: false,
})
export class SalesOpportunityTagsComponent implements OnDestroy {
  @Input() set opportunity(opp: Opportunity | AppOpportunity) {
    this.opportunity$$.next(opp);
  }

  @Output() tagsChanged: EventEmitter<string[]> = new EventEmitter();

  private readonly opportunity$$ = new ReplaySubject<Opportunity | AppOpportunity>(1);
  readonly opportunity$ = this.opportunity$$.asObservable();
  readonly tags$: Observable<string[]>;
  readonly hasTags$: Observable<boolean>;
  subscriptions = SubscriptionList.new();

  constructor(
    private readonly dialog: MatDialog,
    private readonly accountTagApi: AccountTagsApiService,
  ) {
    this.tags$ = this.opportunity$.pipe(
      switchMap((opp) =>
        this.accountTagApi.getAccountTags(opp.accountGroupId).pipe(
          map((res) => {
            const oppTags = opp ? opp.tags || [] : [];
            return oppTags.concat(res.tags);
          }),
        ),
      ),
      shareReplay(1),
    );
    this.hasTags$ = this.tags$.pipe(
      map((t) => Boolean(t)),
      startWith(false),
    );
  }

  openEditTagsDialog(): void {
    this.dialog.closeAll();
    this.subscriptions.add(this.opportunity$.pipe(take(1)), (opp) => {
      const editTagsDialogRef = this.dialog.open(AddTagsDialogComponent, {
        minWidth: '40vw',
        maxWidth: '100vw',
        data: <AddTagsDialogData>{
          opportunityId: opp.opportunityId,
          accountGroupId: opp.accountGroupId,
          opportunityTags: opp.tags,
        },
      });

      this.subscriptions.add(editTagsDialogRef.afterClosed().pipe(filter(Boolean)), (tags) => {
        this.tagsChanged.emit(tags);
      });
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }
}
