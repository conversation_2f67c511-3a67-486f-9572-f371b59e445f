import { ReplaySubject, Observable, EMPTY as ObservableEmpty } from 'rxjs';
import { map, catchError, finalize } from 'rxjs/operators';
import { Injectable } from '@angular/core';

import { SalesOpportunitiesApiService } from '../../../core';
import { AppService, SalesToolNotification, SalesToolNotificationType } from '../../../app.service';
import { TranslateService } from '@ngx-translate/core';
import { GetTagsRequest } from '@vendasta/sales-ui';

@Injectable()
export class AddTagsDialogStoreService {
  private readonly loading$$: ReplaySubject<boolean> = new ReplaySubject(1);
  private readonly tags$$: ReplaySubject<string[]> = new ReplaySubject(1);

  constructor(
    private readonly salesOpportunitiesApiService: SalesOpportunitiesApiService,
    private readonly appService: AppService,
    private readonly translate: TranslateService,
  ) {}

  get loading$(): Observable<boolean> {
    return this.loading$$.asObservable();
  }

  get tags$(): Observable<string[]> {
    return this.tags$$.asObservable();
  }

  getTags(partnerId: string, marketId: string): void {
    const req: GetTagsRequest = {
      partnerId: partnerId,
      marketId: marketId,
    };

    this.loading$$.next(true);
    this.salesOpportunitiesApiService
      .getTags(req)
      .pipe(
        map((resp) => this.tags$$.next(resp.tags)),
        catchError((err) => {
          const errorMessage = this.translate.instant('OPPORTUNITY_TAGS.ADD_OPPORTUNITY_TAGS');
          const notification = new SalesToolNotification(errorMessage, SalesToolNotificationType.ERROR, err.code);
          this.appService.pushNewMessage(notification);
          this.tags$$.next([]);
          return ObservableEmpty;
        }),
        finalize(() => this.loading$$.next(false)),
      )
      .subscribe();
  }
}
