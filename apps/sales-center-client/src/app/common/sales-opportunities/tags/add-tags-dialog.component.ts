import { Component, Inject, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Observable, Subscription } from 'rxjs';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { map, take } from 'rxjs/operators';
import { SalesOpportunityTagsService } from './sales-opportunity-tags.service';
import { AjaxBusinessApiService } from '../../../business';
import { TagOption } from '../../../sales-opportunities/form-options';
import { AddTagsDialogStoreService } from './add-tags-dialog.store.service';
import { LoggedInUserInfoService } from '../../../logged-in-user-info';

export interface AddTagsDialogData {
  accountGroupId: string;
  opportunityId: string;
  opportunityTags: string[];
}

@Component({
  templateUrl: './add-tags-dialog.component.html',
  styleUrls: ['./add-tags-dialog.component.scss'],
  standalone: false,
})
export class AddTagsDialogComponent implements OnInit, OnDestroy {
  accountGroupId: string;
  opportunityId: string;
  opportunityTags: string[];
  private readonly subscriptions: Subscription[] = [];
  tagOptions$: Observable<TagOption[]>;
  loadingTags$: Observable<boolean>;
  opportunityTagsForm: UntypedFormGroup;
  required = false;

  constructor(
    private readonly dialogRef: MatDialogRef<AddTagsDialogComponent>,
    private readonly currentUserService: LoggedInUserInfoService,
    private readonly salesOpportunityTagsService: SalesOpportunityTagsService,
    private readonly tagsStoreService: AddTagsDialogStoreService,
    private readonly businessService: AjaxBusinessApiService,
    @Inject(MAT_DIALOG_DATA) private readonly data: AddTagsDialogData,
  ) {
    this.tagOptions$ = this.tagsStoreService.tags$.pipe(
      map((tags) =>
        tags.map((t) => {
          return { name: t, value: t };
        }),
      ),
    );
  }

  ngOnInit(): void {
    this.accountGroupId = this.data.accountGroupId;
    this.opportunityId = this.data.opportunityId;
    this.opportunityTags = this.data.opportunityTags;
    this.loadingTags$ = this.tagsStoreService.loading$;

    this.createForm();
    this.loadOptions();
    this.setFormValues();
  }

  private createForm(): void {
    this.opportunityTagsForm = new UntypedFormGroup({
      salesPersonId: new UntypedFormControl('', []),
      tags: new UntypedFormControl([], []),
    });
  }

  private loadOptions(): void {
    this.subscriptions.push(
      this.businessService
        .getBusiness(this.accountGroupId)
        .pipe(take(1))
        .subscribe((business) => {
          this.tagsStoreService.getTags(business.partnerId, business.marketId);
        }),
    );
  }

  private setFormValues(): void {
    this.currentUserService.loggedInUserInfo$.subscribe((info) => {
      this.opportunityTagsForm.controls.salesPersonId.setValue(info.salespersonId);
    });

    this.opportunityTagsForm.controls.tags.setValue(this.opportunityTags);
  }

  saveTags(): void {
    this.salesOpportunityTagsService
      .updateTags(
        this.opportunityId,
        this.accountGroupId,
        this.opportunityTagsForm.controls.salesPersonId.value,
        this.opportunityTagsForm.controls.tags.value,
      )
      .subscribe((tags) => {
        this.dialogRef.close(tags);
      });
  }

  cancel(): void {
    this.dialogRef.close(null);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
  }
}
