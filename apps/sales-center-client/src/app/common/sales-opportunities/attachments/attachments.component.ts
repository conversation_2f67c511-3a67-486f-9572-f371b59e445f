import { ChangeDetectionStrategy, Component, Input, OnDestroy } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Opportunity as AppOpportunity } from '@vendasta/sales-ui';
import {
  DialogResult,
  UploadAttachmentDialogComponent,
} from './upload-attachment-dialog/upload-attachment-dialog.component';
import { take } from 'rxjs/operators';
import { Opportunity } from '@vendasta/sales-opportunities';
import { ReplaySubject } from 'rxjs';
import { Attachment } from '@vendasta/sales-opportunities/lib/_internal';
import { SubscriptionList } from '@vendasta/rx-utils';

@Component({
  selector: 'app-attachments-card',
  styleUrls: ['./attachments.component.scss'],
  templateUrl: './attachments.component.html',
  changeDetection: ChangeDetectionStrategy.Default,
  standalone: false,
})
export class AttachmentsComponent implements OnDestroy {
  @Input() set opportunity(opp: Opportunity | AppOpportunity) {
    this.opportunity$$.next(opp);
  }

  private readonly opportunity$$ = new ReplaySubject<Opportunity | AppOpportunity>(1);
  readonly opportunity$ = this.opportunity$$.asObservable();

  private readonly subscriptions = SubscriptionList.new();

  constructor(private readonly dialog: MatDialog) {}

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }

  openAddAttachmentDialog(): void {
    this.dialog.closeAll();
    this.subscriptions.add(this.opportunity$.pipe(take(1)), (opportunity) => {
      const ref = this.dialog.open(UploadAttachmentDialogComponent, {
        data: {
          opportunityId: opportunity.opportunityId,
          accountGroupId: opportunity.accountGroupId,
        },
        width: '500px',
        maxWidth: '100vw',
      });

      this.subscriptions.add(ref.afterClosed().pipe(take(1)), (result: DialogResult) => {
        if (result && result.status) {
          if (opportunity.attachments === undefined) {
            opportunity.attachments = [];
          }
          const att = <Attachment>{
            attachmentId: '',
            name: result.name,
            url: result.url,
            created: null,
          };
          opportunity.attachments.push(att);
        }
      });
    });
  }
}
