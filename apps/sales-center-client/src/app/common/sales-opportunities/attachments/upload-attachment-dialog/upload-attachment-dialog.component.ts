import { Component, Inject, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { SalesOpportunitiesService } from '../../../../sales-opportunities/sales-opportunities.service';

@Component({
  selector: 'app-upload-attachment-dialog',
  templateUrl: './upload-attachment-dialog.component.html',
  styleUrls: ['./upload-attachment-dialog.component.scss'],
  standalone: false,
})
export class UploadAttachmentDialogComponent implements OnInit {
  opportunityId: string;
  accountGroupId: string;
  uploadForm: UntypedFormGroup;
  attachmentsUploadUrl = '/ajax/v1/sales-orders/file/upload';

  constructor(
    private readonly alertService: SnackbarService,
    private readonly salesOpportunitiesService: SalesOpportunitiesService,
    public dialogRef: MatDialogRef<UploadAttachmentDialogComponent, DialogResult>,
    @Inject(MAT_DIALOG_DATA) public data: { opportunityId: string; accountGroupId: string },
    private readonly translate: TranslateService,
  ) {
    this.opportunityId = data.opportunityId;
    this.accountGroupId = data.accountGroupId;
  }

  close(status = false, name = '', url = ''): void {
    this.dialogRef.close({ status: status, name: name, url: url });
  }

  submit(): void {
    const name: string = this.uploadForm.controls.attachmentsFormControl.value[0]['name'];
    const url: string = this.uploadForm.controls.attachmentsFormControl.value[0]['url'];
    this.salesOpportunitiesService
      .addAttachmentToOpportunity(
        this.opportunityId,
        this.accountGroupId,
        this.uploadForm.controls.attachmentsFormControl.value[0]['name'],
        url,
      )
      .subscribe(
        () => {
          this.close(true, name, url);
        },
        () => {
          this.alertService.openErrorSnack('OPPORTUNITY_ATTACHMENT.ATTACHMENT_ADDING_ERROR');
        },
      );
  }

  ngOnInit(): void {
    this.uploadForm = new UntypedFormGroup({
      attachmentsFormControl: new UntypedFormControl([], [Validators.required]),
    });
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  handleFileUploadError(err: Error): void {
    this.alertService.openErrorSnack('COMMON.ERRORS.ATTACHMENT_UPLOAD_ERROR');
  }
}

export interface DialogResult {
  status: boolean;
  name: string;
  url: string;
}
