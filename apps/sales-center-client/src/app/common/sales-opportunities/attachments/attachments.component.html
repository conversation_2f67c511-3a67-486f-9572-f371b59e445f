<div *ngIf="opportunity$ | async as opportunity">
  <ng-container *ngIf="opportunity.attachments && opportunity.attachments.length > 0; else emptyState">
    <mat-list>
      <mat-list-item *ngFor="let attachment of opportunity.attachments">
        <a target="_blank" href="{{ attachment.url }}" download>
          {{ attachment.name }}
        </a>
      </mat-list-item>
    </mat-list>
  </ng-container>
</div>

<ng-template #emptyState>
  <div class="empty-state">
    <uikit-empty-state iconName="attach_file">
      <span state-description>
        {{ 'OPPORTUNITY_ATTACHMENT.NO_ATTACHMENTS_MESSAGE' | translate }}
      </span>
    </uikit-empty-state>
  </div>
</ng-template>
