import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CreateOpportunityFormComponent } from './create-opportunity-form.component';
import { MatStepperModule } from '@angular/material/stepper';
import { AccountSelectorModule } from '../../form-fields';
import { MatButtonModule } from '@angular/material/button';
import {
  BUSINESS_ID_TOKEN,
  MARKET_ID_TOKEN,
  PARTNER_ID_TOKEN as SALES_UI_PARTNER_ID,
  SalesOrderConfirmationModule,
  SlideOutPanelModule,
} from '@vendasta/sales-ui';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';
import { PipelineSelectorModule } from '../../../sales-opportunities/pipeline-selector/pipeline-selector.module';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { MatDividerModule } from '@angular/material/divider';
import { SalesWorkflowModule } from '../../../sales-workflow/sales-workflow.module';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatSelectModule } from '@angular/material/select';
import { VaFormsModule } from '@vendasta/forms';
import { MatIconModule } from '@angular/material/icon';
import { BusinessService } from '../../../business/business.service';
import { PARTNER_ID_TOKEN } from '../../providers';
import { map } from 'rxjs/operators';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TypeSelectorComponent } from '@vendasta/sales-ui';

@NgModule({
  declarations: [CreateOpportunityFormComponent],
  exports: [CreateOpportunityFormComponent],
  imports: [
    CommonModule,
    MatButtonModule,
    MatStepperModule,
    AccountSelectorModule,
    SalesOrderConfirmationModule,
    TranslateModule,
    ReactiveFormsModule,
    PipelineSelectorModule,
    MatFormFieldModule,
    MatInputModule,
    MatDatepickerModule,
    MatDividerModule,
    SalesWorkflowModule,
    MatExpansionModule,
    MatSelectModule,
    VaFormsModule,
    MatIconModule,
    SlideOutPanelModule,
    MatProgressSpinnerModule,
    TypeSelectorComponent,
  ],
  providers: [
    BusinessService,
    {
      provide: SALES_UI_PARTNER_ID,
      useExisting: PARTNER_ID_TOKEN,
    },
    {
      provide: BUSINESS_ID_TOKEN,
      useFactory: (businessService: BusinessService) => {
        return businessService.business$.pipe(map((business) => (business ? business.accountGroupId : '')));
      },
      deps: [BusinessService],
    },
    {
      provide: MARKET_ID_TOKEN,
      useFactory: (businessService: BusinessService) => {
        return businessService.business$.pipe(
          map((business) =>
            !!business && !!business.externalIdentifiers ? business.externalIdentifiers.marketId : 'default',
          ),
        );
      },
      deps: [BusinessService],
    },
  ],
})
export class CreateOpportunityFormModule {}
