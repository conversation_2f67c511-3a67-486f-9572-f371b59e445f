import { Component, EventEmitter, Inject, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { DateAdapter, NativeDateAdapter } from '@angular/material/core';
import { MatStepper } from '@angular/material/stepper';
import { Router } from '@angular/router';
import { ProjectionFilter } from '@galaxy/account-group';
import { AppKey, AppPartnerService } from '@galaxy/marketplace-apps';
import { Salesperson } from '@galaxy/types';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { SubscriptionList } from '@vendasta/rx-utils';
import {
  CreateOpportunityRequestCreateOpportunityPackageInterface,
  Opportunity,
  SalesOpportunitiesSdk,
  SalesOpportunitiesService,
} from '@vendasta/sales-opportunities';
import { LineItem } from '@vendasta/sales-orders'; // The type returned from SalesOrderConfirmationComponent is from sales-orders
import {
  BUSINESS_ID_TOKEN,
  getDefaultOpportunityType,
  OrderToOpportunityLineItems,
  SalesOrderConfirmationComponent,
  SidePanelState,
  SidePanelStateService,
  SlideOutPanelService,
} from '@vendasta/sales-ui';
import { Button } from '@vendasta/uikit';
import { endOfDay } from 'date-fns';
import { zonedTimeToUtc } from 'date-fns-tz';
import { combineLatest, Observable, ReplaySubject } from 'rxjs';
import { map, startWith, switchMap, take } from 'rxjs/operators';
import { BusinessService } from '../../../business/business.service';
import { USER_PARTNER_MARKET_TOKEN } from '../../../core/feature-flag.service';
import { ALL_SALESPEOPLE_STARTING_WITH_CURRENT_TOKEN } from '../../../data-providers/salespeople';
import { PartnerMarket } from '../../../logged-in-user-info/logged-in-user-info.service';
import { PackageService } from '../../../package';
import { Configuration, PARTNER_CONFIG_TOKEN } from '../../../partner';
import { BYPASS_QUANTITY_RESTRICTIONS } from '../../../partner/partner-overrides';
import { getNextMonth } from '../../../sales-opportunities/create/create-sales-opportunities';
import { CreateOpportunityStoreService } from '../../../sales-opportunities/create/create-sales-opportunities.store.service';
import { TagOption } from '../../../sales-opportunities/form-options';
import { OpportunityValues } from '../../../sales-opportunities/pipeline-selector/pipeline-selector.component';
import {
  CONTRACT_DURATION_FORM_NAME,
  CONTRACT_DURATION_FORM_VALUE,
} from '../../../sales-workflow/sales-contract-duration-form/sales-contract-duration-form.component';
import { OPPORTUNITY_INFO } from '../../../urls';
import { SALESPERSON_ID_TOKEN } from '../../providers';

export interface CreateOpportunityNavigationData {
  destURLBuilderOnSuccess: (accountGroupId: string, opportunityId: string) => string;
  navigateOnSuccess: boolean;
}

export type CreateSourceLocation = 'accounts' | 'board';

export interface CreateOpportunityData {
  sourceLocation?: CreateSourceLocation;
  accountGroupId: string; // This bypasses the account selector
  accountGroupName?: string;
  navigationData?: CreateOpportunityNavigationData;
}

export const DEFAULT_DESTINATION_URL_BUILDER = (accountGroupId: string, opportunityId: string) =>
  OPPORTUNITY_INFO(accountGroupId, opportunityId);

@Component({
  selector: 'app-create-opportunity-form',
  templateUrl: './create-opportunity-form.component.html',
  styleUrls: ['./create-opportunity-form.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: NativeDateAdapter,
    },
    {
      provide: BUSINESS_ID_TOKEN, // The lineItemSelector is dependent on this
      useFactory: (businessService: BusinessService) => {
        return businessService.business$.pipe(
          map((b) => (b ? b.accountGroupId : '')),
          startWith(''),
        );
      },
      deps: [BusinessService],
    },
    CreateOpportunityStoreService,
  ],
  standalone: false,
})
export class CreateOpportunityFormComponent implements OnInit, OnDestroy {
  readonly saving$: Observable<boolean>;
  @Input() data: CreateOpportunityData;
  @Output() saved = new EventEmitter<Opportunity>();

  // first step fields
  accountForm: UntypedFormGroup;
  accountCtrl: UntypedFormControl;

  // second step fields
  lineitemsForm: UntypedFormGroup;
  @ViewChild('salesOrderConfirmation') salesOrderConfirmation: SalesOrderConfirmationComponent;

  // third final step fields
  additionalInfoForm: UntypedFormGroup;
  today = new Date();
  loadingTags$: Observable<boolean>;
  tags$: Observable<TagOption[]>;
  sortedSalespeople$: Observable<Salesperson[]>;
  private readonly subscribers$$: ReplaySubject<TagOption[]> = new ReplaySubject<TagOption[]>(1);
  subscribers$: Observable<TagOption[]> = this.subscribers$$.asObservable();

  @ViewChild('stepper') private readonly stepper: MatStepper;
  @ViewChild('createButton') private readonly createButton: Button;
  private readonly subscriptions: SubscriptionList = SubscriptionList.new();

  readonly currency$: Observable<string>;
  protected readonly defaultOpportunityType = getDefaultOpportunityType();

  constructor(
    private readonly fb: UntypedFormBuilder,
    private readonly businessService: BusinessService,
    private readonly slideOutPanelService: SlideOutPanelService,
    private readonly sidePanelStateService: SidePanelStateService,
    private readonly store: CreateOpportunityStoreService,
    private readonly alertService: SnackbarService,
    private readonly translate: TranslateService,
    private readonly packageService: PackageService,
    private readonly appPartnerService: AppPartnerService,
    private readonly router: Router,
    @Inject(SalesOpportunitiesService) private readonly opportunityApi: SalesOpportunitiesSdk,
    @Inject(USER_PARTNER_MARKET_TOKEN) readonly partnerMarket$: Observable<PartnerMarket>,
    @Inject(BYPASS_QUANTITY_RESTRICTIONS) readonly bypassQuantityRestrictions$: Observable<boolean>,
    @Inject(ALL_SALESPEOPLE_STARTING_WITH_CURRENT_TOKEN)
    private readonly salespeopleStartingWithCurrent$: Observable<Salesperson[]>,
    @Inject(SALESPERSON_ID_TOKEN) private readonly loggedInSalespersonId$: Observable<string>,
    @Inject(PARTNER_CONFIG_TOKEN) private readonly configuration$: Observable<Configuration>,
  ) {
    this.saving$ = this.store.saving$;
    this.initTagsFormField();
    this.currency$ = this.configuration$.pipe(map((c) => c.defaultDisplayCurrency));
  }

  ngOnInit(): void {
    this.initFirstStep();
    this.initSecondStep();
    this.initFinalStep();
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }

  triggerExternalSalesUIBusinessIdDep(accountGroupId: string): void {
    const mask = new ProjectionFilter({ napData: true, accountGroupExternalIdentifiers: true });
    const business$ = this.businessService.get(accountGroupId, mask);
    this.subscriptions.add(business$);
  }

  secondStepNext(): void {
    const valid = this.salesOrderConfirmation.isFormValid(); // emits error snack if false
    const items = this.salesOrderConfirmation.getCurrentLineItems();
    this.lineitemsForm.controls.secondStepCtrl.patchValue(items);
    this.lineitemsForm.markAsTouched();
    this.lineitemsForm.updateValueAndValidity();
    if (valid) {
      this.stepper.next();
    }
  }

  setPipelineValues(values: OpportunityValues): void {
    this.additionalInfoForm.controls.pipelineAndStageCtrl.patchValue(values);
    this.additionalInfoForm.controls.pipelineAndStageCtrl.markAllAsTouched();
    this.additionalInfoForm.controls.pipelineAndStageCtrl.updateValueAndValidity();
  }

  invalidExpectedDate(): boolean {
    return this.additionalInfoForm.controls.expectedClosedDate.hasError('invalidDate');
  }

  expectedDateBeforeToday(): boolean {
    return this.additionalInfoForm.controls.expectedClosedDate.hasError('matDatepickerMin');
  }

  cancel(): void {
    this.slideOutPanelService.closeSlideOut();
  }

  saveHandler(partnerMarket: PartnerMarket): void {
    const lineItems = this.salesOrderConfirmation.getCurrentLineItems();
    this.createButton.disabled = true;
    if (lineItems?.length === 0) {
      this.createButton.disabled = false;
      this.alertService.openErrorSnack('SALES_OPPORTUNITIES.OPPORTUNITY_ITEMS_REQUIRED');
      return;
    }

    if (this.areFormsInvalid()) {
      this.createButton.disabled = false;
      this.alertService.openErrorSnack('FORMS.INVALID_FORM');
      return;
    }

    const subscriberUserIds = this.additionalInfoForm.controls.selectedSubscribers?.value;
    const assigneeId = this.additionalInfoForm.controls.selectedSalesperson?.value.id;

    if (subscriberUserIds?.length > 5) {
      this.createButton.disabled = false;
      const message = this.translate.instant('SALES_OPPORTUNITY_ERRORS.EXCEED_SUBSCRIBERS_LIMIT');
      this.alertService.openErrorSnack(message);
      return;
    }

    if (subscriberUserIds?.includes(assigneeId)) {
      this.createButton.disabled = false;
      const message = this.translate.instant('SALES_OPPORTUNITY_ERRORS.INVALID_ASSIGNEE_AND_SUBSCRIBERS');
      this.alertService.openErrorSnack(message);
      return;
    }
    this.autoGenerateOpportunityNameFromLineItem$(lineItems[0], partnerMarket)
      .pipe(
        take(1),
        switchMap((autoGenOppNameFromLineItem) => {
          return this.createOpportunity(lineItems, partnerMarket, autoGenOppNameFromLineItem);
        }),
      )
      .subscribe({
        next: (o: Opportunity) => this.onSubmitSuccess(o),
        error: () => {
          this.alertService.openErrorSnack('ERRORS.GENERIC_ERROR');
          this.createButton.disabled = false;
        },
      });
  }

  private get expectedContractDuration(): any {
    const form = this.additionalInfoForm.controls[CONTRACT_DURATION_FORM_NAME];
    return form.value[CONTRACT_DURATION_FORM_VALUE] ? form.value : null;
  }

  private get packages(): CreateOpportunityRequestCreateOpportunityPackageInterface[] {
    return this.salesOrderConfirmation
      .getCurrentLineItems()
      .filter((item) => !!item.packageId)
      .map(
        (p) =>
          <CreateOpportunityRequestCreateOpportunityPackageInterface>{
            packageId: p.packageId,
            quantity: p.quantity,
          },
      );
  }

  private autoGenerateOpportunityNameFromLineItem$(li: LineItem, pm: PartnerMarket): Observable<string> {
    return li.packageId
      ? this.getPackageName(li.packageId)
      : this.getAppName(li.appKey?.appId, li.appKey?.editionId, pm);
  }

  private createOpportunity(items: LineItem[], pm: PartnerMarket, nameFallback: string): Observable<Opportunity> {
    const rawExpectedCloseDate = this.additionalInfoForm.controls.expectedClosedDate.value;
    const expectCloseDate = zonedTimeToUtc(endOfDay(rawExpectedCloseDate), 'UTC');

    return this.opportunityApi.create(
      this.accountForm.controls.firstStepCtrl.value.accountGroupId,
      this.additionalInfoForm.controls.selectedSalesperson.value.id,
      pm.marketId,
      pm.partnerId,
      expectCloseDate,
      this.additionalInfoForm.controls.pipelineAndStageCtrl.value.probability,
      this.additionalInfoForm.controls.pipelineAndStageCtrl.value.pipelineId,
      this.additionalInfoForm.controls.opportunityName.value || nameFallback,
      this.packages,
      this.additionalInfoForm.controls.overviewNotes.value,
      this.additionalInfoForm.controls.tags.value,
      this.expectedContractDuration,
      OrderToOpportunityLineItems(items),
      this.additionalInfoForm.controls.selectedSubscribers.value,
      this.additionalInfoForm.controls.opportunityType.value,
    );
  }

  private initFirstStep(): void {
    this.accountCtrl = new UntypedFormControl('', Validators.required);
    this.accountForm = this.fb.group({
      firstStepCtrl: this.accountCtrl,
    });
    this.initAccountFormWithExistingAccountGroup();
  }

  private initSecondStep(): void {
    this.lineitemsForm = this.fb.group({
      secondStepCtrl: [[], Validators.required],
    });
  }

  private initFinalStep(): void {
    const expectedClosedDate = new UntypedFormControl(getNextMonth(this.today));
    this.additionalInfoForm = this.fb.group({
      pipelineAndStageCtrl: [undefined, Validators.required],
      expectedClosedDate: expectedClosedDate,
      opportunityName: new UntypedFormControl('', []),
      selectedSalesperson: new UntypedFormControl('', []),
      overviewNotes: new UntypedFormControl('', []),
      tags: new UntypedFormControl([], []),
      selectedSubscribers: new UntypedFormControl([], []),
      opportunityType: new UntypedFormControl(this.defaultOpportunityType.id),
    });
    this.initSalespersonFormField();
  }

  private initAccountFormWithExistingAccountGroup(): void {
    if (this.data) {
      this.accountForm.controls.firstStepCtrl.setValue({
        companyName: this.data.accountGroupName,
        accountGroupId: this.data.accountGroupId,
      });
      this.triggerExternalSalesUIBusinessIdDep(this.data.accountGroupId);
    }
  }

  private initTagsFormField(): void {
    this.loadingTags$ = this.store.loadingTags$;
    this.tags$ = this.store.tags$.pipe(
      map((tags) =>
        tags.map((t) => {
          return <TagOption>{ name: t, value: t };
        }),
      ),
    );
    this.subscriptions.add(this.partnerMarket$, (partnerMarket) => {
      this.store.getTags(partnerMarket.partnerId, partnerMarket.marketId);
    });
  }

  private initSalespersonFormField(): void {
    this.sortedSalespeople$ = combineLatest([this.salespeopleStartingWithCurrent$, this.loggedInSalespersonId$]).pipe(
      map(([people, loggedInSalespersonId]) => {
        const selectedSalesperson: Salesperson = people.find((sp) => sp.id === loggedInSalespersonId);
        this.setSalespersonFormValue(selectedSalesperson);
        return people.sort((sp1, sp2) => sp1.fullName.localeCompare(sp2.fullName));
      }),
    );

    this.subscriptions.add(this.sortedSalespeople$, (sortedSalespeople) => {
      this.subscribers$$.next(sortedSalespeople.map((sp) => <TagOption>{ name: sp.fullName, value: sp.id }));
    });
  }

  private setSalespersonFormValue(salesperson: Salesperson): void {
    this.additionalInfoForm.controls.selectedSalesperson.setValue(salesperson);
    this.additionalInfoForm.controls.selectedSalesperson.markAllAsTouched();
    this.additionalInfoForm.controls.selectedSalesperson.updateValueAndValidity();
  }

  private areFormsInvalid(): boolean {
    return this.accountForm.invalid || !this.salesOrderConfirmation.isFormValid() || this.additionalInfoForm.invalid;
  }

  private getAppName(appId: string, editionId: string, partnerMarket: PartnerMarket): Observable<string> {
    return this.appPartnerService
      .getMulti(
        [
          new AppKey({
            appId: appId,
            editionId: editionId,
          }),
        ],
        partnerMarket.partnerId,
        partnerMarket.marketId,
        null,
      )
      .pipe(map((apps) => apps[0]?.sharedMarketingInformation?.name));
  }

  private getPackageName(packageId: string): Observable<string> {
    return this.packageService.getMultiPackages([packageId]).pipe(map((packages) => packages[0]?.name));
  }

  private onSubmitSuccess(o: Opportunity): void {
    if (this.data) {
      const destinationURL = this.data?.navigationData?.destURLBuilderOnSuccess(o.accountGroupId, o.opportunityId);
      const action = this.alertService
        .openWithOptions('SALES_OPPORTUNITIES.SUCCESSFUL_CREATION', {
          duration: 4000,
          action: this.translate.instant('COMMON.ACTION_LABELS.VIEW'),
        })
        .onAction();
      action.subscribe(() => {
        this.router.navigateByUrl(destinationURL);
      });

      if (this.data?.navigationData?.navigateOnSuccess) {
        this.router.navigateByUrl(destinationURL);
      } else if (!!this.data?.sourceLocation && this.data?.sourceLocation === 'accounts') {
        this.slideOutPanelService.closeSlideOut();
        this.sidePanelStateService.switchState(SidePanelState.empty);
      }
    } else {
      this.alertService.openSuccessSnack('SALES_OPPORTUNITIES.SUCCESSFUL_CREATION');
    }

    this.saved.emit(o);
  }

  handleTypeChanged(newType: string): void {
    this.additionalInfoForm.controls.opportunityType.setValue(newType);
  }
}
