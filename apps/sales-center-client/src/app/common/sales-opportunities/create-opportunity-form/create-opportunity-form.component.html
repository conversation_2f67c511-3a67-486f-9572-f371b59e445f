<sales-ui-slide-out-panel-header
  [title]="'SALES_OPPORTUNITIES.OPPORTUNITY_CREATE' | translate"
  icon="add"
></sales-ui-slide-out-panel-header>
<mat-vertical-stepper class="stepper" [linear]="true" #stepper *ngIf="partnerMarket$ | async as pm">
  <mat-step [stepControl]="accountForm" *ngIf="!data">
    <form class="form-container" [formGroup]="accountForm">
      <ng-template matStepLabel>
        {{ 'FORMS.CHOOSE_ACCOUNT' | translate }}
      </ng-template>
      <app-account-selector
        [allowNoneOption]="false"
        [parentFormCtrl]="accountCtrl"
        (selectedNonNoneValueAccountGroupId)="triggerExternalSalesUIBusinessIdDep($event)"
      ></app-account-selector>
      <div class="step-actions--first">
        <button data-action="create-opportunity:selected-account" mat-stroked-button matStepperNext>
          {{ 'COMMON.ACTION_LABELS.NEXT' | translate }}
        </button>
      </div>
    </form>
  </mat-step>

  <mat-step [stepControl]="lineitemsForm">
    <form class="form-container" [formGroup]="lineitemsForm">
      <ng-template matStepLabel>
        {{ 'FORMS.CHOOSE_LINE_ITEMS' | translate }}
      </ng-template>
      <sales-ui-sales-order-confirmation
        #salesOrderConfirmation
        *ngIf="partnerMarket$ | async as partnerMarket"
        [title]="'SALES_OPPORTUNITIES.OPPORTUNITY_CONTENTS' | translate"
        [lineItems]="[]"
        [partnerId]="partnerMarket.partnerId"
        [marketId]="partnerMarket.marketId"
        [businessId]="accountForm.controls.firstStepCtrl.value.accountGroupId"
        [editable]="true"
        [startEditing]="true"
        [allowNegativeValues]="true"
        [bypassQuantityRestrictions]="bypassQuantityRestrictions$ | async"
        [defaultDisplayCurrency]="currency$ | async"
        [enableEditBillingSchedule]="true"
        [allowDuplicateItems]="true"
      ></sales-ui-sales-order-confirmation>
      <div class="step-actions">
        <button mat-stroked-button matStepperPrevious>
          {{ 'COMMON.ACTION_LABELS.BACK' | translate }}
        </button>
        <button data-action="create-opportunity:added-items" mat-stroked-button (click)="secondStepNext()">
          {{ 'COMMON.ACTION_LABELS.NEXT' | translate }}
        </button>
      </div>
    </form>
  </mat-step>

  <mat-step class="additional-info-step">
    <ng-template matStepLabel>
      {{ 'FORMS.ADDITIONAL_INFO' | translate }}
    </ng-template>

    <form class="form-container" [formGroup]="additionalInfoForm" autocomplete="off" novalidate>
      <app-new-opp-pipeline-selector (pipelineValues)="setPipelineValues($event)"></app-new-opp-pipeline-selector>

      <mat-form-field class="full-width" appearance="outline">
        <mat-label>
          {{ 'SALES_OPPORTUNITIES.EXPECTED_CLOSE_PLACEHOLDER' | translate }}
        </mat-label>
        <label>
          <input
            matInput
            [matDatepicker]="datePicker"
            name="date"
            [min]="today"
            formControlName="expectedClosedDate"
            [required]="true"
            (click)="datePicker.open()"
          />
        </label>
        <mat-datepicker-toggle matSuffix [for]="datePicker"></mat-datepicker-toggle>
        <mat-datepicker #datePicker></mat-datepicker>
        <mat-error *ngIf="expectedDateBeforeToday()">
          {{ 'SALES_OPPORTUNITY_ERRORS.BAD_DATE_ERROR' | translate }}
        </mat-error>
        <mat-error *ngIf="invalidExpectedDate()">
          {{ 'SALES_OPPORTUNITY_ERRORS.INCORRECT_DATE_FORMAT' | translate }}
        </mat-error>
      </mat-form-field>
      <sales-ui-type-selector
        [defaultType]="defaultOpportunityType"
        (typeChanged)="handleTypeChanged($event)"
      ></sales-ui-type-selector>

      <mat-divider></mat-divider>

      <app-sales-contract-duration-form
        [parentForm]="additionalInfoForm"
        matFormFieldAppearance="outline"
      ></app-sales-contract-duration-form>

      <mat-divider class="last-divider"></mat-divider>

      <mat-accordion [multi]="true">
        <mat-expansion-panel *ngIf="sortedSalespeople$ | async as salespeople">
          <mat-expansion-panel-header>
            <mat-panel-title>
              {{ 'SALES_OPPORTUNITIES.DETAILS' | translate }}
            </mat-panel-title>
            <mat-panel-description>
              <span
                class="detail-salesperson-text"
                *ngIf="additionalInfoForm.controls.selectedSalesperson.value?.fullName as spFullName; else loading"
              >
                {{ 'SALES_OPPORTUNITIES.ASSIGNED_TO' | translate: { person: spFullName } }}
              </span>
              <ng-template #loading>
                <span>{{ 'COMMON.LOADING' | translate }}</span>
              </ng-template>
            </mat-panel-description>
          </mat-expansion-panel-header>
          <div class="section-columns">
            <div class="section-column">
              <mat-form-field class="full-width" appearance="outline">
                <mat-label>
                  {{ 'SALES_OPPORTUNITIES.OPPORTUNITY_NAME' | translate }}
                </mat-label>
                <input matInput formControlName="opportunityName" />
              </mat-form-field>
            </div>
            <div class="section-column">
              <mat-form-field class="full-width" appearance="outline">
                <mat-label>
                  {{ 'SALES_OPPORTUNITIES.ASSIGN_TO' | translate }}
                </mat-label>
                <mat-select formControlName="selectedSalesperson">
                  <mat-option *ngFor="let sp of salespeople" [value]="sp">
                    {{ sp.fullName }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>
          <div>
            <forms-va-input-tags
              class="full-width"
              appearance="outline"
              [options]="subscribers$ | async"
              [formControl]="additionalInfoForm.controls.selectedSubscribers"
              [placeholder]="'OPPORTUNITY_SUBSCRIBERS.ADD_SUBSCRIBER_PLACEHOLDER' | translate"
              [allowCustomTags]="true"
              [allowDuplicates]="false"
              [required]="false"
            ></forms-va-input-tags>
          </div>
          <div>
            <mat-form-field class="full-width" appearance="outline">
              <mat-label>
                {{ 'SALES_OPPORTUNITIES.OVERVIEW' | translate }}
              </mat-label>
              <textarea matInput matTextareaAutosize formControlName="overviewNotes"></textarea>
            </mat-form-field>
          </div>
          <div>
            <forms-va-input-tags
              class="full-width"
              appearance="outline"
              [options]="(tags$ | async) || []"
              [loading]="loadingTags$ | async"
              [formControl]="additionalInfoForm.controls.tags"
              [placeholder]="'OPPORTUNITY_TAGS.ADD_TAG_PLACEHOLDER' | translate"
              [allowCustomTags]="true"
              [allowDuplicates]="false"
              [required]="false"
            ></forms-va-input-tags>
          </div>
        </mat-expansion-panel>
      </mat-accordion>

      <div class="step-actions">
        <button mat-stroked-button matStepperPrevious>
          {{ 'COMMON.ACTION_LABELS.BACK' | translate }}
        </button>
        <div class="cancel-create-container">
          <button data-action="create-opportunity:cancelled" mat-stroked-button (click)="cancel()">
            {{ 'COMMON.ACTION_LABELS.CANCEL' | translate }}
          </button>
          <button
            data-action="create-opportunity:created"
            #createButton
            mat-flat-button
            color="primary"
            (click)="saveHandler(pm)"
            [disabled]="saving$ | async"
          >
            <mat-spinner *ngIf="saving$ | async; else submitButtonText" [diameter]="20" />
            <ng-template #submitButtonText>
              {{ 'COMMON.ACTION_LABELS.CREATE' | translate }}
            </ng-template>
          </button>
        </div>
      </div>
    </form>
  </mat-step>
</mat-vertical-stepper>
