@use 'design-tokens' as *;

.step-actions--first {
  display: flex;
  justify-content: flex-end;
}

.step-actions {
  margin-top: $spacing-3;
  display: flex;
  justify-content: space-between;
  gap: $spacing-2;

  .cancel-create-container {
    display: flex;
    justify-content: flex-end;
    gap: $spacing-2;
    flex-grow: 1;
  }
}

.full-width {
  width: 100%;
}

.last-divider {
  margin-bottom: $spacing-3;
}

.stepper {
  margin-left: $spacing-2;
  margin-right: $spacing-2;
}

.form-container {
  margin-top: $spacing-2;
}
