/**
 * Opening a new window exposes users to security exploit where malicious
 * parties can modify their browsers' "back history", leading to phishing
 * attacks.
 *
 * This adds the "noopener" option which prevents that exploit.
 */
export function openSecureNewTab(url: string): Window {
  // tslint:disable-next-line:ban
  return window.open(url, '_blank', 'noopener');
}

export function openSecureCurrentTab(url: string): Window {
  // tslint:disable-next-line:ban
  return window.open(url, undefined, 'noopener');
}
