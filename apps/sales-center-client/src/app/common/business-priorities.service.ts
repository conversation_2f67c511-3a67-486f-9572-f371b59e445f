import { Injectable } from '@angular/core';
import {
  OnboardingStrategiesApiService,
  ListGoalsForPartnerResponse,
  ListGoalsForPartnerRequestInterface,
} from '@vendasta/prospect';
import { Observable } from 'rxjs';

@Injectable()
export class BusinessPrioritiesService {
  constructor(private readonly onboardingStrategiesApiService: OnboardingStrategiesApiService) {}

  public listGoalsForPartner(r: ListGoalsForPartnerRequestInterface): Observable<ListGoalsForPartnerResponse> {
    return this.onboardingStrategiesApiService.listGoalsForPartner(r);
  }
}
