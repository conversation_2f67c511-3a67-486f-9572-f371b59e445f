@use 'design-tokens' as *;
@import 'utilities';

.side-drawer-container {
  min-height: 100%;
  width: 100vw;
  display: flex;
  flex-direction: column;
  @include phone-large {
    width: 400px;
  }
}

.side-drawer-toolbar {
  z-index: 2;
  background-color: $white;
  position: sticky;
  top: 0 !important;
  border-bottom: thin solid $light-gray;
}

.side-drawer-toolbar-title {
  flex: 1;
}

.side-drawer-content {
  flex: 1;
}

.side-drawer-footer {
  background-color: $primary-background-color;
  position: sticky;
  bottom: 0;
}
