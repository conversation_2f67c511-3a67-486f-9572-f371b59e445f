import { Component, Directive, EventEmitter, HostBinding, Input, Output } from '@angular/core';
import { DynamicOpenCloseTemplateRefService } from '../dynamic-open-close-template-ref.service';

@Directive({
  selector: 'app-side-drawer-content, [app-side-drawer-content], [appSideDrawerContent]',
  standalone: false,
})
export class SideDrawerContentDirective {
  @HostBinding('class') className = 'side-drawer-content';
}

@Directive({
  selector: 'app-side-drawer-footer, [app-side-drawer-footer], [appSideDrawerFooter]',
  standalone: false,
})
export class SideDrawerFooterDirective {
  @HostBinding('class') className = 'side-drawer-footer';
}

@Directive({
  selector: 'app-side-drawer-title-actions, [app-side-drawer-title-actions], [appSideDrawerTitleActions]',
  standalone: false,
})
export class SideDrawerTitleActionsDirective {
  @HostBinding('class') className = 'side-drawer-title-actions';
}

@Component({
  selector: 'app-closing-side-drawer',
  templateUrl: './closing-side-drawer.component.html',
  styleUrls: ['./closing-side-drawer.component.scss'],
  standalone: false,
})
export class ClosingSideDrawerComponent {
  @Input()
  title: string;

  @Input()
  preventClose: boolean;

  @Output()
  closeDrawer: EventEmitter<void> = new EventEmitter<void>();

  constructor(private readonly dynamicOpenCloseTemplateRefService: DynamicOpenCloseTemplateRefService) {}

  closeSideDrawer(): void {
    this.closeDrawer.emit();
    this.dynamicOpenCloseTemplateRefService.close();
  }
}
