<div class="side-drawer-container">
  <mat-toolbar class="side-drawer-toolbar">
    <mat-toolbar-row>
      <h1 class="side-drawer-toolbar-title">{{ title }}</h1>
      <div class="side-drawer-title-actions">
        <ng-content select="app-side-drawer-title-actions"></ng-content>
      </div>
      <ng-container>
        <button mat-icon-button *ngIf="!preventClose" (click)="closeSideDrawer()">
          <mat-icon>close</mat-icon>
        </button>
      </ng-container>
    </mat-toolbar-row>
  </mat-toolbar>
  <div class="side-drawer-content">
    <ng-content select="app-side-drawer-content"></ng-content>
  </div>
  <footer class="side-drawer-footer">
    <ng-content select="app-side-drawer-footer"></ng-content>
  </footer>
</div>
