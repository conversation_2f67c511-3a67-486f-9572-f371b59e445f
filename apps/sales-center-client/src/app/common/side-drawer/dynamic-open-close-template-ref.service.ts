import { Injectable, TemplateRef } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { map, publishReplay, refCount } from 'rxjs/operators';

export interface DynamicOpenCloseTemplateRef {
  id: string;
  templateRef: TemplateRef<any>;
}

@Injectable()
export class DynamicOpenCloseTemplateRefService {
  private templateMap = new Map<string, TemplateRef<any>>();
  private openTemplate$$ = new BehaviorSubject<string>('');
  openTemplate$: Observable<DynamicOpenCloseTemplateRef>;

  private open$$ = new BehaviorSubject<boolean>(false);
  open$ = this.open$$.asObservable();

  constructor() {
    this.openTemplate$ = this.openTemplate$$.pipe(
      map((t) => {
        const templateRef = this.templateMap.get(t);
        const id = templateRef ? t : '';
        if (id) {
          this.open$$.next(true);
        }
        return {
          id: id,
          templateRef: templateRef,
        };
      }),
      publishReplay(1),
      refCount(),
    );
  }

  close(): void {
    this.open$$.next(false);
  }

  registerTemplate(id: string, template: TemplateRef<any>): void {
    this.templateMap.set(id, template);
    if (this.openTemplate$$.getValue() === id) {
      this.open(id);
    }
  }

  open(id: string): void {
    window.setTimeout(() => {
      this.openTemplate$$.next(id);
      this.open$$.next(true);
    });
  }

  toggle(id: string): void {
    if (this.open$$.getValue()) {
      this.close();
    } else {
      this.open(id);
    }
  }
}
