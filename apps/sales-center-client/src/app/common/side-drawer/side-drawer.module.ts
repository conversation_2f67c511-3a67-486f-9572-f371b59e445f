import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SalesToolCommonModule as VendastaCommonModule } from '../../common';
import {
  ClosingSideDrawerComponent,
  SideDrawerContentDirective,
  SideDrawerFooterDirective,
  SideDrawerTitleActionsDirective,
} from './closing-side-drawer/closing-side-drawer.component';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';

import { ProductAnalyticsModule } from '@vendasta/product-analytics';

@NgModule({
  declarations: [
    ClosingSideDrawerComponent,
    SideDrawerContentDirective,
    SideDrawerFooterDirective,
    SideDrawerTitleActionsDirective,
  ],
  imports: [
    CommonModule,
    VendastaCommonModule,
    MatToolbarModule,
    MatIconModule,
    MatButtonModule,

    ProductAnalyticsModule,
  ],
  exports: [
    ClosingSideDrawerComponent,
    SideDrawerContentDirective,
    SideDrawerFooterDirective,
    SideDrawerTitleActionsDirective,
  ],
})
export class SideDrawerModule {}
