<ng-container *ngIf="salesToolBusinessCreated$ | async; then ready; else building"></ng-container>

<ng-template #ready>
  <va-status-banner
    *ngIf="showCTA"
    [statusText]="'ACCOUNT_DETAILS.BUSINESS_IS_READY' | translate"
    actionTitle="{{ 'ACCOUNT_DETAILS.GO_TO_NEW_BUSINESS' | translate }}"
    type="info"
    [actionCallback]="goToAccountInfo"
  ></va-status-banner>
  <va-status-banner
    *ngIf="!showCTA"
    [statusText]="'ACCOUNT_DETAILS.BUSINESS_IS_READY' | translate"
    actionTitle="{{ 'ACCOUNT_DETAILS.GO_TO_NEW_BUSINESS' | translate }}"
    type="info"
  ></va-status-banner>
</ng-template>

<ng-template #building>
  <va-status-banner [statusText]="'ACCOUNT_DETAILS.BUILDING_BUSINESS' | translate" type="info"></va-status-banner>
</ng-template>
