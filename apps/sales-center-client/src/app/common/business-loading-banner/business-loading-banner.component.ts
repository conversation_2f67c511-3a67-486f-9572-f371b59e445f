import { Component, EventEmitter, Inject, Input, OnChanges, OnDestroy, Output, SimpleChanges } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, ReplaySubject } from 'rxjs';
import { AjaxBusinessApiService, BusinessApiService } from '../../business';
import { delay, filter, mapTo, retryWhen, switchMap, take } from 'rxjs/operators';
import { SubscriptionList } from '@vendasta/rx-utils';
import { ACCOUNT_INFO } from '../../urls';

@Component({
  selector: 'app-business-loading-banner',
  templateUrl: './business-loading-banner.component.html',
  standalone: false,
})
export class BusinessLoadingBannerComponent implements OnChanges, OnDestroy {
  @Input() accountGroupId: string;
  @Input() showCTA = true;
  @Output() businessReady: EventEmitter<null> = new EventEmitter<null>();
  private readonly accountGroupId$$ = new ReplaySubject<string>(1);
  salesToolBusinessCreated$: Observable<boolean>;
  accountInfoUrl: string;
  private readonly subscriptions = SubscriptionList.new();

  constructor(
    @Inject(AjaxBusinessApiService) private readonly businessApi: BusinessApiService,
    private readonly router: Router,
  ) {
    const nonEmptyId$ = this.accountGroupId$$.pipe(filter((id) => !!id));
    this.salesToolBusinessCreated$ = nonEmptyId$.pipe(
      switchMap((id) => this.businessApi.getBusiness(id).pipe(mapTo(true))),
      retryWhen((errors) => errors.pipe(delay(2000), take(20))),
    );

    this.subscriptions.add(this.salesToolBusinessCreated$, (created) => {
      if (created) {
        this.businessReady.emit(null);
      }
    });
  }

  // this needs to be a lexical closure to keep a reference to `this`
  // https://stackoverflow.com/questions/********/typescript-this-scoping-issue-when-called-in-jquery-callback/********#********
  // alternative is writing our own decorator https://github.com/JohnWeisz/BoundMethods/blob/master/src/bound.ts
  goToAccountInfo: () => void = () => {
    this.router.navigateByUrl(this.accountInfoUrl);
  };

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['accountGroupId']) {
      this.accountGroupId$$.next(this.accountGroupId);
      if (this.accountGroupId) {
        this.accountInfoUrl = ACCOUNT_INFO(this.accountGroupId);
      }
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }
}
