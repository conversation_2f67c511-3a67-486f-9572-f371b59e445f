@use 'design-tokens' as *;
@import 'breaks';

$pipeline-mobile-breakpoint: $media--tablet-minimum;

@mixin pipeline-table-button-base {
  display: flex;
  font-weight: bold;
  align-items: center;
  background-color: $light-gray;
  color: black;
  mat-icon {
    display: inline-block;
    @include respond-to(mobile) {
      display: none;
    }
  }
}

.pipeline-buttons > button {
  @include pipeline-table-button-base;
  @media screen {
    &:not(:first-child) {
      margin-left: 4px;
    }
  }
}

.show-pipeline-table {
  #pipeline-board-button,
  #pipeline-board-button--mobile {
    opacity: 0.5;
    cursor: pointer;
  }
}

.show-pipeline-board {
  #pipeline-table-button,
  #pipeline-table-button--mobile {
    opacity: 0.5;
    cursor: pointer;
  }
}
.header-button-wrapper {
  display: flex;
  flex-flow: row;
  align-items: center;
  @media screen {
    margin: 8px;
  }
}

.potential-revenue {
  display: flex;
  flex-flow: wrap;
  @media screen and (min-width: $pipeline-mobile-breakpoint) {
    display: -webkit-box;
  }
}

.export-csv {
  display: flex;
  flex-flow: wrap;
  margin-left: 8px;
  @media screen and (min-width: $pipeline-mobile-breakpoint) {
    display: -webkit-box;
  }
}

.loading-spinner {
  display: flex;
  padding-top: 8px;
}
