import { HttpClient, HttpErrorResponse, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { catchError, first, map } from 'rxjs/operators';
import { Observable, throwError } from 'rxjs';

export class ApiError implements Error {
  name: string;
  message: string;
  code: number;

  constructor(message: string, code: number) {
    this.name = `ApiError${code}`;
    this.message = message;
    this.code = code;
  }
}

/**
 * @deprecated Use HttpClient
 */
@Injectable()
export class ApiService {
  apiOptions = {
    withCredentials: true,
  };

  constructor(private readonly http: HttpClient) {}

  public get(url: string, params?: HttpParams): Observable<any> {
    return this.http
      .get<any>(url, Object.assign({}, this.apiOptions, { params: params }))
      .pipe(first(), map(this.extractData), catchError(this.handleError));
  }

  public put(url: string, body: any, params?: HttpParams): Observable<any> {
    return this.http
      .put<any>(url, body, Object.assign({}, this.apiOptions, { params: params }))
      .pipe(first(), map(this.extractData), catchError(this.handleError));
  }

  public post(url: string, body: any, params?: HttpParams): Observable<any> {
    return this.http
      .post<any>(url, body, Object.assign({}, this.apiOptions, { params: params }))
      .pipe(first(), map(this.extractData), catchError(this.handleError));
  }

  public postForm(url: string, body: URLSearchParams): Observable<any> {
    const options = {
      withCredentials: true,
      headers: new HttpHeaders({ 'Content-Type': 'application/x-www-form-urlencoded' }),
    };
    return this.http
      .post<any>(url, body.toString(), options)
      .pipe(first(), map(this.extractData), catchError(this.handleError));
  }

  private extractData(res: any): void {
    return res.data || res || {};
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    try {
      const errorInfo = error.error;
      const status = error.status || errorInfo.status || errorInfo.statusCode;
      const err = new ApiError(errorInfo.message, status);
      return throwError(err);
    } catch (error) {
      const err = new ApiError('Unknown Error', 500);
      return throwError(err);
    }
  }
}
