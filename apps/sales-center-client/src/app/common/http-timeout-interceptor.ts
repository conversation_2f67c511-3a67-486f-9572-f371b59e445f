import { Inject, Injectable, InjectionToken } from '@angular/core';
import { Http<PERSON><PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Observable } from 'rxjs';
import { timeout } from 'rxjs/operators';

// Set this on your HTTP request to set a custom timeout.
// (Our microservices use CORS settings from our vendaska/gosdks,
// which most browsers respect, and don't allow the request.
// So the interceptor will remove the header before the request goes out.)
export const CUSTOM_TIMEOUT_HEADER_TO_SET = 'http-request-interceptor-timeout';
export const DEFAULT_TIMEOUT_MS = new InjectionToken<number>('defaultTimeout');

@Injectable()
export class HttpTimeoutInterceptor implements HttpInterceptor {
  constructor(@Inject(DEFAULT_TIMEOUT_MS) protected defaultTimeout: number) {}

  intercept(req: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {
    const customTimeout = req.headers.get(CUSTOM_TIMEOUT_HEADER_TO_SET);
    if (customTimeout) {
      req = req.clone({ headers: req.headers.delete(CUSTOM_TIMEOUT_HEADER_TO_SET) });
    }

    const time = Number(customTimeout) || this.defaultTimeout;
    if (time) {
      return next.handle(req).pipe(timeout(time));
    }
    return next.handle(req);
  }
}
