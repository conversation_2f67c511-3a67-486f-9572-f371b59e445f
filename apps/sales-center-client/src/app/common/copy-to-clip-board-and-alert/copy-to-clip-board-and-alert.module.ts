import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CopyToClipBoardAndAlertDirective as CopyEmailToClipBoardAndAlertDirective } from './copy-to-clip-board-and-alert.directive';
import { ClipboardModule } from '@angular/cdk/clipboard';
import { GalaxySnackbarModule } from '@vendasta/galaxy/snackbar-service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

@NgModule({
  declarations: [CopyEmailToClipBoardAndAlertDirective],
  imports: [CommonModule, ClipboardModule, GalaxySnackbarModule, TranslateModule],
  exports: [CopyEmailToClipBoardAndAlertDirective],
  providers: [TranslateService],
})
export class CopyEmailToClipBoardAndAlertModule {}
