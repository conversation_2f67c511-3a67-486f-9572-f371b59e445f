import { CdkCopyToClipboard, Clipboard } from '@angular/cdk/clipboard';
import { Directive, Input, NgZone, OnDestroy, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { SubscriptionList } from '@vendasta/rx-utils';
import { firstValueFrom } from 'rxjs';

export interface CopyEmailToClipboardConfig {
  email: string;
  attempts?: number;
}

@Directive({
  // eslint-disable-next-line @angular-eslint/directive-selector
  selector: '[copyToClipBoardAndAlert]',
  standalone: false,
})
export class CopyToClipBoardAndAlertDirective extends CdkCopyToClipboard implements OnInit, OnDestroy {
  private subscriptions = SubscriptionList.new();

  @Input() set copyToClipBoardAndAlert(config: CopyEmailToClipboardConfig) {
    this.text = config.email;
    this.attempts = config?.attempts;
  }

  constructor(
    _clipboard: Clipboard,
    _ngZone: NgZone,
    private readonly snackbarService: SnackbarService,
    private readonly translateService: TranslateService,
  ) {
    super(_clipboard, _ngZone);
  }

  ngOnInit(): void {
    this.subscriptions.add(this.copied, (copied: string) => this.emitSnackbarMessage(copied));
  }

  ngOnDestroy(): void {
    super.ngOnDestroy();
    this.subscriptions.destroy();
  }

  private emitSnackbarMessage(copied: string): void {
    firstValueFrom(
      this.translateService.stream(['CONTACTS.COPY_EMAIL_SUCCESS_MESSAGE', 'CONTACTS.COPY_EMAIL_ERROR_MESSAGE']),
    ).then((translations) => {
      if (this.text && copied) {
        this.snackbarService.openSuccessSnack(translations['CONTACTS.COPY_EMAIL_SUCCESS_MESSAGE']);
      } else {
        this.snackbarService.openErrorSnack(translations['CONTACTS.COPY_EMAIL_ERROR_MESSAGE']);
      }
    });
  }
}
