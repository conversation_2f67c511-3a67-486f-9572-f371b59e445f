import { textContainsOnlyInternalEmails } from './redactor-utils';

const domain = 'harvardmedia.com';

describe('textContainsOnlyInternalEmails', () => {
  it('Should return false if blank', () => {
    const actual = textContainsOnlyInternalEmails('', domain);
    expect(actual).toBe(false);
  });
  it('Should return true if all emails are of the domain supplied', () => {
    const is = textContainsOnlyInternalEmails(
      `Recipient(s): <EMAIL>\n\nSubject: Testing forwarding\n\nHello. Let's see if forwarding works first.`,
      domain,
    );
    expect(is).toBe(true);
  });
  it('Should return false if one email is not from the specified domain', () => {
    const actual = textContainsOnlyInternalEmails(
      `Recipient(s): <EMAIL>, <EMAIL>, <EMAIL>\n\nSubject: Hello`,
      domain,
    );
    expect(actual).toBe(false);
  });
  it('Should return false if text does not contain emails', () => {
    const actual = textContainsOnlyInternalEmails('Recipient(s): \n\n Subject: Hello World', domain);
    expect(actual).toBe(false);
  });
  it('Should return true if text contains Recipient(s) at the start and all emails are internal', () => {
    const actual = textContainsOnlyInternalEmails(
      `Recipient(s): <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>\n\nSubject: RE: Weather Spos - CFWD`,
      domain,
    );
    expect(actual).toBe(true);
  });
  it('Should capture only up to the first newline character', () => {
    const actual = textContainsOnlyInternalEmails(
      `Recipient(s): <EMAIL>\n\nSubject: RE: LDR - Perfection\\n\\nHere you go! Let me know if you have any questions\\r\\n\\r\\nFrom: <<EMAIL>>\\r\\nSent: Friday, August 18, 2023 10:00 AM\\r\\nTo: RB <<EMAIL>>\\r\\nSubject: LDR - Perfection\\r\\n\\r\\nCan you please send `,
      domain,
    );
    expect(actual).toBe(true);
  });
});
