import { formatPhoneNumber } from './phone-utils';

describe('formatPhoneNumber', () => {
  describe('National formatting', () => {
    const format = 'NATIONAL';

    it('returns the formatted number when there is an extension', () => {
      const phoneNumber = '3061234567';
      const phoneNumberCountry = 'US';
      const phoneExtension = '890';

      const actual = formatPhoneNumber(phoneNumber, format, phoneExtension, phoneNumberCountry);
      const expected = '(************* ext. 890';

      expect(actual).toEqual(expected);
    });

    it('returns the formatted number when there is no extension', () => {
      const phoneNumber = '3061234567';
      const phoneNumberCountry = 'US';

      const actual = formatPhoneNumber(phoneNumber, format, undefined, phoneNumberCountry);
      const expected = '(*************';

      expect(actual).toEqual(expected);
    });

    it('returns the formatted phone number if there is no country (country defaults to US)', () => {
      const phoneNumber = '3061234567';

      const actual = formatPhoneNumber(phoneNumber, format);
      const expected = '(*************';

      expect(actual).toEqual(expected);
    });

    it('returns the formatted phone number if the country is invalid (country defaults to US)', () => {
      const phoneNumber = '3061234567';
      const phoneNumberCountry = 'Invalid-Country-Abbreviation';
      const actual = formatPhoneNumber(phoneNumber, format, undefined, phoneNumberCountry);
      const expected = '(*************';

      expect(actual).toEqual(expected);
    });

    it('transforms non-formatted US numbers with extension', () => {
      const phoneNumber = '3063211234,1080';
      const phoneNumberCountry = 'US';

      const expected = '(************* ext. 1080';
      const actual = formatPhoneNumber(phoneNumber, format, undefined, phoneNumberCountry);
      expect(actual).toEqual(expected);
    });

    it('transforms formatted US numbers with extension', () => {
      const phoneNumber = '(*************,239';
      const phoneNumberCountry = 'US';

      const expected = '(************* ext. 239';
      const actual = formatPhoneNumber(phoneNumber, format, undefined, phoneNumberCountry);
      expect(actual).toEqual(expected);
    });
  });
  describe('International Formatting', () => {
    const format = 'INTERNATIONAL';

    it('returns the formatted number when there is an extension', () => {
      const phoneNumber = '3061234567';
      const phoneNumberCountry = 'US';
      const phoneExtension = '890';

      const actual = formatPhoneNumber(phoneNumber, format, phoneExtension, phoneNumberCountry);
      const expected = '****** 123 4567 ext. 890';

      expect(actual).toEqual(expected);
    });

    it('returns the formatted number when there is no extension', () => {
      const phoneNumber = '3061234567';
      const phoneNumberCountry = 'US';

      const actual = formatPhoneNumber(phoneNumber, format, undefined, phoneNumberCountry);
      const expected = '****** 123 4567';

      expect(actual).toEqual(expected);
    });

    it('returns the formatted phone number if there is no country (country defaults to US)', () => {
      const phoneNumber = '3061234567';

      const actual = formatPhoneNumber(phoneNumber, format);
      const expected = '****** 123 4567';

      expect(actual).toEqual(expected);
    });

    it('transforms non-formatted US numbers with extension', () => {
      const phoneNumber = '3063211234,1080';
      const phoneNumberCountry = 'US';

      const expected = '****** 321 1234 ext. 1080';
      const actual = formatPhoneNumber(phoneNumber, format, undefined, phoneNumberCountry);
      expect(actual).toEqual(expected);
    });

    it('transforms formatted US numbers with extension', () => {
      const phoneNumber = '(*************,239';
      const phoneNumberCountry = 'US';

      const expected = '****** 454 5201 ext. 239';
      const actual = formatPhoneNumber(phoneNumber, format, undefined, phoneNumberCountry);
      expect(actual).toEqual(expected);
    });

    it('returns the formatted phone number if the country is invalid (country defaults to US)', () => {
      const phoneNumber = '3061234567';
      const phoneNumberCountry = 'Invalid-Country-Abbreviation';
      const actual = formatPhoneNumber(phoneNumber, format, undefined, phoneNumberCountry);
      const expected = '****** 123 4567';

      expect(actual).toEqual(expected);
    });

    it('Removes the leading 0 when converting a German number', () => {
      const phoneNumber = '0234 1234;123';
      const phoneNumberCountry = 'DE';
      const actual = formatPhoneNumber(phoneNumber, format, undefined, phoneNumberCountry);
      const expected = '+49 234 1234 ext. 123';

      expect(actual).toEqual(expected);
    });
    it('Removes the leading 0 when converting a German number in a shorter format', () => {
      const phoneNumber = '040 153';
      const phoneNumberCountry = 'DE';
      const extension = '123';
      const actual = formatPhoneNumber(phoneNumber, format, extension, phoneNumberCountry);
      const expected = '+49 40 153 ext. 123';

      expect(actual).toEqual(expected);
    });
  });
});
