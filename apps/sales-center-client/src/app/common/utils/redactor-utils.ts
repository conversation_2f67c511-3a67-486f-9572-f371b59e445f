export const REDACTED_PID = '53JK';
export const REDACTED_DOMAIN = 'harvardmedia.com';

// Determines if the emails contained within the text only come from the email domain provided
export function textContainsOnlyInternalEmails(text: string, emaildomain: string): boolean {
  const RECIPIENT_REGEX = /^(Recipient\(s\):).*?\n/g;
  const EMAIL_REGEX =
    /(?<name>[a-zA-Z0-9.!#$%&'*+/=?^_ \x60{|}~-]+)@(?<domain>[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)/g;

  if (text === '') {
    return false;
  }

  const match = RECIPIENT_REGEX.exec(text);
  if (!match) {
    return false;
  }

  const recipients = match.shift();

  if (!recipients) {
    return false;
  }

  const regexp = new RegExp(EMAIL_REGEX);
  const matches = recipients.matchAll(regexp);

  let count = 0;

  for (const match of matches) {
    count++;

    if (!match?.groups?.domain.startsWith(emaildomain)) {
      return false;
    }
  }

  return count !== 0;
}
