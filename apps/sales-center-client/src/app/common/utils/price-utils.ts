export function convertCentsToDollars(centValue: number): number {
  if (!centValue) {
    return centValue;
  }

  if (!Number.isInteger(centValue)) {
    centValue = Number(centValue);
    centValue = Math.round(centValue);
  }
  return centValue / 100;
}

export function convertDollarsToCents(dollarValue: number): number {
  if (!dollarValue) {
    return dollarValue;
  }

  let centValue = dollarValue * 100;
  if (!Number.isInteger(centValue)) {
    centValue = Number(centValue);
    centValue = Math.round(centValue);
  }
  return centValue;
}
