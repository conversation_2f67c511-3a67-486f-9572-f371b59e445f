import { convertCentsToDollars, convertDollarsToCents } from './price-utils';

describe('convertCentsToDollars', () => {
  it('should convert an amount in cents to dollars', () => {
    const centValue = 2967;
    const expected = 29.67;
    const result = convertCentsToDollars(centValue);
    expect(result).toEqual(expected);
  });
  it('should return the rounded dollar value even if it is a decimal', () => {
    const centValue = 8542.98;
    const result = convertCentsToDollars(centValue);
    expect(result).toEqual(85.43);
  });
  it('should return null if the input is null', () => {
    const centValue: number = null;
    const result = convertCentsToDollars(centValue);
    expect(result).toBe(null);
  });
  it('should throw an error when cent amount is undefined', () => {
    const result = convertCentsToDollars(undefined);
    expect(result).toBe(undefined);
  });
});
describe('convertDollarsToCents', () => {
  it('should convert an amount in dollars to cents', () => {
    const dollarValue = 86.49;
    const expected = 8649;
    const result = convertDollarsToCents(dollarValue);
    expect(result).toEqual(expected);
  });
  it('should return the cent value when the dollar value contains more than two decimals', () => {
    const dollarValue = 87.398;
    const result = convertDollarsToCents(dollarValue);
    expect(result).toEqual(8740);
  });
  it('should return null when input is null', () => {
    const dollarValue: number = null;
    const result = convertDollarsToCents(dollarValue);
    expect(result).toEqual(null);
  });
  it('should return undefined when input is undefined', () => {
    const result = convertDollarsToCents(undefined);
    expect(result).toEqual(undefined);
  });
  it('should return undefined when input is undefined', () => {
    const result = convertDollarsToCents(undefined);
    expect(result).toEqual(undefined);
  });
});
