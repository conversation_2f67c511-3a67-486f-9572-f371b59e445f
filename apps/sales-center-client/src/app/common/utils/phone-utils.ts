import { CountryCode, formatNumber, NumberFormat, getCountries } from 'libphonenumber-js';

export type PhoneNumberFormat = 'NATIONAL' | 'INTERNATIONAL' | 'E.164' | 'RFC3966' | 'IDD';
const countries = getCountries();

function buildNumberWithExtension(number: string, extension?: string): string {
  if (!extension) {
    return number;
  }

  return `${number};${extension}`;
}

export function formatPhoneNumber(
  phoneNumber: string,
  format: PhoneNumberFormat,
  extension?: string,
  countryCode?: string,
): string {
  const numberWithExtension = buildNumberWithExtension(phoneNumber, extension);

  let code: CountryCode = countryCode as CountryCode;
  const countryFound = countries.find((c) => c === code);
  if (!countryFound) {
    code = 'US';
  }

  const fmt: NumberFormat = format;

  try {
    return formatNumber(numberWithExtension, code, fmt);
  } catch (err) {
    return numberWithExtension;
  }
}
