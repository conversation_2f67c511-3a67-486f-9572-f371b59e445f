import { Component, EventEmitter, Input, Output } from '@angular/core';

export interface ItemStatusValue {
  item?: any;
  status?: any;
}

@Component({
  selector: 'app-multi-items-and-status',
  template: `
    <ng-container *ngFor="let itemStatus of itemList">
      <app-items-and-status
        [options]="options"
        [displayProperty]="displayProperty"
        [placeholderText]="placeholderText"
        [sorted]="sorted"
        [alwaysFirstValue]="alwaysFirstValue"
        [itemStatus]="itemStatus"
        (selection)="changeItemAndStatusValue(itemStatus)"
      ></app-items-and-status>
      <div
        *ngIf="selectItemOptions().length > 0 || itemList.indexOf(itemStatus) !== itemList.length - 1"
        class="item-and-status-separator"
      ></div>
    </ng-container>
    <app-items-and-status
      *ngIf="selectItemOptions().length > 0 || options.length === 0"
      [options]="selectItemOptions()"
      [displayProperty]="displayProperty"
      [placeholderText]="placeholderText"
      [sorted]="sorted"
      [alwaysFirstValue]="alwaysFirstValue"
      [itemStatus]="addItemStatus"
      (selection)="addItemAndStatus($event)"
    ></app-items-and-status>
  `,
  styles: [
    `
      .item-and-status-separator {
        margin-top: 10px;
      }
    `,
  ],
  standalone: false,
})
export class MultiItemAndStatusComponent {
  @Output() selection = new EventEmitter();
  @Input() options: any[];
  @Input() alwaysFirstValue: string;
  @Input() displayProperty: string;
  @Input() sorted = false;
  @Input() placeholderText: string;
  @Input() itemList: ItemStatusValue[];
  addItemStatus: ItemStatusValue = {};

  addItemAndStatus(value: ItemStatusValue): void {
    this.itemList.push(value);
    this.selection.emit(this.itemList);

    this.addItemStatus = {};
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  changeItemAndStatusValue(value: ItemStatusValue): void {
    this.selection.emit(this.itemList);
  }

  selectItemOptions(): any[] {
    return this.options.filter((option) => {
      return !this.itemList.find((itemList) => itemList.item === option.item);
    });
  }
}
