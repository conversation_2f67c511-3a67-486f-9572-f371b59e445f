import { Component, EventEmitter, Input, Output } from '@angular/core';

export interface ItemStatusValue {
  item: any;
  status: any;
}

@Component({
  selector: 'app-items-and-status',
  template: `
    <uikit-search-select
      (selection)="addItemListItem($event)"
      [options]="itemMenuOptions()"
      [alwaysFirstValue]="alwaysFirstValue"
      [showSearch]="true"
      [displayProperty]="displayProperty"
      [sorted]="sorted"
      [selected]="itemStatus.item"
      [placeholderText]="placeholderText"
    ></uikit-search-select>
    <div *ngIf="!!itemStatus.item" class="item-and-status-separator"></div>
    <uikit-search-select
      *ngIf="!!itemStatus.item"
      (selection)="changeItemStatus($event)"
      [options]="statusMenuOptions()"
      [alwaysFirstValue]="alwaysFirstValue"
      [displayProperty]="displayProperty"
      [sorted]="sorted"
      [selected]="itemStatus.status"
      [placeholderText]=""
      [showSearch]="false"
    ></uikit-search-select>
  `,
  styles: [
    `
      .item-and-status-separator {
        margin-top: 5px;
      }
    `,
  ],
  standalone: false,
})
export class ItemAndStatusComponent {
  @Output() selection = new EventEmitter();
  @Input() options: any[];
  @Input() alwaysFirstValue: string;
  @Input() displayProperty: string;
  @Input() sorted = false;
  @Input() placeholderText: string;
  @Input() itemStatus: ItemStatusValue;

  addItemListItem(item: any): void {
    this.itemStatus.item = item;
    this.itemStatus.status = this.statusMenuOptions()[0];
    this.selection.emit(this.itemStatus);
  }

  changeItemStatus(status: any): void {
    this.itemStatus.status = status;
    this.selection.emit(this.itemStatus);
  }

  itemMenuOptions(): any[] {
    return this.options.map((option) => option.item) || [];
  }

  statusMenuOptions(): any[] {
    return this.options.find((option) => option.item === this.itemStatus.item).status;
  }
}
