import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';

import { MenuItem } from './menu-item';
import { catchError, map } from 'rxjs/operators';

@Injectable()
export class MenuService {
  constructor(private readonly http: HttpClient) {}

  getMenuItems(url: string): Observable<MenuItem> {
    return this.http.get<any>(url).pipe(map(this.extractData), catchError(this.handleError));
  }

  private extractData(res: any): any {
    return res.data || res || {};
  }

  private handleError(error: HttpErrorResponse): any {
    const errMsg = error.message ? error.message : error.toString();
    console.error(errMsg);
    return throwError(errMsg);
  }
}
