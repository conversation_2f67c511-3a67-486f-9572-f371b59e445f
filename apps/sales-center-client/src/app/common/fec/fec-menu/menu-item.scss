@use 'design-tokens' as *;

:host {
  display: block;

  -webkit-user-select: none; /* webkit (safari, chrome) browsers */
  -moz-user-select: none; /* mozilla browsers */
  -khtml-user-select: none; /* webkit (konqueror) browsers */
  -ms-user-select: none; /* IE10+ */

  ul {
    list-style: none;
    margin: 0;
    display: block;
    padding: 0;
    overflow: hidden;
  }

  li {
    position: relative;
    transition-duration: 200ms;
  }

  app-va-menu-item {
    display: block;
  }
  a {
    cursor: pointer;
    width: 100%;
    padding: 13px 10px 13px 56px;
    position: relative;
    transition: all 0.3s;
    display: inline-block;
    text-align: left;
    text-decoration: none;
  }
  a:focus {
    outline: 0;
  }
  a:active,
  a:focus {
    outline: 0;
  }
  va-icon {
    position: absolute;
    top: 50%;
    margin-top: -12px;
  }
  ::ng-deep va-icon {
    .fec-icon:before,
    .material-icons {
      transition-duration: 200ms;
    }
    .menu-open:before {
      transform: rotate(90deg);
    }
  }
  va-icon[dropdown-icon] {
    right: 1em;
  }
  va-icon:not([dropdown-icon]) {
    left: 1em;
  }

  a {
    // Remove the gray background color from active links in IE 10.
    background-color: transparent;
  }

  /**
   * Improve readability when focused and also mouse hovered in all browsers.
   */
  a:active,
  a:hover {
    outline: 0;
  }
}
