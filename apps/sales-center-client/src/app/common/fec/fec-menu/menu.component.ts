/* tslint:disable */
import { Component, ElementRef, EventEmitter, HostBinding, Input, OnInit, Output } from '@angular/core';

import { MenuItem } from './menu-item';
import { MenuService } from './menu.service';

@Component({
  selector: 'app-va-menu',
  template: `
    <ul>
      <app-va-menu-item
        *ngFor="let menuItem of menuItems"
        [activeMenuId]="activeMenuId"
        [menuItem]="menuItem"
        (menuItemClickEvent)="onMenuItemClick($event)"
      ></app-va-menu-item>
    </ul>
  `,
  styleUrls: ['./menu.scss'],
  standalone: false,
})
export class MenuComponent implements OnInit {
  @Input() menuItems: MenuItem[];
  @Input() activeMenuId: string;
  @Input() url: string;
  @Input() activeMenuItem: string;
  @Input() autoClose: boolean;
  @Output() menuItemClickEvent: EventEmitter<any> = new EventEmitter();
  errorMessage: string;

  @HostBinding('document:click') backgroundClick($event) {
    if (!this.containsElement($event.target) && this.autoClose && this.menuItems[0]) {
      this.menuItems[0].expanded = false;
    }
  }
  // Requires MenuService if you're using the url
  constructor(
    private menuService: MenuService,
    private elementRef: ElementRef,
  ) {}

  getMenuItems() {
    this.menuService.getMenuItems(this.url).subscribe(
      (menuItems) => (this.menuItems = <any>menuItems),
      (error) => (this.errorMessage = <any>error),
    );
  }

  ngOnInit() {
    if (this.url && (!this.menuItems || this.menuItems == undefined)) {
      this.getMenuItems();
    }
  }

  onMenuItemClick($event: any) {
    this.menuItemClickEvent.emit($event);
  }

  containsElement(target: any) {
    return this.elementRef.nativeElement.contains(target);
  }
}
