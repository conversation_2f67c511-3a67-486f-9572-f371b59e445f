/* tslint:disable */
import { animate, state, style, transition, trigger } from '@angular/animations';
import { Component, EventEmitter, Input, OnInit, Output, OnChanges } from '@angular/core';

import { MenuItem } from './menu-item';

const icon_snippet = `
    <app-va-icon icon *ngIf="menuItem.icon" [iconClass]="menuItem.icon"></app-va-icon>
    {{menuItem.label}}
    <app-va-icon dropdown-icon *ngIf="rightIcon" [iconClass]="rightIcon"></app-va-icon>`;

@Component({
  selector: 'app-va-menu-item',
  template:
    `
    <li [class.active]="active">
        <a *ngIf="!menuItem.url" (click)="onClick()">` +
    icon_snippet +
    `</a>
        <a *ngIf="menuItem.url && !menuItem.ngRoute" [href]="menuItem.url" [attr.target]="popOut ? '_blank' : '_self'">` +
    icon_snippet +
    `</a>
        <a *ngIf="menuItem.url && menuItem.ngRoute" [routerLink]="menuItem.url" [attr.target]="popOut ? '_blank' : '_self'">` +
    icon_snippet +
    `</a>
    </li>
    <ul class="sub-menu" [@expand]="menuItem.expanded.toString()">
        <app-va-menu-item *ngFor="let subMenuItem of menuItem.nestedItems" [activeMenuId]="activeMenuId" [menuItem]="subMenuItem" (menuItemClickEvent)="onMenuItemClick($event);"></app-va-menu-item>
    </ul>
    `,
  styleUrls: ['./menu-item.scss'],
  animations: [
    trigger('expand', [
      state('false', style({ height: 0 })),
      transition('1 => 0', [animate('200ms')]),
      transition('0 => 1', [animate('200ms')]),
    ]),
  ],
  standalone: false,
})
export class MenuItemComponent implements OnInit, OnChanges {
  @Input() menuItem: MenuItem;
  @Input() activeMenuId: string;
  @Output() menuItemClickEvent: EventEmitter<any> = new EventEmitter();
  active = false;
  rightIcon: string;
  popOut: boolean;
  shouldToggleIcon = false;

  ngOnInit() {
    this.rightIcon = this.getRightIcon();
    this.popOut = this.menuItem.popOut;
    this.menuItem.expanded = false;
  }

  getToggledIcon() {
    if (this.menuItem.expanded == true) {
      return 'fec-icon-right menu-open';
    } else {
      return 'fec-icon-right';
    }
  }

  goToUrl(url: string) {
    // TODO: use angular router to navigate (unless going to a KO page)
    window.location.href = url;
  }

  onClick() {
    if (this.menuItem.url) {
      this.goToUrl(this.menuItem.url);
    } else {
      if (!this.menuItem.nestedItems) {
        const event = [{ value: this.menuItem.menuId }];
        this.menuItemClickEvent.emit(event);
      } else {
        this.expandAction(!this.menuItem.expanded);
      }
    }
  }

  getRightIcon() {
    if (this.menuItem.rightIcon !== undefined) {
      return this.menuItem.rightIcon;
    } else if (this.menuItem.nestedItems !== undefined) {
      this.shouldToggleIcon = true;
      return this.getToggledIcon();
    } else {
      return;
    }
  }

  onMenuItemClick($event: any) {
    this.menuItemClickEvent.emit($event);
  }

  expandAction(expanded: boolean) {
    this.menuItem.expanded = expanded;
    if (this.menuItem.expanded === true) {
      this.active = true;
    } else {
      for (const subMenu of this.menuItem.nestedItems) {
        if (subMenu.menuId === this.activeMenuId) {
          this.active = true;
          break;
        }
        this.active = false;
      }
    }
    if (this.shouldToggleIcon === true) {
      this.rightIcon = this.getToggledIcon();
    }
  }

  checkDropdownToggle(changes: any) {
    for (const subMenu of this.menuItem.nestedItems) {
      if (subMenu.menuId === changes.activeMenuId.currentValue) {
        this.active = true;
        this.expandAction(true);
        break;
      }
      this.expandAction(false);
    }
  }

  ngOnChanges(changes: any) {
    if (this.menuItem.nestedItems) {
      this.checkDropdownToggle(changes);
    } else {
      if (this.menuItem.menuId === changes.activeMenuId.currentValue) {
        this.active = !this.active;
      } else {
        this.active = false;
      }
    }
  }
}
