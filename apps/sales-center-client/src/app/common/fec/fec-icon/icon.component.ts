import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-va-icon',
  template: `
    <span *ngIf="isMaterial()" [class]="'material-icons'">{{ iconName() }}</span>
    <span *ngIf="!isMaterial()" [class]="'fec-icon ' + iconName()"></span>
  `,
  standalone: false,
})
export class IconComponent {
  @Input() iconClass: string;

  isMaterial(): boolean {
    const parts: string[] = this.iconClass.split(':');
    return parts.length > 1 && parts[0] === 'material';
  }

  iconName(): string {
    const parts: string[] = this.iconClass.split(':');
    if (parts.length === 1) {
      return parts[0];
    } else {
      return parts[1];
    }
  }
}
