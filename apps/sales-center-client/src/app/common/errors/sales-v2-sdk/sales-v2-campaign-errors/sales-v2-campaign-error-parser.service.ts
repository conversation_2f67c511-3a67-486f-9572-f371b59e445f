import { Injectable } from '@angular/core';
import { AddToCampaignError, AddToCampaignErrorContents } from './errors';

@Injectable()
export class SalesV2CampaignErrorParserService {
  private static removeNewlineAndEscapeCharacters(msg: string): string {
    const ESCAPE_REGEX = /[\\]+/g;
    const NEWLINE_REGEX = /(\\n)+/g;
    msg = msg.replace(NEWLINE_REGEX, '');
    msg = msg.replace(ESCAPE_REGEX, '');
    return msg;
  }

  private static getProductNameFromError(msg: string): string {
    const PRODUCT_REGEX = /Account does not have access to (?<productName>.*)"]/;
    const m = msg.match(PRODUCT_REGEX);

    return m?.groups['productName'] || '';
  }

  determineAddToCampaignErrorType(msg: string): AddToCampaignErrorContents {
    const cleanedMessage = SalesV2CampaignErrorParserService.removeNewlineAndEscapeCharacters(msg);
    if (cleanedMessage.includes(AddToCampaignError.MAILING_INFO_NOT_CONFIGURED)) {
      return <AddToCampaignErrorContents>{
        errorType: AddToCampaignError.MAILING_INFO_NOT_CONFIGURED,
      };
    }

    if (cleanedMessage.includes(AddToCampaignError.SNAPSHOT_MISSING)) {
      return <AddToCampaignErrorContents>{
        errorType: AddToCampaignError.SNAPSHOT_MISSING,
      };
    }

    if (cleanedMessage.includes(AddToCampaignError.CANNOT_ACCESS_PRODUCT)) {
      const productName = SalesV2CampaignErrorParserService.getProductNameFromError(cleanedMessage);
      return <AddToCampaignErrorContents>{
        errorType: AddToCampaignError.CANNOT_ACCESS_PRODUCT,
        productName: productName,
      };
    }

    return <AddToCampaignErrorContents>{
      errorType: AddToCampaignError.UNKNOWN,
    };
  }
}
