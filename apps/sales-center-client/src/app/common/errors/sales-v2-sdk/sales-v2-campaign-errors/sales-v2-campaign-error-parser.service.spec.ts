import { SalesV2CampaignErrorParserService } from './sales-v2-campaign-error-parser.service';
import { AddToCampaignError, AddToCampaignErrorContents } from './errors';

describe('SalesV2CampaignErrorParserService', () => {
  let service: SalesV2CampaignErrorParserService;
  describe('Retrieving error types from strings', () => {
    beforeEach(() => {
      service = new SalesV2CampaignErrorParserService();
    });
    it('Should extract mail configuration errors', () => {
      const errString = `{"code":9,"message":"Bad Request: {\n  "error": {\n    "field": "campaign_id",\n    "message": "mailing-info-not-configured"\n  },\n  "took": 123\n}"}`;
      const t = service.determineAddToCampaignErrorType(errString);
      const expected = <AddToCampaignErrorContents>{
        errorType: AddToCampaignError.MAILING_INFO_NOT_CONFIGURED,
      };
      expect(t).toEqual(expected);
    });
    it('Should extract snapshot missing errors', () => {
      const errString = `{\n "error": {\n "message": "["Snapshot report is missing"]" \n}, "took": 389 \n}`;
      const t = service.determineAddToCampaignErrorType(errString);
      const expected = <AddToCampaignErrorContents>{
        errorType: AddToCampaignError.SNAPSHOT_MISSING,
      };
      expect(t).toEqual(expected);
    });
    it('Should extract product activation errors, along with the product name', () => {
      const errString = `{\n "error": {\n "message": "[\\"Account does not have access to Social Marketing\\"]" \n}, "took": 937 \n}`;
      const t = service.determineAddToCampaignErrorType(errString);
      const expected = <AddToCampaignErrorContents>{
        errorType: AddToCampaignError.CANNOT_ACCESS_PRODUCT,
        productName: 'Social Marketing',
      };
      expect(t).toEqual(expected);
    });
    it('Should return unknown if a type cannot be extracted', () => {
      const errString = `Potentially anything`;
      const t = service.determineAddToCampaignErrorType(errString);
      const expected = <AddToCampaignErrorContents>{
        errorType: AddToCampaignError.UNKNOWN,
      };
      expect(t).toEqual(expected);
    });
  });
});
