import { GenericTableLabels } from '@vendasta/va-filter2-table';

export const COMMON_TABLE_LABELS: GenericTableLabels = {
  noResults: 'PROSPECT_COMPONENT.ENUMS.NO_RESULTS',
  cancel: 'COMMON.ACTION_LABELS.CANCEL',
  none: 'COMMON.ACTION_LABELS.NONE_LABEL',
  update: 'COMMON.ACTION_LABELS.UPDATE',
  sortBy: 'PROSPECT_COMPONENT.SORT.SORT_COLUMN_ORDER_DESCRIPTION_TEXT.SORT_BY',
  thenSortBy: 'PROSPECT_COMPONENT.SORT.SORT_COLUMN_ORDER_DESCRIPTION_TEXT.THEN_SORT_BY',
  addAnotherSortColumn: 'PROSPECT_COMPONENT.SORT.SELECT_COLUMN_PLACEHOLDER',
  chooseColumnToSortBy: 'PROSPECT_COMPONENT.SORT.SELECT_COLUMN_PLACEHOLDER_EMPTY_STATE',
  columnSorting: {
    date: {
      ascending: 'PROSPECT_COMPONENT.SORT.SORT_TOGGLE_LABELS.EARLIEST_TO_LATEST',
      descending: 'PROSPECT_COMPONENT.SORT.SORT_TOGGLE_LABELS.LATEST_TO_EARLIEST',
    },
    number: {
      ascending: 'PROSPECT_COMPONENT.SORT.SORT_TOGGLE_LABELS.1_TO_9',
      descending: 'PROSPECT_COMPONENT.SORT.SORT_TOGGLE_LABELS.9_TO_1',
    },
    string: {
      ascending: 'PROSPECT_COMPONENT.SORT.SORT_TOGGLE_LABELS.A_TO_Z',
      descending: 'PROSPECT_COMPONENT.SORT.SORT_TOGGLE_LABELS.Z_TO_A',
    },
    default: {
      ascending: 'PROSPECT_COMPONENT.SORT.SORT_TOGGLE_LABELS.FIRST_TO_LAST',
      descending: 'PROSPECT_COMPONENT.SORT.SORT_TOGGLE_LABELS.LAST_TO_FIRST',
    },
  },
  export: {
    buttonText: 'PROSPECT_COMPONENT.EXPORT.BUTTON',
    buttonTooltipBeforeCount: 'PROSPECT_COMPONENT.EXPORT.BUTTON_TOOLTIP_BEFORE_COUNT',
    buttonTooltipAfterCount: 'PROSPECT_COMPONENT.EXPORT.BUTTON_TOOLTIP_AFTER_COUNT',
    description: 'PROSPECT_COMPONENT.EXPORT.BUTTON_DESCRIPTION',
  },
  columnSelector: {
    buttonText: 'PROSPECT_COMPONENT.COLUMN_SELECTOR.BUTTON',
  },
  actionsButton: 'PROSPECT_COMPONENT.BULK_ACTIONS.BUTTON',
};
