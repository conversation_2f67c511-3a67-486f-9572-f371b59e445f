import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { Environment, EnvironmentService } from '@galaxy/core';
import { Observable, of } from 'rxjs';
import { CreateUsersService } from '../vbc_sdk/create-users.service';
import { UserResult, VBCUserService } from './user.service';

const AGID = 'AG-123';
const PID = 'TEST';
const DEMO_LOOKUP_PATH = `https://vbc-demo.appspot.com/internalApi/v3/user/lookupByAccountGroup/?partnerId=${PID}&accountGroupId=${AGID}&mask=email&mask=userId&mask=firstName&mask=lastName&mask=unifiedUserId`;
class MockEnvService {
  getEnvironment(): Environment {
    return Environment.DEMO;
  }
}

class MockCreateUsersService {
  createAndAssociateUser(): Observable<any> {
    throw new Error('Method not implemented');
  }
}

describe('UserService', () => {
  let service: VBCUserService;
  let httpMock: HttpTestingController;
  const mockCreateUsrSrvc = new MockCreateUsersService();
  const createUsersSpy = jest.spyOn(mockCreateUsrSrvc, 'createAndAssociateUser');
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        { provide: EnvironmentService, useValue: new MockEnvService() },
        VBCUserService,
        { provide: CreateUsersService, useValue: mockCreateUsrSrvc },
      ],
    });
    service = TestBed.inject(VBCUserService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should return the users for the account', (done) => {
    const mockUsers = <UserResult[]>[
      { userId: 'U-123', email: '<EMAIL>', firstName: 'Test', lastName: 'User', unifiedUserId: 'U-123' },
      { userId: 'U-124', email: '<EMAIL>', firstName: 'Test2', lastName: 'User2', unifiedUserId: 'U-124' },
    ];

    createUsersSpy.mockReturnValueOnce(of(mockUsers));
    service.getUsersForAccount(PID, AGID).subscribe((contact) => {
      expect(contact).toEqual(mockUsers);
      done();
    });

    const req = httpMock.expectOne(DEMO_LOOKUP_PATH);
    expect(req.request.method).toBe('GET');
    req.flush({ data: mockUsers });
  });

  it('should return an empty array if there are no users', (done) => {
    service.getUsersForAccount(PID, AGID).subscribe((users) => {
      expect(users).toEqual([]);
      done();
    });

    const req = httpMock.expectOne(DEMO_LOOKUP_PATH);
    expect(req.request.method).toBe('GET');
    req.flush({ data: [] });
  });
});
