import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { AccessChecker, Feature, SSCAccessService } from '../../../access';
import { SalesActivityOpportunitiesService } from '../../../sales-activity/sales-activity-opportunities.service';
import { firstValueFrom } from 'rxjs';

export interface AddSalesActivityDialogData {
  accountGroupId: string;
  accountGroupName: string;
  partnerId: string;
  marketId: string;
}

@Component({
  selector: 'app-add-sales-activity-dialog',
  templateUrl: 'add-sales-activity-dialog.component.html',
  standalone: false,
})
export class AddSalesActivityDialogComponent implements OnInit {
  accountGroupId: string;
  partnerId: string;
  marketId: string;
  accountGroupName: string;

  constructor(
    private readonly salesActivityOpportunitiesService: SalesActivityOpportunitiesService,
    public dialogRef: MatDialogRef<AddSalesActivityDialogComponent>,
    private readonly alertService: SnackbarService,
    @Inject(MAT_DIALOG_DATA) data: AddSalesActivityDialogData,
    @Inject(SSCAccessService) private readonly access: AccessChecker,
    private readonly i18n: TranslateService,
  ) {
    this.accountGroupId = data.accountGroupId;
    this.accountGroupName = data.accountGroupName;
    this.partnerId = data.partnerId;
    this.marketId = data.marketId;
  }

  async ngOnInit(): Promise<void> {
    const hasAccess = await firstValueFrom(this.access.hasAccessToFeature(Feature.pipeline));
    if (hasAccess) {
      this.salesActivityOpportunitiesService.init(this.partnerId, this.marketId, this.accountGroupId);
    }
  }

  savedHandler(): void {
    const text = this.i18n.instant('MANAGE_ACCOUNTS.ACTIVITY_ADD_CONFIRMATION');
    this.alertService.openSnackBar(text);
    this.dialogRef.close();
  }
}
