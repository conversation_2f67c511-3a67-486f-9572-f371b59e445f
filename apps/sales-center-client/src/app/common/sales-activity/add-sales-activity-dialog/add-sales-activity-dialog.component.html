<div class="add-sales-activity-modal">
  <h3 mat-dialog-title>
    {{ 'MANAGE_ACCOUNTS.ADD_SALES_ACTIVITY_DIALOG_TITLE' | translate: { accountGroupName: accountGroupName } }}
  </h3>
  <mat-dialog-content>
    <app-sales-activity-composer
      [showOpportunitiesSelector]="true"
      [accountGroupId]="accountGroupId"
      [partnerId]="partnerId"
      (saved)="savedHandler()"
      (cancelled)="dialogRef.close()"
    ></app-sales-activity-composer>
  </mat-dialog-content>
</div>
