import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AddSalesActivityDialogComponent } from './add-sales-activity-dialog.component';
import { MatDialogModule } from '@angular/material/dialog';
import { SalesActivityModule } from '../../../sales-activity/sales-activity.module';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  declarations: [AddSalesActivityDialogComponent],
  imports: [CommonModule, MatDialogModule, SalesActivityModule, TranslateModule.forChild()],
})
export class AddSalesActivityDialogModule {}
