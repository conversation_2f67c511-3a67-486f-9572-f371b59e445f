import { AccountSelectorComponent } from './account-selector.component';
import { FormControl, Validators } from '@angular/forms';

describe('AccountSelectorComponent', () => {
  describe('isControlRequired', () => {
    it('should return false if form control is undefined', () => {
      const comp = new AccountSelectorComponent(undefined);
      const actual = comp.isControlRequired(undefined);
      expect(actual).toBe(false);
    });

    it('should return false if form control has no validators', () => {
      const comp = new AccountSelectorComponent(undefined);
      const c = new FormControl(undefined);
      const actual = comp.isControlRequired(c);
      expect(actual).toBe(false);
    });

    it('should return false if form control is not required', () => {
      const comp = new AccountSelectorComponent(undefined);
      const c = new FormControl(undefined, Validators.email);
      const actual = comp.isControlRequired(c);
      expect(actual).toBe(false);
    });

    it('should return true if form control is required', () => {
      const comp = new AccountSelectorComponent(undefined);
      const c = new FormControl(undefined, [Validators.email, Validators.required]);
      const actual = comp.isControlRequired(c);
      expect(actual).toBe(true);
    });
  });
});
