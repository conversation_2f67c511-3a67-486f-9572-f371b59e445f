import { Component, EventEmitter, Input, OnInit, Optional, Output } from '@angular/core';
import { AbstractControl, UntypedFormControl } from '@angular/forms';
import { Observable } from 'rxjs';
import { debounceTime, map, startWith, switchMap } from 'rxjs/operators';
import { AccountGroup, ProjectionFilter } from '@galaxy/account-group';
import { BusinessService } from '../../../business/business.service';

export interface Account {
  companyName: string;
  accountGroupId: string;
}

const accountGroupsToUIAccounts = (ags: AccountGroup[]): Account[] => ags.map(accountGroupToUIAccount);

function accountGroupToUIAccount(ag: AccountGroup): Account {
  return { companyName: ag.napData.companyName, accountGroupId: ag.accountGroupId };
}

@Component({
  selector: 'app-account-selector',
  templateUrl: './account-selector.component.html',
  styleUrls: ['./account-selector.component.scss'],
  standalone: false,
})
export class AccountSelectorComponent implements OnInit {
  @Input() allowNoneOption: boolean;
  @Optional() @Input() parentFormCtrl: UntypedFormControl;

  @Output() selectedNonNoneValueAccountGroupId: EventEmitter<string> = new EventEmitter<string>();

  filteredAccounts$: Observable<Account[]>;
  accountControl: UntypedFormControl;
  inputPlaceHolder$: Observable<string>;
  isControlRequired = (c: AbstractControl): boolean => {
    if (c?.validator) {
      const v = c.validator({} as AbstractControl);
      if (v?.required) {
        return true;
      }
    }
    return false;
  };

  constructor(private readonly businessService: BusinessService) {}

  ngOnInit(): void {
    this.initFormControl();
    this.filteredAccounts$ = this.getFilteredAccounts();
    this.setupValueEmitter();
  }

  displayCompanyName(account?: Account): string | undefined {
    return account ? account.companyName : undefined;
  }

  private initFormControl(): void {
    this.accountControl = this.parentFormCtrl || new UntypedFormControl(undefined);
  }

  private getFilteredAccounts(): Observable<Account[]> {
    const pf = new ProjectionFilter({ napData: true });
    return this.accountControl.valueChanges.pipe(
      startWith<string | Account | undefined>(''),
      map((inputTerm) => {
        const noneIsSelected = inputTerm === undefined;
        if (noneIsSelected) {
          return '';
        }
        return typeof inputTerm === 'string' ? inputTerm : inputTerm.companyName;
      }),
      debounceTime(300),
      switchMap((searchTerm) => this.businessService.search(pf, searchTerm, 20)),
      map(accountGroupsToUIAccounts),
    );
  }

  private setupValueEmitter(): void {
    this.accountControl.valueChanges.subscribe((value) => {
      if (typeof value === 'string') {
        return;
      }
      this.selectedNonNoneValueAccountGroupId.emit(value.accountGroupId);
    });
  }
}
