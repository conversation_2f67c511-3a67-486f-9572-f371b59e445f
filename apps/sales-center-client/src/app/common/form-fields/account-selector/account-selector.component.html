<mat-form-field class="account-selector-field" appearance="outline">
  <mat-label>{{ 'COMMON.LABELS.ACCOUNT' | translate }}</mat-label>
  <input
    type="text"
    matInput
    aria-label="Account"
    [required]="isControlRequired(accountControl)"
    [formControl]="accountControl"
    [matAutocomplete]="auto"
  />
  <mat-autocomplete #auto="matAutocomplete" autoActiveFirstOption [displayWith]="displayCompanyName">
    <mat-option *ngIf="allowNoneOption">
      {{ 'COMMON.NONE_LABEL' | translate }}
    </mat-option>
    <mat-option *ngFor="let account of filteredAccounts$ | async" [value]="account">
      {{ account.companyName }}
    </mat-option>
  </mat-autocomplete>
</mat-form-field>
