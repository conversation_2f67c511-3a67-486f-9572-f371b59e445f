import Autolinker from 'autolinker';

/**
 * Converts the plain text argument to "html" by turning any urls into links (which
 * open in a new window) and replacing new lines with line breaks in the following way:
 *      - a series of new lines becomes two html line breaks
 *      - a single new line is replaced with a line break
 * @param str - plain text to convert to html
 * @returns string - An html version of the original text.
 */
export function convertToHTML(str: string): string {
  let html = str;
  /* replace whitespace lines with empty lines (so that the next replacement
   * works even if there is whitespace between double new lines) */
  /* tslint:disable */
  html = html.replace(
    // eslint-disable-next-line no-irregular-whitespace
    /^[ \f\t\v​\u00a0\u1680​\u180e\u2000​\u2001\u2002​\u2003\u2004\u2005\u2006​\u2007\u2008​\u2009\u200a​\u2028\u2029​\u202f\u205f​\u3000]+$/gm,
    '',
  );
  /* tslint:enable */
  /* turn two (or more) new lines into two html line breaks */
  html = html.replace(/(\r\n|\r|\n)\1{1,}/g, '<br/><br/>');
  /* replaces any remaining new lines with html line breaks */
  html = html.replace(/(\r\n|\r|\n)/g, '<br/>');

  return Autolinker.link(html);
}
