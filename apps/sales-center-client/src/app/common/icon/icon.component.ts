import { Component, Input } from '@angular/core';

export interface IconDescriptor {
  iconUrl: string;
  name: string;
  diameter?: number;
}

@Component({
  selector: 'app-st-icon',
  template: `
    <div
      class="icon-container"
      [style.background-color]="iconUrl ? '' : getIconColorForName()"
      [ngStyle]="getIconSize()"
    >
      <img *ngIf="iconUrl" [src]="iconUrl" />
      <span *ngIf="!iconUrl" class="icon" [ngStyle]="getTextSize()">
        {{ getAbbreviationForName() }}
      </span>
    </div>
  `,
  styleUrls: ['./icon.component.scss'],
  standalone: false,
})
export class IconComponent {
  @Input() iconUrl: string;
  @Input() name: string;
  @Input() diameter = 60;

  getAbbreviationForName(): string {
    const defaultAbbreviation = 'U'; // untitled
    if (this.name) {
      // example input "Partner Central"
      // example output "PC"

      // example input "Partner"
      // example output "P"
      const names = this.name.split(' ');
      return names.length > 1 ? names[0][0] + names[1][0] : names[0][0];
    }
    return defaultAbbreviation;
  }

  getIconColorForName(): string {
    // determine an icon color for a product with no icon by using the product name
    const COLOR_CODES = [
      '#EF5350',
      '#42A5F5',
      '#66BB6A',
      '#FFA726',
      '#AB47BC',
      '#FFCA28',
      '#EC407A',
      '#26C6DA',
      '#FF7B57',
    ];
    let nameSum = 0;
    const defaultColor = '#808080';
    if (!this.name) {
      return defaultColor;
    }
    for (let i = 0; i < this.name.length; i++) {
      nameSum += this.name[i].charCodeAt(0);
    }
    const index = nameSum % COLOR_CODES.length;
    return COLOR_CODES[index];
  }

  getIconSize(): any {
    return { height: this.diameter + 'px', width: this.diameter + 'px' };
  }

  getTextSize(): any {
    const fontSize: number = (this.diameter * 25) / 60;
    return { 'line-height': this.diameter + 'px', 'font-size': fontSize + 'px' };
  }
}
