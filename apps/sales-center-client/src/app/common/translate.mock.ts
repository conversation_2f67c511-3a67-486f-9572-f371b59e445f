import { Observable, of } from 'rxjs';
import * as englishTranslations from '../../assets/i18n/en_devel.json';

export class MockTranslateService {
  instant(): string | any {
    return {};
  }

  stream(): Observable<string | any> {
    return of({});
  }
}

/**
 * Returns the translation key, unchanged.
 */
export class IdentityMockTranslateService {
  instant(key: string | Array<string>): string | any {
    return key;
  }

  stream(key: string | Array<string>): Observable<string | any> {
    return of(key);
  }
}

/** This class will return the full english translation for instant given a key like "COMMON.ACTION_LABELS.SAVE" it will return "Save" */
export class MockTranslateServiceEnglishValueReturn {
  instant(key: string | Array<string>, interpolateParams?: unknown): string | any {
    let keyValues;
    let currentTranslations = englishTranslations;
    let value = '';

    // this can be improved upon, currently it only handles a single string input not an array
    if (typeof key as string) {
      const stringKey = key as string;
      keyValues = stringKey.split('.');

      for (const [i, keyValue] of keyValues.entries()) {
        try {
          if (i === keyValues.length - 1) {
            value = (<any>currentTranslations)[keyValue];
          }
          currentTranslations = (<any>currentTranslations)[keyValue];
        } catch (e) {
          return key;
        }
      }
    }

    // this should handle any instances when something other than a string is passed in for a key.
    // It may need to be updated to handle an array of string in the future.
    if (value === undefined) {
      return key;
    } else {
      if (interpolateParams) {
        const keys = Object.keys(interpolateParams);
        for (const k of keys) {
          const regex = new RegExp('{{ *' + k + ' *}}', 'g');
          value = value.replace(regex, interpolateParams[k]);
        }
      }

      return value;
    }
  }

  // Needs to be implemented.
  stream(key: string | Array<string>): [string] {
    let keyValues;
    let currentValue = englishTranslations;

    // this can be improved upon, currently it only handles a single string input not an array, and
    // it does not insert any parameters passed in.
    if (typeof key as string) {
      const stringKey = key as string;
      keyValues = stringKey.split('.');

      for (const keyValue of keyValues) {
        try {
          currentValue = (<any>currentValue)[keyValue];
        } catch (e) {
          return [stringKey];
        }
      }
    }

    // this should handle any instances when something other than a string is passed in for a key.
    // It may need to be updated to handle an array of string in the future.
    if (currentValue === undefined) {
      return [key] as [string];
    } else {
      return [currentValue] as unknown as [string];
    }
  }
}
