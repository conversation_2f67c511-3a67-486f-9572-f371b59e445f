import { Component, EventEmitter, Input, Output } from '@angular/core';
@Component({
  selector: 'app-select-and-toggle',
  template: `
    <uikit-search-select
      #searchSelect
      (selection)="addToggleListItem($event)"
      [options]="dropdownMenuOptions()"
      [alwaysFirstValue]="alwaysFirstValue"
      [showSearch]="showSearch"
      [selected]="null"
      [displayProperty]="displayProperty"
      [sorted]="sorted"
      [placeholderText]="placeholderText"
      [width]="width"
    ></uikit-search-select>
    <ng-container *ngFor="let item of toggleItemList">
      <mat-slide-toggle color="primary" [checked]="item.toggleValue">
        {{ searchSelect.optionText(item.item) }}
      </mat-slide-toggle>
    </ng-container>
  `,
  standalone: false,
})
export class SelectAndToggleComponent {
  @Output() selection = new EventEmitter();
  @Input() toggleItemList: any[] = [];
  @Input() options: any[];
  @Input() alwaysFirstValue: string;
  @Input() showSearch = false;
  @Input() displayProperty: string;
  @Input() sorted = false;
  @Input() placeholderText: string;
  @Input() width: number;

  addToggleListItem(item: any): void {
    const toggleItem = { item: item, toggleValue: false };
    this.selection.emit(toggleItem);
  }

  dropdownMenuOptions(): any[] {
    return this.options.filter((option) => !this.toggleItemList.some((item) => item === option));
  }
}
