import { TranslateService } from '@ngx-translate/core';
import { ArchivedFilter, LatestSalesActivityOption } from '@vendasta/prospect';
import { CheckboxFilterControl, SelectFilterControl } from '@vendasta/va-filter2';
import { assertNever } from 'assert-never';
import { of } from 'rxjs';

export function NewCheckboxFilterControl(name: string, label: string, hint?: string): CheckboxFilterControl {
  return new CheckboxFilterControl(name, label, false, {
    appliedValueMapper: (avName) => ({
      name: avName,
      label: label,
    }),
    hint: hint,
    hintClass: 'respect-whitespace',
  });
}

export enum SalesActivityFilterOption {
  AllTime = 'all-time',
  WithinTheLast30Days = 'within-30-days',
  OlderThan30Days = 'older-than-30-days',
}

export function NewLatestSalesActivityFilter(ts: TranslateService): SelectFilterControl {
  return new SelectFilterControl(
    'latestSalesActivity',
    ts.instant('COMMON.ACCOUNT_ATTRIBUTES.LAST_SALES_ACTIVITY'),
    of(
      new Map<string, SalesActivityFilterOption>([
        [ts.instant('PROSPECT_COMPONENT.FILTERS.SALES_ACTIVITY_OPTIONS.ALL'), SalesActivityFilterOption.AllTime],
        [
          ts.instant('PROSPECT_COMPONENT.FILTERS.SALES_ACTIVITY_OPTIONS.30_DAYS'),
          SalesActivityFilterOption.WithinTheLast30Days,
        ],
        [
          ts.instant('PROSPECT_COMPONENT.FILTERS.SALES_ACTIVITY_OPTIONS.OLDER_THAN_30_DAYS'),
          SalesActivityFilterOption.OlderThan30Days,
        ],
      ]),
    ),
  );
}

export function SalesActivityFilterOptionToSdk(filter?: SalesActivityFilterOption): LatestSalesActivityOption | null {
  switch (filter) {
    case SalesActivityFilterOption.AllTime: {
      return LatestSalesActivityOption.LATEST_SALES_ACTIVITY_OPTION_ALL_TIME;
    }
    case SalesActivityFilterOption.WithinTheLast30Days: {
      return LatestSalesActivityOption.LATEST_SALES_ACTIVITY_OPTION_WITHIN_THE_LAST_30_DAYS;
    }
    case SalesActivityFilterOption.OlderThan30Days: {
      return LatestSalesActivityOption.LATEST_SALES_ACTIVITY_OPTION_OLDER_THAN_30_DAYS;
    }
    default: {
      return assertNever(filter);
    }
  }
}

export interface ArchivedFilterValue {
  description: string;
  value: Partial<keyof typeof ArchivedFilter>;
}
