@use 'design-tokens' as *;
$toggle--transition-duration: 0.3s !default;

.toggle-input {
  display: none;
  &:checked + .toggle-label {
    background-color: $green;

    .toggle-slider {
      transform: translate(20px, 0);
    }
  }
  &:disabled + .toggle-label {
    background-color: $light-gray;
  }
}

.toggle-label {
  display: inline-block;
  width: 50px;
  height: 30px;
  padding: 1px;
  margin: 20px;
  border-radius: 15px;
  cursor: pointer;
  background-color: $red;
  transition: background-color $toggle--transition-duration ease-in-out;
}

.toggle-value-label {
  position: relative;
  bottom: 30px;
  display: inline;
  width: 75%;
  color: $red;
  &.toggle-checked {
    color: $green;
  }
  &.toggle-disabled {
    color: $light-gray;
  }
}

.toggle-slider {
  display: block;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: $white;
  transition: transform $toggle--transition-duration ease-in-out;
}
