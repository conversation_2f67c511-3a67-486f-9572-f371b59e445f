<!--<EXP__glxy-wrap>  TODO: Replace this with a non-experimental version that is compatible with Angular 10 -->
<mat-form-field id="link-to-account-selector" class="full-width" appearance="outline">
  <mat-label>{{ label }}</mat-label>
  <input type="text" matInput [formControl]="searchControl" [matAutocomplete]="auto" />
  <mat-autocomplete autoActiveFirstOption #auto="matAutocomplete">
    <mat-option
      *ngFor="let option of options$ | async"
      [value]="option.display || option.title"
      class="multiline-auto-complete-options"
      (onSelectionChange)="onSelect(option)"
    >
      <span class="title" *ngIf="option.title && getTextParts(option.title) as parts">
        {{ parts.start }}
        <span class="highlighted">{{ parts.highlighted }}</span>
        {{ parts.end }}
      </span>
      <br />
      <span class="subtitle" *ngIf="option.subtitle && getTextParts(option.subtitle) as parts">
        {{ parts.start }}
        <span class="highlighted">{{ parts.highlighted }}</span>
        {{ parts.end }}
      </span>
      <br />
    </mat-option>
  </mat-autocomplete>
  <mat-icon class="clear" (click)="clearSearch()" matSuffix>clear</mat-icon>
</mat-form-field>
<!--</EXP__glxy-wrap>-->
