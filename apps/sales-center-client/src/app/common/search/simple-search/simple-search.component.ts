import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Inject,
  InjectionToken,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl } from '@angular/forms';
import { Observable } from 'rxjs';
import { debounceTime, distinctUntilChanged, startWith, switchMap, tap } from 'rxjs/operators';

export interface SearchOption {
  id: string;
  title: string;
  subtitle?: string;
  // Used when displaying the value in the search box
  display?: string;
}

export interface SimpleSearchService {
  search(searchTerm: string): Observable<SearchOption[]>;
}

export const SIMPLE_SEARCH_SERVICE_TOKEN = new InjectionToken<SimpleSearchService>(
  '[GalaxySearch]: token for galaxy search',
);

@Component({
  selector: 'app-simple-search',
  templateUrl: './simple-search.component.html',
  styleUrls: ['./simple-search.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class SimpleSearchComponent implements OnInit {
  @Input() label: string;
  @Input() debounceTime = 250;
  // null is emitted when the field is cleared
  @Output() selected: EventEmitter<SearchOption | null> = new EventEmitter();

  searchControl: UntypedFormControl;

  options$: Observable<SearchOption[]>;
  constructor(
    @Inject(SIMPLE_SEARCH_SERVICE_TOKEN) private readonly searchService: SimpleSearchService,
    private readonly fb: UntypedFormBuilder,
    private readonly cd: ChangeDetectorRef,
  ) {
    this.searchControl = this.fb.control('');
  }

  ngOnInit(): void {
    const term$ = this.searchControl.valueChanges.pipe(
      startWith(''),
      tap(() => this.cd.markForCheck()),
      distinctUntilChanged(),
      debounceTime(this.debounceTime),
    );
    this.options$ = term$.pipe(switchMap((searchTerm) => this.searchService.search(searchTerm)));
  }

  onSelect(option: SearchOption): void {
    this.selected.emit(option);
  }

  clearSearch(): void {
    this.searchControl.setValue('');
    this.selected.emit(null);
  }

  getTextParts(title: string): { start: string; highlighted: string; end: string } {
    const term = (this.searchControl.value || '').toLowerCase();
    const titleCompare = (title || '').toLowerCase();
    if (!term || !title || titleCompare.indexOf(term) < 0) {
      return { start: title, highlighted: '', end: '' };
    }
    const indexOfTerm = titleCompare.indexOf(term);
    const start = title.slice(0, indexOfTerm) || '';
    const highlighted = title.slice(indexOfTerm, indexOfTerm + term.length);
    const end = title.slice(indexOfTerm + term.length, title.length);
    return { start, highlighted, end };
  }
}
