import { NgModule } from '@angular/core';
import { SimpleSearchModule } from '../simple-search/simple-search.module';
import { AccountGroupSearchComponent } from './account-group-search.component';
import { BusinessSearchService } from './business-search.service';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  imports: [SimpleSearchModule, TranslateModule],
  declarations: [AccountGroupSearchComponent],
  exports: [AccountGroupSearchComponent],
  providers: [BusinessSearchService],
})
export class AccountGroupSearchModule {}
