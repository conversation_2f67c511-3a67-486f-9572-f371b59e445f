import { Injectable } from '@angular/core';
import { SimpleSearchService, SearchOption } from '../simple-search/simple-search.component';
import { Observable } from 'rxjs';
import { BusinessService } from '../../../business/business.service';
import { ProjectionFilter } from '@galaxy/account-group';
import { map } from 'rxjs/operators';

@Injectable()
export class BusinessSearchService implements SimpleSearchService {
  constructor(private readonly businessService: BusinessService) {}

  search(searchTerm: string): Observable<SearchOption[]> {
    return this.businessService.search(new ProjectionFilter({ napData: true }), searchTerm, 25).pipe(
      map((businesses) => {
        return (businesses || []).map((b) => {
          const { companyName, city, zip, state, address } = b.napData;
          let subtitle = `${address ? address + ',' : ''} ${city ? city + ',' : ''} ${state ? state + ',' : ''} ${
            zip ? zip + ',' : ''
          }`.trim();
          if (subtitle[subtitle.length - 1] === ',') {
            subtitle = subtitle.slice(0, subtitle.length - 1);
          }
          return {
            id: b.accountGroupId,
            title: companyName,
            subtitle,
            display: companyName,
          };
        });
      }),
    );
  }
}
