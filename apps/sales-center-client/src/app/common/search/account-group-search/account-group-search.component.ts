import { Component, EventEmitter, Output } from '@angular/core';
import { SIMPLE_SEARCH_SERVICE_TOKEN, SearchOption } from '../simple-search/simple-search.component';
import { BusinessSearchService } from './business-search.service';
@Component({
  selector: 'app-account-group-search',
  templateUrl: './account-group-search.component.html',
  styleUrls: ['./account-group-search.component.scss'],
  providers: [{ provide: SIMPLE_SEARCH_SERVICE_TOKEN, useExisting: BusinessSearchService }],
  standalone: false,
})
export class AccountGroupSearchComponent {
  @Output() accountGroupSelected: EventEmitter<string | undefined> = new EventEmitter<string>();

  onSelected(option: SearchOption | null): void {
    this.accountGroupSelected.emit(option?.id);
  }
}
