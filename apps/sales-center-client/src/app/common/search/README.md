# Simple Search
This component is used to provide a generic style for searching through entities.

# Usage
SimpleSearch requires that you provide a service that meets the `SimpleSearchService` interface.
The service you inject here will be responsible for serving as an adapter between the entity / entities you are searching across and
the component itself.

Often, you will simply provide the service in a parent component like so:

```
@Component({
  selector: 'account-group-search',
  templateUrl: './account-group-search.component.html',
  styleUrls: ['./account-group-search.component.scss'],
  providers: [{provide: SIMPLE_SEARCH_SERVICE_TOKEN, useClass: BusinessSearchService}]
})
```

and use it in the parent's HTML:
```
<app-simple-search (selected)="onSelected($event)"></app-simple-search>
```

In the simple case, BusinessSearchService could then simply look something like this:

```
@Injectable()
export class BusinessSearchService implements SimpleSearchService {
  constructor(private readonly businessService: BusinessService) {}

  search(searchTerm: string): Observable<SearchOption[]> {
    return this.businessService.search(new ProjectionFilter({ napData: true }), searchTerm, 25).pipe(
      map((businesses) => {
        return (businesses || []).map((b) => {
          const { companyName, city, zip, state, address } = b.napData;
          const subtitle = `${address}, ${city}, ${state}, ${zip}`;
          return {
            id: b.accountGroupId,
            title: companyName,
            subtitle,
            display: `${companyName} - ${subtitle}`,
          };
        });
      }),
    );
  }
}
```

Note that the service only needs to expose a `search` function the `SimpleSearchComponent` calls.
However, you could add functionality here to suit your needs. For example, you may want it to use filters or other parameters available
on the page in its search.
