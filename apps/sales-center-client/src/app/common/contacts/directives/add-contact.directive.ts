import { Directive, HostListener, Input, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { AddContactComponent } from '../forms/add-contact.component';

@Directive({
  selector: '[appAddContact]',
  standalone: false,
})
export class AddContactDirective implements OnInit {
  @Input() businessId: string;

  constructor(private readonly dialog: MatDialog) {}

  ngOnInit(): void {
    if (!this.businessId) {
      throw new Error('Add Contact Directive: attribute [BusinessId] required');
    }
  }

  @HostListener('click', ['$event'])
  async openAddContactDialog(): Promise<void> {
    this.dialog.closeAll();
    this.dialog.open(AddContactComponent, {
      width: '540px',
      maxWidth: '100vw',
      data: { accountGroupId: this.businessId },
    });
  }
}
