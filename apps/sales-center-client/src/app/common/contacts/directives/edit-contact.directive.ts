import { Directive, HostListener, Inject, Input } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { PartnerMarket } from '@galaxy/types';
import { Observable, firstValueFrom } from 'rxjs';
import { EditContactComponent, EditContactDialogData } from '../forms/edit-contact.component';
import { USER_PARTNER_MARKET_TOKEN } from '../../../core/feature-flag.service';
import { Contact } from '../contact';

@Directive({
  selector: '[appEditContact]',
  standalone: false,
})
export class EditContactDirective {
  @Input() contactToEdit: Contact;
  @Input() businessId: string;

  constructor(
    private readonly dialog: MatDialog,
    @Inject(USER_PARTNER_MARKET_TOKEN) private readonly partnerMarketIds: Observable<PartnerMarket>,
  ) {}

  @HostListener('click', ['$event'])
  async openEditContactDialog(): Promise<void> {
    const partnerId = await firstValueFrom(this.partnerMarketIds).then((pm) => pm.partnerId);

    const editContactDialogData = <EditContactDialogData>{
      partnerId: partnerId,
      accountGroupId: this.businessId,
      contact: this.contactToEdit,
    };

    this.dialog.open(EditContactComponent, { width: '540px', maxWidth: '100vw', data: editContactDialogData });
  }
}
