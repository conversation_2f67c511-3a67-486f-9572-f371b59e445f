import { HttpClient, HttpParameterCodec, HttpParams } from '@angular/common/http';
import { Inject, Injectable, Optional } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ObservableWorkState, ObservableWorkStateMap } from '@vendasta/rx-utils/work-state';
import { EMPTY, Observable, ReplaySubject, SchedulerLike, forkJoin, of, throwError } from 'rxjs';
import { catchError, filter, map, shareReplay, switchMap, take, tap } from 'rxjs/operators';
import { USER_INFO_TOKEN } from '../../core/feature-flag.service';
import { LoggedInUserInfo } from '../../logged-in-user-info';
import { doWorkIfInitial } from '../../rx-utils/state-maps';
import { Contact } from './contact';

import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { BusinessServiceInterface } from '@vendasta/sales-v2';
import { BUSINESS_SDK_TOKEN } from '../providers/tokens';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import type { ContactsV2Service } from './contacts-v2.service';

export const GET_CONTACTS_FOR_BUSINESS = '/ajax/v1/contacts/';
export const ADD_CONTACT_FOR_BUSINESS = '/api/v1/contact/create/';
export const UPDATE_CONTACT_FOR_BUSINESS = '/internalApi/v2/contact/update/';
export const DELETE_CONTACT_FOR_BUSINESS = '/ajax/v1/contact/delete/';

interface UpdateResponse {
  data: {
    contact_id: string;
  };
}

interface DeleteResponse {
  accountGroupId: string;
  contactId: string;
  success: boolean;
}

interface DeleteRawResponse {
  data: string;
}

interface CreateResponse {
  data: {
    contactId: string;
    email: string;
    firstName: string;
    hasUser: boolean;
    lastName: string;
    name: string;
    notes: string;
    phoneExtension: string;
    phoneNumber: string;
    title: string;
  };
}

interface ContactEvent {
  accountGroupId: string;
  contact?: Contact;
  updateState?: boolean;
}

interface ContactsGetResponse {
  data: [
    {
      contactId: string;
      userId: string;
      unifiedUserId: string;
      email: string;
      firstName: string;
      hasUser: boolean;
      lastName: string;
      name: string;
      notes: string;
      phoneExtension: string;
      phoneNumber: string;
      phoneNumberCountry: string;
      title: string;
    },
  ];
}

export class UrlEncodingCodec implements HttpParameterCodec {
  encodeKey(key: string): string {
    return encodeURIComponent(key);
  }

  encodeValue(value: string): string {
    return encodeURIComponent(value);
  }

  decodeKey(key: string): string {
    return decodeURIComponent(key);
  }

  decodeValue(value: string): string {
    return decodeURIComponent(value);
  }
}

export interface ContactCardService {
  loadContacts(accountGroupId: string): void;

  isLoadingContacts$(accountGroupId: string): Observable<boolean>;

  isSuccessContacts$(accountGroupId: string): Observable<boolean>;

  contacts$(accountGroupId: string): Observable<Contact[] | null>;

  addContact(req: {
    operationId?: string;
    accountGroupId: string;
    firstName: string;
    lastName: string;
    phoneNumber: string;
    phoneExtension: string;
    email: string;
    title: string;
    notes: string;
    createUser: boolean;
    preferredLocale?: string;
    greetingName?: string;
  }): void;

  successfullyAddedContact$(): Observable<Contact>;

  isAddingContactSuccessEvent$(): Observable<boolean>;

  isEditingContactSuccessEvent$(): Observable<boolean>;

  isDeletingContactSuccessEvent$(): Observable<boolean>;

  /**
   * @deprecated: use contacts-v2 service {@link ContactsV2Service#update}.
   */
  editContact(
    accountGroupId: string,
    contactId: string,
    firstName: string,
    lastName: string,
    phone: string,
    phoneExtension: string,
    email: string,
    title: string,
    note: string,
    partnerId?: string,
    unifiedUserId?: string,
  ): void;

  /**
   * @deprecated: use contacts-v2 service {@link ContactsV2Service#delete}.
   */
  deleteContact(accountGroupId: string, contactId: string): void;
}

export interface AddMultiContactResult {
  // The list of contacts that you tried to add that were duplicates
  duplicateContacts: Contact[];
}

@Injectable({
  providedIn: 'root',
})
export class ContactsService implements ContactCardService {
  private readonly state = new ObservableWorkStateMap<string, Contact[]>();
  private readonly addingState = new ObservableWorkState<ContactEvent>();
  private readonly editingState = new ObservableWorkState<ContactEvent>();
  private readonly deletingState = new ObservableWorkState<DeleteResponse>();
  private readonly accountGroupId$$ = new ReplaySubject<string>(1);

  // the Contacts represent contacts that failed to be added
  private readonly multiAddState = new ObservableWorkStateMap<string, AddMultiContactResult>();
  private readonly primaryContactsState = new ObservableWorkStateMap<string, string>();
  readonly accountGroupId$ = this.accountGroupId$$.asObservable();

  constructor(
    private readonly http: HttpClient,
    @Inject(BUSINESS_SDK_TOKEN) private readonly businessService: BusinessServiceInterface,
    private readonly alertService: SnackbarService,
    @Inject(USER_INFO_TOKEN) private readonly loggedInUser: Observable<LoggedInUserInfo>,
    private readonly translate: TranslateService,
    @Optional() @Inject('Scheduler') private readonly scheduler?: SchedulerLike,
  ) {
    this.addingState.workResults$
      .pipe(
        // stop undefined results coming through
        filter(Boolean),
      )
      .subscribe((result: ContactEvent) => this.appendContact(result.accountGroupId, result.contact));
    this.editingState.workResults$.subscribe((result: ContactEvent) => {
      if (result.updateState) {
        this.state.startWork(result.accountGroupId, this.loadContacts(result.accountGroupId));
      }
    });
    this.deletingState.workResults$.subscribe((result: DeleteResponse) => {
      if (result.success) {
        this.removeContact(result.accountGroupId, result.contactId);
      }
    });
  }

  public removeContact(accountGroupId: string, contactId: string): void {
    const work = this.state.getWorkResults$(accountGroupId).pipe(
      map((contacts: Contact[]) => {
        return contacts.filter((contact) => contact.contactId !== contactId);
      }),
    );
    this.state.startWork(accountGroupId, work);
  }

  public deleteContact(accountGroupId: string, contactId: string): void {
    const params = new HttpParams().set('accountGroupId', accountGroupId).set('contactId', contactId);
    const work = this.http.post(DELETE_CONTACT_FOR_BUSINESS, params, { withCredentials: true }).pipe(
      map((resp: DeleteRawResponse) => {
        return <DeleteResponse>{
          accountGroupId: accountGroupId,
          contactId: contactId,
          success: resp.data === 'Success',
        };
      }),
    );
    this.deletingState.startWork(work);
  }

  isDeletingContactSuccessEvent$(): Observable<boolean> {
    return this.deletingState.successEvents$;
  }

  private appendContact(accountGroupId: string, c: Contact): void {
    const work = this.state.getWorkResults$(accountGroupId).pipe(
      map((contacts: Contact[]) => {
        let replaceIndex = -1;
        contacts.forEach((con, index) => {
          if (con.contactId === c.contactId) {
            replaceIndex = index;
          }
        });
        if (replaceIndex > -1) {
          contacts[replaceIndex] = c;
        } else {
          contacts.push(c);
        }
        return contacts;
      }),
    );
    this.state.startWork(accountGroupId, work);
  }

  public loadContacts(accountGroupId: string): Observable<Contact[]> {
    return this.getPrimaryContact(accountGroupId).pipe(
      catchError(() => of('')),
      switchMap((primaryContactId) => {
        return this.doLoadContacts(accountGroupId).pipe(
          take(1),
          map((contacts) =>
            contacts.sort((value) => {
              return value.contactId === primaryContactId ? -1 : 1;
            }),
          ),
        );
      }),
    );
  }

  private doLoadContacts(accountGroupId: string): Observable<Contact[]> {
    return this.loggedInUser.pipe(
      map((userInfo) =>
        new HttpParams()
          .set('accountGroupId', accountGroupId)
          .set('salesPersonId', userInfo.salespersonId)
          .set('marketId', userInfo.marketId)
          .set('partnerId', userInfo.partnerId),
      ),
      switchMap((params) => this.http.post(GET_CONTACTS_FOR_BUSINESS, params, { withCredentials: true })),
      map((response: ContactsGetResponse) =>
        response.data.map((contact) => new Contact(Object.assign(contact, { accountGroupId: accountGroupId }))),
      ),
    );
  }

  isLoadingContacts$(accountGroupId: string): Observable<boolean> {
    return this.state.isLoading$(accountGroupId);
  }

  isSuccessContacts$(accountGroupId: string): Observable<boolean> {
    return this.state.isSuccess$(accountGroupId);
  }

  contacts$(accountGroupId: string): Observable<Contact[] | null> {
    return doWorkIfInitial(
      accountGroupId,
      this.state,
      () => this.loadContacts(accountGroupId),
      this.scheduler || undefined,
    );
  }

  primaryContactId$(accountGroupId: string): Observable<string> {
    return this.primaryContactsState.getWorkResults$(accountGroupId);
  }

  setNewPrimaryContactAndUpdateContacts(newPrimaryContactId: string, accountGroupId: string): void {
    this.state
      .getWorkResults$(accountGroupId)
      .pipe(
        take(1),
        map((contacts) => {
          const updatedContacts = contacts.sort((value) => {
            return value.contactId === newPrimaryContactId ? -1 : 1;
          });

          this.state.startWork(accountGroupId, of(updatedContacts));
          this.primaryContactsState.startWork(accountGroupId, of(newPrimaryContactId));
        }),
      )
      .subscribe(() => {
        this.translate
          .stream('CONTACTS.CHANGE_PRIMARY_CONTACT_SUCCESS')
          .pipe(take(1))
          .subscribe((message) => {
            this.alertService.openSuccessSnack(message);
          });
      });
  }

  buildAddContactParams(req: {
    accountGroupId: string;
    firstName: string;
    lastName: string;
    phoneNumber: string;
    phoneExtension: string | number;
    email: string;
    title: string;
    notes: string;
    createUser: boolean;
    preferredLocale?: string;
    greetingName?: string;
  }): HttpParams {
    let params = new HttpParams({ encoder: new UrlEncodingCodec() })
      .set('accountGroupId', req.accountGroupId)
      .set('firstName', req.firstName)
      .set('lastName', req.lastName)
      .set('phoneNumber', req.phoneNumber)
      .set('phoneExtension', req.phoneExtension ? String(req.phoneExtension) : '')
      .set('email', req.email)
      .set('title', req.title)
      .set('notes', req.notes)
      .set('createUserFlag', req.createUser.toString());
    if (req.preferredLocale) {
      params = params.set('preferredLocale', req.preferredLocale);
    }
    if (req.greetingName) {
      params = params.set('greetingName', req.greetingName);
    }
    return params;
  }

  addContact(req: {
    accountGroupId: string;
    firstName: string;
    lastName: string;
    phoneNumber: string;
    phoneExtension: string;
    email: string;
    title: string;
    notes: string;
    createUser: boolean;
    preferredLocale?: string;
    greetingName?: string;
  }): void {
    const params = this.buildAddContactParams(req);

    const work = this.http.post<CreateResponse>(ADD_CONTACT_FOR_BUSINESS, params, { withCredentials: true }).pipe(
      map((response) => {
        return {
          accountGroupId: req.accountGroupId,
          contact: new Contact(Object.assign(response.data, { accountGroupId: req.accountGroupId })),
        };
      }),
      shareReplay(1),
    );
    this.addingState.startWork(work);
  }

  addMultiContact(operationId: string, accountGroupId: string, contacts: Contact[]): void {
    const calls$ = contacts.map((c) => {
      const params = this.buildAddContactParams({
        accountGroupId,
        firstName: c.firstName,
        lastName: c.lastName,
        phoneNumber: c.phoneNumber,
        phoneExtension: c.phoneExtension,
        email: c.email,
        title: c.title,
        notes: c.notes,
        createUser: false,
      });
      const work$ = this.http.post<CreateResponse>(ADD_CONTACT_FOR_BUSINESS, params, { withCredentials: true }).pipe(
        map((response) => {
          return {
            accountGroupId,
            duplicate: false,
            contact: new Contact(Object.assign(response.data, { accountGroupId: accountGroupId })),
          };
        }),
        catchError((err) => {
          if (err.status === 409) {
            return of({ accountGroupId: accountGroupId, contact: c, duplicate: true });
          }
          throwError(err);
        }),
        shareReplay(1),
      );
      // startWork so the contact is also shared via the current pattern with the addingState.
      // shareReplay(1) usage to avoid duplicate API calls.
      this.addingState.startWork(work$);
      return work$;
    });

    // of(null) used when no contacts were passed do the contactResults are updated
    const callsToJoin$ = calls$ && calls$.length > 0 ? calls$ : [of(null)];
    const allCalls$ = forkJoin(callsToJoin$).pipe(
      map((responses: { accountGroupId: string; contact: Contact; duplicate: boolean }[]) => {
        return {
          duplicateContacts: responses
            .filter(Boolean)
            .filter((ac) => ac.duplicate)
            .map((ac) => ac.contact)
            .filter(Boolean),
        };
      }),
    );

    this.multiAddState.startWork(operationId, allCalls$);
  }

  addMultiContactResults$(operationId: string): Observable<AddMultiContactResult> {
    return this.multiAddState.getWorkResults$(operationId);
  }

  isAddingContactSuccessEvent$(): Observable<boolean> {
    return this.addingState.successEvents$;
  }

  successfullyAddedContact$(): Observable<Contact> {
    return this.addingState.workResults$.pipe(map((ce) => ce.contact));
  }

  /**
   * @deprecated: use {@link ContactsV2Service#update}.
   */
  editContact(
    accountGroupId: string,
    contactId: string,
    firstName: string,
    lastName: string,
    phone: string,
    phoneExtension: string,
    email: string,
    title: string,
    note: string,
    partnerId?: string,
    unifiedUserId?: string,
  ): void {
    let params = new HttpParams({ encoder: new UrlEncodingCodec() })
      .set('contactId', contactId || '')
      .set('accountGroupId', accountGroupId || '')
      .set('firstName', firstName || '')
      .set('lastName', lastName || '')
      .set('phoneNumber', phone || '')
      .set('extension', phoneExtension || '')
      .set('email', email || '')
      .set('title', title || '')
      .set('notes', note || '')
      .set('overwriteBlankFlag', 'true');
    if (partnerId && unifiedUserId) {
      params = params.set('partnerId', partnerId).set('userId', unifiedUserId);
    }

    const work = this.http.post(UPDATE_CONTACT_FOR_BUSINESS, params, { withCredentials: true }).pipe(
      catchError((err) => {
        if (err.code === 400) {
          this.alertService.openErrorSnack(err.message);
          return EMPTY;
        } else {
          return throwError(err);
        }
      }),
      map((resp: UpdateResponse): ContactEvent => {
        if (resp.data.contact_id) {
          return {
            accountGroupId: accountGroupId,
            updateState: true,
          };
        } else {
          return {
            accountGroupId: accountGroupId,
            updateState: false,
          };
        }
      }),
    );
    this.editingState.startWork(work);
  }

  isEditingContactSuccessEvent$(): Observable<boolean> {
    return this.editingState.successEvents$;
  }

  setAccountGroupId(actGrp: string): void {
    this.accountGroupId$$.next(actGrp);
  }

  private getPrimaryContact(accountGroupId: string): Observable<string> {
    return this.loggedInUser.pipe(
      switchMap((userInfo) =>
        this.businessService.getPrimaryContact(accountGroupId, userInfo.partnerId, userInfo.marketId),
      ),
      tap((primaryContactId) => this.primaryContactsState.startWork(accountGroupId, of(primaryContactId))),
      shareReplay(1),
    );
  }

  setPrimaryContact(accountId: string, contactId: string): void {
    this.loggedInUser
      .pipe(
        switchMap((userInfo) => {
          return this.businessService.setPrimaryContact(contactId, accountId, userInfo.partnerId, userInfo.marketId);
        }),
        map(() => ''),
      )
      .subscribe({
        next: () => {
          this.setNewPrimaryContactAndUpdateContacts(contactId, accountId);
        },
        error: () => {
          this.translate
            .stream('CONTACTS.FAILED_TO_CHANGE_PRIMARY_CONTACT')
            .pipe(take(1))
            .subscribe((message) => {
              this.alertService.openErrorSnack(message);
            });
        },
      });
  }
}
