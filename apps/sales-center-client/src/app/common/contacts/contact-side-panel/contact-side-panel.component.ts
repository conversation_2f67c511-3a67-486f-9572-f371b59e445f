import { Component, Input } from '@angular/core';
import { Contact } from '../contact';

@Component({
  selector: 'app-contact-side-panel',
  template: `
    <sales-ui-slide-out-panel-header
      [title]="'CONTACTS.TITLE' | translate"
      icon="contacts"
    ></sales-ui-slide-out-panel-header>
    <app-contact-accordion [contacts]="contacts"></app-contact-accordion>
  `,
  standalone: false,
})
export class ContactSidePanelComponent {
  @Input() contacts: Contact[];
  @Input() accountGroupId: string;
}
