import { Contact } from './contact';

describe('Contact', () => {
  describe('hasUser', () => {
    // skipping more to document that even though the function says it returns a boolean to expect null or undefined
    it.skip('should be false if unset', () => {
      const c = new Contact({});
      expect(c.hasUser).toBe(false);
    });
    it('should be falsy if unset', () => {
      const c = new Contact({});
      expect(c.hasUser).toBeFalsy();
    });
    it('should be false if set to false', () => {
      const c = new Contact({ hasUser: false });
      expect(c.hasUser).toBe(false);
    });
    it('should be true if set to true', () => {
      const c = new Contact({ hasUser: true });
      expect(c.hasUser).toBe(true);
    });
  });

  describe('fullName', () => {
    describe('when firstName and lastName are truthy', () => {
      it('should return both first and last combined if name is truthy', () => {
        const c = new Contact({ firstName: '<PERSON>', lastName: '<PERSON>', name: '<PERSON>' });
        expect(c.fullName).toBe('<PERSON>');
      });
      it('should return both first and last combined if name is falsy', () => {
        const c = new Contact({ firstName: 'Jim', lastName: 'Johnston' });
        expect(c.fullName).toBe('Jim Johnston');
      });
    });

    describe('when firstName is truthy and lastName is falsy', () => {
      it('should return firstName with trimmed whitespace if name is truthy', () => {
        const c = new Contact({ firstName: 'Jim', name: 'Jimmy James' });
        expect(c.fullName).toBe('Jim');
      });
    });

    describe('when firstName is falsy and lastName is truthy', () => {
      it('should return lastName with trimmed whitespace if name is falsy', () => {
        const c = new Contact({ lastName: 'Johnston', name: 'Jimmy James' });
        expect(c.fullName).toBe('Johnston');
      });
    });

    describe('when firstName and lastName are falsy', () => {
      it('should return empty string if name is falsy', () => {
        const c = new Contact({ name: 'Jimmy James' });
        expect(c.fullName).toBe('Jimmy James');
      });
      it('should return name if name is truthy', () => {
        const c = new Contact({});
        expect(c.fullName).toBe('');
      });
    });
  });
});
