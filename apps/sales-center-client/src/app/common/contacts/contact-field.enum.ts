// eslint-disable-next-line @typescript-eslint/no-unused-vars
import type { Contact } from '@vendasta/sales-v2';

// Matches json keys on Contact proto
export enum ContactApiMaskField {
  CONTACT_ID = 'contact_id',
  EMAIL = 'contact_email',
  FIRST_NAME = 'first_name',
  LAST_NAME = 'last_name',
  NOTES = 'notes',
  PHONE_EXTENSION = 'phone_extension',
  PHONE_NUMBER = 'phone_number',
  TITLE = 'title',
  PHONE_COUNTRY = 'phone_number_country',
}

/**
 * ContactField values must match object fields of {@link Contact}
 * Matches contact object keys
 */
export enum ContactField {
  CONTACT_ID = 'contactId',
  EMAIL = 'contactEmail',
  FIRST_NAME = 'firstName',
  LAST_NAME = 'lastName',
  NOTES = 'notes',
  PHONE_EXTENSION = 'phoneExtension',
  PHONE_NUMBER = 'phoneNumber',
  TITLE = 'title',
  PHONE_COUNTRY = 'phoneNumberCountry',
}

export const AllContactFields: Readonly<ContactField[]> = Object.values(ContactField);

export const ContactApiMask: Record<ContactField, ContactApiMaskField> = {
  [ContactField.CONTACT_ID]: ContactApiMaskField.CONTACT_ID,
  [ContactField.EMAIL]: ContactApiMaskField.EMAIL,
  [ContactField.FIRST_NAME]: ContactApiMaskField.FIRST_NAME,
  [ContactField.LAST_NAME]: ContactApiMaskField.LAST_NAME,
  [ContactField.NOTES]: ContactApiMaskField.NOTES,
  [ContactField.PHONE_EXTENSION]: ContactApiMaskField.PHONE_EXTENSION,
  [ContactField.PHONE_NUMBER]: ContactApiMaskField.PHONE_NUMBER,
  [ContactField.TITLE]: ContactApiMaskField.TITLE,
  [ContactField.PHONE_COUNTRY]: ContactApiMaskField.PHONE_COUNTRY,
};
