import { TestBed } from '@angular/core/testing';
import { schedule } from '@vendasta/rx-utils';
import {
  Contact,
  ContactCreateAttributesInterface,
  ContactsService,
  ContactsServiceInterface,
  GetContactsForAccountsResult,
  ResendUserWelcomeEmailResult,
} from '@vendasta/sales-v2';
import { ContactInterface } from '@vendasta/sales-v2/lib/_internal';
import { EMPTY, Observable, combineLatest, firstValueFrom, of, throwError } from 'rxjs';
import { map } from 'rxjs/operators';
import { TestScheduler } from 'rxjs/testing';
import { USER_PARTNER_MARKET_TOKEN } from '../../core/feature-flag.service';
import { PartnerMarket } from '../../logged-in-user-info/logged-in-user-info.service';
import { UserCreateAttributes, UserResult, VBCUserService } from '../user.service';
import { AllContactFields, ContactApiMask, ContactField } from './contact-field.enum';
import { ContactShadow } from './contact-v2';
import { ContactsV2Service } from './contacts-v2.service';

const toBaseContacts = (list: Observable<ContactShadow[]>) =>
  list.pipe(map((contacts) => contacts.map((c) => c.baseContact)));

class MockApi implements ContactsServiceInterface {
  resendUserWelcomeEmail(): Observable<ResendUserWelcomeEmailResult> {
    throw new Error('Method not implemented.');
  }

  getContactsForAccounts(): Observable<GetContactsForAccountsResult> {
    throw new Error('Method not implemented.');
  }

  delete(): Observable<any> {
    throw new Error('Method not implemented.');
  }

  update(): Observable<any> {
    throw new Error('Method not implemented.');
  }
  create(): Observable<Contact> {
    throw new Error('Method not implemented.');
  }
  listContactsForBusiness(): Observable<ContactInterface[]> {
    throw new Error('Method not implemented.');
  }
}

class MockVBCUserService {
  getUsersForAccount(): Observable<any> {
    throw new Error('Method not implemented.');
  }

  createUser(): Observable<UserResult> {
    throw new Error('Method not implemented');
  }
}

const MID = 'default';
const PID = 'VUNI';
const AGID = 'AG-123';

describe('ContactsV2Service', () => {
  let service: ContactsV2Service;
  const api = new MockApi();
  const users = new MockVBCUserService();
  const updateSpy = jest.spyOn(api, 'update');
  const deleteSpy = jest.spyOn(api, 'delete');
  const createSpy = jest.spyOn(api, 'create');
  const listSpy = jest.spyOn(api, 'listContactsForBusiness');
  const getUsersSpy = jest.spyOn(users, 'getUsersForAccount');
  const createUserSpy = jest.spyOn(users, 'createUser');
  const resendSpy = jest.spyOn(api, 'resendUserWelcomeEmail');
  let scheduler: TestScheduler;

  beforeEach(() => {
    updateSpy.mockClear();
    listSpy.mockClear();
    deleteSpy.mockClear();
    createSpy.mockClear();
    getUsersSpy.mockClear();
    createUserSpy.mockClear();
    resendSpy.mockClear();
    TestBed.configureTestingModule({
      providers: [
        { provide: ContactsService, useValue: api },
        { provide: VBCUserService, useValue: users },
        { provide: USER_PARTNER_MARKET_TOKEN, useValue: of(new PartnerMarket(PID, MID)) },
      ],
    });
    service = TestBed.inject(ContactsV2Service);
    scheduler = new TestScheduler((actual, expected) => expect(actual).toEqual(expected));
  });

  afterEach(() => {
    scheduler.flush();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('update', () => {
    it('Creates correct mutator mask', () => {
      updateSpy.mockReturnValueOnce(EMPTY);

      const contact = new ContactShadow(new Contact(), AllContactFields);
      contact.firstName = 'a';
      contact.lastName = 'b';
      contact.title = 'c';
      contact.contactEmail = 'd@d.d';
      contact.phoneNumber = '1111111';
      contact.phoneExtension = '222';
      contact.notes = 'eeeeee';
      const expectedUpdatedFields = [
        ContactApiMask[ContactField.FIRST_NAME],
        ContactApiMask[ContactField.LAST_NAME],
        ContactApiMask[ContactField.TITLE],
        ContactApiMask[ContactField.EMAIL],
        ContactApiMask[ContactField.PHONE_NUMBER],
        ContactApiMask[ContactField.NOTES],
        ContactApiMask[ContactField.PHONE_EXTENSION],
      ];

      service.update$(contact).subscribe();

      const actualUpdatedFields = updateSpy.mock.calls[0][3].sort();
      expect(actualUpdatedFields).toEqual(expectedUpdatedFields.sort());
    });

    it('returns correctly updated contact', () => {
      updateSpy.mockReturnValueOnce(EMPTY);

      const expectedContact = new Contact({
        contactId: 'CO-2',
        firstName: 'f',
        lastName: 'g',
        title: 'h',
        contactEmail: 'i@i.i',
        phoneNumber: '3333333',
        phoneExtension: '444',
        notes: 'jjjjjjj',
      });

      const contact = new ContactShadow(new Contact({ contactId: 'CO-2' }), AllContactFields);
      contact.firstName = expectedContact.firstName;
      contact.lastName = expectedContact.lastName;
      contact.title = expectedContact.title;
      contact.contactEmail = expectedContact.contactEmail;
      contact.phoneNumber = expectedContact.phoneNumber;
      contact.phoneExtension = expectedContact.phoneExtension;
      contact.notes = expectedContact.notes;

      service.update$(contact).subscribe();

      const actualContact = updateSpy.mock.calls[0][2];
      expect(actualContact).toEqual(expectedContact);
    });

    it('updates the appropriate contact list', () => {
      const existingContact = new ContactShadow(
        new Contact({ contactId: 'CO-1', firstName: 'Chorly' }),
        [ContactField.CONTACT_ID, ContactField.FIRST_NAME],
        [AGID],
      );
      listSpy.mockImplementation(() => of([existingContact.baseContact]));
      updateSpy.mockImplementation(() => of(null));
      getUsersSpy.mockReturnValueOnce(of([]));

      const expectedContact = new ContactShadow(new Contact({ contactId: 'CO-1', firstName: 'Zesty' }), [
        ContactField.CONTACT_ID,
        ContactField.FIRST_NAME,
      ]);

      const contactList = service.list$(AGID, ContactField.CONTACT_ID, ContactField.FIRST_NAME);
      existingContact.firstName = 'Zesty';
      schedule(scheduler, '---|', () => service.update$(existingContact).subscribe());

      const baseContacts = contactList.pipe(toBaseContacts);

      scheduler.expectObservable(baseContacts).toBe('x--y', {
        x: [existingContact.baseContact],
        y: [expectedContact.baseContact],
      });
    });

    it('updates multiple contact lists', () => {
      const existingContact = new ContactShadow(
        new Contact({ contactId: 'CO-1', firstName: 'Chorly' }),
        [ContactField.CONTACT_ID, ContactField.FIRST_NAME],
        [AGID, 'ag-2'],
      );
      listSpy.mockImplementation(() => of([existingContact.baseContact]));
      updateSpy.mockImplementation(() => of(null));
      getUsersSpy.mockReturnValue(of([]));
      const expectedContact = new ContactShadow(new Contact({ contactId: 'CO-1', firstName: 'Zesty' }), [
        ContactField.CONTACT_ID,
        ContactField.FIRST_NAME,
      ]);

      const contactList = service.list$(AGID, ContactField.CONTACT_ID, ContactField.FIRST_NAME);
      const contactList2 = service.list$('ag-2', ContactField.CONTACT_ID, ContactField.FIRST_NAME);
      existingContact.firstName = 'Zesty';
      schedule(scheduler, '---|', () => service.update$(existingContact).subscribe());

      scheduler.expectObservable(contactList.pipe(toBaseContacts)).toBe('x--y', {
        x: [existingContact.baseContact],
        y: [expectedContact.baseContact],
      });
      scheduler.expectObservable(contactList2.pipe(toBaseContacts)).toBe('x--y', {
        x: [existingContact.baseContact],
        y: [expectedContact.baseContact],
      });
    });

    it('updated contact preserves readonly fields', () => {
      type ReadonlyFields = Pick<ContactShadow, 'accountGroupIds' | 'contactId' | 'userId'>;
      const mapToReadonlyFields = (contacts: ContactShadow[]): ReadonlyFields[] =>
        contacts.map((contact) => ({
          accountGroupIds: contact.accountGroupIds,
          contactId: contact.contactId,
          userId: contact.userId,
        }));
      const knownFields = [ContactField.CONTACT_ID, ContactField.FIRST_NAME, ContactField.EMAIL];
      const expectedAgids = [AGID];
      const expectedUserId = 'U-123';

      const existingContact = new ContactShadow(
        new Contact({ contactId: 'CO-1', firstName: 'Chorly', contactEmail: '<EMAIL>' }),
        knownFields,
        expectedAgids,
        expectedUserId,
      );
      listSpy.mockImplementation(() => of([existingContact.baseContact]));
      updateSpy.mockImplementation(() => of(null));
      getUsersSpy.mockReturnValue(of([{ email: '<EMAIL>', userId: expectedUserId }]));
      const expectedContact = new ContactShadow(
        new Contact({ contactId: 'CO-1', firstName: 'Zesty', contactEmail: '<EMAIL>' }),
        knownFields,
        expectedAgids,
        expectedUserId,
      );

      const contactList = service
        .list$(AGID, ContactField.CONTACT_ID, ContactField.FIRST_NAME, ContactField.EMAIL)
        .pipe(map((contacts): ReadonlyFields[] => mapToReadonlyFields(contacts)));
      existingContact.firstName = 'Zesty';
      schedule(scheduler, '---|', () => service.update$(existingContact).subscribe());

      scheduler.expectObservable(contactList).toBe('x--y', {
        x: mapToReadonlyFields([existingContact]),
        y: mapToReadonlyFields([expectedContact]),
      });
    });
  });

  describe('list', () => {
    it('performs only one list call when a burst of calls are made - if default caching enabled ', () => {
      const first = new Contact({ contactId: 'CO-1' });
      const second = new Contact({ contactId: 'CO-2' });
      const third = new Contact({ contactId: 'CO-3' });
      getUsersSpy.mockReturnValue(of([]));

      listSpy.mockImplementationOnce(() => of([first]));
      listSpy.mockImplementationOnce(() => of([second]));
      listSpy.mockImplementationOnce(() => of([third]));

      const actual = combineLatest([
        service.list$(AGID, ContactField.CONTACT_ID),
        service.list$(AGID, ContactField.CONTACT_ID),
        service.list$(AGID, ContactField.CONTACT_ID),
      ]);

      scheduler
        .expectObservable(actual.pipe(map((actual) => actual.map((c) => c.map((d) => d.baseContact)))))
        .toBe('x', {
          x: [[first], [first], [first]],
        });
    });
    it('returns the expected ContactList object with all loaded contacts', async () => {
      const contacts = [
        new Contact({ contactId: 'CO-1' }),
        new Contact({ contactId: 'CO-2' }),
        new Contact({ contactId: 'CO-3' }),
      ];
      listSpy.mockImplementationOnce(() => of(contacts));
      getUsersSpy.mockReturnValueOnce(of([]));

      const expectedContacts = contacts.map((contact) => new ContactShadow(contact, [ContactField.CONTACT_ID], [AGID]));

      return firstValueFrom(service.list$(AGID, ContactField.CONTACT_ID)).then((contacts: ContactShadow[]) => {
        expect(contacts).toEqual(expectedContacts);
      });
    });

    it('Returns ContactList if one exists, and updates it', () => {
      const expectedContacts = [new Contact({ contactId: 'CO-first' })];
      const expectedUpdatedContacts = [new Contact({ contactId: 'CO-second' })];
      listSpy
        .mockImplementationOnce(() => of(expectedContacts))
        .mockImplementationOnce(() => of(expectedUpdatedContacts));
      getUsersSpy.mockReturnValue(of([]));

      const actual = service.list$({ accountGroupId: AGID, fields: [] });

      schedule(scheduler, '----|', () => {
        service.list$(AGID).subscribe();
      });

      scheduler.expectObservable(actual.pipe(toBaseContacts)).toBe('x---y', {
        x: expectedContacts,
        y: expectedUpdatedContacts,
      });
    });

    it('should return the contacts with user ids for the account if they are users', (done) => {
      const mockContacts = <Contact[]>[
        new Contact({ contactEmail: '<EMAIL>', contactId: 'CO-123' }),
        new Contact({ contactEmail: '<EMAIL>', contactId: 'CO-124' }),
        new Contact({ contactEmail: '<EMAIL>', contactId: 'CO-125' }),
      ];
      const mockUsers = <UserResult[]>[
        { userId: 'U-123', email: '<EMAIL>' },
        { userId: 'U-124', email: '<EMAIL>' },
      ];

      listSpy.mockReturnValueOnce(of(mockContacts));
      getUsersSpy.mockReturnValueOnce(of(mockUsers));

      service.list$(AGID, ContactField.CONTACT_ID).subscribe((contact) => {
        expect(contact).toEqual([
          new ContactShadow(mockContacts[0], [ContactField.CONTACT_ID], [AGID], 'U-123'),
          new ContactShadow(mockContacts[1], [ContactField.CONTACT_ID], [AGID], 'U-124'),
          new ContactShadow(mockContacts[2], [ContactField.CONTACT_ID], [AGID]),
        ]);
        done();
      });
    });

    it('should return the contacts with user ids for the account if they are users and the emails match case insensitive', (done) => {
      const mockContacts = <Contact[]>[
        new Contact({ contactEmail: '<EMAIL>', contactId: 'CO-123' }),
        new Contact({ contactEmail: '<EMAIL>', contactId: 'CO-124' }),
        new Contact({ contactEmail: '<EMAIL>', contactId: 'CO-125' }),
      ];
      const mockUsers = <UserResult[]>[
        { userId: 'U-123', email: '<EMAIL>' },
        { userId: 'U-124', email: '<EMAIL>' },
      ];

      listSpy.mockReturnValueOnce(of(mockContacts));
      getUsersSpy.mockReturnValueOnce(of(mockUsers));

      service.list$(AGID, ContactField.CONTACT_ID).subscribe((contact) => {
        expect(contact).toEqual([
          new ContactShadow(mockContacts[0], [ContactField.CONTACT_ID], [AGID], 'U-123'),
          new ContactShadow(mockContacts[1], [ContactField.CONTACT_ID], [AGID], 'U-124'),
          new ContactShadow(mockContacts[2], [ContactField.CONTACT_ID], [AGID]),
        ]);
        done();
      });
    });

    it('should return the contacts with no user ids for the account if they are users and have falsey email values', (done) => {
      const mockContacts = <Contact[]>[
        new Contact({ contactEmail: '', contactId: 'CO-123' }),
        new Contact({ contactEmail: undefined, contactId: 'CO-124' }),
        new Contact({ contactEmail: '<EMAIL>', contactId: 'CO-125' }),
      ];
      const mockUsers = <UserResult[]>[
        { userId: 'U-123', email: '' },
        { userId: 'U-124', email: null },
        { userId: 'U-321', email: undefined },
        { userId: 'U-456', email: '<EMAIL>' },
      ];

      listSpy.mockReturnValueOnce(of(mockContacts));
      getUsersSpy.mockReturnValueOnce(of(mockUsers));

      service.list$(AGID, ContactField.CONTACT_ID).subscribe((contact) => {
        expect(contact).toEqual([
          new ContactShadow(mockContacts[0], [ContactField.CONTACT_ID], [AGID]),
          new ContactShadow(mockContacts[1], [ContactField.CONTACT_ID], [AGID]),
          new ContactShadow(mockContacts[2], [ContactField.CONTACT_ID], [AGID]),
        ]);
        done();
      });
    });

    it('should return an empty array if there are no contacts', (done) => {
      listSpy.mockReturnValueOnce(of([]));
      getUsersSpy.mockImplementation(() => throwError('getUsersForAccount should not have been called'));

      service
        .list$(AGID, ContactField.CONTACT_ID)
        .pipe(toBaseContacts)
        .subscribe((contacts) => {
          expect(contacts).toEqual([]);
          done();
        });
    });

    it('should return contacts even if none have users', (done) => {
      const mockContacts = <Contact[]>[
        new Contact({ contactEmail: '<EMAIL>', contactId: 'CO-123' }),
        new Contact({ contactEmail: '<EMAIL>', contactId: 'CO-124' }),
        new Contact({ contactEmail: '<EMAIL>', contactId: 'CO-125' }),
      ];

      listSpy.mockReturnValueOnce(of(mockContacts));
      getUsersSpy.mockReturnValueOnce(of([]));

      service.list$(AGID, ContactField.CONTACT_ID, ContactField.EMAIL).subscribe((contact) => {
        expect(contact).toEqual([
          new ContactShadow(mockContacts[0], [ContactField.CONTACT_ID, ContactField.EMAIL], [AGID]),
          new ContactShadow(mockContacts[1], [ContactField.CONTACT_ID, ContactField.EMAIL], [AGID]),
          new ContactShadow(mockContacts[2], [ContactField.CONTACT_ID, ContactField.EMAIL], [AGID]),
        ]);
        done();
      });
    });
  });

  describe('delete', () => {
    it('removes contact from related ContactLists', () => {
      const existingContact = new ContactShadow(
        new Contact({ contactId: 'CO-1', firstName: 'Chorly' }),
        [ContactField.CONTACT_ID, ContactField.FIRST_NAME],
        [AGID],
      );
      const contactToDelete = new ContactShadow(
        new Contact({ contactId: 'CO-2', firstName: 'Jemma' }),
        [ContactField.CONTACT_ID, ContactField.FIRST_NAME],
        [AGID],
      );
      listSpy.mockImplementation(() => of([existingContact.baseContact, contactToDelete.baseContact]));
      deleteSpy.mockImplementation(() => of(null));
      getUsersSpy.mockReturnValueOnce(of([]));

      const contactList = service.list$(AGID, ContactField.CONTACT_ID, ContactField.FIRST_NAME);
      existingContact.firstName = 'Zesty';
      schedule(scheduler, '---|', () => service.delete$(contactToDelete).subscribe());

      const baseContacts = contactList.pipe(toBaseContacts);

      scheduler.expectObservable(baseContacts).toBe('x--y', {
        x: [existingContact.baseContact, contactToDelete.baseContact],
        y: [existingContact.baseContact],
      });
    });

    it('completes the observable automatically', () => {
      const contactToDelete = new ContactShadow(
        new Contact({ contactId: 'CO-2' }),
        [ContactField.CONTACT_ID],
        [AGID, 'ag-2'],
      );
      deleteSpy.mockImplementation(() => of(null));
      listSpy.mockImplementation(() => of([new Contact({ contactId: 'CO-2' })]));
      listSpy.mockReturnValueOnce(of([]));

      service.list$(AGID);

      scheduler.expectObservable(service.delete$(contactToDelete)).toBe('(x|)', {
        x: undefined,
      });
    });

    it('does not delete contact if api returns error', () => {
      const existingContact = new ContactShadow(new Contact({ contactId: 'CO-2' }), [ContactField.CONTACT_ID], [AGID]);

      getUsersSpy.mockReturnValue(of([]));
      listSpy.mockImplementation(() => of([existingContact.baseContact]));

      const list = service.list$(AGID, ContactField.CONTACT_ID);

      deleteSpy.mockImplementation(() => {
        throw throwError('Contact not found');
      });

      schedule(scheduler, '---|', () => service.delete$(existingContact).subscribe());
      scheduler.expectObservable(list).toBe('x', { x: [existingContact] });
    });
  });

  describe('create', () => {
    it('should create a new contact and insert it into the ContactList', (done) => {
      listSpy.mockImplementation(() => of([]));
      const expectedContact = new Contact({
        contactId: 'CO-New',
        firstName: 'Bobby',
      });

      const contactAttributes = <ContactCreateAttributesInterface>{
        firstName: 'Bobby',
      };

      createSpy.mockImplementation(() => of(expectedContact));
      getUsersSpy.mockReturnValueOnce(of([]));
      listSpy.mockReturnValueOnce(of([]));

      service.create$(AGID, contactAttributes).subscribe((contact) => {
        expect(contact).toEqual(new ContactShadow(expectedContact, AllContactFields, [AGID]));
        expect(listSpy).toHaveBeenCalledTimes(1);
        done();
      });
    });
  });

  describe('upgradeContactToUser', () => {
    it('should create a new user and update the contact in the ContactList', () => {
      const expectedContact = new Contact({
        contactId: 'CO-New',
        firstName: 'Bobby',
        contactEmail: '<EMAIL>',
      });
      const oldContactShadow = new ContactShadow(expectedContact, AllContactFields, [AGID]);
      const expContactShadow = new ContactShadow(expectedContact, AllContactFields, [AGID], 'U-123');

      createUserSpy.mockReturnValueOnce(of(<UserResult>{ userId: 'U-123' }));
      getUsersSpy.mockReturnValueOnce(of([]));
      listSpy.mockReturnValue(of([expectedContact]));

      const userAttributes = <UserCreateAttributes>{
        accountGroupId: 'AG-123',
        sendWelcomeEmail: true,
      };

      schedule(scheduler, '---|', () => service.upgradeContactToUser$(oldContactShadow, userAttributes).subscribe());

      scheduler.expectObservable(service.list$(AGID, ...AllContactFields)).toBe('x--y', {
        x: [oldContactShadow],
        y: [expContactShadow],
      });
    });
  });

  describe('resendWelcomeEmailForContactUser', () => {
    it('should call the resend endpoint once and return a success result of true', (done) => {
      resendSpy.mockReturnValueOnce(of({ success: true }));
      service.resendWelcomeEmailForContactUser$('U-123').subscribe((r) => {
        expect(r).toEqual(true);
        expect(resendSpy).toBeCalledTimes(1);
        done();
      });
    });

    it('should call the resend endpoint once and return a success result of false', (done) => {
      resendSpy.mockReturnValueOnce(of({ success: false }));
      service.resendWelcomeEmailForContactUser$('U-123').subscribe((r) => {
        expect(r).toEqual(false);
        expect(resendSpy).toBeCalledTimes(1);
        done();
      });
    });

    it('should throw an error if the api call fails', (done) => {
      resendSpy.mockReturnValueOnce(throwError('an error'));
      service.resendWelcomeEmailForContactUser$('U-123').subscribe(
        () => {
          return;
        },
        (err) => {
          expect(err).toEqual('an error');
          expect(resendSpy).toBeCalledTimes(1);
          done();
        },
      );
    });
  });
});
