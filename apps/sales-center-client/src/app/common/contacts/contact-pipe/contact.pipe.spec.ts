import { Contact } from '../contact';
import { Contact as V2Contact } from '@vendasta/sales-v2';
import { AllContactFields } from '../contact-field.enum';
import { ContactShadow } from '../contact-v2';
import { ContactFullNamePipe, ContactFullPhoneNumberPipe } from './contact.pipe';

describe('fullName', () => {
  let fnp: ContactFullNamePipe;

  beforeEach(() => {
    fnp = new ContactFullNamePipe();
  });

  describe('when firstName and lastName are truthy', () => {
    it('should return both first and last combined if name is truthy', () => {
      const c = new Contact({ firstName: '<PERSON>', lastName: '<PERSON>', name: '<PERSON>' });
      expect(fnp.transform(c)).toBe('<PERSON>');
    });
    it('should return both first and last combined if name is falsy', () => {
      const c = new Contact({ firstName: '<PERSON>', lastName: '<PERSON>' });
      expect(fnp.transform(c)).toBe('<PERSON>');
    });
  });

  describe('when firstName is truthy and lastName is falsy', () => {
    it('should return firstName with trimmed whitespace if name is truthy', () => {
      const c = new Contact({ firstName: '<PERSON>', name: '<PERSON>' });
      expect(fnp.transform(c)).toBe('Jim');
    });
  });

  describe('when first<PERSON>ame is falsy and last<PERSON>ame is truthy', () => {
    it('should return lastName with trimmed whitespace if name is falsy', () => {
      const c = new Contact({ last<PERSON>ame: 'Johnston', name: 'Jimmy James' });
      expect(fnp.transform(c)).toBe('Johnston');
    });
  });

  describe('when firstName and lastName are falsy', () => {
    it('should return empty string if name is falsy', () => {
      const c = new Contact({ name: 'Jimmy James' });
      expect(fnp.transform(c)).toBe('Jimmy James');
    });
    it('should return name if name is truthy', () => {
      const c = new Contact({});
      expect(fnp.transform(c)).toBe('');
    });
  });
});

describe('fullContactPhoneNumber', () => {
  let fpnp: ContactFullPhoneNumberPipe;

  beforeEach(() => {
    fpnp = new ContactFullPhoneNumberPipe();
  });

  it('should return a parsed phone number object if number and country code exist and are valid', () => {
    const testContact = new ContactShadow(
      <V2Contact>{ contactId: 'CO-1', phoneNumber: '3067155002', phoneNumberCountry: 'CA' },
      AllContactFields,
      ['AG-123'],
    );
    const result = fpnp.transform(testContact);
    expect(result).toBeTruthy();
  });

  it('should return null if country code is missing', () => {
    const testContact = new ContactShadow(
      <V2Contact>{ contactId: 'CO-1', phoneNumber: '3067155002' },
      AllContactFields,
      ['AG-123'],
    );
    const result = fpnp.transform(testContact);
    expect(result).toBeNull();
  });

  it('should return null if phone number and country code are not valid combined', () => {
    const testContact = new ContactShadow(
      <V2Contact>{ contactId: 'CO-1', phoneNumber: '3067155002', phoneNumberCountry: 'AU' },
      AllContactFields,
      ['AG-123'],
    );
    const result = fpnp.transform(testContact);
    expect(result).toBeNull();
  });
});
