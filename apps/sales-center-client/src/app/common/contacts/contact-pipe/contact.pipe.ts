import { Pipe, PipeTransform } from '@angular/core';
import { CountryCode, Extension, PhoneNumber, isValidPhoneNumber, parsePhoneNumber } from 'libphonenumber-js';
import { Contact } from '../contact';
import { ContactShadow } from '../contact-v2';

@Pipe({
  name: 'contactFullName',
  standalone: false,
})
export class ContactFullNamePipe implements PipeTransform {
  transform(contact: Contact | ContactShadow): string {
    const noFirstLast = !contact.firstName && !contact.lastName;
    if (contact instanceof Contact) {
      if (noFirstLast && contact.name) {
        return contact.name;
      }
      if (noFirstLast && !contact.name) {
        return '';
      }
    }

    if (noFirstLast) return '';
    const firstName = contact.firstName ? contact.firstName : '';
    const lastName = contact.lastName ? contact.lastName : '';
    return [firstName, lastName].filter((v) => Boolean(v)).join(' ');
  }
}

@Pipe({
  name: 'contactFullPhoneNumber',
  standalone: false,
})
export class ContactFullPhoneNumberPipe implements PipeTransform {
  transform(contact: ContactShadow, skipExtension?: boolean): PhoneNumber {
    let parsedNumber: PhoneNumber;

    if (!contact.phoneNumber || !contact.phoneNumberCountry) return null;
    try {
      parsedNumber = parsePhoneNumber(contact.phoneNumber, contact.phoneNumberCountry as CountryCode);
    } catch {
      return null;
    }

    if (parsedNumber.nationalNumber !== contact.phoneNumber) {
      return null;
    }

    if (!isValidPhoneNumber(contact.phoneNumber, contact.phoneNumberCountry as CountryCode)) return null;

    if (!skipExtension) {
      parsedNumber.ext = contact.phoneExtension as Extension;
    }

    return parsedNumber;
  }
}

@Pipe({
  name: 'isContactPhoneFixable',
  standalone: false,
})
export class IsContactPhoneFixablePipe implements PipeTransform {
  transform(contact: ContactShadow): PhoneNumber {
    let parsedNumber: PhoneNumber;
    if (!contact.phoneNumber) return null;

    try {
      parsedNumber = parsePhoneNumber(contact.phoneNumber, contact.phoneNumberCountry as CountryCode);
    } catch (error) {
      return null;
    }
    parsedNumber.ext = contact.phoneExtension as Extension;

    if (!isValidPhoneNumber(contact.phoneNumber, contact.phoneNumberCountry as CountryCode)) return null;
    return parsedNumber;
  }
}
