import { Pipe, PipeTransform } from '@angular/core';
import { Contact } from './contact';
@Pipe({
  name: 'callNowContactDisplay',
  standalone: false,
})
export class CallNowContactDisplayPipe implements PipeTransform {
  transform(contact: Contact): string {
    const constructedName = ((contact.firstName || '') + ' ' + (contact.lastName || '')).trim();

    return contact?.name || constructedName || '-';
  }
}
