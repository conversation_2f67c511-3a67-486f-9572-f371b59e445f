import { Component, Inject, Injectable, ViewChild } from '@angular/core';
import { NgModel } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { CountryCode, Extension } from 'libphonenumber-js';
import { firstValueFrom, Observable } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import { USER_PARTNER_MARKET_TOKEN } from '../../../core/feature-flag.service';
import { PartnerMarket } from '../../../logged-in-user-info/logged-in-user-info.service';
import { PhoneInputValue } from '../../phone-number-fields/phone-input/phone-input.interface';
import { Contact } from '../contact';
import { AllContactFields, ContactField } from '../contact-field.enum';
import { ContactShadow } from '../contact-v2';
import { ContactsV2Service } from '../contacts-v2.service';

export interface EditContactDialogData {
  accountGroupId: string;
  partnerId?: string;
  contact: Contact | ContactShadow;
}

@Component({
  selector: 'app-edit-contact-dialog-modal',
  styleUrls: ['./contact-forms.component.scss'],
  templateUrl: './edit-contact.component.html',
  standalone: false,
})
@Injectable()
export class EditContactComponent {
  @ViewChild('phoneInput') private readonly phoneInput: NgModel;
  readonly fields = ContactField;
  readonly contactShadow: ContactShadow;
  readonly isModified: Observable<boolean>;
  phoneInputValue: PhoneInputValue;

  constructor(
    public dialogRef: MatDialogRef<EditContactComponent>,
    private readonly alertService: SnackbarService,
    @Inject(ContactsV2Service) private readonly contactsV2Service: ContactsV2Service,
    @Inject(MAT_DIALOG_DATA) private readonly data: EditContactDialogData,
    @Inject(USER_PARTNER_MARKET_TOKEN) readonly partnerMarket$: Observable<PartnerMarket>,
  ) {
    this.contactShadow =
      this.data.contact instanceof ContactShadow
        ? this.data.contact
        : new ContactShadow(this.data.contact.toV2Contact(), AllContactFields);

    this.phoneInputValue = {
      countryCode: (this.contactShadow.phoneNumberCountry as CountryCode) ?? 'US',
      extension: this.contactShadow.phoneExtension as Extension,
      phoneNumber: this.contactShadow.phoneNumber,
    };

    this.isModified = this.contactShadow.changed$.pipe(
      map(() => this.contactShadow.updates.size > 0),
      startWith(false),
    );

    // Automatically completed by MatDialog component
    this.dialogRef.backdropClick().subscribe(() => this.contactShadow.clearUpdates());
  }

  async submit(): Promise<void> {
    firstValueFrom(this.contactsV2Service.update$(this.contactShadow))
      .then(() => {
        this.alertService.openSuccessSnack('CONTACTS.EDIT_CONTACT_SUCCESS');
        this.close();
      })
      .catch(() => this.alertService.openErrorSnack('CONTACTS.EDIT_CONTACT_FAILURE'));
  }

  updateContactPhoneNumber(value: PhoneInputValue): void {
    if (value.phoneNumber) this.phoneInput.control.markAllAsTouched();
    this.contactShadow.phoneNumber = value.phoneNumber;
    this.contactShadow.phoneNumberCountry = value.countryCode;
    this.contactShadow.phoneExtension = value.extension as string;
  }

  close(): void {
    this.dialogRef.close();
  }
}
