<form [formGroup]="addContactForm" autocomplete="off" (submit)="submit()">
  <h2 mat-dialog-title>{{ 'CONTACTS.ADD_CONTACT_FORM_TITLE' | translate }}</h2>
  <mat-dialog-content>
    <p [innerHtml]="'FORMS.CREATE_CONTACT_MESSAGE' | translate"></p>
    <mat-slide-toggle class="create-user-toggle" #createUserToggle [formControlName]="CNTRL_NAMES.CREATE_USER">
      {{ 'CONTACTS.CREATE_USER_FIELD_LABEL' | translate }}
    </mat-slide-toggle>

    <ng-container *ngIf="createUserToggle.checked === true">
      <glxy-form-field *ngIf="createUserToggle.checked === true">
        <glxy-label>{{ 'CONTACTS.USER_GREETING_NAME_LABEL' | translate }}</glxy-label>
        <input matInput [formControlName]="CNTRL_NAMES.GREETING_NAME" maxlength="100" />
      </glxy-form-field>
    </ng-container>

    <glxy-form-row>
      <glxy-form-field bottomSpacing="none">
        <glxy-label>{{ 'FORMS.FIRST_NAME_PLACEHOLDER' | translate }}</glxy-label>
        <input #firstName matInput [formControlName]="CNTRL_NAMES.FIRST_NAME" maxlength="100" />
        <glxy-hint align="end">{{ 'FORMS.MAX_NUM_CHARACTERS' | translate : { max_number: 100 } }}</glxy-hint>
      </glxy-form-field>

      <glxy-form-field bottomSpacing="none">
        <glxy-label>{{ 'FORMS.LAST_NAME_PLACEHOLDER' | translate }}</glxy-label>
        <input #lastName matInput [formControlName]="CNTRL_NAMES.LAST_NAME" maxlength="100" />
        <glxy-hint align="end">{{ 'FORMS.MAX_NUM_CHARACTERS' | translate : { max_number: 100 } }}</glxy-hint>
      </glxy-form-field>
    </glxy-form-row>

    <glxy-form-field>
      <glxy-label>{{ 'CONTACTS.JOB_TITLE_LABEL' | translate }}</glxy-label>
      <input #title matInput [formControlName]="CNTRL_NAMES.TITLE" maxlength="100" />
    </glxy-form-field>

    <glxy-form-field *ngIf="createUserToggle.checked === true">
      <glxy-label>{{ 'CONTACTS.USER_EMAIL_LANGUAGE_LABEL' | translate }}</glxy-label>
      <mat-select [formControlName]="CNTRL_NAMES.PREFERRED_LOCALE">
        <mat-option *ngFor="let languageOption of languageOptions" [value]="languageOption.value">
          {{ languageOption.label }}
        </mat-option>
      </mat-select>
    </glxy-form-field>

    <glxy-form-field>
      <glxy-label>{{ 'FORMS.EMAIL_PLACEHOLDER' | translate }}</glxy-label>
      <input matInput [formControlName]="CNTRL_NAMES.EMAIL" type="email" />
      <glxy-error *ngIf="addContactForm?.controls?.email?.invalid === true">
        {{ 'FORMS.INVALID_EMAIL_WARNING' | translate }}
      </glxy-error>
    </glxy-form-field>

    <app-phone-number-fields [formControlName]="CNTRL_NAMES.PHONE_NUMBER"></app-phone-number-fields>

    <glxy-form-field>
      <glxy-label>{{ 'FORMS.NOTES_PLACEHOLDER' | translate }}</glxy-label>
      <textarea matInput [formControlName]="CNTRL_NAMES.NOTE"></textarea>
    </glxy-form-field>
  </mat-dialog-content>
  <mat-dialog-actions>
    <button mat-stroked-button mat-dialog-close="canceled">
      {{ 'COMMON.ACTION_LABELS.CANCEL' | translate }}
    </button>
    <button
      mat-flat-button
      color="primary"
      type="submit"
      [disabled]="addContactForm.invalid || addContactForm.pristine || loading"
    >
      <glxy-button-loading-indicator [isLoading]="loading">{{ actionLabel }}</glxy-button-loading-indicator>
    </button>
  </mat-dialog-actions>
</form>
