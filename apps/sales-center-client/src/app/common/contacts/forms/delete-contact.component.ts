import { Component, Inject, Injectable, OnD<PERSON>roy } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Subscription, firstValueFrom } from 'rxjs';
import { Contact } from '../contact';
import { AllContactFields } from '../contact-field.enum';
import { ContactShadow } from '../contact-v2';
import { ContactsV2Service } from '../contacts-v2.service';

export interface DeleteContactDialogData {
  accountGroupId: string;
  contact: Contact | ContactShadow;
}

@Component({
  selector: 'app-delete-contact',
  template: `
    <h1 mat-dialog-title>{{ 'CONTACTS.DELETE_DIALOG.TITLE' | translate }}</h1>
    <mat-dialog-content>
      <p [innerHtml]="'CONTACTS.DELETE_DIALOG.DESCRIPTION' | translate: { name: data.contact | contactFullName }"></p>
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button mat-stroked-button (click)="dialogRef.close()">
        {{ 'CONTACTS.DELETE_DIALOG.KEEP_CONTACT' | translate }}
      </button>
      <button mat-flat-button color="warn" (click)="deleteContact()">
        {{ 'CONTACTS.DELETE_CONTACT' | translate }}
      </button>
    </mat-dialog-actions>
  `,
  standalone: false,
})
@Injectable()
export class DeleteContactComponent implements OnDestroy {
  private readonly subscriptions: Subscription[] = [];
  constructor(
    @Inject(MAT_DIALOG_DATA) readonly data: DeleteContactDialogData,
    @Inject(ContactsV2Service) private readonly contactsService: ContactsV2Service,
    public dialogRef: MatDialogRef<DeleteContactComponent>,
    private readonly alertService: SnackbarService,
  ) {}

  deleteContact(): void {
    firstValueFrom(
      this.contactsService.delete$(
        this.data.contact instanceof Contact
          ? new ContactShadow(this.data.contact.toV2Contact(), AllContactFields, [this.data.accountGroupId])
          : this.data.contact,
      ),
    )
      .then(() => {
        this.alertService.openSuccessSnack('CONTACTS.DELETE_DIALOG.CONTACT_DELETED');
        this.dialogRef.close();
      })
      .catch(() => this.alertService.openErrorSnack('CONTACTS.DELETE_DIALOG.CANNOT_DELETE_CONTACT'));
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }
}
