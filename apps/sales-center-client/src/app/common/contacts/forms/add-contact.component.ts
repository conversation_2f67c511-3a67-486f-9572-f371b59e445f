import { AfterViewInit, Component, EventEmitter, Inject, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, UntypedFormControl, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { SubscriptionList } from '@vendasta/rx-utils';
import { ContactCreateAttributesInterface } from '@vendasta/sales-v2';
import { Extension } from 'libphonenumber-js';
import { Observable, firstValueFrom } from 'rxjs';
import { map, startWith, switchMap, tap } from 'rxjs/operators';
import { USER_PARTNER_MARKET_TOKEN } from '../../../core/feature-flag.service';
import { PartnerMarket } from '../../../logged-in-user-info/logged-in-user-info.service';
import { Configuration, PARTNER_CONFIG_TOKEN } from '../../../partner';
import { PhoneNumberControl } from '../../phone-number-fields/phone-input/phone-input.interface';
import { UserCreateAttributes } from '../../user.service';
import { ContactShadow } from '../contact-v2';
import {
  atLeastOneFieldEnteredValidator,
  userRequiredFieldsEnteredIfCreatingUserValidator,
} from '../contacts-form-helpers';
import { ContactsV2Service } from '../contacts-v2.service';

enum CNTRL_NAMES {
  FIRST_NAME = 'contactFirstName',
  LAST_NAME = 'contactLastName',
  TITLE = 'title',
  EMAIL = 'email',
  NOTE = 'note',
  CREATE_USER = 'createUser',
  PHONE_NUMBER = 'phoneNumber',
  PREFERRED_LOCALE = 'preferredLocale',
  GREETING_NAME = 'greetingName',
}

export type AddContactResult = 'success_contact_only' | 'success_contact_and_user' | 'canceled';

export interface AddContactDialogResult {
  result: AddContactResult;
  contact?: ContactShadow;
}

export interface AddContactDialogData {
  accountGroupId: string;
  actionLabel: string;
  forceUserCreation: boolean;
}

@Component({
  selector: 'app-add-contact-dialog-modal',
  styleUrls: ['./contact-forms.component.scss'],
  templateUrl: './add-contact.component.html',
  standalone: false,
})
export class AddContactComponent implements OnInit, OnDestroy, AfterViewInit {
  readonly addContactForm = new FormGroup({
    [CNTRL_NAMES.FIRST_NAME]: new FormControl<string>('', { validators: [Validators.max(100)] }),
    [CNTRL_NAMES.LAST_NAME]: new FormControl<string>('', { validators: [Validators.max(100)] }),
    [CNTRL_NAMES.TITLE]: new FormControl<string>('', { validators: [Validators.max(100)] }),
    [CNTRL_NAMES.EMAIL]: new FormControl<string>('', { validators: [Validators.email], nonNullable: true }),
    [CNTRL_NAMES.PHONE_NUMBER]: new PhoneNumberControl({
      countryCode: 'US',
      phoneNumber: '',
      extension: '' as Extension,
    }),
    [CNTRL_NAMES.NOTE]: new FormControl<string>(''),
    [CNTRL_NAMES.CREATE_USER]: new FormControl<boolean>(false, []),
    [CNTRL_NAMES.PREFERRED_LOCALE]: new FormControl<string>(''),
    [CNTRL_NAMES.GREETING_NAME]: new FormControl<string>('', { validators: [Validators.max(100)] }),
  });

  private readonly subscriptions = SubscriptionList.new();
  actionLabel: string;
  forceUserCreation: boolean;
  loading = false;

  readonly CNTRL_NAMES = CNTRL_NAMES;

  languageOptions: { value: string; label: string }[] = [
    { value: 'en', label: 'English' },
    { value: 'cs', label: 'Čeština' },
    { value: 'de', label: 'Deutsch' },
    { value: 'es-419', label: 'Español' },
    { value: 'fr', label: 'Français' },
    { value: 'fr-ca', label: 'Français (Canada)' },
    { value: 'it', label: 'Italiano' },
    { value: 'nl', label: 'Nederlands' },
    { value: 'pt-br', label: 'Português' },
    { value: 'ru', label: 'Русский' },
  ];

  @Output() errorMessage = new EventEmitter<string>();
  hasPermissionToCreateUsers$ = this.salesConfiguration$.pipe(
    map((config) => config.hasBusinessCenterAccess),
    tap((hasPermissionToCreateUser) => {
      if (!hasPermissionToCreateUser) {
        this.addContactForm.controls[CNTRL_NAMES.CREATE_USER].setValue(false);
      }
    }),
    startWith(false),
  );

  constructor(
    public dialogRef: MatDialogRef<AddContactComponent, AddContactDialogResult>,
    private readonly alertService: SnackbarService,
    private readonly contactsV2Service: ContactsV2Service,
    @Inject(MAT_DIALOG_DATA)
    private readonly data: AddContactDialogData,
    @Inject(PARTNER_CONFIG_TOKEN) private readonly salesConfiguration$: Observable<Configuration>,
    @Inject(USER_PARTNER_MARKET_TOKEN) private readonly partnerMarket$: Observable<PartnerMarket>,
    private readonly translate: TranslateService,
  ) {
    this.actionLabel = data.actionLabel || this.translate.instant('COMMON.ACTION_LABELS.ADD');
    this.forceUserCreation = data.forceUserCreation;
  }

  ngOnInit(): void {
    this.setupFormGroup();

    this.subscriptions.add(this.addContactForm.controls[CNTRL_NAMES.CREATE_USER].valueChanges, (value) => {
      if (value === true) {
        this.addContactForm.controls[CNTRL_NAMES.EMAIL].addValidators(Validators.required);
      } else {
        this.addContactForm.controls[CNTRL_NAMES.EMAIL].removeValidators(Validators.required);
      }
      this.addContactForm.controls[CNTRL_NAMES.EMAIL].updateValueAndValidity();
    });

    this.dialogRef
      .beforeClosed()
      .subscribe((result) => this.dialogRef.close(result ?? <AddContactDialogResult>{ result: 'canceled' }));
  }

  setupFormGroup(): void {
    if (this.forceUserCreation) {
      this.addContactForm.controls.createUser.setValue(this.forceUserCreation);
      this.addContactForm.controls.createUser.disable();
    }

    this.addContactForm.controls[CNTRL_NAMES.PREFERRED_LOCALE].setValue(this.languageOptions[0].value);

    firstValueFrom(this.hasPermissionToCreateUsers$).then((canCreateUser) => {
      if (this.forceUserCreation || canCreateUser) {
        this.addContactForm.controls[CNTRL_NAMES.CREATE_USER].disable();
      }
    });
  }

  ngAfterViewInit(): void {
    this.addContactForm.addValidators([
      atLeastOneFieldEnteredValidator([
        this.addContactForm.controls[CNTRL_NAMES.FIRST_NAME],
        this.addContactForm.controls[CNTRL_NAMES.LAST_NAME],
        this.addContactForm.controls[CNTRL_NAMES.EMAIL],
        this.addContactForm.controls.phoneNumber,
      ]),
      userRequiredFieldsEnteredIfCreatingUserValidator,
    ]);
  }

  async submit(): Promise<void> {
    this.loading = true;
    const formData = this.addContactForm.getRawValue();
    const phoneData = formData[CNTRL_NAMES.PHONE_NUMBER];

    const contactAttributes = <ContactCreateAttributesInterface>{
      title: formData[CNTRL_NAMES.TITLE],
      firstName: formData[CNTRL_NAMES.FIRST_NAME],
      lastName: formData[CNTRL_NAMES.LAST_NAME],
      email: formData[CNTRL_NAMES.EMAIL],
      notes: formData[CNTRL_NAMES.NOTE],
      phoneNumber: phoneData?.phoneNumber,
      phoneCountryCode: phoneData?.countryCode,
      phoneExtension: phoneData?.extension,
    };

    const userAttributes = <UserCreateAttributes>{
      locale: formData[CNTRL_NAMES.PREFERRED_LOCALE],
      greetingName: formData[CNTRL_NAMES.GREETING_NAME],
      sendWelcomeEmail: true,
      accountGroupId: this.data.accountGroupId,
    };

    this.doCreate(
      contactAttributes,
      formData[CNTRL_NAMES.CREATE_USER],
      formData[CNTRL_NAMES.CREATE_USER] ? userAttributes : null,
    );
  }

  private async doCreate(
    contactAttributes: ContactCreateAttributesInterface,
    createUser: boolean,
    userAttributes?: UserCreateAttributes,
  ): Promise<void> {
    const contact$ = this.partnerMarket$.pipe(
      switchMap(() => this.contactsV2Service.create$(this.data.accountGroupId, contactAttributes)),
    );

    return firstValueFrom(contact$)
      .then(
        (contact) => {
          if (createUser) {
            return this.doCreateUser(contact, userAttributes);
          }
          this.alertService.openSuccessSnack('CONTACTS.CREATE_SUCCESS');
          this.dialogRef.close({ result: 'success_contact_only', contact: contact });
        },
        () => {
          this.alertService.openErrorSnack('CONTACTS.CREATE_ERROR');
          this.dialogRef.close({ result: 'canceled' });
        },
      )
      .finally(() => {
        this.loading = false;
      });
  }

  private async doCreateUser(contact: ContactShadow, userAttributes: UserCreateAttributes): Promise<void> {
    return firstValueFrom(this.contactsV2Service.upgradeContactToUser$(contact, userAttributes))
      .then(
        (upgradedContact) => {
          this.alertService.openSuccessSnack('CONTACTS.CREATE_CONTACT_AND_USER_SUCCESS');
          this.dialogRef.close({ result: 'success_contact_and_user', contact: upgradedContact });
        },
        () => {
          this.alertService.openErrorSnack('CONTACTS.CREATE_USER_ERROR');
          this.dialogRef.close({ result: 'canceled' });
        },
      )
      .finally(() => {
        this.loading = false;
      });
  }

  get email(): UntypedFormControl {
    return this.addContactForm.controls[CNTRL_NAMES.EMAIL] as UntypedFormControl;
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }
}
