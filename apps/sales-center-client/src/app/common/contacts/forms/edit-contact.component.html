<form class="mat-typography contact-form" autocomplete="off" (ngSubmit)="submit()" #contactForm="ngForm">
  <h2 mat-dialog-title>{{ 'CONTACTS.EDIT_CONTACT_FORM_TITLE' | translate }}</h2>
  <mat-dialog-content>
    <glxy-form-row>
      <glxy-form-field bottomSpacing="none">
        <glxy-label>{{ 'FORMS.FIRST_NAME_PLACEHOLDER' | translate }}</glxy-label>
        <input matInput [(ngModel)]="contactShadow.firstName" [name]="fields.FIRST_NAME" maxlength="100" />
        <glxy-hint align="end">{{ 'FORMS.MAX_NUM_CHARACTERS' | translate : { max_number: 100 } }}</glxy-hint>
      </glxy-form-field>

      <glxy-form-field bottomSpacing="none">
        <glxy-label>{{ 'FORMS.LAST_NAME_PLACEHOLDER' | translate }}</glxy-label>
        <input matInput [(ngModel)]="contactShadow.lastName" [name]="fields.LAST_NAME" maxlength="100" />
        <glxy-hint align="end">{{ 'FORMS.MAX_NUM_CHARACTERS' | translate : { max_number: 100 } }}</glxy-hint>
      </glxy-form-field>
    </glxy-form-row>

    <glxy-form-field>
      <glxy-label>{{ 'CONTACTS.JOB_TITLE_LABEL' | translate }}</glxy-label>
      <input matInput [(ngModel)]="contactShadow.title" [name]="fields.TITLE" maxlength="100" />
    </glxy-form-field>

    <glxy-form-field>
      <glxy-label>{{ 'FORMS.EMAIL_PLACEHOLDER' | translate }}</glxy-label>
      <input matInput [(ngModel)]="contactShadow.contactEmail" email [name]="fields.EMAIL" #emailControl="ngModel" />
      <glxy-error *ngIf="emailControl.invalid && (emailControl.dirty || emailControl.touched)">
        {{ 'FORMS.INVALID_EMAIL_WARNING' | translate }}
      </glxy-error>
    </glxy-form-field>

    <app-phone-number-fields
      [name]="fields.PHONE_NUMBER"
      [(ngModel)]="phoneInputValue"
      (ngModelChange)="updateContactPhoneNumber($event)"
      appPhoneInputValidator
      appPhoneExtensionValidator
      #phoneInput="ngModel"
    ></app-phone-number-fields>

    <glxy-form-field>
      <glxy-label>{{ 'FORMS.NOTES_PLACEHOLDER' | translate }}</glxy-label>
      <textarea matInput [(ngModel)]="contactShadow.notes" [name]="fields.NOTES"></textarea>
    </glxy-form-field>
  </mat-dialog-content>
  <mat-dialog-actions align="end">
    <div>
      <button mat-button type="button" color="secondary" (click)="close()">
        {{ 'COMMON.ACTION_LABELS.CANCEL' | translate }}
      </button>
      <button
        mat-raised-button
        type="submit"
        color="primary"
        [disabled]="(isModified | async) === false || !contactForm.valid"
      >
        {{ 'COMMON.ACTION_LABELS.UPDATE' | translate }}
      </button>
    </div>
  </mat-dialog-actions>
</form>
