import { Component, Input } from '@angular/core';
import { ReplaySubject } from 'rxjs';
import { Contact } from '../contact';

@Component({
  selector: 'app-contact-accordion',
  templateUrl: './contact-accordion.component.html',
  styleUrls: ['./contact-accordion.component.scss'],
  standalone: false,
})
export class ContactAccordionComponent {
  private readonly contacts$$ = new ReplaySubject<Contact[]>(1);
  readonly contacts$ = this.contacts$$.asObservable();

  @Input() set contacts(contacts: Contact[]) {
    if (contacts.length > 0) {
      this.contacts$$.next(contacts);
    }
  }
}
