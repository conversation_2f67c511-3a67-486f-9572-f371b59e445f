<mat-expansion-panel
  (opened)="hideExpansionHeaderQuickActions()"
  (closed)="showExpansionHeaderQuickActions()"
  class="contact-row-container"
>
  <mat-expansion-panel-header class="expansion-header">
    <va-icon [diameter]="32" [iconName]="contact | contactFullName" class="name-icon align-baseline"></va-icon>
    <mat-panel-title class="align-baseline name">
      {{ (isEditMode$ | async) ? ('CONTACTS.EDIT_CONTACT_FORM_TITLE' | translate) : (contact | contactFullName) }}
    </mat-panel-title>
    <mat-panel-description
      *ngIf="isHeaderQuickActionsVisible && contact.phoneNumber"
      appClickToCallNow
      [callNowInfo]="{
        phoneNumber: contact.phoneNumber,
        extension: contact.phoneExtension
      }"
    >
      <span
        class="phone actions actionable"
        (click)="$event.stopPropagation()"
        appClickToCallNow
        [callNowInfo]="{
          phoneNumber: contact.phoneNumber,
          extension: contact.phoneExtension
        }"
      >
        {{ contact.phoneNumber }}
      </span>
    </mat-panel-description>
    <mat-panel-description *ngIf="isHeaderQuickActionsVisible && contact.email">
      <span
        class="email actions actionable"
        [copyToClipBoardAndAlert]="{ email: contact.email }"
        (click)="$event.stopPropagation()"
      >
        {{ contact.email }}
      </span>
    </mat-panel-description>
  </mat-expansion-panel-header>
  <ng-container *ngIf="isEditMode$ | async; else expandedDetails"></ng-container>
</mat-expansion-panel>

<ng-template #expandedDetails>
  <app-contact-expanded-details [contact]="contact"></app-contact-expanded-details>
  <div class="edit-button">
    <button mat-flat-button class="edit-icon" color="primary" (click)="enableEditMode()">
      <mat-icon>edit</mat-icon>
      Edit contact
    </button>
  </div>
</ng-template>
