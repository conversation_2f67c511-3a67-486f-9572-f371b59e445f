import { Component, Input } from '@angular/core';
import { ReplaySubject } from 'rxjs';
import { startWith } from 'rxjs/operators';
import { Contact } from '../contact';

@Component({
  selector: 'app-contact-expansion-row',
  templateUrl: './contact-expansion-row.component.html',
  styleUrls: ['./contact-expansion-row.component.scss', '../contact-common-styles.scss'],
  standalone: false,
})
export class ContactExpansionRowComponent {
  isHeaderQuickActionsVisible = true;

  @Input() contact: Contact;

  private readonly isEditMode$$: ReplaySubject<boolean> = new ReplaySubject<boolean>(1);
  readonly isEditMode$ = this.isEditMode$$.pipe(startWith(false));

  showExpansionHeaderQuickActions(): void {
    this.isHeaderQuickActionsVisible = true;
  }

  hideExpansionHeaderQuickActions(): void {
    this.isHeaderQuickActionsVisible = false;
  }

  enableEditMode(): void {
    this.isEditMode$$.next(true);
  }
}
