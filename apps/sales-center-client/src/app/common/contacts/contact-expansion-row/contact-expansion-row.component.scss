@use 'design-tokens' as *;

:host {
  display: block;

  .expansion-header {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
  }

  .phone {
    white-space: nowrap;
  }

  .email {
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .actions {
    font-size: $font-preset-5-size;
  }

  .name {
    width: max-content;
  }

  .name-icon {
    flex-shrink: 1;
    padding-right: $spacing-2;
    order: -1;
  }

  .edit-button {
    width: 100%;
    display: flex;
    justify-content: right;
  }

  mat-panel-description,
  mat-panel-title {
    align-items: center;
  }
}
