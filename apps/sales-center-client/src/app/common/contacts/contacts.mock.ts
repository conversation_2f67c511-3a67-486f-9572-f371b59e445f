export const mockGetContactsResponse = {
  data: [
    {
      phoneExtension: null,
      title: null,
      notes: null,
      hasUser: true,
      contactId: 'CO-0d6845c17d284dfc91ba29b18b340b96',
      phoneNumber: '111',
      email: '<EMAIL>',
      name: 'Test Person',
    },
    {
      phoneExtension: null,
      title: 'idk',
      notes: 'idk',
      hasUser: false,
      contactId: 'CO-2091f7cfb18a49db852c0406cfa74987',
      phoneNumber: null,
      email: '<EMAIL>',
      name: 'idk test',
    },
  ],
};

export const mockAddContactResponse = {
  data: {
    name: '<PERSON>',
    firstName: 'Jane',
    title: null,
    lastName: 'Doe',
    notes: null,
    contactId: 'CO-0b7850983edd4dbda49c39938ba66788',
    phoneNumber: null,
    email: null,
    phoneExtension: null,
  },
};
export const mockUpdateContactRawResponse = {
  log: [],
  version: '2.0',
  requestId: '5ceef52100ff0c6251ee65fbbf0001737e73616c6573746f6f6c2d64656d6f0001617069733a636f6e74696e756f757300010108',
  responseTime: 281,
  data: { contact_id: 'CO-a5d1247daf2e4e518399b0ecaf4fb9c5' },
  statusCode: 200,
};

export const mockUpdateContactResponse = mockUpdateContactRawResponse;

export const mockContactForAddContactApi = {
  accountGroupId: 'AG-123',
  firstName: 'mock_first_name',
  lastName: 'mock_last_name',
  phoneNumber: '**********',
  phoneExtension: null,
  email: '<EMAIL>',
  title: null,
  notes: 'mock_notes',
};
