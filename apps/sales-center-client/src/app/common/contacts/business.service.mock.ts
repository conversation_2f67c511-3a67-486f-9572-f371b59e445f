/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  AccountLatestSalesActivity,
  BusinessSearchResult,
  BusinessServiceInterface,
  ListAccountsPagedRequestOptions,
  ListAccountsResult,
  PagedRequestOptions,
  Sort,
  SortDirection,
} from '@vendasta/sales-v2';
import { Observable, of } from 'rxjs';

export class MockBusinessService implements BusinessServiceInterface {
  listAccounts(
    fieldMask: string[],
    partner: string,
    market: string,
    pagingOptions: PagedRequestOptions,
    searchTerm: string,
    indexedPagingOptions: ListAccountsPagedRequestOptions,
  ): Observable<ListAccountsResult> {
    return of(<ListAccountsResult>{});
  }

  getLatestSalesActivityForAccounts(
    partnerId: string,
    marketId: string,
    accountIds: string[],
  ): Observable<AccountLatestSalesActivity[]> {
    return of([]);
  }

  getPrimaryContact(accountId: string, partnerId: string, marketId: string): Observable<string> {
    return of('');
  }
  setPrimaryContact(contactId: string, accountId: string, partnerId: string, marketId: string): Observable<null> {
    return of(null);
  }

  search(
    partner: string,
    market: string,
    sort: Sort,
    direction: SortDirection,
    pageSize: number,
  ): Observable<BusinessSearchResult[]> {
    return of([]);
  }

  searchBusinessesByContactEmail(
    partner: string,
    market: string,
    contactEmailQuery: string,
  ): Observable<BusinessSearchResult[]> {
    return of([]);
  }
}
