import { Inject, Injectable, OnDestroy } from '@angular/core';
import { PartnerMarket } from '@galaxy/types';
import { BusinessService } from '@vendasta/sales-v2';
import { add } from 'date-fns';
import { BehaviorSubject, EMPTY, Observable, ReplaySubject, Subscription, combineLatest, of } from 'rxjs';
import { catchError, filter, map, switchMap, take } from 'rxjs/operators';
import { USER_PARTNER_MARKET_TOKEN } from '../../core/feature-flag.service';

export const CACHE_TIME_SEC = 30;

interface PrimaryContactCached {
  primaryContactId$: BehaviorSubject<string>;
  expiry: Date;
}

@Injectable({
  providedIn: 'root',
})
export class PrimaryContactService implements OnDestroy {
  private readonly primaryContactMap = new Map<string, PrimaryContactCached>();

  private readonly getPrimaryContact$$ = new ReplaySubject<string>();
  private triggerSubscription: Subscription;

  constructor(
    private readonly businessService: BusinessService,
    @Inject(USER_PARTNER_MARKET_TOKEN) private readonly partnerMarket$: Observable<PartnerMarket>,
  ) {
    this.triggerSubscription = this.getPrimaryContact$$
      .pipe(
        filter((curr) => this.shouldDoLookup(curr, this.isExpired(curr))),
        switchMap((accountGroupId) => combineLatest([of(accountGroupId), this.partnerMarket$])),
        switchMap(([agid, pm]) => {
          const call$ = this.businessService
            .getPrimaryContact(agid, pm.partnerId, pm.marketId)
            .pipe(catchError(() => EMPTY));
          return combineLatest([call$, of(agid)]);
        }),
      )
      .subscribe(([primaryContactId, accountGroupId]) => {
        this.setOrUpdateId(primaryContactId, accountGroupId);
      });
  }

  ngOnDestroy(): void {
    this.triggerSubscription.unsubscribe();
  }

  setPrimaryContactForAccount$(contactId: string, accountGroupId: string): Observable<void> {
    return this.partnerMarket$.pipe(
      take(1),
      switchMap((pm) => this.businessService.setPrimaryContact(contactId, accountGroupId, pm.partnerId, pm.marketId)),
      map(() => this.setOrUpdateId(contactId, accountGroupId)),
    );
  }

  getPrimaryContactForAccount$(accountGroupId: string): Observable<string> {
    if (!this.primaryContactMap.has(accountGroupId)) {
      this.setOrUpdateId(null, accountGroupId);
    }

    this.getPrimaryContact$$.next(accountGroupId);
    return this.primaryContactMap.get(accountGroupId).primaryContactId$;
  }

  private setOrUpdateId(primaryContactId: string, accountGroupId: string): void {
    if (this.primaryContactMap.has(accountGroupId)) {
      this.primaryContactMap.get(accountGroupId).primaryContactId$.next(primaryContactId);
      this.primaryContactMap.get(accountGroupId).expiry = this.getNewExpiryDate();
    } else {
      const obj = <PrimaryContactCached>{
        primaryContactId$: new BehaviorSubject<string>(primaryContactId),
        expiry: primaryContactId ? this.getNewExpiryDate() : new Date(0),
      };
      this.primaryContactMap.set(accountGroupId, obj);
    }
  }

  private isExpired(accountGroupId: string): boolean {
    if (!this.primaryContactMap.has(accountGroupId)) return false;

    const expiryDate = this.primaryContactMap.get(accountGroupId).expiry;
    const curDate = new Date();

    return expiryDate <= curDate;
  }

  private getNewExpiryDate(): Date {
    return add(new Date(), { seconds: CACHE_TIME_SEC });
  }

  private shouldDoLookup(agid: string, currAgidExpired: boolean): boolean {
    if (currAgidExpired) {
      this.primaryContactMap.get(agid).expiry = this.getNewExpiryDate();
      return true;
    }
    return false;
  }
}
