import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { ReplaySubject, Subscription } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { Contact } from '@vendasta/sales-v2';
import { ContactShadow } from '../contact-v2';
import { AllContactFields } from '../contact-field.enum';

@Component({
  selector: 'app-contact-notes',
  templateUrl: './contact-notes.component.html',
  styleUrls: ['./contact-notes.component.scss', '../contact-common-styles.scss'],
  standalone: false,
})
export class ContactNotesComponent implements OnInit, OnDestroy {
  @Input() contact: Contact;

  private readonly isEditMode$$ = new ReplaySubject<boolean>(1);
  readonly isEditMode$ = this.isEditMode$$.asObservable();

  private changesSubscription: Subscription;

  formContact: ContactShadow;

  ngOnInit(): void {
    this.formContact = new ContactShadow(this.contact, AllContactFields);
    this.formContact.changed$.pipe(debounceTime(2000)).subscribe(() => this.saveNotes());
    this.disableEditMode();
  }

  ngOnDestroy(): void {
    this.changesSubscription.unsubscribe();
  }

  disableEditMode(): void {
    this.isEditMode$$.next(false);
  }

  enableEditMode(): void {
    this.isEditMode$$.next(true);
  }

  saveNotes(): void {
    return;
  }
}
