<div class="title-container">
  <div class="title-container">
    <mat-icon>notes</mat-icon>
    {{ 'COMMON.NOTES_LABEL' | translate }}
  </div>
  <div>
    <mat-slide-toggle (change)="$event.checked ? enableEditMode() : disableEditMode()">
      {{ 'COMMON.ACTION_LABELS.EDIT' | translate }}
    </mat-slide-toggle>
  </div>
</div>
<mat-divider></mat-divider>
<mat-form-field appearance="outline" (focusout)="saveNotes()">
  <textarea
    matInput
    #textArea
    appVoiceTextArea
    [htmlTextAreaElement]="textArea"
    [showIcon]="isEditMode$ | async"
    rows="6"
    name="notes"
    [(ngModel)]="formContact.notes"
    [disabled]="(isEditMode$ | async) === false"
  ></textarea>
</mat-form-field>
