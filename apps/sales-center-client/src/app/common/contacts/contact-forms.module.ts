import { ClipboardModule } from '@angular/cdk/clipboard';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialogModule } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyButtonLoadingIndicatorModule } from '@vendasta/galaxy/button-loading-indicator';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { SlideOutPanelModule } from '@vendasta/sales-ui';
import { VaBadgeModule, VaIconModule } from '@vendasta/uikit';
import { AsteriskService } from '../../astbe_sdk';
import { AsteriskModule } from '../../asterisk/asterisk.module';
import { VbcSdkModule } from '../../vbc_sdk/vbc-sdk.module';
import { CopyEmailToClipBoardAndAlertModule } from '../copy-to-clip-board-and-alert/copy-to-clip-board-and-alert.module';
import { PhoneNumberFieldsModule } from '../phone-number-fields/phone-number-fields.module';
import { VBCUserService } from '../user.service';
import { VoiceRecognitionModule } from '../voice-recognition/voice-recognition.module';
import { CallNowContactDisplayPipe } from './call-now-contact-display.pipe';
import { ContactAccordionComponent } from './contact-accordion/contact-accordion.component';
import { ContactExpandedDetailsComponent } from './contact-expanded-details/contact-expanded-details.component';
import { ContactExpansionRowComponent } from './contact-expansion-row/contact-expansion-row.component';
import { ContactNotesComponent } from './contact-notes/contact-notes.component';
import { ContactPipeModule } from './contact-pipe/contact-pipe.module';
import { ContactSelectorDialogComponent } from './contact-selector-dialog/contact-selector-dialog.component';
import { ContactSidePanelComponent } from './contact-side-panel/contact-side-panel.component';
import { AddContactDirective } from './directives/add-contact.directive';
import { EditContactDirective } from './directives/edit-contact.directive';
import { AddContactComponent } from './forms/add-contact.component';
import { DeleteContactComponent } from './forms/delete-contact.component';
import { EditContactComponent } from './forms/edit-contact.component';

@NgModule({
  declarations: [
    AddContactComponent,
    EditContactComponent,
    DeleteContactComponent,
    ContactSelectorDialogComponent,
    ContactAccordionComponent,
    ContactSidePanelComponent,
    CallNowContactDisplayPipe,
    EditContactDirective,
    AddContactDirective,
    ContactExpansionRowComponent,
    ContactExpandedDetailsComponent,
    ContactNotesComponent,
  ],
  exports: [
    AddContactComponent,
    EditContactComponent,
    DeleteContactComponent,
    ContactSidePanelComponent,
    EditContactDirective,
    AddContactDirective,
  ],
  imports: [
    CommonModule,
    MatButtonModule,
    MatCheckboxModule,
    MatDialogModule,
    MatInputModule,
    MatTooltipModule,
    ReactiveFormsModule,
    MatCardModule,
    MatIconModule,
    FormsModule,
    MatMenuModule,
    MatSelectModule,
    MatChipsModule,
    MatListModule,
    MatFormFieldModule,
    MatTableModule,
    TranslateModule,
    VaBadgeModule,
    VaIconModule,
    AsteriskModule,
    ClipboardModule,
    SlideOutPanelModule,
    MatProgressSpinnerModule,
    MatExpansionModule,
    MatSlideToggleModule,
    CopyEmailToClipBoardAndAlertModule,
    VoiceRecognitionModule,
    GalaxyButtonLoadingIndicatorModule,
    VbcSdkModule,
    GalaxyPipesModule,
    ContactPipeModule,
    PhoneNumberFieldsModule,
    GalaxyFormFieldModule,
  ],
  providers: [AsteriskService, VBCUserService],
})
export class ContactFormsModule {}
