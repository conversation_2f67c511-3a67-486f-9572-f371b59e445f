import { CallNowContactDisplayPipe } from './call-now-contact-display.pipe';
import { Contact } from './contact';

describe('CallNowContactDisplayPipe', () => {
  it('returns hyphen if no name fields defined', () => {
    const pipe = new CallNowContactDisplayPipe();

    const expected = pipe.transform(<Contact>{});

    expect(expected).toEqual('-');
  });

  it('returns display name if displayName defined', () => {
    const m = new Contact({ name: 'carl' });
    const pipe = new CallNowContactDisplayPipe();

    const expected = 'carl';

    expect(pipe.transform(m)).toEqual(expected);
  });

  it('returns first name if only firstname is set', () => {
    const m = new Contact({ firstName: 'carl' });
    const pipe = new CallNowContactDisplayPipe();

    const expected = 'carl';

    expect(pipe.transform(m)).toEqual(expected);
  });
  it('returns last name if only lastname is set', () => {
    const m = new Contact({ lastName: 'banks' });
    const pipe = new CallNowContactDisplayPipe();

    const expected = 'banks';

    expect(pipe.transform(m)).toEqual(expected);
  });
  it('returns first + last name if both set', () => {
    const m = new Contact({ firstName: 'carl', lastName: 'banks' });
    const pipe = new CallNowContactDisplayPipe();

    const expected = 'carl banks';

    expect(pipe.transform(m)).toEqual(expected);
  });
});
