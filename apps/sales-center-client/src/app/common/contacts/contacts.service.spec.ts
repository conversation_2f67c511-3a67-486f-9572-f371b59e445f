import { HttpParams } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { TestScheduler } from 'rxjs/testing';
import {
  mockAddContactResponse,
  mockContactForAddContactApi,
  mockGetContactsResponse,
  mockUpdateContactResponse,
} from './contacts.mock';
import {
  ADD_CONTACT_FOR_BUSINESS,
  ContactsService,
  UPDATE_CONTACT_FOR_BUSINESS,
  DELETE_CONTACT_FOR_BUSINESS,
  GET_CONTACTS_FOR_BUSINESS,
  UrlEncodingCodec,
} from './contacts.service';
import { schedule } from '@vendasta/rx-utils';
import { Contact } from './contact';
import { map } from 'rxjs/operators';
import { MockBusinessService } from './business.service.mock';

let scheduler: TestScheduler;

const loggedInUserInfo = of({
  partnerId: 'PID-123',
  marketId: 'MKT-123',
  salesPersonId: 'SP-123',
});

class MockHttpClient {
  failUpdateCall = false;
  params: HttpParams;

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  post(url: string, params: any, options?: HttpParams): Observable<any> {
    this.params = params;

    switch (url) {
      case ADD_CONTACT_FOR_BUSINESS:
        return of(mockAddContactResponse);
      case DELETE_CONTACT_FOR_BUSINESS: {
        const response = { data: 'Success' };
        return of(response);
      }
      case UPDATE_CONTACT_FOR_BUSINESS:
        if (this.failUpdateCall) {
          return throwError('bad update');
        } else {
          return of(mockUpdateContactResponse);
        }
      case GET_CONTACTS_FOR_BUSINESS:
        return of(mockGetContactsResponse);
      default:
        return throwError(`unknown api called, implement api mock.  Route used was ${url}`);
    }
  }
}

describe('ContactsService', () => {
  beforeEach(() => {
    scheduler = new TestScheduler((a, b) => expect(a).toEqual(b));
  });

  afterEach(() => {
    scheduler.flush();
  });

  describe('buildAddContactParams', () => {
    test('Should return the same information that was sent with proper encoding', () => {
      const mockBusinessService = new MockBusinessService();
      const service = new ContactsService(
        new MockHttpClient() as any,
        mockBusinessService,
        null,
        loggedInUserInfo as any,
        new MockHttpClient() as any,
      );
      const contact = mockContactForAddContactApi;
      const response = service.buildAddContactParams({
        accountGroupId: contact.accountGroupId,
        firstName: contact.firstName,
        lastName: contact.lastName,
        phoneNumber: contact.phoneNumber,
        phoneExtension: contact.phoneExtension,
        email: contact.email,
        title: contact.title,
        notes: contact.notes,
        createUser: true,
      });
      expect(response.toString()).toEqual(
        'accountGroupId=AG-123' +
          '&firstName=mock_first_name' +
          '&lastName=mock_last_name' +
          '&phoneNumber=**********' +
          '&phoneExtension=' +
          '&email=test%2Bemail_123%40testemail.com' +
          '&title=null' +
          '&notes=mock_notes' +
          '&createUserFlag=true',
      );
    });
  });

  describe('contacts$', () => {
    test('Should load all contacts by account group id', () => {
      const accountGroupId = 'AG-123';
      const mockBusinessService = new MockBusinessService();
      const service = new ContactsService(
        new MockHttpClient() as any,
        mockBusinessService,
        null,
        loggedInUserInfo as any,
        null,
        scheduler,
      );

      const actual$ = service.contacts$(accountGroupId);

      const expected = mockGetContactsResponse.data.map(
        (c) => new Contact(Object.assign(c, { accountGroupId: accountGroupId })),
      );
      scheduler.expectObservable(actual$).toBe('-x', { x: expected });
    });
  });

  describe('addContact on success', () => {
    let service: ContactsService;
    beforeEach(() => {
      const mockBusinessService = new MockBusinessService();
      service = new ContactsService(
        new MockHttpClient() as any,
        mockBusinessService,
        null,
        loggedInUserInfo as any,
        null,
        scheduler,
      );
    });
    it('should load contacts$ again but not emit if contact is not different', () => {
      const accountGroupId = 'AG-123';
      const actual$ = service.contacts$(accountGroupId);
      schedule(scheduler, '-|', () =>
        service.addContact({
          accountGroupId,
          firstName: 'John',
          lastName: 'Wick',
          phoneNumber: '123-4321',
          phoneExtension: '123',
          email: '<EMAIL>',
          notes: '',
          title: '',
          createUser: true,
        }),
      );

      // TODO: Assert API was called twice
      scheduler.expectObservable(actual$).toBe('-x', { x: expect.any(Array) });
    });
    it('should set a value on the workstate', () => {
      const accountGroupId = 'AG-123';
      schedule(scheduler, '-|', () => service.loadContacts(accountGroupId)); // Must populate base list of contacts first
      schedule(scheduler, '--|', () => {
        service.addContact({
          accountGroupId,
          firstName: 'John',
          lastName: 'Wick',
          phoneNumber: '123-4321',
          phoneExtension: '123',
          email: '<EMAIL>',
          notes: '',
          title: '',
          createUser: true,
        });
      });
      scheduler.expectObservable(service.isAddingContactSuccessEvent$()).toBe('--t', { t: true });
    });
  });

  describe('editContact on success', () => {
    it('should load contacts$ again', () => {
      const accountGroupId = 'AG-123';
      const mockBusinessService = new MockBusinessService();
      const service = new ContactsService(
        new MockHttpClient() as any,
        mockBusinessService,
        null,
        loggedInUserInfo as any,
        null,
        scheduler,
      );

      const actual$ = service.contacts$(accountGroupId);
      schedule(scheduler, '--|', () =>
        service.editContact(accountGroupId, 'CO-123', 'John', 'Wick', '123-4321', '123', '<EMAIL>', '', ''),
      );

      scheduler.expectObservable(actual$).toBe('-xy', { x: expect.any(Array), y: expect.any(Array) });
    });
  });

  describe('deleting a contact', () => {
    it('should delete a contact', () => {
      const accountGroupId = 'AG-123';
      const mockBusinessService = new MockBusinessService();
      const service = new ContactsService(
        new MockHttpClient() as any,
        mockBusinessService,
        null,
        loggedInUserInfo as any,
        null,
        scheduler,
      );
      const idToDelete = 'CO-0d6845c17d284dfc91ba29b18b340b96';
      let actualContact: Contact;

      schedule(scheduler, '-|', () => {
        service
          .contacts$(accountGroupId)
          .pipe(map((contacts) => contacts.find((contact) => contact.contactId === idToDelete)))
          .subscribe((contact) => (actualContact = contact));
      });

      schedule(scheduler, '----|', () => {
        service.deleteContact(accountGroupId, idToDelete);
      });

      schedule(scheduler, '---|', () => {
        expect(actualContact).toBeTruthy();
      });

      schedule(scheduler, '-----|', () => {
        expect(actualContact).toBeFalsy();
      });
    });
  });

  describe('editContact on failure attempt then success', () => {
    it('should not load contacts$ on failure but keep the contacts$ stream open', () => {
      const accountGroupId = 'AG-123';
      const apiSrvMock = new MockHttpClient();
      const mockBusinessService = new MockBusinessService();
      const service = new ContactsService(
        new MockHttpClient() as any,
        mockBusinessService,
        null,
        loggedInUserInfo as any,
        null,
        scheduler,
      );

      const actual$ = service.contacts$(accountGroupId);
      schedule(scheduler, '-|', () => {
        apiSrvMock.failUpdateCall = true;
        service.editContact(accountGroupId, 'CO-123', 'John', 'Wick', '123-4321', '123', '<EMAIL>', '', '');
      });
      schedule(scheduler, '---|', () => {
        apiSrvMock.failUpdateCall = false;
        service.editContact(accountGroupId, 'CO-123', 'John', 'Wick', '123-4321', '123', '<EMAIL>', '', '');
      });

      scheduler.expectObservable(actual$).toBe('-x-y', { x: expect.any(Array), y: expect.any(Array) });
    });
  });

  describe('isEditingContactSuccessEvent$', () => {
    it('should emit false on failure and true on successive success', () => {
      const accountGroupId = 'AG-123';
      const httpClientMock = new MockHttpClient();
      const service = new ContactsService(httpClientMock as any, null, null, null, null);

      const actual$ = service.isEditingContactSuccessEvent$();
      schedule(scheduler, '-|', () => {
        httpClientMock.failUpdateCall = true;
        service.editContact(accountGroupId, 'CO-123', 'John', 'Wick', '123-4321', '123', '<EMAIL>', '', '');
      });
      schedule(scheduler, '---|', () => {
        httpClientMock.failUpdateCall = false;
        service.editContact(accountGroupId, 'CO-123', 'John', 'Wick', '123-4321', '123', '<EMAIL>', '', '');
      });

      scheduler.expectObservable(actual$).toBe('-f-t', { f: false, t: true });
    });
  });

  describe('editContact', () => {
    let httpClientMock: MockHttpClient;
    let contactsService: ContactsService;

    beforeEach(() => {
      httpClientMock = new MockHttpClient();
      contactsService = new ContactsService(httpClientMock as any, null, null, null, null);
    });

    it('does not attempt updating any field with the undefined type', () => {
      contactsService.editContact('ag-id', 'con-id', '', undefined, '', '', '', undefined, '', 'apartner', 'auserid');
      const expected = new HttpParams({ encoder: new UrlEncodingCodec() })
        .set('contactId', 'con-id')
        .set('accountGroupId', 'ag-id')
        .set('firstName', '')
        .set('lastName', '')
        .set('phoneNumber', '')
        .set('extension', '')
        .set('email', '')
        .set('title', '')
        .set('notes', '')
        .set('overwriteBlankFlag', 'true')
        .set('partnerId', 'apartner')
        .set('userId', 'auserid');

      expect(httpClientMock.params).toEqual(expected);
    });

    it('does attempt updating all fields with valid strings', () => {
      contactsService.editContact('ag-id', 'con-id', '1', '2', '3', '4', '5', '6', '7', 'apartner', 'auserid');
      const expected = new HttpParams({ encoder: new UrlEncodingCodec() })
        .set('contactId', 'con-id')
        .set('accountGroupId', 'ag-id')
        .set('firstName', '1')
        .set('lastName', '2')
        .set('phoneNumber', '3')
        .set('extension', '4')
        .set('email', '5')
        .set('title', '6')
        .set('notes', '7')
        .set('overwriteBlankFlag', 'true')
        .set('partnerId', 'apartner')
        .set('userId', 'auserid');

      expect(httpClientMock.params).toEqual(expected);
    });
  });
});
