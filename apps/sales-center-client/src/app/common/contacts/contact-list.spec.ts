import { TestScheduler } from 'rxjs/testing';
import { schedule } from '@vendasta/rx-utils';

import { Contact } from '@vendasta/sales-v2';
import { ContactField } from './contact-field.enum';
import { ContactList } from './contact-list';
import { ContactShadow } from './contact-v2';
import { map } from 'rxjs/operators';

const mapToBaseContacts = (contacts: ContactShadow[]): Contact[] => contacts.map((contact) => contact.baseContact);

describe('ContactList', () => {
  let cl: ContactList;
  let scheduler: TestScheduler;

  beforeEach(() => {
    scheduler = new TestScheduler((actual, expected) => expect(actual).toEqual(expected));
  });
  afterEach(() => scheduler.flush());
  it('updates itself with updated contact', () => {
    const existing = new ContactShadow(new Contact({ contactId: 'CO-1' }), [ContactField.CONTACT_ID]);
    cl = new ContactList();
    cl.set([existing]);

    const updatedContact = new ContactShadow(new Contact({ contactId: 'CO-1', firstName: 'Charlie' }), [
      ContactField.CONTACT_ID,
      ContactField.FIRST_NAME,
    ]);

    schedule(scheduler, '---|', () => cl.update(updatedContact));

    scheduler
      .expectObservable(cl.contacts$.pipe(map((contacts) => contacts.map((contact) => contact.baseContact))))
      .toBe('x--y', { x: [existing.baseContact], y: [updatedContact.baseContact] });
  });
  it('removes deleted contact', () => {
    const contactToDelete = new ContactShadow(
      new Contact({ contactId: 'CO-3', firstName: 'Llanfairpwllgwyngyllgogerychwyrndrobwllllantysiliogogogoch' }),
      [ContactField.CONTACT_ID, ContactField.FIRST_NAME],
    );
    const expectedContacts = [
      new ContactShadow(new Contact({ contactId: 'CO-1', firstName: 'Charlie' }), [
        ContactField.CONTACT_ID,
        ContactField.FIRST_NAME,
      ]),
      new ContactShadow(new Contact({ contactId: 'CO-2', firstName: 'Winston' }), [
        ContactField.CONTACT_ID,
        ContactField.FIRST_NAME,
      ]),
    ];

    cl = new ContactList();
    cl.set([...expectedContacts, contactToDelete]);

    schedule(scheduler, '---|', async () => cl.delete(contactToDelete));

    scheduler
      .expectObservable(cl.contacts$.pipe(map((contacts) => contacts.map((contact) => contact.baseContact))))
      .toBe('x--y', {
        x: mapToBaseContacts([...expectedContacts, contactToDelete]),
        y: mapToBaseContacts([...expectedContacts]),
      });
  });
  it('adds a newly created contact to itself', () => {
    const existing = new ContactShadow(new Contact({ contactId: 'CO-1' }), [ContactField.CONTACT_ID]);
    cl = new ContactList();
    cl.set([existing]);

    const newContact = new ContactShadow(new Contact({ contactId: 'CO-2', firstName: 'Charlie' }), [
      ContactField.CONTACT_ID,
      ContactField.FIRST_NAME,
    ]);

    schedule(scheduler, '---|', () => cl.add(newContact));

    scheduler
      .expectObservable(cl.contacts$.pipe(map((contacts) => contacts.map((contact) => contact.baseContact))))
      .toBe('x--y', { x: [existing.baseContact], y: mapToBaseContacts([newContact, existing]) });
  });
});
