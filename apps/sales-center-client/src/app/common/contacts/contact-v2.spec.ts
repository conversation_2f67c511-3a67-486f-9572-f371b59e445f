import { ContactShadow } from './contact-v2';
import { AllContactFields, ContactField } from './contact-field.enum';
import { Contact } from '@vendasta/sales-v2';

describe('ContactShadow updates its internal update object', () => {
  it('adds new property if an existing property for a field does not exist', () => {
    const contact = new Contact({ firstName: 'Charlulu', contactId: 'c-123' });
    const expected = '<PERSON> the Gawd';
    const u = new ContactShadow(contact, AllContactFields);

    u.addValueChange(ContactField.TITLE, expected);
    expect(u.updates.get(ContactField.TITLE)).toEqual(expected);
  });

  it('does not update if requested value change is current contact value', () => {
    const contact = new Contact({ firstName: 'Charlulu', contactId: 'c-123' });
    const expected = new Map();
    const u = new ContactShadow(contact, AllContactFields);
    u.addValueChange(ContactField.FIRST_NAME, 'Charlulu');

    expect(u.updates).toEqual(expected);
  });

  it('updates a field more than once', () => {
    const contact = new Contact({ firstName: 'Charlulu', contactId: 'c-123' });
    const expected = 'updated next';
    const u = new ContactShadow(contact, AllContactFields);
    u.addValueChange(ContactField.FIRST_NAME, 'updated first');
    u.addValueChange(ContactField.FIRST_NAME, expected);

    expect(u.updates.get(ContactField.FIRST_NAME)).toEqual(expected);
  });

  it('removes an updated field if set back to original contact value', () => {
    const contact = new Contact({ firstName: 'Charlulu', contactId: 'c-123' });
    const expected = new Map();
    const u = new ContactShadow(contact, AllContactFields);
    u.addValueChange(ContactField.FIRST_NAME, 'updated first');
    u.addValueChange(ContactField.FIRST_NAME, 'Charlulu');

    expect(u.updates).toEqual(expected);
  });

  it('does not allow falsy updates if contact value was falsy', () => {
    const contact = new Contact({ firstName: '', contactId: 'c-123' });
    const expected = new Map();
    const u = new ContactShadow(contact, AllContactFields);
    u.addValueChange(ContactField.FIRST_NAME, '');
    u.addValueChange(ContactField.FIRST_NAME, undefined);

    expect(u.updates).toEqual(expected);
  });

  it('does allow falsy updates if contact value was truly', () => {
    const contact = new Contact({ firstName: 'Charlulu', contactId: 'c-123' });
    const expected = new Map([[ContactField.FIRST_NAME, '']]);
    const u = new ContactShadow(contact, AllContactFields);
    u.addValueChange(ContactField.FIRST_NAME, '');

    expect(u.updates).toEqual(expected);
  });

  it('allows numeric fields to be erased', () => {
    const contact = new Contact({ phoneExtension: '123', contactId: 'c-123' });
    const expected = new Map([[ContactField.PHONE_EXTENSION, null]]);
    const u = new ContactShadow(contact, AllContactFields);
    u.addValueChange(ContactField.PHONE_EXTENSION, null);

    expect(u.updates).toEqual(expected);
  });

  it('allows string case changes', () => {
    const contact = new Contact({ firstName: 'charlulu', lastName: "O'llama", contactId: 'c-123' });
    const expected = new Map<ContactField, any>([
      [ContactField.FIRST_NAME, 'Charlulu'],
      [ContactField.LAST_NAME, "o'llama"],
    ]);
    const u = new ContactShadow(contact, AllContactFields);
    u.addValueChange(ContactField.FIRST_NAME, 'Charlulu');
    u.addValueChange(ContactField.LAST_NAME, "o'llama");

    expect(u.updates).toEqual(expected);
  });

  describe('getters', () => {
    const contact = new Contact({
      contactId: 'c-123',
      firstName: 'Finitial',
      lastName: "O'nitial",
      contactEmail: '<EMAIL>',
      notes: 'Initial notes',
      title: 'Initially Titular',
      phoneNumber: '111111111',
      phoneExtension: '111',
    });

    it('return initial value if updates unset', () => {
      const u = new ContactShadow(contact, AllContactFields);
      expect(u.firstName).toEqual(contact[ContactField.FIRST_NAME]);
      expect(u.lastName).toEqual(contact[ContactField.LAST_NAME]);
      expect(u.contactEmail).toEqual(contact[ContactField.EMAIL]);
      expect(u.notes).toEqual(contact[ContactField.NOTES]);
      expect(u.title).toEqual(contact[ContactField.TITLE]);
      expect(u.phoneNumber).toEqual(contact[ContactField.PHONE_NUMBER]);
      expect(u.phoneExtension).toEqual(contact[ContactField.PHONE_EXTENSION]);
    });

    it('returns expected knownFields', () => {
      const contact = new Contact({ firstName: 'charlulu', lastName: "O'llama", contactId: 'c-123' });
      const expectedKnownFields = [ContactField.FIRST_NAME, ContactField.LAST_NAME];
      const u = new ContactShadow(contact, expectedKnownFields, ['ag-1']);

      expect(u.knownFields).toEqual(expectedKnownFields);
    });
  });
});
