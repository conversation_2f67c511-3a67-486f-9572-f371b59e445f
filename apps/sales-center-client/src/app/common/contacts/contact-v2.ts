import { Subject } from 'rxjs';
import { Contact } from '@vendasta/sales-v2';
import { ContactField as Fields } from './contact-field.enum';
import { ContactFullNamePipe } from './contact-pipe/contact.pipe';
import { formatPhoneNumber } from '../utils/phone-utils';

/**
 * The ContactShadow class wraps a contact object and records change attempts. This was created to ensure
 * data integrity from forms to api calls.
 */
export class ContactShadow implements Omit<Contact, 'toApiJson' | 'accountIds'> {
  readonly contactId: string;
  readonly userId: string;
  readonly created: Date;
  readonly baseContact: Contact;
  readonly accountGroupIds: Readonly<Set<string>>;
  private readonly _updates = new Map<Fields, any>();

  private readonly changes$$ = new Subject<void>();
  readonly changed$ = this.changes$$.asObservable();
  private _knownFields: Readonly<Set<Fields>>;

  constructor(contact: Contact, knownFields: Readonly<Fields[]>, accountGroupIds: string[] = [], userID?: string) {
    this.contactId = contact.contactId;
    this.userId = userID;
    this.created = contact.created;
    this.baseContact = contact;
    this._knownFields = new Set(knownFields);

    const contactAgs = contact?.accountIds ?? [];
    this.accountGroupIds = new Set([...contactAgs, ...accountGroupIds].filter(String));
  }

  static getInternationalPhoneNumberFromBaseContact(contact: Contact): string {
    const phoneNumber = contact.phoneNumber;
    const phoneNumberCountry = contact.phoneNumberCountry;
    const extension = contact.phoneExtension;
    const format = 'INTERNATIONAL';

    return formatPhoneNumber(phoneNumber, format, extension, phoneNumberCountry);
  }

  get fullName(): string {
    const pipe = new ContactFullNamePipe();

    return pipe.transform(this);
  }

  clearUpdates(): void {
    this._updates.clear();
  }

  set [Fields.EMAIL](email: string) {
    this.addValueChange(Fields.EMAIL, email);
  }
  get [Fields.EMAIL](): string {
    return this.baseContact[Fields.EMAIL];
  }

  set [Fields.FIRST_NAME](firstName: string) {
    this.addValueChange(Fields.FIRST_NAME, firstName);
  }
  get [Fields.FIRST_NAME](): string {
    return this.baseContact[Fields.FIRST_NAME];
  }

  set [Fields.LAST_NAME](lastName: string) {
    this.addValueChange(Fields.LAST_NAME, lastName);
  }
  get [Fields.LAST_NAME](): string {
    return this.baseContact[Fields.LAST_NAME];
  }

  set [Fields.NOTES](notes: string) {
    this.addValueChange(Fields.NOTES, notes);
  }
  get [Fields.NOTES](): string {
    return this.baseContact[Fields.NOTES];
  }

  set [Fields.PHONE_EXTENSION](phoneExtension: string) {
    this.addValueChange(Fields.PHONE_EXTENSION, phoneExtension);
  }
  get [Fields.PHONE_EXTENSION](): string {
    return this.baseContact[Fields.PHONE_EXTENSION];
  }

  set [Fields.PHONE_NUMBER](phoneNumber: string) {
    this.addValueChange(Fields.PHONE_NUMBER, phoneNumber);
  }
  get [Fields.PHONE_NUMBER](): string {
    return this.baseContact[Fields.PHONE_NUMBER];
  }

  set [Fields.PHONE_COUNTRY](phoneCountry: string) {
    this.addValueChange(Fields.PHONE_COUNTRY, phoneCountry);
  }
  get [Fields.PHONE_COUNTRY](): string {
    return this.baseContact[Fields.PHONE_COUNTRY];
  }

  set [Fields.TITLE](title: string) {
    this.addValueChange(Fields.TITLE, title);
  }
  get [Fields.TITLE](): string {
    return this.baseContact[Fields.TITLE];
  }

  private removeValueChange(field: Fields): void {
    this._updates.delete(field);
    this.changes$$.next();
  }

  addValueChange(field: Fields, value: any): void {
    // undefined is not an intentional change
    if (value === undefined) {
      return;
    }

    // We aren't changing the update value
    if (this._updates[field] === value) {
      return;
    }

    // We aren't changing the value, delete existing property if it exists
    if (this.baseContact[field] === value) {
      this.removeValueChange(field);

      return;
    }

    this._updates.set(field, value);
    this.changes$$.next();
  }

  get updates(): Map<Fields, any> {
    return this._updates;
  }

  get knownFields(): Fields[] {
    return [...this._knownFields];
  }
}
