import { AbstractControl, UntypedFormGroup, ValidationErrors, ValidatorFn } from '@angular/forms';

export const atLeastOneFieldEnteredValidator = (controlsToCheck: AbstractControl[]): ValidatorFn => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  return (formGroup: UntypedFormGroup): ValidationErrors | null => {
    for (const control of controlsToCheck) {
      if (control.value) {
        return null;
      }
    }

    return { missingAtLeastOneField: true };
  };
};

export const userRequiredFieldsEnteredIfCreatingUserValidator: ValidatorFn = (
  control: AbstractControl,
): ValidationErrors | null => {
  const createUser = control.get('createUser');
  const email = control.get('email');
  const locale = control.get('preferredLocale');

  if (createUser.value === true && (!email.value || !locale.value)) {
    return { createUserInvalid: true };
  }
  return null;
};
