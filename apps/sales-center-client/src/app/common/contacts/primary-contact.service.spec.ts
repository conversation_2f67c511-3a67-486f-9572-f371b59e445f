import { TestBed } from '@angular/core/testing';
import { PartnerMarket } from '@galaxy/types';
import { schedule } from '@vendasta/rx-utils';
import { BusinessService } from '@vendasta/sales-v2';
import { EMPTY, Observable, of } from 'rxjs';
import { take } from 'rxjs/operators';
import { TestScheduler } from 'rxjs/testing';
import { USER_PARTNER_MARKET_TOKEN } from '../../core/feature-flag.service';
import { CACHE_TIME_SEC, PrimaryContactService } from './primary-contact.service';

class MockBusinessService {
  getPrimaryContact(): Observable<string> {
    throw new Error('unimplemented get');
  }

  setPrimaryContact(): Observable<null> {
    throw new Error('unimplemented set');
  }
}

const MockPartnerMarket = <PartnerMarket>{
  partnerId: 'TEST',
  marketId: 'testing',
};

describe('PrimaryContactService', () => {
  let service: PrimaryContactService;
  let scheduler: TestScheduler;
  const api = new MockBusinessService();
  const setApiSpy = jest.spyOn(api, 'setPrimaryContact');
  const getApiSpy = jest.spyOn(api, 'getPrimaryContact');

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        { provide: BusinessService, useValue: api },
        { provide: USER_PARTNER_MARKET_TOKEN, useValue: of(MockPartnerMarket) },
      ],
    });
    service = TestBed.inject(PrimaryContactService);
    scheduler = new TestScheduler((actual, expected) => expect(actual).toEqual(expected));
  });

  afterEach(() => {
    scheduler.flush();
    setApiSpy.mockClear();
    getApiSpy.mockClear();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should add a new behaviour subject and set the primary contact when get is called if not previously set', (done) => {
    getApiSpy.mockReturnValueOnce(of('CO-123'));

    service.getPrimaryContactForAccount$('AG-123').subscribe((contactId) => {
      expect(contactId).toEqual('CO-123');
      done();
    });
  });

  it('should maintain the list for each account group for subsequent subscriptions', () => {
    getApiSpy.mockReturnValueOnce(of('CO-123'));
    getApiSpy.mockReturnValueOnce(of('CO-423'));

    scheduler.expectObservable(service.getPrimaryContactForAccount$('AG-123')).toBe('x', {
      x: 'CO-123',
    });

    scheduler.expectObservable(service.getPrimaryContactForAccount$('AG-423')).toBe('x', {
      x: 'CO-423',
    });

    scheduler.expectObservable(service.getPrimaryContactForAccount$('AG-123')).toBe('x', {
      x: 'CO-123',
    });
  });

  it('should only update the provided account group when a new primary contact is set', () => {
    getApiSpy.mockReturnValueOnce(of('CO-123'));
    getApiSpy.mockReturnValueOnce(of('CO-423'));
    setApiSpy.mockReturnValue(of(null));

    schedule(scheduler, '-|', () => service.setPrimaryContactForAccount$('CO-321', 'AG-123').subscribe());

    scheduler.expectObservable(service.getPrimaryContactForAccount$('AG-123')).toBe('xy', {
      x: 'CO-123',
      y: 'CO-321',
    });

    scheduler.expectObservable(service.getPrimaryContactForAccount$('AG-423')).toBe('x', {
      x: 'CO-423',
    });
  });

  it('should update the get observable if a new primary contact is set', () => {
    getApiSpy.mockReturnValueOnce(of('CO-123'));
    setApiSpy.mockReturnValue(of(null));

    schedule(scheduler, '-|', () => service.setPrimaryContactForAccount$('CO-321', 'AG-123').subscribe());
    schedule(scheduler, '----|', () => service.setPrimaryContactForAccount$('CO-456', 'AG-123').subscribe());

    scheduler.expectObservable(service.getPrimaryContactForAccount$('AG-123')).toBe('xy--z', {
      x: 'CO-123',
      y: 'CO-321',
      z: 'CO-456',
    });
  });

  it('should return null if the get call fails', () => {
    getApiSpy.mockReturnValueOnce(EMPTY);
    scheduler.expectObservable(service.getPrimaryContactForAccount$('AG-123')).toBe('x', {
      x: null,
    });
  });

  it('cache expires after the expiry time has past and a new get call is made', () => {
    getApiSpy.mockReturnValueOnce(of('CO-123'));
    getApiSpy.mockReturnValueOnce(of('CO-123'));
    jest.useFakeTimers();

    service.getPrimaryContactForAccount$('AG-123').pipe(take(1)).subscribe();

    jest.advanceTimersByTime(CACHE_TIME_SEC * 1000 + 2000); // advance to 2s past expiry

    service.getPrimaryContactForAccount$('AG-123').pipe(take(1)).subscribe();

    expect(getApiSpy).toBeCalledTimes(2);
  });

  it('should block multiple calls to the same account until the cache expires', () => {
    getApiSpy.mockClear();
    getApiSpy.mockReturnValueOnce(of('CO-123'));
    jest.useFakeTimers();

    service.getPrimaryContactForAccount$('AG-123').pipe(take(1)).subscribe();

    // These next 4 calls should not trigger a new HTTP request
    service.getPrimaryContactForAccount$('AG-123').pipe(take(1)).subscribe();
    service.getPrimaryContactForAccount$('AG-123').pipe(take(1)).subscribe();
    service.getPrimaryContactForAccount$('AG-123').pipe(take(1)).subscribe();
    service.getPrimaryContactForAccount$('AG-123').pipe(take(1)).subscribe();

    jest.advanceTimersByTime(CACHE_TIME_SEC * 1000 + 2000); // advance to 2s past expiry of the first call

    service.getPrimaryContactForAccount$('AG-123').pipe(take(1)).subscribe();

    expect(getApiSpy).toBeCalledTimes(2);
  });

  it('should block multiple calls to the same account until the cache ex1sddafasdfadsfdsfpires', () => {
    getApiSpy.mockReturnValueOnce(of('CO-123'));
    getApiSpy.mockReturnValueOnce(of('CO-423'));
    jest.useFakeTimers();

    service.getPrimaryContactForAccount$('AG-123').pipe(take(1)).subscribe();
    service.getPrimaryContactForAccount$('AG-423').pipe(take(1)).subscribe();

    // These next 4 calls should not trigger a new HTTP request
    service.getPrimaryContactForAccount$('AG-123').pipe(take(1)).subscribe();
    service.getPrimaryContactForAccount$('AG-123').pipe(take(1)).subscribe();
    service.getPrimaryContactForAccount$('AG-123').pipe(take(1)).subscribe();
    service.getPrimaryContactForAccount$('AG-123').pipe(take(1)).subscribe();

    expect(getApiSpy).toBeCalledTimes(2);
  });
});
