import { Contact as V2Contact } from '@vendasta/sales-v2';

export class Contact {
  public readonly contactId: string;
  public readonly email: string;
  public readonly name: string;
  public readonly firstName: string;
  public readonly lastName: string;
  public readonly notes: string;
  public readonly phoneExtension: number;
  public readonly phoneNumber: string;
  public readonly phoneNumberCountry: string;
  public readonly title: string;
  public readonly userId: string;
  public readonly unifiedUserId: string;
  public readonly created: Date;

  private _hasUser: boolean;

  constructor({
    contactId,
    email,
    hasUser,
    name,
    firstName,
    lastName,
    notes,
    phoneExtension,
    phoneNumber,
    phoneNumberCountry,
    title,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    accountGroupId,
    userId,
    unifiedUserId,
    created,
  }: any) {
    this.contactId = contactId;
    this.email = email;
    this._hasUser = hasUser;
    this.name = name;
    this.firstName = firstName;
    this.lastName = lastName;
    this.notes = notes;
    this.phoneExtension = phoneExtension;
    this.phoneNumber = phoneNumber;
    this.phoneNumberCountry = phoneNumberCountry;
    this.title = title;
    this.userId = userId;
    this.unifiedUserId = unifiedUserId;
    this.created = created;
  }

  set hasUser(h: boolean) {
    this._hasUser = h;
  }

  get hasUser(): boolean {
    return this._hasUser;
  }

  get fullName(): string {
    const noFirstLast = !this.firstName && !this.lastName;
    if (noFirstLast && this.name) {
      return this.name;
    }
    if (noFirstLast && !this.name) {
      return '';
    }
    const firstName = this.firstName ? this.firstName : '';
    const lastName = this.lastName ? this.lastName : '';
    return [firstName, lastName].filter((v) => Boolean(v)).join(' ');
  }

  get currentEmail(): string {
    return this.email || '';
  }

  toV2Contact(): V2Contact {
    return new V2Contact({
      contactId: this.contactId,
      notes: this.notes,
      firstName: this.firstName,
      lastName: this.lastName,
      title: this.title,
      contactEmail: this.currentEmail,
      phoneNumber: this.phoneNumber,
      phoneExtension: this.phoneExtension?.toString(),
      phoneNumberCountry: this.phoneNumberCountry,
      created: this.created,
    });
  }
}
