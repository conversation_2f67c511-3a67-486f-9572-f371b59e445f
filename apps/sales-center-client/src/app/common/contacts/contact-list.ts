import { Contact } from '@vendasta/sales-v2';
import { BehaviorSubject, Observable } from 'rxjs';
import { AllContactFields } from './contact-field.enum';
import { ContactShadow } from './contact-v2';
import { add } from 'date-fns';

export class ContactList {
  private readonly contacts$$ = new BehaviorSubject<ContactShadow[]>([]);
  readonly contacts$: Observable<ContactShadow[]> = this.contacts$$;

  private expiry: Date;

  delete(deletedContact: ContactShadow): void {
    if (!deletedContact?.contactId) {
      throw new Error('Cannot remove contact from list without a contactId');
    }

    const contacts = this.contacts$$.getValue().filter((contact) => contact.contactId !== deletedContact.contactId);

    this.contacts$$.next(contacts);
  }

  update(updatedContact: ContactShadow): void {
    if (!updatedContact?.contactId) {
      throw new Error('Cannot update contact in list without a contactId');
    }

    const contacts = this.contacts$$.getValue().filter((contact) => contact.contactId !== updatedContact.contactId);
    contacts.unshift(updatedContact);

    this.contacts$$.next(contacts);
  }

  // If a contact is added without a shadow, we assume all fields are known
  add(contact: Contact | ContactShadow): void {
    if (!contact?.contactId) {
      throw new Error('Cannot add a contact to the list without a contactId');
    }

    const contacts = this.contacts$$.getValue();
    const newContact: ContactShadow =
      contact instanceof Contact ? new ContactShadow(contact, AllContactFields) : contact;

    this.contacts$$.next([newContact, ...contacts]);
  }

  set(contacts: ContactShadow[], expireIn?: Duration): ContactList {
    this.contacts$$.next(contacts ?? []);
    if (expireIn) this.expiry = add(new Date(), expireIn);

    return this;
  }

  get cacheExpired(): boolean {
    if (!this.expiry) {
      return true;
    }

    return this.expiry < new Date();
  }
}
