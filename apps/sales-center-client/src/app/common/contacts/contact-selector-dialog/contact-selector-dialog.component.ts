import { Component, Inject, OnInit } from '@angular/core';
import { UntypedFormControl, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Observable } from 'rxjs';
import { take } from 'rxjs/operators';
import { User } from '../../../users/user';
import { AllContactFields } from '../contact-field.enum';
import { ContactShadow } from '../contact-v2';
import { ContactsV2Service } from '../contacts-v2.service';
import { AddContactComponent } from '../forms/add-contact.component';

export interface ContactSelectorDialogComponentData {
  partnerId: string;
  accountGroupId: string;
  limitToUsers: boolean;
  addUserActionLabel: string;
  actionLabel: string;
  warningMessage: string;
}

@Component({
  selector: 'app-contact-selector-dialog',
  templateUrl: './contact-selector-dialog.component.html',
  styleUrls: ['./contact-selector-dialog.component.scss'],
  standalone: false,
})
export class ContactSelectorDialogComponent implements OnInit {
  accountGroupId: string;
  partnerId: string;
  addUserActionLabel: string;
  actionLabel: string;
  usersControl = new UntypedFormControl('', [Validators.required]);
  contacts$: Observable<ContactShadow[]>;
  warningMessage: string;

  constructor(
    public dialogRef: MatDialogRef<ContactSelectorDialogComponent, User>,
    @Inject(MAT_DIALOG_DATA) public data: ContactSelectorDialogComponentData,
    private readonly contactsService: ContactsV2Service,
    private readonly dialog: MatDialog,
  ) {
    this.partnerId = data.partnerId;
    this.accountGroupId = data.accountGroupId;
    this.addUserActionLabel = data.addUserActionLabel;
    this.actionLabel = data.actionLabel;
    this.warningMessage = data.warningMessage;
  }

  ngOnInit(): void {
    this.contacts$ = this.contactsService.list$(this.accountGroupId, ...AllContactFields);
  }

  success(): void {
    this.dialogRef.close(this.usersControl.value);
  }

  addUser(): void {
    const ref = this.dialog.open(AddContactComponent, {
      width: '500px',
      maxWidth: '100vw',
      data: {
        accountGroupId: this.accountGroupId,
        actionLabel: this.addUserActionLabel,
        forceUserCreation: false,
      },
    });
    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe((res) => {
        if (res) {
          this.dialogRef.close(res);
        }
      });
  }
}
