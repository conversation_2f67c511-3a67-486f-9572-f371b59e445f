<h3 mat-dialog-title>{{ actionLabel }}</h3>

<mat-card appearance="outlined" *ngIf="warningMessage" class="banner warning-color mat-elevation-z0">
  <mat-icon class="warning-icon">warning</mat-icon>
  <span [innerHTML]="warningMessage"></span>
</mat-card>

<mat-dialog-content class="user-selector-content">
  <ng-container *ngIf="contacts$ | glxyAsyncStatus | async as obs" [ngSwitch]="obs.status">
    <ng-container *ngSwitchCase="'loading'">
      <mat-spinner [diameter]="30"></mat-spinner>
    </ng-container>
    <ng-container *ngSwitchCase="'loaded'">
      <mat-form-field class="user-selector">
        <mat-label>
          {{ 'CONTACTS.SELECT_CONTACT' | translate }}
        </mat-label>

        <mat-select [formControl]="usersControl" required>
          <mat-option *ngFor="let contact of obs.value" [value]="contact">
            {{ contact | contactFullName }}
            <span *ngIf="contact.contactEmail">({{ contact.contactEmail }})</span>
          </mat-option>
          <mat-select-trigger>
            {{
              usersControl.value
                ? (usersControl.value.firstName ?? '') +
                  ' ' +
                  (usersControl.value.lastName ?? '') +
                  ' ' +
                  (usersControl.value.email ? '(' + usersControl.value.email + ')' : '')
                : ''
            }}
          </mat-select-trigger>
        </mat-select>
        <mat-error *ngIf="usersControl.hasError('required')">
          {{ 'FORMS.REQUIRED_FIELD' | translate }}
        </mat-error>
      </mat-form-field>
    </ng-container>
  </ng-container>
</mat-dialog-content>

<mat-dialog-actions>
  <div class="actions">
    <button mat-button color="primary" (click)="addUser()">
      {{ addUserActionLabel | translate }}
    </button>
    <span>
      <button mat-button color="primary" mat-dialog-close>
        {{ 'COMMON.ACTION_LABELS.CANCEL' | translate }}
      </button>
      <button mat-raised-button color="primary" (click)="success()">
        {{ actionLabel | translate }}
      </button>
    </span>
  </div>
</mat-dialog-actions>
