import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ContactsPhoneListComponent } from '../contacts-phone-list/contacts-phone-list.component';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateModule } from '@ngx-translate/core';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ContactsPhoneButtonComponent } from './contacts-phone-button.component';
import { AsteriskModule } from '../../../asterisk/asterisk.module';
import { ContactFormsModule } from '../contact-forms.module';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { ContactPipeModule } from '../contact-pipe/contact-pipe.module';

@NgModule({
  declarations: [ContactsPhoneListComponent, ContactsPhoneButtonComponent],
  imports: [
    CommonModule,
    MatMenuModule,
    TranslateModule,
    MatListModule,
    MatIconModule,
    MatTooltipModule,
    AsteriskModule,
    ContactFormsModule,
    GalaxyPipesModule,
    ContactPipeModule,
  ],
  providers: [ContactsPhoneListComponent, ContactsPhoneButtonComponent],
  exports: [ContactsPhoneListComponent, ContactsPhoneButtonComponent],
})
export class ContactsMenuActionsModule {}
