import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { Contact } from '@vendasta/sales-v2';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { Observable, of } from 'rxjs';
import { ScreenshareService } from '../../../screenshare';
import { AllContactFields } from '../contact-field.enum';
import { ContactPipeModule } from '../contact-pipe/contact-pipe.module';
import { ContactShadow } from '../contact-v2';
import { ContactsV2Service } from '../contacts-v2.service';
import { PrimaryContactService } from '../primary-contact.service';
import { ContactsV2ListComponent } from './contacts-v2-list.component';

class MockV2Service {
  list$(): Observable<ContactShadow[]> {
    throw new Error('unimplemented');
  }
}

class MockPrimaryContactService {
  getPrimaryContactForAccount$(): Observable<string> {
    throw new Error('unimplemented');
  }
}

class MockScreenshareService {}

const AGID = 'AG-123';
const AGIDS = [AGID];
const TEST_PRIMARY_CONTACT_ID = 'CO-124';
const MOCK_CONTACTS: Contact[] = [
  new Contact({
    contactId: 'CO-125',
    firstName: 'Charlie',
    contactEmail: '<EMAIL>',
    phoneNumber: '**********',
    phoneNumberCountry: 'CA',
  }),
  new Contact({
    contactId: 'CO-123',
    firstName: 'Alpha',
    contactEmail: '<EMAIL>',
    phoneNumber: '**********',
    phoneNumberCountry: 'CA',
  }),
  new Contact({
    contactId: TEST_PRIMARY_CONTACT_ID,
    firstName: 'Beta',
    contactEmail: '<EMAIL>',
    phoneNumber: '**********',
    phoneNumberCountry: 'CA',
  }),
];
const MOCK_CONTACT_SHADOWS = <ContactShadow[]>[
  new ContactShadow(MOCK_CONTACTS[0], AllContactFields, AGIDS, 'U-123'),
  new ContactShadow(MOCK_CONTACTS[1], AllContactFields, AGIDS),
  new ContactShadow(MOCK_CONTACTS[2], AllContactFields, AGIDS),
];

const SORTED_MOCK_CONTACT_SHADOWS_WITH_PRIMARY = [
  MOCK_CONTACT_SHADOWS[2],
  MOCK_CONTACT_SHADOWS[1],
  MOCK_CONTACT_SHADOWS[0],
];
const SORTED_MOCK_CONTACT_SHADOWS = [MOCK_CONTACT_SHADOWS[1], MOCK_CONTACT_SHADOWS[2], MOCK_CONTACT_SHADOWS[0]];

describe('ContactsV2ListComponent', () => {
  let component: ContactsV2ListComponent;
  let fixture: ComponentFixture<ContactsV2ListComponent>;
  const mockV2Service = new MockV2Service();
  const mockPrimaryService = new MockPrimaryContactService();
  const listSpy = jest.spyOn(mockV2Service, 'list$');
  const getPrimarySpy = jest.spyOn(mockPrimaryService, 'getPrimaryContactForAccount$');

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ContactsV2ListComponent],
      imports: [
        TranslateTestingModule.withTranslations({}),
        GalaxyPipesModule,
        NoopAnimationsModule,
        ContactPipeModule,
      ],
      providers: [
        { provide: ContactsV2Service, useValue: mockV2Service },
        { provide: PrimaryContactService, useValue: mockPrimaryService },
        { provide: ScreenshareService, useValue: new MockScreenshareService() },
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  });

  beforeEach(() => {
    listSpy.mockClear();
    getPrimarySpy.mockClear();
    listSpy.mockReturnValueOnce(of(MOCK_CONTACT_SHADOWS));
    getPrimarySpy.mockReturnValueOnce(of(TEST_PRIMARY_CONTACT_ID));
    fixture = TestBed.createComponent(ContactsV2ListComponent);
    component = fixture.componentInstance;
    component.accountGroupId = AGID;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load the contacts on component init', (done) => {
    component.contacts$.subscribe((contacts) => {
      expect(contacts).toEqual(SORTED_MOCK_CONTACT_SHADOWS_WITH_PRIMARY);
      done();
    });
  });

  it('should sort the contacts list on load with primary as the first contact, followed by first name sorting', (done) => {
    component.contacts$.subscribe((contacts) => {
      expect(contacts).toEqual(SORTED_MOCK_CONTACT_SHADOWS_WITH_PRIMARY);
      expect(getPrimarySpy).toBeCalledTimes(1);
      done();
    });
  });

  it('should sort the contacts list on load when there is no primary contact by first name', (done) => {
    listSpy.mockClear();
    getPrimarySpy.mockClear();
    listSpy.mockReturnValueOnce(of(MOCK_CONTACT_SHADOWS));
    getPrimarySpy.mockReturnValueOnce(of(null));
    fixture = TestBed.createComponent(ContactsV2ListComponent);
    component = fixture.componentInstance;
    component.accountGroupId = AGID;
    fixture.detectChanges();
    component.contacts$.subscribe((contacts) => {
      expect(contacts).toEqual(SORTED_MOCK_CONTACT_SHADOWS);
      expect(getPrimarySpy).toBeCalledTimes(1);
      done();
    });
  });
});
