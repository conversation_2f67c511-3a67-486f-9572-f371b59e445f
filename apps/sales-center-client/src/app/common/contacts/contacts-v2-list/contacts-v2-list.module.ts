import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatDividerModule } from '@angular/material/divider';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyAvatarModule } from '@vendasta/galaxy/avatar';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GalaxyButtonLoadingIndicatorModule } from '@vendasta/galaxy/button-loading-indicator';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { GalaxyPopoverModule } from '@vendasta/galaxy/popover';
import { GalaxySnackbarModule } from '@vendasta/galaxy/snackbar-service';
import { CopyToClipBoardAndAlertModule } from '../..';
import { ContactFormsModule } from '../contact-forms.module';
import { ContactPipeModule } from '../contact-pipe/contact-pipe.module';
import { ContactItemComponent } from './contact-item/contact-item.component';
import { ContactsV2ListComponent } from './contacts-v2-list.component';

@NgModule({
  declarations: [ContactsV2ListComponent, ContactItemComponent],
  exports: [ContactsV2ListComponent],
  imports: [
    CommonModule,
    GalaxyPipesModule,
    MatProgressBarModule,
    GalaxyAvatarModule,
    GalaxyBadgeModule,
    GalaxyPopoverModule,
    MatButtonModule,
    MatDividerModule,
    MatExpansionModule,
    TranslateModule,
    MatTooltipModule,
    MatProgressSpinnerModule,
    MatIconModule,
    CopyToClipBoardAndAlertModule,
    ContactPipeModule,
    ContactFormsModule,
    GalaxyEmptyStateModule,
    GalaxySnackbarModule,
    GalaxyButtonLoadingIndicatorModule,
    MatMenuModule,
    GalaxyAlertModule,
    RouterModule,
  ],
})
export class ContactsV2ListModule {}
