import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { combineLatest, Observable, ReplaySubject } from 'rxjs';
import { map, shareReplay } from 'rxjs/operators';
import { AllContactFields } from '../contact-field.enum';
import { ContactShadow } from '../contact-v2';
import { ContactsV2Service } from '../contacts-v2.service';
import { PrimaryContactService } from '../primary-contact.service';

@Component({
  selector: 'app-contacts-v2-list',
  templateUrl: './contacts-v2-list.component.html',
  styleUrls: ['./contacts-v2-list.component.scss'],
  standalone: false,
})
export class ContactsV2ListComponent implements OnInit, OnChanges {
  @Input() accountGroupId: string;

  private readonly currentlyExpandedContact$$ = new ReplaySubject<string>(1);
  readonly currentlyExpandedContact$ = this.currentlyExpandedContact$$.asObservable();
  contacts$: Observable<ContactShadow[]>;
  primaryContactId$: Observable<string>;

  constructor(
    private readonly contactsService: ContactsV2Service,
    private readonly primaryContactService: PrimaryContactService,
  ) {}

  ngOnInit(): void {
    if (!this.accountGroupId) throw new Error('accountGroupId is required');
    this.loadContacts();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.accountGroupId) {
      this.loadContacts();
    }
  }

  private loadContacts() {
    const unsortedContacts$ = this.contactsService.list$(this.accountGroupId, ...AllContactFields);
    this.primaryContactId$ = this.primaryContactService
      .getPrimaryContactForAccount$(this.accountGroupId)
      .pipe(shareReplay(1));

    this.contacts$ = combineLatest([unsortedContacts$, this.primaryContactId$]).pipe(
      map(([unsortedContacts, primaryContactId]) => this.sortContacts(unsortedContacts, primaryContactId)),
    );
  }

  private sortContacts(unsortedContacts: ContactShadow[], primaryContactId: string): ContactShadow[] {
    const clonedContactsArray: ContactShadow[] = [...unsortedContacts];
    clonedContactsArray.sort((a, b) => {
      if (a.contactId === primaryContactId) return -1; // primary contact should be at the top of the list
      if (b.contactId === primaryContactId) return 1; // primary contact should be at the top of the list
      if (!a.firstName && !b.firstName) return 0; // considered equal if neither contact has a first name
      if (!a.firstName && b.firstName) return 1; // a contact with a first name comes before one that doesn't
      if (a.firstName && !b.firstName) return -1; // a contact with a first name comes before one that doesn't
      if (a.firstName < b.firstName) return -1; // sort by first name in alphabetical order
      if (a.firstName > b.firstName) return 1;
      return 0;
    });

    return clonedContactsArray;
  }

  panelOpened(contactId: string): void {
    this.currentlyExpandedContact$$.next(contactId);
  }
}
