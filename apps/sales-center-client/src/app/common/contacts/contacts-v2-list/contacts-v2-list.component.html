<ng-container *ngIf="contacts$ | glxyAsyncStatus | async as obs" [ngSwitch]="obs.status">
  <ng-container *ngSwitchCase="'loading'">
    <mat-progress-bar mode="indeterminate"></mat-progress-bar>
  </ng-container>
  <ng-container
    *ngSwitchCase="'loaded'"
    [ngTemplateOutlet]="contactList"
    [ngTemplateOutletContext]="{ contacts: obs.value, selectedContact: currentlyExpandedContact$ | async }"
  ></ng-container>
  <ng-container *ngSwitchCase="'error'" [ngTemplateOutlet]="errorState"></ng-container>
  <ng-container *ngSwitchCase="'empty'" [ngTemplateOutlet]="emptyState"></ng-container>
</ng-container>

<ng-template #contactList let-contacts="contacts" let-selectedContact="selectedContact">
  <ng-container *ngIf="contacts.length !== 0; else emptyState">
    <mat-accordion>
      <mat-expansion-panel
        *ngFor="let contact of contacts"
        (opened)="panelOpened(contact.contactId)"
        [expanded]="selectedContact === contact.contactId"
      >
        <mat-expansion-panel-header>
          <mat-panel-title>
            {{
              (contact | contactFullName)
                ? (contact | contactFullName)
                : contact.contactEmail
                ? contact.contactEmail
                : (contact | contactFullPhoneNumber)
                ? (contact | contactFullPhoneNumber).formatInternational()
                : ('COMMON.NOT_APPLICABLE_LABEL' | translate)
            }}
            <mat-icon
              class="primary-contact-icon primary-color"
              inline
              *ngIf="(primaryContactId$ | async) === contact.contactId"
              [matTooltip]="'CONTACTS.PRIMARY_CONTACT' | translate"
            >
              star
            </mat-icon>
            <glxy-badge class="user-badge" *ngIf="contact.userId" color="green" size="small">
              {{ 'CONTACTS.USER_OF_BUSINESS_CENTER' | translate }}
            </glxy-badge>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <app-contact-item
            [contact]="contact"
            [accountGroupId]="accountGroupId"
            [primaryContactId]="primaryContactId$ | async"
          ></app-contact-item>
        </ng-template>
      </mat-expansion-panel>
    </mat-accordion>
  </ng-container>
</ng-template>

<ng-template #errorState>
  <glxy-empty-state class="empty-error-state">
    <glxy-empty-state-hero>
      <mat-icon>warning</mat-icon>
    </glxy-empty-state-hero>
    <p>{{ 'CONTACTS.CONTACT_LIST_ERROR' | translate }}</p>
  </glxy-empty-state>
</ng-template>

<ng-template #emptyState>
  <glxy-empty-state class="empty-error-state">
    <glxy-empty-state-hero>
      <mat-icon>person_add</mat-icon>
    </glxy-empty-state-hero>
    <p>{{ 'CONTACTS.EMPTY_CONTACTS_ADD_A_CONTACT' | translate }}</p>
    <glxy-empty-state-actions>
      <button mat-button color="primary" appAddContact [businessId]="accountGroupId">
        {{ 'CONTACTS.ADD_CONTACT_FORM_TITLE' | translate }}
      </button>
    </glxy-empty-state-actions>
  </glxy-empty-state>
</ng-template>
