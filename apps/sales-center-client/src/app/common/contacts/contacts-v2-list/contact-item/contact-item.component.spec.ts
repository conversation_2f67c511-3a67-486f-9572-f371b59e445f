import { <PERSON><PERSON>hange } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatExpansionModule } from '@angular/material/expansion';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Contact } from '@vendasta/sales-v2';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { Observable } from 'rxjs';
import { TestScheduler } from 'rxjs/testing';
import { LoggedInUserInfoService } from '../../../../logged-in-user-info';
import { ScreenshareService } from '../../../../screenshare';
import { AllContactFields } from '../../contact-field.enum';
import { ContactPipeModule } from '../../contact-pipe/contact-pipe.module';
import { ContactShadow } from '../../contact-v2';
import { ContactsV2Service } from '../../contacts-v2.service';
import { PrimaryContactService } from '../../primary-contact.service';
import { ContactItemComponent } from './contact-item.component';

jest.mock('../../../../logged-in-user-info', () => ({
  LoggedInUserInfoService: jest.fn(),
}));

class MockContactV2Service {}

class MockSnackbarService {}

class MockScreenshareService {}

class MockPrimaryService {
  getPrimaryContactForAccount$(): Observable<string> {
    throw new Error('unimplemented for test');
  }
}

const AGID = 'AG-123';
const USER_ID = 'U-123';
const testContact = new Contact({ contactEmail: '<EMAIL>', accountIds: [AGID], contactId: 'CO-123' });
const testContactShadow = new ContactShadow(testContact, AllContactFields, [AGID], USER_ID);

describe('ContactItemComponent', () => {
  let component: ContactItemComponent;
  let fixture: ComponentFixture<ContactItemComponent>;
  let scheduler: TestScheduler;

  beforeEach(async () => {
    const mockPrimaryService = new MockPrimaryService();
    await TestBed.configureTestingModule({
      declarations: [ContactItemComponent],
      imports: [
        MatExpansionModule,
        ContactPipeModule,
        TranslateTestingModule.withTranslations({}),
        NoopAnimationsModule,
      ],
      providers: [
        { provide: ContactsV2Service, useValue: new MockContactV2Service() },
        { provide: ScreenshareService, useValue: new MockScreenshareService() },
        { provide: SnackbarService, useValue: new MockSnackbarService() },
        { provide: PrimaryContactService, useValue: mockPrimaryService },
        { provide: LoggedInUserInfoService, useValue: new LoggedInUserInfoService(null, null, null) },
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    scheduler = new TestScheduler((actual, expected) => expect(actual).toEqual(expected));
    fixture = TestBed.createComponent(ContactItemComponent);
    component = fixture.componentInstance;
    component.contact = testContactShadow;
    component.accountGroupId = AGID;
  });

  afterEach(() => {
    scheduler.flush();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should update the isPrimaryContact behaviour subject when a new ID comes in', () => {
    component.primaryContactId = 'CO-123';
    fixture.autoDetectChanges();
    scheduler.schedule(() => {
      component.ngOnChanges({
        primaryContactId: new SimpleChange('CO-123', 'CO-124', true),
      });
    }, scheduler.createTime('--|'));

    scheduler.expectObservable(component.isPrimaryContact$).toBe('x-y', {
      x: true,
      y: false,
    });
  });

  it('should set isPrimaryContact to false if no ID is passed in', () => {
    fixture.autoDetectChanges();
    scheduler.expectObservable(component.isPrimaryContact$).toBe('x', {
      x: false,
    });
  });
});
