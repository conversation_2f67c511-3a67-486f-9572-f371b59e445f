@use 'design-tokens' as dt;

:host {
  display: block;
}

.container {
  display: flex;
  flex-direction: column;
}

.user-badge {
  margin-left: dt.$spacing-2;
}

.invalid-phone-icon {
  color: dt.$warn-icon-color;
}

.fixable-phone-icon {
  color: dt.$success-text-color;
}

.primary-color {
  color: dt.$warn-icon-color;
}

.primary-contact-icon {
  margin-left: dt.$spacing-2;
}

.phone-popover-item {
  -webkit-user-select: all;
  user-select: all;
  cursor: pointer;
  margin: dt.$spacing-2;
}

.info-item {
  margin-top: dt.$spacing-2;
  text-align: left;

  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  display: inline-flex;
  flex-direction: row;
  justify-content: flex-start;
  text-overflow: ellipsis;
}

.invalid-alert {
  margin-top: dt.$spacing-2;
}

.invalid-phone-button {
  width: auto;
  margin-right: dt.$spacing-2;
}
.fix-button {
  margin-top: dt.$spacing-2;
}

.icon {
  margin-right: dt.$spacing-2;
  color: dt.$icon-color;
}

.notes {
  margin: dt.$spacing-2;
  color: dt.$secondary-text-color;
}

.divider {
  margin-top: dt.$spacing-2;
  margin-bottom: dt.$spacing-2;
  width: 100%;
}

.mat-action-row {
  justify-content: flex-start;
}

.management-section {
  margin-top: dt.$spacing-2;
  display: inline-flex;
  justify-content: flex-start;

  .edit-button {
    button {
      margin: auto dt.$spacing-2;
    }
  }
}
