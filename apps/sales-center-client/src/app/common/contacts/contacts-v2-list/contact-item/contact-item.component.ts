import { Component, inject, Input, OnChanges, SimpleChanges, ViewChild, OnInit } from '@angular/core';
import { PopoverComponent } from '@vendasta/galaxy/popover';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { parsePhoneNumber, PhoneNumber } from 'libphonenumber-js';
import { BehaviorSubject, firstValueFrom } from 'rxjs';
import { shareReplay } from 'rxjs/operators';
import { LoggedInUserInfoService } from '../../../../logged-in-user-info';
import { ScreenshareRecipient, ScreenshareService } from '../../../../screenshare';
import { UserCreateAttributes } from '../../../user.service';
import { ContactField } from '../../contact-field.enum';
import { ContactShadow } from '../../contact-v2';
import { ContactsV2Service } from '../../contacts-v2.service';
import { PrimaryContactService } from '../../primary-contact.service';

@Component({
  selector: 'app-contact-item',
  templateUrl: './contact-item.component.html',
  styleUrls: ['./contact-item.component.scss'],
  standalone: false,
})
export class ContactItemComponent implements OnChanges, OnInit {
  @Input() contact: ContactShadow;
  @Input() accountGroupId: string;
  @Input() primaryContactId: string;
  @ViewChild('phonePopover', { static: false }) private readonly phoneNumberPopover: PopoverComponent;

  readonly partnerId$ = inject(LoggedInUserInfoService).partnerId$;

  readonly isPrimaryContact$ = new BehaviorSubject<boolean>(false);
  readonly settingPrimaryContact$ = new BehaviorSubject<boolean>(false);

  private readonly upgradingContact$$ = new BehaviorSubject<boolean>(false);
  readonly upgradingContact$ = this.upgradingContact$$.pipe(shareReplay(1));

  constructor(
    private readonly contactsService: ContactsV2Service,
    private readonly snackbarService: SnackbarService,
    private readonly screenshareService: ScreenshareService,
    private readonly primaryContactService: PrimaryContactService,
  ) {}

  ngOnInit(): void {
    this.isPrimaryContact$.next(this.primaryContactId === this.contact.contactId);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.primaryContactId && !!this.contact) {
      this.isPrimaryContact$.next(changes.primaryContactId.currentValue === this.contact.contactId);
    }
  }

  deleteContact(): void {
    firstValueFrom(this.contactsService.delete$(this.contact))
      .then(() => this.snackbarService.openSuccessSnack('CONTACTS.DELETE_DIALOG.CONTACT_DELETED'))
      .catch(() => this.snackbarService.openErrorSnack('CONTACTS.DELETE_DIALOG.CANNOT_DELETE_CONTACT'));
  }

  upgradeContactToUser(popover: PopoverComponent): void {
    this.upgradingContact$$.next(true);
    const userAttributes: UserCreateAttributes = {
      accountGroupId: this.accountGroupId,
      sendWelcomeEmail: true,
    };

    firstValueFrom(this.contactsService.upgradeContactToUser$(this.contact, userAttributes))
      .catch(() => this.snackbarService.openErrorSnack('CONTACTS.UPGRADE_CONTACT_FAILURE'))
      .then(() => this.snackbarService.openSuccessSnack('CONTACTS.CREATE_USER_SUCCESS'))
      .finally(() => {
        popover.isOpen = false;
        this.upgradingContact$$.next(false);
      });
  }

  shareScreen(): void {
    const recip = <ScreenshareRecipient>{
      email: this.contact.contactEmail,
      name: this.contact.fullName,
      phoneNumber: this.contact.phoneNumber,
      extension: this.contact.phoneExtension,
      countryCode: this.contact.phoneNumberCountry,
    };
    this.screenshareService.startScreenshare(recip, this.accountGroupId);
  }

  async setAsPrimaryContact(): Promise<void> {
    this.settingPrimaryContact$.next(true);
    firstValueFrom(this.primaryContactService.setPrimaryContactForAccount$(this.contact.contactId, this.accountGroupId))
      .then(
        () => this.snackbarService.openSuccessSnack('CONTACTS.CHANGE_PRIMARY_CONTACT_SUCCESS'),
        () => this.snackbarService.openErrorSnack('CONTACTS.FAILED_TO_CHANGE_PRIMARY_CONTACT'),
      )
      .finally(() => this.settingPrimaryContact$.next(false));
  }

  phoneNumRightClick(event: MouseEvent): void {
    event.preventDefault();
    this.phoneNumberPopover.isOpen = true;
  }

  async resendWelcomeEmail(): Promise<void> {
    firstValueFrom(this.contactsService.resendWelcomeEmailForContactUser$(this.contact.userId)).then(
      (success) =>
        success
          ? this.snackbarService.openSuccessSnack('CONTACTS.RESEND_WELCOME_EMAIL_SUCCESS')
          : this.snackbarService.openErrorSnack('CONTACTS.RESEND_WELCOME_EMAIL_ERROR'),
      () => this.snackbarService.openErrorSnack('CONTACTS.RESEND_WELCOME_EMAIL_ERROR'),
    );
  }

  async autoFixPhoneNumber(): Promise<void> {
    let parsedNumber: PhoneNumber;
    try {
      parsedNumber = parsePhoneNumber(this.contact.phoneNumber);
    } catch (error) {
      if (error === 'INVALID_COUNTRY') {
        this.snackbarService.openErrorSnack('CONTACTS.PHONE_NUMBER_UPDATE_ERROR_INVALID_COUNTRY');
      } else {
        this.snackbarService.openErrorSnack('CONTACTS.PHONE_NUMBER_UPDATE_ERROR_UNKNOWN');
      }
      return Promise.resolve();
    }

    this.contact.addValueChange(ContactField.PHONE_COUNTRY, parsedNumber.country);
    this.contact.addValueChange(ContactField.PHONE_NUMBER, parsedNumber.nationalNumber);

    return firstValueFrom(this.contactsService.update$(this.contact)).then(
      () => {
        this.snackbarService.openSuccessSnack('CONTACTS.PHONE_NUMBER_UPDATE_SUCCESS');
      },
      () => {
        this.snackbarService.openErrorSnack('CONTACTS.PHONE_NUMBER_UPDATE_ERROR');
      },
    );
  }
}
