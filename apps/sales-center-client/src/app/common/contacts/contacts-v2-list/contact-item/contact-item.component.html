<div class="container">
  <button mat-button disabled *ngIf="contact.title" class="info-item">
    <mat-icon matPrefix class="icon">work</mat-icon>
    <span>{{ contact.title }}</span>
  </button>

  <ng-container [ngTemplateOutlet]="emailAction"></ng-container>

  <ng-container [ngTemplateOutlet]="phoneNumberAction"></ng-container>
  <ng-container [ngTemplateOutlet]="upgradeToUser"></ng-container>

  <div class="management-section">
    <ng-container [ngTemplateOutlet]="editAction"></ng-container>
    <ng-container [ngTemplateOutlet]="moreOptionsMenu"></ng-container>
  </div>

  <ng-container *ngIf="contact.notes">
    <mat-divider class="divider"></mat-divider>
    <div class="notes">
      <span>{{ 'COMMON.NOTES_LABEL' | translate }}</span>
      <p>{{ contact.notes }}</p>
    </div>
  </ng-container>
</div>

<ng-template #setPrimaryAction>
  <button mat-menu-item [disabled]="isPrimaryContact$ | async" (click)="setAsPrimaryContact()">
    <ng-container *ngIf="(isPrimaryContact$ | async) === false; else alreadyPrimary">
      <mat-icon class="icon">star_outlined</mat-icon>
      <span>{{ 'CONTACTS.MAKE_PRIMARY_CONTACT' | translate }}</span>
    </ng-container>
  </button>
</ng-template>

<ng-template #alreadyPrimary>
  <mat-icon class="primary-color icon">star</mat-icon>
  <span>{{ 'CONTACTS.CURRENT_PRIMARY_CONTACT' | translate }}</span>
</ng-template>

<ng-template #upgradeToUser>
  <button
    class="info-item"
    mat-button
    *ngIf="!contact.userId"
    [glxyPopover]="upgradeToUserPopover"
    (click)="upgradeToUserPopover.isOpen = true"
  >
    <mat-icon class="icon">person_add</mat-icon>
    <span>{{ 'CONTACTS.UPGRADE_CONTACTS' | translate }}</span>
  </button>
</ng-template>

<glxy-popover
  #upgradeToUserPopover
  [hasBackdrop]="true"
  (backdropClick)="upgradeToUserPopover.isOpen = false"
  [showBackdrop]="true"
>
  <glxy-popover-title>{{ 'CONTACTS.UPGRADE_CONTACTS' | translate }}</glxy-popover-title>

  <div
    *ngIf="contact.contactEmail"
    [innerHtml]="'CONTACTS.UPGRADE_CONTACT_CONFIRM' | translate: { name: (contact | contactFullName) }"
  ></div>
  <div *ngIf="!contact.contactEmail">{{ 'CONTACTS.UPGRADE_CONTACT_EXCEPTION' | translate }}</div>

  <glxy-popover-actions>
    <button
      class="info-item"
      mat-button
      color="primary"
      (click)="
        contact.contactEmail ? upgradeContactToUser(upgradeToUserPopover) : (upgradeToUserPopover.isOpen = false)
      "
    >
      <glxy-button-loading-indicator [isLoading]="(upgradingContact$ | async) === true">
        {{ (contact.contactEmail ? 'CONTACTS.UPGRADE_CONTACTS' : 'COMMON.ACTION_LABELS.CLOSE') | translate }}
      </glxy-button-loading-indicator>
    </button>
  </glxy-popover-actions>
</glxy-popover>

<ng-template #editAction>
  <button
    mat-stroked-button
    color="primary"
    class="edit"
    appEditContact
    [contactToEdit]="contact"
    [businessId]="accountGroupId"
  >
    <mat-icon>edit</mat-icon>
    {{ 'COMMON.ACTION_LABELS.EDIT' | translate }}
  </button>
</ng-template>

<ng-template #phoneNumberAction>
  <ng-container
    *ngIf="contact.phoneNumber && !(contact | contactFullPhoneNumber)"
    [ngTemplateOutlet]="invalidPhoneNumber"
  ></ng-container>
  <ng-container *ngIf="contact | contactFullPhoneNumber as phoneNumber">
    <a
      #phoneNumberLink
      mat-button
      class="info-item"
      [href]="phoneNumber.getURI()"
      (contextmenu)="phoneNumRightClick($event)"
      [glxyPopover]="phonePopover"
    >
      <mat-icon matPrefix>call</mat-icon>
      <span>
        {{ phoneNumber.formatInternational() }}
      </span>
    </a>
    <glxy-popover #phonePopover [hasBackdrop]="true" (backdropClick)="phonePopover.isOpen = false">
      <div class="phone-popover-item" *ngIf="contact.phoneExtension">
        {{ (contact | contactFullPhoneNumber: true).formatInternational() }}
      </div>
      <div class="phone-popover-item">
        {{ phoneNumber.formatInternational() }}
      </div>
    </glxy-popover>
  </ng-container>
</ng-template>

<ng-template #emailAction>
  <button
    mat-button
    class="info-item"
    *ngIf="contact.contactEmail"
    [copyToClipBoardAndAlert]="{ email: contact.contactEmail }"
  >
    <mat-icon matPrefix class="icon">mail</mat-icon>
    <span>{{ contact.contactEmail }}</span>
  </button>
</ng-template>

<ng-template #invalidPhoneNumber>
  <glxy-alert
    class="invalid-alert"
    *ngIf="!(contact | isContactPhoneFixable)"
    type="warning"
    [showAction]="true"
    actionTitle="Edit"
    appEditContact
    [contactToEdit]="contact"
    [businessId]="accountGroupId"
  >
    {{ contact.phoneNumber }}
    <glxy-alert-extended>
      <span [innerHtml]="'FORMS.PHONE_ERRORS.BAD_DATA_DETAIL' | translate"></span>
    </glxy-alert-extended>
  </glxy-alert>
  <ng-container
    *ngIf="contact | isContactPhoneFixable as fixableNumber"
    [ngTemplateOutlet]="fixableNumberTemplate"
    [ngTemplateOutletContext]="{ fixableNumber: fixableNumber }"
  ></ng-container>
</ng-template>

<ng-template #fixableNumberTemplate let-fixableNumber="fixableNumber">
  <glxy-alert
    class="invalid-alert"
    type="warning"
    [showAction]="true"
    actionTitle="Fix"
    (actionClick)="autoFixPhoneNumber()"
  >
    {{ fixableNumber.formatInternational() }}
    <glxy-alert-extended>
      <span [innerHtml]="'FORMS.PHONE_ERRORS.AUTO_FIX_DETAIL' | translate"></span>
    </glxy-alert-extended>
  </glxy-alert>
</ng-template>

<ng-template #moreOptionsMenu>
  <button mat-icon-button color="primary" class="edit" [matMenuTriggerFor]="moreMenu" [glxyPopover]="deletePopover">
    <mat-icon>more_vert</mat-icon>
  </button>
  <mat-menu #moreMenu>
    <button mat-menu-item *ngIf="contact.userId" (click)="resendWelcomeEmail()">
      <mat-icon>send</mat-icon>
      <span>{{ 'CONTACTS.RESEND_WELCOME_EMAIL' | translate }}</span>
    </button>
    <ng-container [ngTemplateOutlet]="deleteAction"></ng-container>
    <ng-container [ngTemplateOutlet]="setPrimaryAction"></ng-container>
    <button
      mat-menu-item
      class="edit-button"
      *ngIf="contact.userId"
      [routerLink]="[
        '',
        {
          outlets: {
            action: [
              'partnerId',
              partnerId$ | async,
              'custom-fields',
              'user',
              'object',
              contact.userId,
              'disableManage',
              true
            ]
          }
        }
      ]"
    >
      <mat-icon>edit</mat-icon>
      <span>{{ 'FRONTEND.SALES_UI.EDIT_CUSTOM_FIELDS' | translate }}</span>
    </button>
  </mat-menu>
</ng-template>

<ng-template #deleteAction>
  <button mat-menu-item color="warn" (click)="deletePopover.isOpen = true">
    <mat-icon>delete</mat-icon>
    <span>{{ 'COMMON.ACTION_LABELS.DELETE' | translate }}</span>
  </button>
</ng-template>

<glxy-popover #deletePopover [hasBackdrop]="true" [showBackdrop]="true" (backdropClick)="deletePopover.isOpen = false">
  <glxy-popover-title>{{ 'CONTACTS.DELETE_DIALOG.TITLE' | translate }}</glxy-popover-title>
  <div [innerHtml]="'CONTACTS.DELETE_DIALOG.DESCRIPTION' | translate: { name: (contact | contactFullName) }"></div>
  <glxy-popover-actions>
    <button mat-stroked-button color="warn" (click)="deleteContact()">
      {{ 'CONTACTS.DELETE_CONTACT' | translate }}
    </button>
  </glxy-popover-actions>
</glxy-popover>
