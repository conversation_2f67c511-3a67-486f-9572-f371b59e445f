import { Inject, Injectable } from '@angular/core';
import { PartnerMarket } from '@galaxy/types';
import { Contact, ContactCreateAttributesInterface, ContactsService } from '@vendasta/sales-v2';
import { Observable, combineLatest, of } from 'rxjs';
import { map, shareReplay, switchMap, take, withLatestFrom } from 'rxjs/operators';
import { USER_PARTNER_MARKET_TOKEN } from '../../core/feature-flag.service';
import { UserCreateAttributes, UserResult, VBCUserService } from '../user.service';
import { AllContactFields, ContactApiMask, ContactField } from './contact-field.enum';
import { ContactList } from './contact-list';
import { ContactShadow } from './contact-v2';

const DEFAULT_CACHE_TTL: Duration = { seconds: 30 };

interface ListOptions {
  accountGroupId: string;
  fields: ContactField[];

  /** The duration a ContactList should count as a cache hit.
   *  If undefined, it will always count as a cache miss.
   * */
  cacheTtl?: Duration;
}

function findContactUserMatch(c: Contact, users: UserResult[]): [contact: Contact, userId: string | undefined] {
  const possibleMatch = users.find((r) => safeEmailToLowerCase(r.email) === safeEmailToLowerCase(c.contactEmail));
  if (!c.contactEmail || !possibleMatch?.email) {
    return [c, undefined];
  }

  return [c, possibleMatch?.userId];
}

function safeEmailToLowerCase(email: string | undefined | null): string {
  return (email || '').toLowerCase();
}

@Injectable({
  providedIn: 'root',
})
export class ContactsV2Service {
  private readonly listState = new Map<string, Observable<ContactList>>();

  constructor(
    private readonly api: ContactsService,
    @Inject(USER_PARTNER_MARKET_TOKEN) private readonly partnerMarket$: Observable<PartnerMarket>,
    @Inject(VBCUserService) private readonly users: VBCUserService,
  ) {}

  create$(accountGroupId: string, contactAttributes: ContactCreateAttributesInterface): Observable<ContactShadow> {
    const contact$ = this.partnerMarket$.pipe(
      switchMap((pm) => this.api.create(pm.partnerId, pm.marketId, [accountGroupId], contactAttributes)),
    );
    const contactShadow$ = contact$.pipe(
      map((contact) => new ContactShadow(contact, AllContactFields, [accountGroupId])),
    );

    return contactShadow$.pipe(
      withLatestFrom(
        this.listState.get(accountGroupId) ??
          this.listWork$({ accountGroupId: accountGroupId, fields: [...AllContactFields] }),
      ),
      switchMap(([contactShadow]) => {
        const lists$ = this.getContactListInstances$(contactShadow);
        return combineLatest([lists$, of(contactShadow)]);
      }),
      map(([lists, contact]) => {
        lists.forEach((list) => {
          list.add(contact);
        });
        return contact;
      }),
      take(1),
    );
  }

  upgradeContactToUser$(contact: ContactShadow, userAttributes: UserCreateAttributes): Observable<ContactShadow> {
    const newUser$ = this.partnerMarket$.pipe(
      switchMap((pm) => this.users.createUser(contact.baseContact, userAttributes, pm.partnerId)),
    );

    return combineLatest([this.getContactListInstances$(contact), newUser$]).pipe(
      map(([lists, newUser]) => {
        const updatedContact = new ContactShadow(
          contact.baseContact,
          AllContactFields,
          [...contact.accountGroupIds],
          newUser.userId,
        );
        lists.forEach((list) => list.update(updatedContact));
        return updatedContact;
      }),
    );
  }

  update$(contact: ContactShadow): Observable<void> {
    const updatedContact = <Contact>{ ...contact.baseContact };
    const updatedFields: string[] = [];

    contact.updates.forEach((value, key) => {
      updatedContact[key] = value;
      updatedFields.push(ContactApiMask[key]);
    });

    return this.partnerMarket$.pipe(
      switchMap((pm) => this.api.update(pm.partnerId, pm.marketId, updatedContact, updatedFields)),
      switchMap(() => this.getContactListInstances$(contact)),
      map((lists) =>
        lists.forEach((list) =>
          list.update(
            new ContactShadow(updatedContact, contact.knownFields, [...contact.accountGroupIds], contact.userId),
          ),
        ),
      ),
      take(1),
    );
  }

  delete$(contact: ContactShadow): Observable<void> {
    return this.partnerMarket$.pipe(
      switchMap((pm) => this.api.delete(pm.partnerId, pm.marketId, contact.contactId)),
      switchMap(() => this.getContactListInstances$(contact)),
      map((lists) => lists.forEach((list) => list.delete(contact))),
      take(1),
    );
  }

  private listWork$({ accountGroupId, cacheTtl, fields }: ListOptions): Observable<ContactList> {
    const fieldMask = fields.map((field) => ContactApiMask[field]);
    const work$ = this.api.listContactsForBusiness(accountGroupId, fieldMask).pipe(
      map((contacts) => (contacts ?? []).map((c) => new Contact(c))),
      switchMap((contacts) => this.associateUsersToContacts$(accountGroupId, contacts)),
      map((associations) =>
        associations.map(([contact, userId]) => new ContactShadow(contact, fields, [accountGroupId], userId)),
      ),
      switchMap((work) => contactList.pipe(map((list): ContactList => list.set(work, cacheTtl)))),
    );

    let contactList = this.listState.get(accountGroupId);
    if (contactList === undefined) {
      const list = new ContactList();
      list.set([]);

      contactList = of(list).pipe(shareReplay());
      this.listState.set(accountGroupId, contactList);

      return contactList.pipe(switchMap(() => work$));
    }

    return contactList.pipe(switchMap((list) => (list.cacheExpired ? work$ : contactList)));
  }

  list$(accountGroupId: string, ...fields: ContactField[]): Observable<ContactShadow[]>;
  list$(options: ListOptions): Observable<ContactShadow[]>;
  list$(accountGroupIdOrOptions: string | ListOptions, ...fields: ContactField[]): Observable<ContactShadow[]> {
    const options: ListOptions =
      typeof accountGroupIdOrOptions === 'string'
        ? {
            accountGroupId: accountGroupIdOrOptions,
            fields: fields,
            cacheTtl: DEFAULT_CACHE_TTL,
          }
        : accountGroupIdOrOptions;

    return this.listWork$(options).pipe(
      switchMap((list) => list.contacts$),
      shareReplay(1),
    );
  }

  private getContactListInstances$(contact: ContactShadow): Observable<ContactList[]> {
    const agids = [...contact.accountGroupIds];
    const contactLists = agids.map((ag: string) => this.listState.get(ag));
    const filteredContactLists = contactLists.filter((list$) => list$ !== undefined);

    return combineLatest(filteredContactLists);
  }

  private associateUsersToContacts$(
    accountGroupId: string,
    contacts: Contact[],
  ): Observable<[contact: Contact, userId: string][]> {
    if (contacts.length === 0) {
      return of([]);
    }

    return this.partnerMarket$.pipe(
      switchMap((pm) => this.users.getUsersForAccount(pm.partnerId, accountGroupId)),
      map((userResults) => contacts.map((c) => findContactUserMatch(c, userResults))),
    );
  }

  resendWelcomeEmailForContactUser$(contactUserId: string): Observable<boolean> {
    return this.partnerMarket$.pipe(
      switchMap((pm) => this.api.resendUserWelcomeEmail(pm.partnerId, contactUserId)),
      map((result) => result.success),
    );
  }
}
