<mat-list>
  <mat-list-item
    class="actionable"
    *ngIf="!!contact.phoneNumber"
    appClickToCallNow
    [callNowInfo]="{
      phoneNumber: contact.phoneNumber,
      extension: contact.phoneExtension
    }"
  >
    <mat-icon>phone</mat-icon>
    {{ contact.phoneNumber }}
  </mat-list-item>
  <mat-list-item class="actionable" *ngIf="!!contact.email" [copyToClipBoardAndAlert]="{ email: contact.email }">
    <mat-icon>email</mat-icon>
    {{ contact.email }}
  </mat-list-item>
</mat-list>
<app-contact-notes [contact]="contact"></app-contact-notes>
