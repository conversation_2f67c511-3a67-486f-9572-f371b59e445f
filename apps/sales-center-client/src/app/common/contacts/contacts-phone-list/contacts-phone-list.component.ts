import { Component, Inject, Input, OnInit } from '@angular/core';
import { Observable, ReplaySubject } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { UiBusiness } from '../../../manage-accounts/manage-accounts-ui.service';
import { AllContactFields } from '../contact-field.enum';
import { ContactShadow } from '../contact-v2';
import { ContactsV2Service } from '../contacts-v2.service';

@Component({
  selector: 'app-contacts-phone-list',
  templateUrl: './contacts-phone-list.component.html',
  styleUrls: ['./contacts-phone-list.component.scss'],
  standalone: false,
})
export class ContactsPhoneListComponent implements OnInit {
  contacts$: Observable<ContactShadow[]>;
  accountGroupId$$ = new ReplaySubject<string>(1);
  accountGroupId$: Observable<string> = this.accountGroupId$$;
  @Input() set business(account: UiBusiness) {
    this.accountGroupId$$.next(account.accountGroupId);
  }
  constructor(@Inject(ContactsV2Service) private readonly contactsService: ContactsV2Service) {}
  ngOnInit(): void {
    this.contacts$ = this.accountGroupId$.pipe(
      switchMap((accountGroupId) => this.contactsService.list$(accountGroupId, ...AllContactFields)),
    );
  }
}
