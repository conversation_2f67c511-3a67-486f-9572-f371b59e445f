<ng-container *ngIf="contacts$ | glxyAsyncStatus | async as obs" [ngSwitch]="obs.status">
  <ng-container *ngSwitchCase="'loaded'">
    <ng-container *ngTemplateOutlet="phoneContactsSubMenu; context: { $implicit: obs.value }"></ng-container>
  </ng-container>
  <ng-container *ngSwitchCase="'loading'">
    <ng-container *ngTemplateOutlet="loading"></ng-container>
  </ng-container>
  <ng-container *ngSwitchCase="'empty'">
    <ng-container *ngTemplateOutlet="noContacts"></ng-container>
  </ng-container>
  <ng-container *ngSwitchCase="'error'">
    <ng-container *ngTemplateOutlet="errorRetrievingContacts"></ng-container>
  </ng-container>
</ng-container>

<ng-template #phoneContactsSubMenu let-contacts>
  <ng-container *ngIf="accountGroupId$ | async as accountGroupId">
    <mat-action-list dense>
      <mat-list-item *ngFor="let contact of contacts">
        <ng-container *ngIf="contact.phoneNumber">
          <div
            appClickToCallNow
            [callNowInfo]="{
              phoneNumber: contact.phoneNumber,
              extension: contact.extension
            }"
            class="contacts-phone-list--with-phone"
          >
            <div class="contacts-phone-list__mat-list-item--name" matLine>
              {{ contact | contactFullName }}
            </div>
            <div class="contacts-phone-list__mat-list-item--phone">
              {{ contact.phoneNumber }}
              <ng-container *ngIf="contact.phoneExtension">
                {{ contact.phoneExtension }}
              </ng-container>
            </div>
          </div>
        </ng-container>
        <ng-container *ngIf="!contact.phoneNumber">
          <div
            appEditContact
            [contactToEdit]="contact"
            [businessId]="accountGroupId"
            class="contacts-phone-list--without-phone"
          >
            <div class="contacts-phone-list__mat-list-item--name" matLine>
              {{ contact | contactFullName }}
            </div>
            <div class="contacts-phone-list__mat-list-item--phone">
              {{ 'CONTACTS.ADD_PHONE_NUMBER_TO_CONTACT' | translate }}
            </div>
          </div>
        </ng-container>
      </mat-list-item>
    </mat-action-list>
  </ng-container>
</ng-template>

<ng-template #loading>
  <button mat-menu-item disabled>
    {{ 'ACCOUNT_ANALYTICS.COLUMNS.LOADING_CONTACTS' | translate }}
  </button>
</ng-template>

<ng-template #noContacts>
  <button appAddContact [businessId]="accountGroupId$ | async" mat-menu-item>
    {{ 'CONTACTS.EMPTY_CONTACTS_ADD_A_CONTACT' | translate }}
  </button>
</ng-template>

<ng-template #errorRetrievingContacts>
  <button mat-menu-item>
    {{ 'CONTACTS.CONTACT_LIST_ERROR' | translate }}
  </button>
</ng-template>
