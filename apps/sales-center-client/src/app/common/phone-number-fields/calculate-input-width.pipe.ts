/**
 * Gets the width of the text in pixels. Used to keep inputs in multi-input form fields
 * a consistent width apart. This is a workaround for the fact that the browser
 * doesn't know how wide the text will be until it's rendered.
 *
 * See: https://stackoverflow.com/questions/118241/calculate-text-width-with-javascript/21015393#21015393
 */
import { Pipe, PipeTransform } from '@angular/core';

function getCssStyle(element: HTMLElement, prop: string): string {
  return window.getComputedStyle(element, null).getPropertyValue(prop);
}

function getCanvasFont(el = document.body): string {
  const fontWeight = getCssStyle(el, 'font-weight') || 'normal';
  const fontSize = getCssStyle(el, 'font-size') || '16px';
  const fontFamily = getCssStyle(el, 'font-family') || 'Times New Roman';

  return `${fontWeight} ${fontSize} ${fontFamily}`;
}

export class InputWidthCalculator {
  private static canvas: HTMLCanvasElement;
  private context: CanvasRenderingContext2D;

  constructor() {
    InputWidthCalculator.canvas = InputWidthCalculator.canvas ?? document.createElement('canvas');
    const ctx = InputWidthCalculator.canvas.getContext('2d');
    if (!ctx) {
      throw new Error('Could not get canvas context');
    }
    this.context = ctx;
  }

  /**
   * Uses canvas.measureText to compute and return the width of the given text of given font in pixels.
   *
   * @param {String} text The text to be rendered.
   * @param {String} el The element hosting the text.
   *
   */
  public calculate(text: string, el: HTMLElement): number {
    this.context.font = getCanvasFont(el);
    const metrics = this.context.measureText(text);
    return metrics.width;
  }
}

@Pipe({
  name: 'calcInputWidth',
  standalone: false,
})
export class CalculateInputWidthPipe implements PipeTransform {
  private readonly _inputWidthCalculator = new InputWidthCalculator();

  transform(value: string, el?: HTMLElement): number {
    return this._inputWidthCalculator.calculate(value, el ?? document.body);
  }
}
