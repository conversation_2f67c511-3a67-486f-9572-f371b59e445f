import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PhoneInputComponent } from './phone-input/phone-input.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { countryOptionsFactoryProvider } from './phone-input/country-option';
import { CountryCodeService } from '@vendasta/galaxy/utility/country-codes/src/country-code.service';
import { PhoneInputValidatorDirective, PhoneExtensionValidatorDirective } from './phone-number-validator.directive';
import { PhoneNumberFieldsComponent } from './phone-number-fields.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TranslateModule } from '@ngx-translate/core';
import { MatInputModule } from '@angular/material/input';
import { CalculateInputWidthPipe } from './calculate-input-width.pipe';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';

@NgModule({
  declarations: [
    PhoneInputComponent,
    PhoneInputValidatorDirective,
    PhoneExtensionValidatorDirective,
    PhoneNumberFieldsComponent,
    CalculateInputWidthPipe,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatInputModule,
    MatFormFieldModule,
    MatAutocompleteModule,
    TranslateModule,
    GalaxyFormFieldModule,
  ],
  exports: [
    PhoneInputComponent,
    PhoneInputValidatorDirective,
    PhoneExtensionValidatorDirective,
    PhoneNumberFieldsComponent,
    CalculateInputWidthPipe,
  ],
  providers: [countryOptionsFactoryProvider, CountryCodeService],
})
export class PhoneNumberFieldsModule {}
