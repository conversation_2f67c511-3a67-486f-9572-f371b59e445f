import { Directive, forwardRef } from '@angular/core';
import { AbstractControl, NG_VALIDATORS, ValidationErrors, Validator, ValidatorFn } from '@angular/forms';
import { isValidPhoneNumber } from 'libphonenumber-js';
import { PhoneInputValue } from './phone-input/phone-input.interface';

export enum PHONE_INPUT_ERRORS {
  MISSING_COUNTRY_CODE = 'missingCountryCode',
  INVALID_NUMBER_FOR_COUNTRY = 'invalidNumberForCountry',
  INVALID_NUMBER_FOR_EXTENSION = 'invalidNumberForExtension',
  INVALID_EXTENSION = 'invalidExtension',
}

/**
 * A validator for the `PhoneNumberInput` that validates the phone number and country code.
 */
export const phoneInputValidator: ValidatorFn = ({
  value,
}: AbstractControl<PhoneInputValue>): ValidationErrors | null => {
  if (!value?.phoneNumber) return null;
  if (!value.countryCode) return { [PHONE_INPUT_ERRORS.MISSING_COUNTRY_CODE]: true };
  if (!isValidPhoneNumber(value.phoneNumber, value.countryCode)) {
    return { [PHONE_INPUT_ERRORS.INVALID_NUMBER_FOR_COUNTRY]: true };
  }

  return null;
};

/**
 * A validator directive for the `PhoneNumberInput` that validates the phone number and country code for
 * template-driven forms.
 */
@Directive({
  selector: '[appPhoneInputValidator]',
  providers: [
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => PhoneInputValidatorDirective),
      multi: true,
    },
  ],
  standalone: false,
})
export class PhoneInputValidatorDirective implements Validator {
  validate(control: AbstractControl<PhoneInputValue>): ValidationErrors | null {
    return phoneInputValidator(control);
  }
}

export const extensionValidator = (control: AbstractControl<PhoneInputValue>) => {
  if (!control.value) return null;
  const { phoneNumber, extension } = control.value;
  if (!phoneNumber && extension?.length > 0) {
    return { [PHONE_INPUT_ERRORS.INVALID_NUMBER_FOR_EXTENSION]: true };
  }
  if (!extension || !phoneNumber) {
    return null;
  }
  if (RegExp(/^\d+$/).test(<string>extension) === false) {
    return { [PHONE_INPUT_ERRORS.INVALID_EXTENSION]: true };
  }
  return null;
};

/**
 * A validator directive for the `PhoneNumberFieldsComponent` that validates the phone number and country code for
 * template-driven forms.
 */
@Directive({
  selector: '[appPhoneExtensionValidator]',
  providers: [
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => PhoneExtensionValidatorDirective),
      multi: true,
    },
  ],
  standalone: false,
})
export class PhoneExtensionValidatorDirective implements Validator {
  validate(control: AbstractControl<PhoneInputValue>): ValidationErrors | null {
    return extensionValidator(control);
  }
}
