<div class="phone-fields">
  <glxy-form-field class="phone-input" bottomSpacing="small">
    <glxy-label>{{ 'FORMS.PHONE_NUMBER_PLACEHOLDER' | translate }}</glxy-label>

    <app-phone-input insideInputWrapper [formControl]="form.controls.phone" (input)="handleInput()" />

    <glxy-error *ngIf="form.controls.phone.invalid && (form.controls.phone.dirty || form.controls.phone.touched)">
      <span *ngIf="form.controls.phone.hasError(PHONE_ERRORS.MISSING_COUNTRY_CODE)">
        {{ 'FORMS.PHONE_NUMBER_MISSING_COUNTRY' | translate }}
      </span>
      <span *ngIf="form.controls.phone.hasError(PHONE_ERRORS.INVALID_NUMBER_FOR_COUNTRY)">
        {{ 'FORMS.PHONE_INVALID_FOR_REGION' | translate }}
      </span>
      <span *ngIf="form.controls.phone.hasError(PHONE_ERRORS.INVALID_NUMBER_FOR_EXTENSION)">
        {{ 'FORMS.PHONE_EXTENSION_ERROR' | translate }}
      </span>
      <span *ngIf="form.controls.phone.hasError(PHONE_ERRORS.INVALID_EXTENSION)">
        {{ 'FORMS.EXTENSION_ERROR' | translate }}
      </span>
    </glxy-error>
  </glxy-form-field>

  <glxy-form-field class="extension">
    <glxy-label>{{ 'FORMS.PHONE_EXTENSION_PLACEHOLDER' | translate }}</glxy-label>
    <input matInput [formControl]="form.controls.extension" maxlength="8" (input)="handleInput()" />
  </glxy-form-field>
</div>
