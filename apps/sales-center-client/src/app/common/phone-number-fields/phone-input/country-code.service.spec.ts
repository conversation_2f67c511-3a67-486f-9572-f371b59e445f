import { TestBed } from '@angular/core/testing';
import { TranslateService } from '@ngx-translate/core';

import { TranslateTestingModule } from 'ngx-translate-testing';
import { map } from 'rxjs/operators';
import { TestScheduler } from 'rxjs/testing';
import { CountryCodeService } from './country-code.service';
import { countryOptionsFactoryProvider } from './country-option';

describe('CountryCodeService', () => {
  let service: CountryCodeService;
  let translateService: TranslateService;
  let scheduler: TestScheduler;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [countryOptionsFactoryProvider, CountryCodeService],
      imports: [TranslateTestingModule.withTranslations('en', {})],
    });
    translateService = TestBed.inject(TranslateService);
    service = TestBed.inject(CountryCodeService);
    scheduler = new TestScheduler((actual, expected) => {
      expect(actual).toEqual(expected);
    });
  });

  afterEach(() => {
    scheduler.flush();
    window.localStorage.clear();
  });

  it('is created', () => {
    expect(service).toBeTruthy();
  });

  it('returns full country options every subscription to countryOptions$', () => {
    scheduler.run(({ expectObservable }) => {
      service.countryOptions$.subscribe();
      expectObservable(service.countryOptions$.pipe(map((options) => options.length > 0))).toBe('a', { a: true });
    });
  });

  it('updates country options when language changes and re-emits countries when update changes', () => {
    scheduler.run(({ expectObservable }) => {
      const result = service.countryOptions$.pipe(
        map((options) => options.find((option) => option.countryCode === 'US')?.countryName),
      );

      scheduler.schedule(() => {
        translateService.use('fr');
      }, scheduler.createTime('---|'));

      expectObservable(result).toBe('a--b', { a: 'United States', b: 'États-Unis' });
    });
  });

  it('sorts country options by usage', () => {
    scheduler.run(({ expectObservable }) => {
      const result = service.countryOptions$.pipe(
        map((options) =>
          options
            .filter((option) => ['CA', 'AF', 'US'].includes(option.countryCode))
            .slice(0, 3)
            .map((option) => option.countryCode),
        ),
      );

      scheduler.schedule(() => {
        service.countryCodeUsed('US');
      }, scheduler.createTime('--|'));

      scheduler.schedule(() => {
        service.countryCodeUsed('CA');
      }, scheduler.createTime('----|'));

      expectObservable(result).toBe('a-b-c', { a: ['AF', 'CA', 'US'], b: ['US', 'AF', 'CA'], c: ['US', 'CA', 'AF'] });
    });
  });
});
