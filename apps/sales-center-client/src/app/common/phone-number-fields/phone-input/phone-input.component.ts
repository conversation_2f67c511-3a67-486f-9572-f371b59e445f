import { FocusMonitor } from '@angular/cdk/a11y';
import { BooleanInput, coerceBooleanProperty } from '@angular/cdk/coercion';
import { Component, ElementRef, HostBinding, HostListener, Input, Optional, Self, ViewChild } from '@angular/core';
import {
  AbstractControl,
  ControlValueAccessor,
  FormControl,
  FormGroup,
  NgControl,
  ValidationErrors,
  ValidatorFn,
} from '@angular/forms';
import { MatAutocompleteActivatedEvent, MatAutocompleteTrigger } from '@angular/material/autocomplete';
import { MatFormFieldControl } from '@angular/material/form-field';
import { CountryCode, PhoneNumber, isValidNumberForRegion, parsePhoneNumber } from 'libphonenumber-js';
import { Observable, Subject, combineLatest, firstValueFrom } from 'rxjs';
import { map, shareReplay, startWith } from 'rxjs/operators';
import { InputWidthCalculator } from '../calculate-input-width.pipe';
import { CountryCodeService } from '@vendasta/galaxy/utility/country-codes';
import { CountryOption, isCountryOption } from './country-option';
import { PhoneInputValue } from './phone-input.interface';

@Component({
  selector: 'app-phone-input',
  templateUrl: './phone-input.component.html',
  styleUrls: ['./phone-input.component.scss'],
  providers: [{ provide: MatFormFieldControl, useExisting: PhoneInputComponent }],
  standalone: false,
})
export class PhoneInputComponent implements ControlValueAccessor, MatFormFieldControl<PhoneInputValue> {
  private static nextId = 0;

  /**
   * PhoneInputComponent-specific fields
   */
  @ViewChild('phoneInput') phoneInputElement: ElementRef;
  @ViewChild('countryInput') countryInputElement: ElementRef;
  @ViewChild(MatAutocompleteTrigger) autocomplete: MatAutocompleteTrigger;

  readonly form = new FormGroup({
    countryOption: new FormControl<CountryOption | null>(null, [this.countryCodeValidator()]),
    countryCode: new FormControl<CountryCode | null>(null),
    // This is the unformatted number that is sent to the server.
    phoneNumber: new FormControl(''),
    phoneInput: new FormControl('', [this.phoneNumberValidator()]),
  });

  readonly filteredCountryOptions$: Observable<CountryOption[]>;
  readonly widthCalculator = new InputWidthCalculator();
  private preferredOption: CountryOption | null = null;
  showPanel: boolean;

  /**
   * Implement MatFormFieldControl interface
   */
  @Input()
  get disabled(): boolean {
    return this._disabled;
  }
  set disabled(value: BooleanInput) {
    this._disabled = coerceBooleanProperty(value);
    this._disabled ? this.form.disable() : this.form.enable();
    this.stateChanges$$.next();
  }
  private _disabled = false;

  @Input()
  get required(): boolean {
    return this._required;
  }
  set required(value: BooleanInput) {
    this._required = coerceBooleanProperty(value);
    this.stateChanges$$.next();
  }
  private _required = false;

  @Input()
  get value(): PhoneInputValue | null {
    return <PhoneInputValue>this.form.value;
  }
  set value(tel: PhoneInputValue | null) {
    const parse = tel?.phoneNumber ? this.safeParsePhoneNumber(tel?.phoneNumber, tel?.countryCode) : null;

    this.form.patchValue({
      phoneInput: parse?.formatNational() ?? tel?.phoneNumber,
      phoneNumber: tel?.phoneNumber,
      countryCode: tel?.countryCode,
      countryOption: tel?.countryCode ? this.countryCodeService.getCountryOption(tel?.countryCode) : null,
    });
    this.onChange(tel);
    this.stateChanges$$.next();
  }

  @Input()
  get placeholder() {
    return this._placeholder;
  }
  set placeholder(plh) {
    this._placeholder = plh;
    this.stateChanges$$.next();
  }
  private _placeholder: string;

  get empty(): boolean {
    const value = this.form.value;
    return !value.countryOption && !value.phoneInput;
  }

  get errorState(): boolean {
    return this.ngControl.errors !== null && !!this.ngControl.touched;
  }

  @HostBinding('[id]') id = `phone-control-${PhoneInputComponent.nextId++}`;
  @HostBinding('[class.floating]') get shouldLabelFloat(): boolean {
    return this.focused || !this.empty;
  }

  private readonly stateChanges$$ = new Subject<void>();
  readonly stateChanges: Observable<void> = this.stateChanges$$;
  focused: boolean;
  readonly controlType = 'phone-input';
  autofilled?: boolean;
  userAriaDescribedBy?: string;
  onChange: (value: PhoneInputValue | null) => void = () => ({});
  onTouched: () => void = () => ({});

  /**
   * PhoneInputComponent-specific methods
   */
  @HostListener('focusout', ['$event.relatedTarget']) onFocusOut(target: HTMLElement): void {
    // if target part of the component, do nothing
    if (this._elementRef.nativeElement.contains(target)) return;

    this.focused = false;
    this.onTouched();
    this.stateChanges$$.next();
  }

  constructor(
    private _focusMonitor: FocusMonitor,
    private _elementRef: ElementRef<HTMLElement>,
    private readonly countryCodeService: CountryCodeService,
    @Optional() @Self() public ngControl: NgControl,
  ) {
    if (this.ngControl != null) {
      this.ngControl.valueAccessor = this;
    }

    // Even though mat-autocomplete is set to activate onInit, it doesn't seem to fire off an activatedOption event
    // so we need to set the preferred option initially.
    firstValueFrom(this.countryCodeService.countryOptions$).then((options) => {
      this.preferredOption = options?.[0] ?? null;
    });

    // As the user types, we want to filter the country options. according to their input.
    const countryInput = this.form.controls.countryOption.valueChanges.pipe(startWith(''));
    this.filteredCountryOptions$ = combineLatest([this.countryCodeService.countryOptions$, countryInput]).pipe(
      map(([options, input]) => {
        if (!input) return options;
        if (typeof input === 'string') return this.filterCountries(input, options);
        return [input];
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  /**
   * updateControlState is called for any user input event. It re-evaluates the form to see if any inference can be
   * made about the country code or phone number and updates both the internal and external values with the new
   * information.
   */
  updateControlState(): void {
    const { phoneInput, countryOption } = this.form.value;
    const parse = this.inferPhoneNumber(phoneInput ?? '', countryOption);
    if (parse) this.updateInternalState(parse);

    // update external state
    this.form.patchValue({
      phoneNumber: <string>parse?.nationalNumber ?? phoneInput,
      countryCode: parse?.country || countryOption?.countryCode,
    });

    if (isCountryOption(countryOption)) {
      this.focusPhone();
    }
    this.onChange(this.value);
    this.stateChanges$$.next();
  }

  async countryInputChanged(mode?: 'complete'): Promise<void> {
    const { countryOption } = this.form.value;
    if (mode === 'complete' && !isCountryOption(countryOption)) {
      await this.autocompleteCountrySelection(countryOption ?? '');
    }
    this.updateControlState();
  }

  private async autocompleteCountrySelection(countryOption: string): Promise<void> {
    const options = await firstValueFrom(this.filteredCountryOptions$);
    const preferred = options.find((opt) => opt.countryCode === this.preferredOption?.countryCode) ?? null;
    if (preferred) this.countryCodeService.countryCodeUsed(preferred.countryCode);

    this.form.patchValue({
      countryOption: preferred,
      phoneInput: preferred ? this.form.value.phoneInput : countryOption.concat(this.form.value.phoneInput ?? ''),
    });
  }

  phoneInputChanged(): void {
    const { phoneInput } = this.form.value;

    // If user deletes one parenthesis, delete the other as a courtesy
    const sanitizedPhoneNumber = (phoneInput ?? '').replace(/[^\d()-]/g, '');
    const parenthesesPair = sanitizedPhoneNumber.match(/(\(\d*\))/g);
    if (!parenthesesPair) {
      this.form.patchValue({ phoneInput: sanitizedPhoneNumber.replace(/[()]/g, '') });
    }

    this.updateControlState();
  }

  private inferPhoneNumber(phoneInput: string, countryOption?: CountryOption | string | null): PhoneNumber | null {
    // no information to infer from
    if (!countryOption && !phoneInput) return null;

    // the user may be typing the full phone number in the country code field
    if (typeof countryOption === 'string') {
      const intlPhoneNumber = countryOption[0] !== '+' ? `+${countryOption}` : countryOption;
      return this.safeParsePhoneNumber(intlPhoneNumber);
    }

    // a country code has been selected, try to parse the phone number
    return this.safeParsePhoneNumber(phoneInput, countryOption?.countryCode);
  }

  // This updates the internal form control with a parsed phone number
  private updateInternalState(phoneNumber: PhoneNumber): void {
    if (!phoneNumber?.isValid()) return;
    const cc = phoneNumber.country ? this.countryCodeService.getCountryOption(phoneNumber.country) : null;
    if (cc) this.countryCodeService.countryCodeUsed(cc.countryCode);
    this.form.patchValue({
      countryOption: cc,
      phoneInput: phoneNumber.formatNational(),
    });
  }

  countryOptionActivated(option: MatAutocompleteActivatedEvent): void {
    this.preferredOption = option?.option?.value ?? null;
  }

  countryListDisplay(country: CountryOption): string {
    return country ? country.callingCode : '';
  }

  private filterCountries(value: CountryOption | string, countryOptions: CountryOption[]): CountryOption[] {
    const filterValue = isCountryOption(value) ? value.countryCode : value.toUpperCase();

    return countryOptions.filter(
      (option) =>
        option.countryCode.toUpperCase().includes(filterValue) ||
        option.callingCode.includes(filterValue) ||
        option.countryName.toUpperCase().startsWith(filterValue),
    );
  }

  private safeParsePhoneNumber(phoneNumber: string, countryCode?: CountryCode): PhoneNumber | null {
    if (!phoneNumber) return null;

    try {
      return parsePhoneNumber(phoneNumber, countryCode) ?? null;
    } catch (e) {
      return null;
    }
  }

  // if caret position is at the beginning of the input, return focus to the country-input
  returnToCountry($event: KeyboardEvent): void {
    if ($event.target instanceof HTMLInputElement === this.countryInputElement.nativeElement) return;
    if ($event.target instanceof HTMLInputElement && $event.target.selectionStart === 0) {
      // set caret position to end of input
      this.countryInputElement.nativeElement.selectionStart = this.countryInputElement.nativeElement.value.length;
      this.focusCountry();
    }
  }

  // if caret position is end of input, return focus to phone input
  jumpToPhone($event: KeyboardEvent): void {
    if ($event.target === this.phoneInputElement.nativeElement) return;
    if ($event.target instanceof HTMLInputElement && $event.target.selectionStart === $event.target.value.length) {
      this.focusPhone();
      // set caret position to beginning of input
      this.phoneInputElement.nativeElement.setSelectionRange(0, 0);
    }
  }

  focusCountry(): void {
    this.autocomplete.openPanel();
    this._focusMonitor.focusVia(this.countryInputElement, 'program');
  }

  focusPhone(): void {
    this._focusMonitor.focusVia(this.phoneInputElement, 'program');
    this.autocomplete.closePanel();
  }

  /**
   * Implement ControlValueAccessor interface
   */
  writeValue(value: PhoneInputValue): void {
    this.value = value;
  }
  registerOnChange(fn: (v: PhoneInputValue) => void): void {
    this.onChange = fn;
  }
  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }
  setDisabledState?(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  /**
   * Implement MatFormFieldControl interface
   */
  setDescribedByIds(ids: string[]): void {
    const controlElement =
      this._elementRef.nativeElement.querySelector('.ssc-phone-input') ?? this._elementRef.nativeElement;
    controlElement?.setAttribute('aria-describedby', ids.join(' '));
  }
  onContainerClick(_event: MouseEvent): void {
    this.focused = true;
    if (
      (_event.target === this.phoneInputElement?.nativeElement && this.form.value.phoneInput) ||
      _event.target === this.countryInputElement?.nativeElement
    ) {
      return;
    }
    // focus on the furthest left input that is empty or invalid
    const { phoneInput, countryOption } = this.form.value;
    if (!isCountryOption(countryOption)) {
      this.focusCountry();
      return;
    }
    if (!phoneInput || this.form.invalid) {
      this.focusPhone();
      // set caret to end of input
      this.phoneInputElement.nativeElement.setSelectionRange(
        this.phoneInputElement.nativeElement.value.length,
        this.phoneInputElement.nativeElement.value.length,
      );

      return;
    }
  }

  /**
   * Validators to validate the internal state of the form control. This is used to
   * automatically focus the correct input for the user.
   */
  countryCodeValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (isCountryOption(control.value)) return null;
      return { invalidCountryCode: true };
    };
  }

  phoneNumberValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!isCountryOption(this.form?.value?.countryOption)) return { invalidPhoneNumber: true };
      if (!control.value) return { invalidPhoneNumber: true };

      const isValid = isValidNumberForRegion(control.value, this.form.value.countryOption.countryCode);
      if (!isValid) return { invalidPhoneNumber: true };

      return null;
    };
  }
}
