import { Inject, Injectable } from '@angular/core';
import { CountryCode } from 'libphonenumber-js';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { CountryOption, PHONE_COUNTRY_OPTIONS_TOKEN } from './country-option';

const USED_COUNTRY_CODES = 'usedCountryCodes';

@Injectable({
  providedIn: 'root',
})
export class CountryCodeService {
  private readonly countryOptions$$ = new BehaviorSubject<CountryOption[]>([]);
  readonly countryOptions$: Observable<CountryOption[]>;

  constructor(@Inject(PHONE_COUNTRY_OPTIONS_TOKEN) _countryOptions$: Observable<CountryOption[]>) {
    _countryOptions$.subscribe(this.countryOptions$$);

    this.countryOptions$ = this.countryOptions$$.pipe(map((options) => this.sortCountryOptions(options)));
  }

  // Returns the country option that matches the given country code.
  getCountryOption(countryCode: CountryCode): CountryOption | null {
    return this.countryOptions$$.getValue().find((option) => option.countryCode === countryCode) ?? null;
  }

  private sortCountryOptions(countryOptions: CountryOption[]): CountryOption[] {
    const cc = this.getUsedCountryCodes();
    return countryOptions.sort((a, b) => {
      const aUsed = cc.includes(a.countryCode);
      const bUsed = cc.includes(b.countryCode);
      const bothUsed = aUsed && bUsed;
      const neitherUsed = !(aUsed || bUsed);

      if (aUsed && !bUsed) return -1;
      if (!aUsed && bUsed) return 1;
      if (bothUsed && cc.indexOf(a.countryCode) < cc.indexOf(b.countryCode)) return -1;
      if (bothUsed && cc.indexOf(a.countryCode) > cc.indexOf(b.countryCode)) return 1;
      if (neitherUsed && a.countryName < b.countryName) return -1;
      if (neitherUsed && a.countryName > b.countryName) return 1;

      return 0;
    });
  }

  // The service doesn't know about the input component, so it can't update the used country codes itself.
  countryCodeUsed(countryCode: CountryCode): void {
    const usedCountryCodes = this.getUsedCountryCodes();
    if (!usedCountryCodes.includes(countryCode)) {
      usedCountryCodes.push(countryCode);
      localStorage.setItem(USED_COUNTRY_CODES, JSON.stringify(usedCountryCodes));
      const newOptions = this.sortCountryOptions(this.countryOptions$$.getValue());
      this.countryOptions$$.next(newOptions);
    }

    // rearrange the used country codes so that the most recently used is at the start
    const index = usedCountryCodes.indexOf(countryCode);
    if (index > 0) {
      usedCountryCodes.splice(index, 1);
      usedCountryCodes.unshift(countryCode);
      localStorage.setItem(USED_COUNTRY_CODES, JSON.stringify(usedCountryCodes));
    }
  }

  private getUsedCountryCodes(): CountryCode[] {
    const usedCountryCodes = localStorage.getItem(USED_COUNTRY_CODES);
    return usedCountryCodes ? JSON.parse(usedCountryCodes) : [];
  }
}
