<div class="phone-control" role="group" [formGroup]="form">
  <div class="flag-icon" *ngIf="form.value?.countryOption?.flag" (click)="countryInput.select()">
    {{ form.value?.countryOption?.flag }}
  </div>
  <input
    class="country-code"
    [style.width]="(countryInput.value | calcInputWidth : countryInput) + 'px'"
    aria-label="Country"
    (input)="countryInputChanged()"
    (keydown.Tab)="countryInputChanged('complete')"
    (keyup.ArrowRight)="jumpToPhone($event)"
    [matAutocomplete]="auto"
    formControlName="countryOption"
    autocomplete="off"
    #countryInput
  />

  <input
    class="phone-number"
    formControlName="phoneInput"
    autocomplete="off"
    (input)="phoneInputChanged()"
    (keyup.backspace)="returnToCountry($event)"
    (keyup.ArrowLeft)="returnToCountry($event)"
    #phoneInput
  />

  <mat-autocomplete
    #auto
    [displayWith]="countryListDisplay"
    panelWidth="auto"
    (optionActivated)="countryOptionActivated($event)"
    (optionSelected)="countryInputChanged()"
    autoActiveFirstOption
    showPanel="true"
  >
    <mat-option *ngFor="let countryOption of filteredCountryOptions$ | async" [value]="countryOption">
      {{ countryOption.displayValue }}
    </mat-option>
  </mat-autocomplete>
</div>
