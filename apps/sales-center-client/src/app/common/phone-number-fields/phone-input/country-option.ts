import { TranslateService } from '@ngx-translate/core';
import { CountryCode, getCountries, getCountryCallingCode } from 'libphonenumber-js';
import { Observable } from 'rxjs';
import { map, shareReplay, startWith } from 'rxjs/operators';

export class CountryOption {
  readonly type = 'CountryOption' as const;
  countryCode: CountryCode;
  callingCode: string;
  flag: string;
  countryName: string;
  displayValue: string;
}

export function getCountryOptions(language: string): CountryOption[] {
  const countries = getCountries();

  const lang = language || 'en-US';
  const regionNames = new Intl.DisplayNames([lang], { type: 'region' });
  return countries.map((countryCode) => configureCountry(countryCode, regionNames));
}

function configureCountry(countryCode: CountryCode, regionNames: Intl.DisplayNames): CountryOption {
  // Explanation: https://github.com/vendasta/galaxy/pull/10512#discussion_r889339586
  const alphaToFlagAlpha = (a: string) => String.fromCodePoint(0x1f1a5 + a.toUpperCase().codePointAt(0));
  const countryFlag = countryCode.slice(0, 2).split('').map(alphaToFlagAlpha).join('');
  const callingCode = '+' + getCountryCallingCode(countryCode).toString();

  return {
    ...new CountryOption(),
    countryCode: countryCode,
    flag: countryFlag,
    callingCode: callingCode,
    countryName: regionNames.of(countryCode),
    displayValue: `${callingCode} (${countryFlag} ${regionNames.of(countryCode)})`,
  };
}

// define typeguard for CountryOption
export function isCountryOption(value: unknown): value is CountryOption {
  if (value === null || value === undefined) return false;
  return typeof value === 'object' && Object.keys(value).includes('type') && value['type'] === 'CountryOption';
}

export const PHONE_COUNTRY_OPTIONS_TOKEN = 'PHONE_COUNTRY_OPTIONS_TOKEN';
export const countryOptionsFactoryProvider = {
  provide: PHONE_COUNTRY_OPTIONS_TOKEN,
  useFactory: (translate: TranslateService): Observable<CountryOption[]> =>
    translate.onLangChange.pipe(
      startWith({ lang: translate.currentLang }),
      map((event) => getCountryOptions(event.lang)),
      shareReplay({ refCount: true, bufferSize: 1 }),
    ),
  deps: [TranslateService],
};
