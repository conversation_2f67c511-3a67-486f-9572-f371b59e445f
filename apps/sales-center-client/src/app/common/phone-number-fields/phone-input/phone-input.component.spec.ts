import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Form<PERSON>uilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { PhoneInputComponent } from './phone-input.component';
import { countryOptionsFactoryProvider } from './country-option';
import { Pipe, PipeTransform } from '@angular/core';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
jest.mock('../calculate-input-width.pipe.ts');

@Pipe({
  name: 'calcInputWidth',
  standalone: false,
})
class mockCalcPipe implements PipeTransform {
  transform = jest.fn(() => 0);
}

describe('PhoneInputComponent', () => {
  let component: PhoneInputComponent;
  let fixture: ComponentFixture<PhoneInputComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TranslateTestingModule.withTranslations({}), MatAutocompleteModule, FormsModule, ReactiveFormsModule],
      providers: [FormBuilder, countryOptionsFactoryProvider],
      declarations: [PhoneInputComponent, mockCalcPipe],
    }).compileComponents();

    fixture = TestBed.createComponent(PhoneInputComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
