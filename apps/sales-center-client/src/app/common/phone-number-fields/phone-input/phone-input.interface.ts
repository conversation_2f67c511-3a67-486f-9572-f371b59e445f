import { FormControl, ValidatorFn } from '@angular/forms';
import { CountryCode, Extension } from 'libphonenumber-js';
import { extensionValidator, phoneInputValidator } from '../phone-number-validator.directive';

export interface PhoneInputValue {
  countryCode: CountryCode;
  phoneNumber: string;
  extension: Extension;
}

/**
 * A Form Control for the `PhoneInputComponent` to easily attach it to forms. Includes
 * validation for phone number and country code by default.
 */
export class PhoneNumberControl extends FormControl<PhoneInputValue> {
  constructor(value: PhoneInputValue, validators?: ValidatorFn[]) {
    super(value, [...(validators ?? []), phoneInputValidator, extensionValidator]);
  }
}
