@use 'design-tokens' as *;

:host {
  display: block;
  width: 100%;
}

.phone-control {
  display: flex;
  gap: $spacing-1;
  align-items: center;
}

// we need to add :host here to increase the specificity of the selector so it overrides the default styles in glxy-form-field
:host input {
  border: none;
  background: none;
  outline: none;
  font: inherit;
  text-align: left;
  margin: 0;
  padding: 0; // glxy-form-field adds padding by default. this removes that
}

.flag-icon {
  flex-grow: 0;
  flex-shrink: 0;
}

input.country-code {
  flex-grow: 0;
  flex-shrink: 0;
}

.phone-number {
  flex-grow: 3;
  flex-shrink: 3;
}
