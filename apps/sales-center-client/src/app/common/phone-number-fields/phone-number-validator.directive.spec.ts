// Add tests for phone-input-validator.directive.ts

import { AbstractControl } from '@angular/forms';
import { Extension } from 'libphonenumber-js/types';
import {
  extensionValidator,
  phoneInputValidator,
  PhoneInputValidatorDirective,
} from './phone-number-validator.directive';
import * as validatorModule from './phone-number-validator.directive';

describe('PhoneInputValidatorDirective', () => {
  let phoneValidator;
  beforeEach(() => {
    phoneValidator = jest.spyOn(validatorModule, 'phoneInputValidator');
  });

  it('should create an instance', () => {
    const directive = new PhoneInputValidatorDirective();
    expect(directive).toBeTruthy();
  });

  it('just calls the phoneInputValidator', () => {
    const directive = new PhoneInputValidatorDirective();
    directive.validate(<AbstractControl>{
      value: { phoneNumber: '3054567890', countryCode: 'US', extension: <Extension>'' },
    });
    expect(phoneValidator).toHaveBeenCalled();
  });
});

const getValidPhoneNumber = () => {
  return '3054567890';
};
const getValidCountryCode = () => {
  return 'US';
};

describe('phoneInputValidator', () => {
  it('returns null if phone number is valid', () => {
    expect(
      phoneInputValidator(<AbstractControl>{
        value: { phoneNumber: getValidPhoneNumber(), countryCode: 'US', extension: <Extension>'' },
      }),
    ).toBeNull();
  });

  it('returns an error for invalid phone number', () => {
    expect(
      phoneInputValidator(<AbstractControl>{
        // 123 is an invalid area code for US
        value: { phoneNumber: '1234567890', countryCode: 'US', extension: <Extension>'' },
      }),
    ).not.toBeNull();
  });

  it('returns an error for missing country code', () => {
    expect(
      phoneInputValidator(<AbstractControl>{
        value: { phoneNumber: '1234567890', countryCode: '', extension: <Extension>'' },
      }),
    ).not.toBeNull();
  });
});

describe('extensionValidator', () => {
  it('returns an error if phone number is missing but extension is present', () => {
    expect(
      extensionValidator(<AbstractControl>{
        value: {
          phoneNumber: '',
          countryCode: getValidCountryCode(),
          extension: <Extension>'123',
        },
      }),
    ).not.toBeNull();
  });

  it('returns null if phone number is valid and extension is empty', () => {
    expect(
      extensionValidator(<AbstractControl>{
        value: {
          phoneNumber: getValidPhoneNumber(),
          countryCode: getValidCountryCode(),
          extension: <Extension>'',
        },
      }),
    ).toBeNull();
  });

  it('returns null if phone number is empty and extension is empty', () => {
    // In this case, we will lean on the phone number validator, not the extension validator.
    expect(
      extensionValidator(<AbstractControl>{
        value: {
          phoneNumber: '',
          countryCode: getValidCountryCode(),
          extension: <Extension>'',
        },
      }),
    ).toBeNull();
  });

  it('returns null if phone number is valid and extension is valid (123)', () => {
    expect(
      extensionValidator(<AbstractControl>{
        value: {
          phoneNumber: getValidPhoneNumber(),
          countryCode: getValidCountryCode(),
          extension: <Extension>'123',
        },
      }),
    ).toBeNull();
  });

  it('returns null if phone number is valid and extension is valid (0)', () => {
    expect(
      extensionValidator(<AbstractControl>{
        value: {
          phoneNumber: getValidPhoneNumber(),
          countryCode: getValidCountryCode(),
          extension: <Extension>'0',
        },
      }),
    ).toBeNull();
  });

  it('returns an error if phone number is valid but extension is not a number', () => {
    expect(
      extensionValidator(<AbstractControl>{
        value: {
          phoneNumber: getValidPhoneNumber(),
          countryCode: getValidCountryCode(),
          extension: <Extension>'not a number',
        },
      }),
    ).not.toBeNull();
  });

  it('returns an error if phone number is valid but extension is a negative number', () => {
    expect(
      extensionValidator(<AbstractControl>{
        value: {
          phoneNumber: getValidPhoneNumber(),
          countryCode: getValidCountryCode(),
          extension: <Extension>'-1',
        },
      }),
    ).not.toBeNull();
  });

  it('returns an error if phone number is valid but extension is a non-integer number', () => {
    expect(
      extensionValidator(<AbstractControl>{
        value: {
          phoneNumber: getValidPhoneNumber(),
          countryCode: getValidCountryCode(),
          extension: <Extension>'1.5',
        },
      }),
    ).not.toBeNull();
  });
});
