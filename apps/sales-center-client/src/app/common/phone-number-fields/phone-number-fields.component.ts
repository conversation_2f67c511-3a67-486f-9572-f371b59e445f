import { Component, Optional, Self } from '@angular/core';
import { Control<PERSON><PERSON>ueAccessor, FormBuilder, NgControl } from '@angular/forms';
import { Extension } from 'libphonenumber-js';
import { PHONE_INPUT_ERRORS } from './phone-number-validator.directive';
import { PhoneInputValue } from './phone-input/phone-input.interface';

@Component({
  selector: 'app-phone-number-fields',
  templateUrl: './phone-number-fields.component.html',
  styleUrls: ['./phone-number-fields.component.scss'],
  standalone: false,
})
export class PhoneNumberFieldsComponent implements ControlValueAccessor {
  readonly PHONE_ERRORS = PHONE_INPUT_ERRORS;
  readonly form = this._fb.group({
    phone: <PhoneInputValue>null,
    extension: <Extension>'',
  });

  onChange: (value: PhoneInputValue) => void = () => null;
  onTouched: () => void = () => null;

  constructor(
    private readonly _fb: FormBuilder,
    @Optional() @Self() public ngControl: NgControl,
  ) {
    if (ngControl !== null) {
      this.ngControl.valueAccessor = this;
    }

    // TODO (SSC-5527): This is here to ensure errors propogate from parent form. Evaluate
    // if there is a better way to do this.
    this.form.controls.phone.addValidators(() => this.ngControl?.errors);
    this.form.controls.extension.addValidators(() => this.ngControl?.errors);
  }

  handleInput(): void {
    const { phone, extension } = this.form.value;
    const value = { ...phone, extension };
    this.onChange(value);
    this.form.controls.phone.updateValueAndValidity();
    this.form.controls.phone.markAsTouched();
    this.form.controls.extension.updateValueAndValidity();
    this.form.controls.extension.markAsTouched();
  }

  writeValue(obj: PhoneInputValue): void {
    this.form.patchValue({ phone: obj, extension: obj?.extension });
    this.onTouched();
    this.onChange(obj);
  }
  registerOnChange(fn: (v: PhoneInputValue) => void): void {
    this.onChange = fn;
  }
  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }
  setDisabledState?(isDisabled: boolean): void {
    isDisabled ? this.form.disable() : this.form.enable();
  }
}
