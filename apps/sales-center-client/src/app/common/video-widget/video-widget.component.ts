import { Component, Inject, OnInit, Pipe, PipeTransform } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

export interface VideoWidgetData {
  videoTitle: string;
  videoURL: string;
}

@Component({
  selector: 'app-video-widget',
  templateUrl: './video-widget.component.html',
  styleUrls: ['./video-widget.component.scss'],
  standalone: false,
})
export class VideoWidgetComponent implements OnInit {
  videoURL: string;
  videoTitle: string;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: VideoWidgetData,
    public dialogRef: MatDialogRef<VideoWidgetComponent>,
  ) {}

  ngOnInit(): void {
    this.videoTitle = this.data.videoTitle;
    this.videoURL = this.data.videoURL;
  }
}

@Pipe({
  name: 'safe',
  standalone: false,
})
export class SafePipe implements PipeTransform {
  constructor(private readonly sanitizer: DomSanitizer) {}

  transform(url): SafeResourceUrl {
    return this.sanitizer.bypassSecurityTrustResourceUrl(url);
  }
}
