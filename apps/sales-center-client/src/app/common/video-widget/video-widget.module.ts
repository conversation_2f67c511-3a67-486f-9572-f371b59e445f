import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule } from '@angular/material/dialog';
import { SafePipe, VideoWidgetComponent } from './video-widget.component';
import { SalesToolCommonModule } from '../common.module';

@NgModule({
  imports: [CommonModule, SalesToolCommonModule, MatDialogModule],
  declarations: [VideoWidgetComponent, SafePipe],
  exports: [VideoWidgetComponent],
})
export class VideoWidgetModule {}
