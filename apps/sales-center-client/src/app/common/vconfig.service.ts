import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';

const GET_VCONFIG_PRICE_SETTING_URL = '/ajax/v1/is-price-visible';

@Injectable()
export class VConfigService {
  constructor(private readonly apiService: HttpClient) {}

  shouldShowWholesalePrice(): Observable<boolean> {
    return this.apiService.post(GET_VCONFIG_PRICE_SETTING_URL, {}).pipe(map((res) => res['data'].isPriceVisible));
  }
}
