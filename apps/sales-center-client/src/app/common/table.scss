@use 'design-tokens' as *;

a {
  text-decoration: none;
}

.no-action {
  cursor: default;
}

.sorting,
.sorting-desc,
.sorting-asc {
  cursor: pointer;
}

.sorting:after,
.sorting_desc:after,
.sorting_asc:after {
  line-height: 30px;
  padding-left: 10px;
  float: none;
  vertical-align: middle;
  cursor: pointer;
}

.sorting:after {
  text-shadow: none;
  font-size: 14px;
  padding-left: 0px;
  color: $primary-accent-color;
  font-family: 'Platform Icons';
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  margin-top: -2px;
  content: '';
  display: inline-block;
}

.sorting-asc:after {
  text-shadow: none;
  font-size: 14px;
  color: $primary-accent-color;
  font-family: 'Platform Icons';
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  margin-top: -2px;
  content: '';
  display: inline-block;
}

.sorting-desc:after {
  text-shadow: none;
  color: $primary-accent-color;
  font-size: 14px;
  font-family: 'Platform Icons';
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  margin-top: -2px;
  content: '';
  display: inline-block;
}

.no-results {
  color: $dark-gray;
  display: flex;
  justify-content: center;

  span {
    padding: 20px;
  }
}

table {
  color: $dark-gray;
  table-layout: fixed;
  border-collapse: collapse;
  background-color: #fff;
  border-color: #ffa;
  border-width: 5px;
  width: 100%;

  th {
    text-align: left;
    background-color: #e0edf4;
    line-height: 30px;
    padding: 5px 10px;
    user-select: none;
    font-weight: normal;
  }

  tr {
    &:nth-child(even) {
      background-color: $lighter-gray;
    }

    td {
      border: none;
      padding: 10px 10px;
      font-size: 14px;

      md-icon {
        margin: 3px;

        &.enabled {
          color: $vendasta-green;
        }
      }
    }

    .delete {
      text-align: right !important;

      md-icon {
        color: #cccccc;
      }
    }
  }
}

.table-header {
  background-color: grey;
}
