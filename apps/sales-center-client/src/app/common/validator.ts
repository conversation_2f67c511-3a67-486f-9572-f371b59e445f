import { UntypedFormControl } from '@angular/forms';

// Following regex matches North American, South African, UK, and Australian phone numbers, with some support for extensions
// tslint:disable-next-line:max-line-length
const PHONE_NUMBER_REGEX =
  /(^(?:(?:\+?1\s*(?:[.-]\s*)?)?(?:\(\s*([2-9]1[02-9]|[2-9][02-8]1|[2-9][02-8][02-9])\s*\)|([2-9]1[02-9]|[2-9][02-8]1|[2-9][02-8][02-9]))\s*(?:[.-]\s*)?)?([2-9]1[02-9]|[2-9][02-9]1|[2-9][02-9]{2})\s*(?:[.-]\s*)?([0-9]{4})(?:\s*(?:#|x\.?|ext\.?|extension)\s*(\d+))?$)|(^(((\+44\s?\d{4}|\(?0\d{4}\)?)\s?\d{3}\s?\d{3})|((\+44\s?\d{3}|\(?0\d{3}\)?)\s?\d{3}\s?\d{4})|((\+44\s?\d{2}|\(?0\d{2}\)?)\s?\d{4}\s?\d{4}))(\s?#(\d{4}|\d{3}))?$)|(^(\+\d{2}[ -]{0,1}){0,1}(((\({0,1}[ -]{0,1})0{0,1}\){0,1}[2|3|7|8]{1}\){0,1}[ -]*(\d{4}[ -]{0,1}\d{4}))|(1[ -]{0,1}(300|800|900|902)[ -]{0,1}((\d{6})|(\d{3}[ -]{0,1}\d{3})))|(13[ -]{0,1}([\d -]{5})|((\({0,1}[ -]{0,1})0{0,1}\){0,1}4{1}[\d -]{8,10})))$)|((\(0\d\d\)\s\d{3}[\s-]+\d{4})|(0\d\d[\s-]+\d{3}[\s-]+\d{4})|(0\d{9})|(\+\d\d\s?[(\s]\d\d[)\s]\s?\d{3}[\s-]?\d{4}))/;
// tslint:disable-next-line:max-line-length
const POSTAL_CODE_UK_REGEX =
  /^([Gg][Ii][Rr] 0[Aa]{2})|((([A-Za-z][0-9]{1,2})|(([A-Za-z][A-Ha-hJ-Yj-y][0-9]{1,2})|(([AZa-z][0-9][A-Za-z])|([A-Za-z][A-Ha-hJ-Yj-y][0-9]?[A-Za-z]))))[0-9][A-Za-z]{2})$/;
const ZIP_POSTAL_CODE_CAN_US_REGEX =
  /(^([A-Y]|[a-y])\d([A-Z]|[a-z])( )?\d([A-Z]|[a-z])\d$)|(^\d{5}(-\d{4})?$)|(^\d{4}$)/;

export class Validator {
  static isZipOrPostalCode(control: UntypedFormControl): any {
    if (
      control.value &&
      (control.value.match(ZIP_POSTAL_CODE_CAN_US_REGEX) || control.value.match(POSTAL_CODE_UK_REGEX))
    ) {
      return null;
    } else if (control.value) {
      return { invalidZipCode: true };
    }
  }

  static isPhoneNumber(control: UntypedFormControl): any {
    if (control.value && control.value.match(PHONE_NUMBER_REGEX)) {
      return null;
    } else if (control.value) {
      return { invalidPhoneNumber: true };
    }
  }
}
