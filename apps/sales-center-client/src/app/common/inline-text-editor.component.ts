import { Component, OnInit, Input, Output, ViewChild, ElementRef, EventEmitter } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

export interface DataValidation {
  valid: boolean;
  message?: string;
  sanitized: string | number;
}

@Component({
  selector: 'app-inline-text-editor',
  styleUrls: ['./inline-text-editor.component.scss'],
  template: `
    <div class="inline-text-editor-container" (click)="click()">
      <div><ng-content select="[prefix]"></ng-content></div>
      <div>
        <span
          #content
          class="edit-area"
          [contentEditable]="editable"
          (keypress)="keyPress($event)"
          (paste)="paste($event)"
          (blur)="blur()"
          (drop)="onDrop($event)"
          (dataValidate)="(dataValidate)"
          (click)="contentEditableClick($event)"
        >
          {{ value }}
        </span>
      </div>
      <div><ng-content select="[suffix]"></ng-content></div>
    </div>
  `,
  standalone: false,
})
export class InlineTextEditorComponent implements OnInit {
  @Input() value: any;
  @Input() editable = true;
  @Input() required = true;
  @Input() dataValidate: (value: any) => DataValidation;

  @Output() changeEvent: EventEmitter<any> = new EventEmitter();
  @Output() validationError: EventEmitter<any> = new EventEmitter();

  @ViewChild('content')
  content: ElementRef;
  private savedValue: any;

  constructor(readonly translate: TranslateService) {}

  public ngOnInit(): void {
    this.savedValue = this.value || '';

    // if there is no data validation func provided by parent comp, we assume all values are valid.
    if (!this.dataValidate) {
      this.dataValidate = (value: any) => {
        return { valid: true, msg: '', sanitized: value };
      };
    }
  }

  keyPress(event: KeyboardEvent): void {
    event.cancelBubble = true;
    if (event.which === 13) {
      // Prevent new lines when the user presses enter
      event.preventDefault();
      this.content.nativeElement.blur();
      // Without this the cursor is still in the text area even though it's blurred.
      // So if they were to start typing the content would appear in the area.
      window.getSelection().removeAllRanges();
    }
  }

  paste(event: ClipboardEvent): void {
    event.preventDefault();
    // Prevent dom elements from being inserted into this text area if they were copied
    let text = event.clipboardData.getData('text/plain');
    text = this.escape(text);
    document.execCommand('insertHTML', false, text);
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
  }

  private escape(content: string): string {
    const input = document.createElement('textarea');
    input.textContent = content;

    return input.innerHTML;
  }

  blur(): void {
    this.save();
  }

  private save(): void {
    let newValue = this.content.nativeElement.innerText;
    // If value is invalid, roll back
    const validation = this.dataValidate(newValue);
    if (!validation.valid) {
      this.content.nativeElement.innerText = this.savedValue;
      this.validationError.emit(validation.message);
      return;
    }
    newValue = validation.sanitized.toString().replace(/(\r\n|\n|\r)/gm, '');
    if (this.required && (!newValue || newValue.length === 0)) {
      this.content.nativeElement.innerText = this.savedValue;
      return;
    }

    if (newValue !== this.savedValue) {
      this.savedValue = newValue;
      this.changeEvent.emit(this.savedValue);
    }

    this.content.nativeElement.innerText = this.savedValue;
  }

  click(): void {
    this.content.nativeElement.focus();
    if (typeof window.getSelection !== 'undefined' && typeof document.createRange !== 'undefined') {
      const range = document.createRange();
      range.selectNodeContents(this.content.nativeElement);
      range.collapse(false);
      const sel = window.getSelection();
      sel.removeAllRanges();
      sel.addRange(range);
    }
  }

  contentEditableClick(event: MouseEvent): void {
    event.stopPropagation();
  }
}
