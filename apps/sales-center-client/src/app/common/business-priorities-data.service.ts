import { distinctUntilChanged, map, switchMap, take } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { OnboardingStrategiesApiService, PriorityInterface } from '@vendasta/prospect';
import { LoggedInUserInfoService } from '../logged-in-user-info';
import { Observable } from 'rxjs';

export interface AccountGoal {
  name: string;
  value: string;
  description: string;
}

@Injectable({
  providedIn: 'root',
})
export class BusinessPrioritiesDataService {
  goals = new Map<string, AccountGoal>();
  priorities: PriorityInterface[] = [];
  priorities$: Observable<PriorityInterface[]>;

  constructor(
    private readonly loggedInUserService: LoggedInUserInfoService,
    private readonly onboardingStrategiesService: OnboardingStrategiesApiService,
  ) {
    this.loadGoals();
    this.loadPrioritiesForPartner();
  }

  loadGoals(): void {
    this.loggedInUserService
      .getPartnerId()
      .pipe(
        distinctUntilChanged(),
        switchMap((partnerId) => this.onboardingStrategiesService.listGoalsForPartner({ partnerId })),
      )
      .subscribe((resp) => {
        return (resp?.goals || []).forEach((goal) =>
          this.goals.set(goal.id, { name: goal.id, value: goal.title, description: goal.description }),
        );
      });
  }

  getGoalTitle(goal): string {
    if (this.goals.has(goal)) {
      return this.goals.get(goal).value;
    }
  }

  getGoalDescription(goal): string {
    if (this.goals.has(goal)) {
      return this.goals.get(goal).description;
    }
  }

  loadPrioritiesForPartner(): void {
    this.priorities$ = this.loggedInUserService.getPartnerIdAndMarketId$().pipe(
      take(1),
      switchMap((pm) => {
        return this.onboardingStrategiesService.listPrioritiesForPartner({ partnerId: pm.partnerId }).pipe(
          take(1),
          map((resp) => resp.priorities || []),
        );
      }),
    );
  }

  getPriorities(): Observable<PriorityInterface[]> {
    return this.priorities$;
  }
}
