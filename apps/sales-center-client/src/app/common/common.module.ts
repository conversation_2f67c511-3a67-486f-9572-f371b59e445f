import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { RouterModule } from '@angular/router';
import { CountryStateServiceInterfaceToken } from '@vendasta/country-state';
import { VaProductDetailsModule } from '@vendasta/store';
import { VaSearchSelectModule } from '@vendasta/uikit';
import { CountryStateService } from './country-state.service';
import { IconModule } from './icon';
import { InlineTextEditorModule } from './inline-text-editor.module';
import { ItemAndStatusComponent } from './items-and-status.component';
import { MultiItemAndStatusComponent } from './multi-items-and-status.component';
import { SelectAndToggleComponent } from './select-and-toggle.component';
import { SideDrawerContainerComponent } from './side-drawer-container/side-drawer-container.component';
import { SortedPipe } from './sort';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    ReactiveFormsModule,
    IconModule,
    MatIconModule,
    MatCardModule,
    MatButtonModule,
    MatSlideToggleModule,
    VaProductDetailsModule,
    MatInputModule,
    MatSidenavModule,
    VaSearchSelectModule,
    InlineTextEditorModule,
  ],
  exports: [
    FormsModule,
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    SortedPipe,
    IconModule,
    SelectAndToggleComponent,
    ItemAndStatusComponent,
    MultiItemAndStatusComponent,
    VaProductDetailsModule,
    SideDrawerContainerComponent,
  ],
  declarations: [
    SortedPipe,
    SelectAndToggleComponent,
    ItemAndStatusComponent,
    MultiItemAndStatusComponent,
    SideDrawerContainerComponent,
  ],
  providers: [CountryStateService, { provide: CountryStateServiceInterfaceToken, useExisting: CountryStateService }],
})
export class SalesToolCommonModule {}
