import { camelCaseToTitleCase } from './strings';

describe('camelCaseToTitleCase', () => {
  test('empty string', () => {
    expect(camelCaseToTitleCase('')).toEqual('');
  });
  test('one word', () => {
    expect(camelCaseToTitleCase('word')).toEqual('Word');
  });
  test('two words', () => {
    expect(camelCaseToTitleCase('two words')).toEqual('Two Words');
  });
  test('null', () => {
    expect(camelCaseToTitleCase(null)).toEqual('');
  });
});
