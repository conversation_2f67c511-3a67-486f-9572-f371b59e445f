@use 'design-tokens' as dt;

:host {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

mat-dialog-content {
  height: 100%;
  max-height: inherit;
  padding-bottom: dt.$spacing-2;
  overflow: hidden;
}

.text-editor {
  height: 100%;
  // Make sure the editor is not hidden behind the mat dialog actions
  padding-bottom: 80px;
}

.readonly-text {
  width: 100%;
  height: 100%;
  resize: none;
  overflow: scroll;
  overflow-wrap: break-word;
}
