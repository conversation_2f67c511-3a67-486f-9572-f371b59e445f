import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyRichTextEditorModule } from '@vendasta/galaxy/rich-text-editor';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyInputModule } from '@vendasta/galaxy/input';

export interface TextModalDialogData {
  placeholder: string;
  title: string;
  text: string;
  editMode: boolean;
}

export interface TextModalDialogResult {
  text: string;
  title: string;
}

export const TextModalDefaultSizes = {
  width: '600px',
  height: '600px',
  maxWidth: '100vw',
  maxHeight: '100vh',
};

@Component({
  selector: 'app-text-modal',
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatInputModule,
    TranslateModule,
    MatDividerModule,
    ReactiveFormsModule,
    FormsModule,
    GalaxyRichTextEditorModule,
    GalaxyFormFieldModule,
    GalaxyInputModule,
  ],
  templateUrl: './text-modal.component.html',
  styleUrls: ['./text-modal.component.scss'],
})
export class TextDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<TextDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: TextModalDialogData,
  ) {}

  closeDialog() {
    return this.dialogRef.close({
      text: this.data.text,
      title: this.data.title,
    });
  }
}
