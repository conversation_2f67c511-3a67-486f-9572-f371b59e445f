<h1 mat-dialog-title>
  <ng-container *ngIf="data.title">{{ data.title }}</ng-container>
  <ng-container *ngIf="!data.title">{{ data.placeholder }}</ng-container>
</h1>
<mat-dialog-content>
  <ng-container *ngIf="data.editMode === true; else viewOnly">
    <glxy-form-field>
      <glxy-label>{{ data.placeholder }}</glxy-label>
      <input matInput [(ngModel)]="data.title" />
    </glxy-form-field>
    <glxy-rich-text-editor
      #richTextEditor
      class="text-editor"
      elementId="rich-text-element-content"
      [(ngModel)]="data.text"
      plugins="link lists"
      [allowResize]="false"
      [toolbar]="['styleselect | bold italic link unlink | alignleft aligncenter alignright | bullist numlist']"
    ></glxy-rich-text-editor>
  </ng-container>
  <ng-template #viewOnly>
    <div>{{ data.title }}</div>
    <div class="readonly-text" [innerHTML]="data.text"></div>
  </ng-template>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-flat-button cdkFocusInitial mat-dialog-close>
    {{ 'COMMON.ACTION_LABELS.CLOSE' | translate }}
  </button>
  <button mat-flat-button *ngIf="data.editMode" color="primary" (click)="closeDialog()">
    {{ 'COMMON.ACTION_LABELS.SAVE' | translate }}
  </button>
</mat-dialog-actions>
