import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { Directive, ElementRef, Input, OnDestroy, OnInit, Renderer2 } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { BehaviorSubject, Observable, SubscriptionLike, combineLatest, firstValueFrom } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { openSecureNewTab } from '../secure';
import { ContinuousVoiceRecognitionService } from './continuous-voice-recognition.service';

const MIC = 'mic';
const MIC_OFF = 'mic_off';

@Directive({
  selector: '[appVoiceTextArea]',
  providers: [ContinuousVoiceRecognitionService, SnackbarService, ProductAnalyticsService],
  standalone: false,
})
export class VoiceTextAreaDirective implements OnInit, OnDestroy {
  @Input() htmlTextAreaElement: HTMLTextAreaElement;
  @Input() set showIcon(showIcon: boolean) {
    this.showIcon$$.next(showIcon);
  }

  private showIcon$$ = new BehaviorSubject<boolean>(true);
  private showIconSubscription: SubscriptionLike;

  private iconListener: () => void;
  private unavailableIconListener: () => void;
  private initialValue: string;
  private iconText = this.renderer.createText(MIC);
  private readonly isMobileScreen$: Observable<boolean>;
  private enableSpeech$: Observable<boolean>;

  constructor(
    private readonly el: ElementRef,
    private readonly renderer: Renderer2,
    private readonly voiceService: ContinuousVoiceRecognitionService,
    private readonly breakpointObserver: BreakpointObserver,
    private readonly snackbarService: SnackbarService,
    private readonly translate: TranslateService,
  ) {
    this.isMobileScreen$ = this.breakpointObserver.observe([Breakpoints.XSmall]).pipe(map((result) => result.matches));
  }

  ngOnInit(): void {
    this.enableSpeech$ = combineLatest([this.isMobileScreen$, this.voiceService.isSpeechRecognitionAvailable$]).pipe(
      map(([isMobileScreen, isSpeechAvailable]) => {
        if (!isMobileScreen && !isSpeechAvailable) {
          // show greyed out icon and suggest user use's Google Chrome
          const icon = this.addIconToDOM(MIC_OFF);
          this.addNotAvailableIconListener(icon);
        }
        return !isMobileScreen && isSpeechAvailable;
      }),
    );

    this.enableSpeech$.subscribe((enableSpeech) => {
      if (enableSpeech === true) {
        const icon = this.addIconToDOM(MIC);
        this.listenToIconClick(icon);
      }
    });

    this.voiceService.outputText$.subscribe((text) => {
      this.updateTextAreaValue(text);
    });
  }

  updateTextAreaValue(newText: string): void {
    if (newText === '') {
      return;
    }

    if (!!this.initialValue && this.initialValue !== '') {
      this.htmlTextAreaElement.value = this.initialValue + ' ' + newText;
    } else {
      const sentenceCaseValue = newText[0].toUpperCase() + newText.slice(1);
      this.htmlTextAreaElement.value = sentenceCaseValue;
    }
    this.htmlTextAreaElement.dispatchEvent(new Event('input'));
  }

  ngOnDestroy(): void {
    firstValueFrom(this.enableSpeech$.pipe(filter(Boolean))).then(() => this.voiceService.stop());

    this.showIconSubscription?.unsubscribe();

    if (this.iconListener) {
      this.iconListener();
    }

    if (this.unavailableIconListener) {
      this.unavailableIconListener();
    }
  }

  listenToIconClick(icon: any): void {
    this.iconListener = this.renderer.listen(icon, 'click', () => this.toggleVoiceRecognition(icon));
  }

  private toggleVoiceRecognition(icon: any): void {
    if (this.voiceService.isListening === false) {
      this.initialValue = this.htmlTextAreaElement.value;
      this.renderer.removeChild(icon, this.iconText);
      this.renderer.addClass(icon, 'pulsating-circle');
    } else {
      this.renderer.appendChild(icon, this.iconText);
      this.renderer.removeClass(icon, 'pulsating-circle');
      this.initialValue = this.htmlTextAreaElement.value;
    }
    this.voiceService.toggleVoiceRecognition();
  }

  addIconToDOM(iconName: string): any {
    const icon = this.renderer.createElement('mat-icon');
    if (iconName === MIC) {
      this.renderer.appendChild(icon, this.iconText);
    } else {
      this.renderer.appendChild(icon, this.renderer.createText(MIC_OFF));
    }
    this.renderer.addClass(icon, 'mat-icon');
    this.renderer.addClass(icon, 'material-icons');

    this.renderer.setStyle(icon, 'position', 'absolute');
    this.renderer.setStyle(icon, 'top', '8px');
    this.renderer.setStyle(icon, 'right', '8px');

    this.renderer.setStyle(icon, 'cursor', 'pointer');

    this.renderer.setStyle(icon, 'border-radius', '16px');

    this.renderer.setStyle(icon, 'width', '18px');
    this.renderer.setStyle(icon, 'height', '18px');
    this.renderer.setStyle(icon, 'font-size', '18px');

    this.showIconSubscription = this.showIcon$$.subscribe((showIcon) => {
      this.renderer.setStyle(icon, 'visibility', showIcon ? 'visible' : 'hidden');
      if (!showIcon && this.voiceService.isListening) {
        this.toggleVoiceRecognition(icon);
      }
    });

    this.renderer.appendChild(this.el.nativeElement.parentNode.parentNode, icon);
    return icon;
  }

  addNotAvailableIconListener(icon: any): any {
    this.unavailableIconListener = this.renderer.listen(icon, 'click', () => {
      this.snackbarService.openSnackBar(
        this.translate.instant('VOICE_TO_TEXT.USE_GOOGLE_CHROME'),
        this.translate.instant('VOICE_TO_TEXT.DOWNLOAD_CHROME'),
        5000,
        () => {
          openSecureNewTab('https://www.google.com/chrome/');
        },
      );
    });
  }
}
