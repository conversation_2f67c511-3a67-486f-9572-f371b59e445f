import { Injectable } from '@angular/core';
import { BehaviorSubject, ReplaySubject } from 'rxjs';

declare let webkitSpeechRecognition: any;

const MIC_ON = 'mic';
const MIC_OFF = 'mic_none';

@Injectable()
export class SingleInputVoiceRecognitionService {
  recognition: any;
  isStoppedSpeechRecog = true;
  private text = '';
  private readonly outputText$$ = new BehaviorSubject<string>('');
  public readonly outputText$ = this.outputText$$.asObservable();

  private readonly micIcon$$ = new BehaviorSubject<string>(MIC_OFF);
  public readonly micIcon$ = this.micIcon$$.asObservable();
  tempWords;

  private readonly isSpeechRecognitionAvailable$$ = new ReplaySubject<boolean>(1);
  public readonly isSpeechRecognitionAvailable$ = this.isSpeechRecognitionAvailable$$.asObservable();

  constructor() {
    this.init();
  }

  toggleVoiceRecognition(): void {
    if (this.isStoppedSpeechRecog === true) {
      this.start();
    } else {
      this.stop();
    }
  }

  private init(): void {
    if (!('webkitSpeechRecognition' in window)) {
      this.isSpeechRecognitionAvailable$$.next(false);
      return;
    }
    this.recognition = new webkitSpeechRecognition();
    this.isSpeechRecognitionAvailable$$.next(true);
    this.recognition.interimResults = true;
    this.recognition.lang = 'en-US';

    this.recognition.addEventListener('result', (e) => {
      this.tempWords = Array.from(e.results)
        .map((result) => result[0])
        .map((result) => result.transcript)
        .join('');
    });
  }

  resetText(): void {
    this.text = '';
    this.outputText$$.next(this.text);
  }

  // stops automatically when user stops talking.
  private start(): void {
    this.isStoppedSpeechRecog = false;
    this.recognition?.start();
    this.micIcon$$.next(MIC_ON);

    this.recognition?.addEventListener('end', () => {
      this.micIcon$$.next(MIC_OFF);
      this.isStoppedSpeechRecog = true;
      this.wordConcat();
      this.resetText();
    });
  }

  // use stop in case there are loud environments
  private stop(): void {
    this.isStoppedSpeechRecog = true;
    this.wordConcat();
    this.recognition?.stop();
    this.micIcon$$.next(MIC_OFF);
  }

  wordConcat(): void {
    if (this.tempWords !== '') {
      this.text = this.text + ' ' + this.tempWords;
      this.outputText$$.next(this.text);
      this.tempWords = '';
    }
  }
}
