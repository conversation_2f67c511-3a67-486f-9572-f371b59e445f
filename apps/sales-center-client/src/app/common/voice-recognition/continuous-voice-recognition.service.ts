import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

declare let webkitSpeechRecognition: any;

@Injectable()
export class ContinuousVoiceRecognitionService {
  private readonly outputText$$ = new BehaviorSubject<string>('');
  public readonly outputText$ = this.outputText$$.asObservable();

  private readonly isSpeechRecognitionAvailable$$ = new BehaviorSubject<boolean>(true);
  public readonly isSpeechRecognitionAvailable$ = this.isSpeechRecognitionAvailable$$.asObservable();

  private recognition: any;
  public isListening = false;

  constructor() {
    if (!('webkitSpeechRecognition' in window)) {
      this.isSpeechRecognitionAvailable$$.next(false);
      return;
    }

    this.recognition = new webkitSpeechRecognition();
    this.recognition.continuous = true;
    this.recognition.interimResults = true;
    // TODO (SSC-2437): support other languages
    this.recognition.lang = 'en-US';

    this.recognition.onresult = (e) => {
      const tempWords = Array.from(e.results)
        .map((result) => result[0])
        .map((result) => result.transcript)
        .join('');

      this.outputText$$.next(tempWords);
    };
  }

  toggleVoiceRecognition(): void {
    if (this.isListening === true) {
      this.stop();
    } else {
      this.start();
    }
  }

  stop(): void {
    this.isListening = false;
    this.recognition?.abort();
  }

  private start(): void {
    this.isListening = true;
    this.recognition?.start();
  }
}
