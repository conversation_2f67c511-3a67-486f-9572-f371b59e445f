import { Directive, HostListener, Input, OnDestroy, OnInit } from '@angular/core';
import { GalaxyInputInterface } from '@vendasta/galaxy/input';
import { combineLatest, Observable } from 'rxjs';
import { SingleInputVoiceRecognitionService } from './single-input-voice-recognition.service';
import { UntypedFormControl } from '@angular/forms';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { map } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';
import { SubscriptionList } from '@vendasta/rx-utils';

@Directive({
  selector: '[appVoiceInput]',
  providers: [SingleInputVoiceRecognitionService],
  standalone: false,
})
export class VoiceInputDirective implements OnInit, OnDestroy {
  private readonly micIcon$: Observable<string>;
  private readonly outputText$: Observable<string>;
  private readonly isMobileScreen$: Observable<boolean>;
  private readonly enableSpeechRecognition$: Observable<boolean>;
  private readonly subscriptions = SubscriptionList.new();

  @Input() galaxyInputInterface: GalaxyInputInterface;
  @Input() formControl: UntypedFormControl;

  constructor(
    private readonly voiceRecognitionService: SingleInputVoiceRecognitionService,
    private readonly breakpointObserver: BreakpointObserver,
    private readonly translate: TranslateService,
  ) {
    this.micIcon$ = this.voiceRecognitionService.micIcon$;
    this.outputText$ = this.voiceRecognitionService.outputText$;

    this.isMobileScreen$ = this.breakpointObserver.observe([Breakpoints.XSmall]).pipe(map((result) => result.matches));

    this.enableSpeechRecognition$ = combineLatest([
      this.isMobileScreen$,
      this.voiceRecognitionService.isSpeechRecognitionAvailable$,
    ]).pipe(
      map(([isMobileScreen, isSpeechAvailable]) => {
        return !isMobileScreen && isSpeechAvailable;
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }

  ngOnInit(): void {
    if (this.galaxyInputInterface === null) {
      throw new Error('Galaxy Input Interface is required');
    }

    if (this.formControl === null) {
      throw new Error('FormControl is required');
    }

    combineLatest([
      this.enableSpeechRecognition$,
      this.voiceRecognitionService.micIcon$,
      this.voiceRecognitionService.outputText$,
    ]).subscribe(([speechRecogAvailable, micIcon, text]) => {
      if (speechRecogAvailable === true) {
        this.galaxyInputInterface.iconClickable = true;

        this.galaxyInputInterface.trailingIcon = micIcon;
        this.subscriptions.add(this.translate.stream('VOICE_TO_TEXT.CLICK_TO_DICTATE'), (t) => {
          this.galaxyInputInterface.hint = t;
        });

        const currentText = this.formControl.value;
        this.formControl.setValue(currentText + text);
      }
    });
  }

  @HostListener('iconClicked', ['$event'])
  toggleVoiceRecognition(): void {
    this.voiceRecognitionService.toggleVoiceRecognition();
  }
}
