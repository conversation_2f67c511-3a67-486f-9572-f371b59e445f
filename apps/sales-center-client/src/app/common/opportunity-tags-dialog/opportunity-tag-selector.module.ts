import { NgModule } from '@angular/core';
import { OpportunityTagSelectorComponent } from './opportunity-tag-selector.component';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { OpportunityTagSelectStoreService } from './opportunity-tag-select.store.service';
import { TranslateModule } from '@ngx-translate/core';
import { MatSelectModule } from '@angular/material/select';
import { MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';

@NgModule({
  declarations: [OpportunityTagSelectorComponent],
  imports: [MatSelectModule, CommonModule, FormsModule, MatDialogModule, TranslateModule, MatButtonModule],
  providers: [OpportunityTagSelectStoreService],
})
export class OpportunityTagSelectorModule {}
