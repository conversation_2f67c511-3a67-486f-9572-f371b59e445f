import { Injectable } from '@angular/core';
import { ReplaySubject } from 'rxjs';

@Injectable()
export class OpportunityTagSelectStoreService {
  private readonly selectedTag$$ = new ReplaySubject<string>(1);
  public readonly selectedTag$ = this.selectedTag$$.asObservable();

  public setTagSelection(tag: string): void {
    this.selectedTag$$.next(tag);
  }

  public clearTagSelection(): void {
    this.selectedTag$$.next(null);
  }
}
