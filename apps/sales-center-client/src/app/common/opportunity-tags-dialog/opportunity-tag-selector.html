<h2 mat-dialog-title>{{'FORMS.SELECT_TAG' | translate}}</h2>
<mat-dialog-content>
  <mat-form-field>
    <mat-select
      (selectionChange)="handleTagSelection($event.value)"
      [placeholder]="'COMMON.TAG' | translate"
      [value]="data.tag"
    >
      <mat-option *ngFor="let tag of tags$ | async as tags" [value]="tag">{{tag}}</mat-option>
    </mat-select>
  </mat-form-field>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button color="primary" mat-dialog-close>{{'COMMON.ACTION_LABELS.CANCEL' | translate}}</button>
</mat-dialog-actions>
