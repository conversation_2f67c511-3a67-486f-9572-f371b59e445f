import { Component, Inject } from '@angular/core';
import { SalesOpportunitiesService } from '@vendasta/sales-opportunities';
import { Observable } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { OpportunityTagSelectStoreService } from './opportunity-tag-select.store.service';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { USER_PARTNER_MARKET_TOKEN } from '../../core/feature-flag.service';
import { PartnerMarket } from '../../logged-in-user-info/logged-in-user-info.service';

export interface TagDialogData {
  tag: string;
}

@Component({
  templateUrl: './opportunity-tag-selector.html',
  standalone: false,
})
export class OpportunityTagSelectorComponent {
  readonly tags$: Observable<string[]>;
  constructor(
    @Inject(MAT_DIALOG_DATA) readonly data: TagDialogData,
    private readonly opportunityService: SalesOpportunitiesService,
    @Inject(USER_PARTNER_MARKET_TOKEN) private readonly partnerMarketIds: Observable<PartnerMarket>,
    private readonly opportunityTagSelectService: OpportunityTagSelectStoreService,
  ) {
    this.tags$ = this.partnerMarketIds.pipe(
      switchMap((ids) => this.opportunityService.getTags(ids.partnerId, ids.marketId)),
    );
  }

  public handleTagSelection(tag: string): void {
    this.opportunityTagSelectService.setTagSelection(tag);
  }
}
