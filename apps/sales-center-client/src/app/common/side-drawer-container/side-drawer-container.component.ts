import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, Optional, QueryList, ViewChildren } from '@angular/core';
import { MatDrawer } from '@angular/material/sidenav';
import { Observable, Subscription } from 'rxjs';
import { tap } from 'rxjs/operators';
import {
  DynamicOpenCloseTemplateRef,
  DynamicOpenCloseTemplateRefService,
} from '../side-drawer/dynamic-open-close-template-ref.service';

@Component({
  selector: 'app-side-drawer-container',
  templateUrl: './side-drawer-container.component.html',
  styleUrls: ['./side-drawer-container.component.scss'],
  standalone: false,
})
export class SideDrawerContainerComponent implements OnInit, OnDestroy {
  @ViewChildren(MatDrawer) drawerReference: QueryList<MatDrawer>;

  sideDrawerTemplate$: Observable<DynamicOpenCloseTemplateRef>;

  private subscriptions: Subscription[] = [];

  constructor(
    @Optional()
    private dynamicOpenCloseTemplateRefService: DynamicOpenCloseTemplateRefService,
  ) {}

  ngOnInit(): void {
    if (this.dynamicOpenCloseTemplateRefService) {
      this.sideDrawerTemplate$ = this.dynamicOpenCloseTemplateRefService.openTemplate$;

      this.subscriptions.push(
        this.dynamicOpenCloseTemplateRefService.open$
          .pipe(
            tap((open) => {
              if (!this.drawerReference?.first) {
                return;
              }

              if (open) {
                this.drawerReference.first.open('program');
              } else {
                this.drawerReference.first.close();
              }
            }),
          )
          .subscribe(),
      );
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }
}
