<ng-container *ngIf="sideDrawerTemplate$ | async as sideDrawer; else page">
  <mat-drawer-container class="side-drawer-container" hasBackdrop="false">
    <mat-drawer mode="over" position="end" autoFocus="false">
      <ng-template [ngTemplateOutlet]="sideDrawer.templateRef"></ng-template>
    </mat-drawer>
    <ng-container [ngTemplateOutlet]="page"></ng-container>
  </mat-drawer-container>
</ng-container>

<ng-template #page>
  <ng-content></ng-content>
</ng-template>
