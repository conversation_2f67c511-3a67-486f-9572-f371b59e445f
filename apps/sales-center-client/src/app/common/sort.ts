import { Pipe, PipeTransform } from '@angular/core';

export enum SortOrder {
  Ascending = 1,
  Descending = -1,
  None = 0,
}

export declare interface SortDescriptor {
  fieldname: string;
  order: SortOrder;
}

@Pipe({
  name: 'sorted',
  standalone: false,
})
export class SortedPipe implements PipeTransform {
  transform(items: any[], sortState: SortDescriptor): any[] {
    return this.sortedWithState(items, sortState);
  }

  private sortedWithState(items: any[], sortState: SortDescriptor): any[] {
    if (sortState.order === SortOrder.Ascending) {
      return this.sortedAscendingByField([...items], sortState.fieldname);
    } else {
      return this.sortedDescendingByField([...items], sortState.fieldname);
    }
  }

  private sortedAscendingByField(items: any[], fieldName: string): any[] {
    try {
      return items.sort((a, b) => a[fieldName].localeCompare(b[fieldName]));
    } catch (error) {
      return items.sort(function (a: any, b: any): number {
        return a[fieldName] > b[fieldName] ? 1 : b[fieldName] > a[fieldName] ? -1 : 0;
      });
    }
  }

  private sortedDescendingByField(items: any[], fieldName: string): any[] {
    try {
      return items.sort((a, b) => b[fieldName].localeCompare(a[fieldName]));
    } catch (error) {
      return items.sort(function (a: any, b: any): number {
        return a[fieldName] < b[fieldName] ? 1 : b[fieldName] < a[fieldName] ? -1 : 0;
      });
    }
  }
}
