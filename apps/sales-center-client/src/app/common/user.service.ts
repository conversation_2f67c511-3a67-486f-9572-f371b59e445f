import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Environment, EnvironmentService } from '@galaxy/core';
import { Contact } from '@vendasta/sales-v2';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { CreateUsersService } from '../vbc_sdk/create-users.service';
import { ContactShadow } from './contacts';

const DEMO_VBC_URL = 'https://vbc-demo.appspot.com/';
const PROD_VBC_URL = 'https://vbc-prod.appspot.com/';
const LOOKUP_BY_ACCOUNT_PATH = 'internalApi/v3/user/lookupByAccountGroup/';

export interface UserResult {
  email: string;
  userId: string;
  firstName?: string;
  lastName?: string;
  unifiedUserId?: string;
}

export interface UserCreateAttributes {
  greetingName?: string;
  sendWelcomeEmail: boolean;
  locale?: string;
  accountGroupId: string;
}

@Injectable()
export class VBCUserService {
  constructor(
    private httpClient: HttpClient,
    private readonly environmentService: EnvironmentService,
    private readonly userSvc: CreateUsersService,
  ) {}

  createUser(contact: Contact, attributes: UserCreateAttributes, partnerId: string): Observable<UserResult> {
    const phoneNumber = ContactShadow.getInternationalPhoneNumberFromBaseContact(contact);

    return this.userSvc
      .createAndAssociateUser(
        partnerId,
        attributes.accountGroupId,
        attributes.sendWelcomeEmail,
        contact.contactEmail,
        contact.firstName,
        contact.lastName,
        attributes.greetingName,
        attributes.locale,
        phoneNumber,
      )
      .pipe(
        map(
          (user) =>
            <UserResult>{
              email: user.email,
              userId: user.userId,
              firstName: user?.firstName,
              lastName: user?.lastName,
              unifiedUserId: user?.unifiedUserId,
            },
        ),
      );
  }

  getUsersForAccount(partnerId: string, accountGroupId: string): Observable<UserResult[]> {
    let params: HttpParams = new HttpParams();
    params = params.set('partnerId', partnerId);
    params = params.set('accountGroupId', accountGroupId);
    params = params.append('mask', 'email');
    params = params.append('mask', 'userId');
    params = params.append('mask', 'firstName');
    params = params.append('mask', 'lastName');
    params = params.append('mask', 'unifiedUserId');
    return this.httpClient
      .get<{ data: UserResult[] }>(this.getUrlForPath(LOOKUP_BY_ACCOUNT_PATH), {
        params: params,
        withCredentials: true,
        responseType: 'json',
      })
      .pipe(map((result) => result?.data ?? []));
  }

  private getUrlForPath(path: string): string {
    const env = this.environmentService.getEnvironment();
    switch (env) {
      case Environment.DEMO:
        return DEMO_VBC_URL + path;
      case Environment.PROD:
        return PROD_VBC_URL + path;
      default:
        throw new Error('Unsupported environment');
    }
  }
}
