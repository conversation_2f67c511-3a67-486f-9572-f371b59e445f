import { HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map, switchMap, take } from 'rxjs/operators';
import { Country, CountryStateServiceInterface, State } from '@vendasta/country-state';
import { ApiService } from './api.service';
import { LoggedInUserInfoService } from '../logged-in-user-info/logged-in-user-info.service';

export const COUNTRY_STATE_URL = '/api/v1/country-state/';
@Injectable()
export class CountryStateService implements CountryStateServiceInterface {
  constructor(private readonly apiService: ApiService, private readonly loggedInUserInfo: LoggedInUserInfoService) {}

  getCountriesOptions(): Observable<Country[]> {
    const url = COUNTRY_STATE_URL + 'list-countries/';
    return this.loggedInUserInfo.getPartnerIdAndMarketId$().pipe(
      take(1),
      map((ids) => new HttpParams().set('partnerId', ids.partnerId).set('marketId', ids.marketId)),
      switchMap((params) =>
        this.apiService.get(url, params).pipe(map((response) => response.map((c) => ({ code: c.code, name: c.name })))),
      ),
    );
  }

  getStatesOptions(countryCode: string): Observable<State[]> {
    const url = COUNTRY_STATE_URL + 'list-states/';
    return this.loggedInUserInfo.getPartnerIdAndMarketId$().pipe(
      take(1),
      map((ids) =>
        new HttpParams().set('partnerId', ids.partnerId).set('marketId', ids.marketId).set('countryCode', countryCode),
      ),
      switchMap((params) =>
        this.apiService.get(url, params).pipe(map((response) => response.map((s) => ({ code: s.code, name: s.name })))),
      ),
    );
  }
}
