import { ADD_TO_CAMPAIGNS_TOKEN } from './tokens';
import { UserConfigurationAcquirer } from '../../configuration';
import { map } from 'rxjs/operators';

const addToCampaignsFactory = (config: UserConfigurationAcquirer) => {
  return config.configuration$.pipe(map((config) => config?.allowCampaignAdditions === true));
};
export const AddToCampaignProvider = {
  provide: ADD_TO_CAMPAIGNS_TOKEN,
  useFactory: addToCampaignsFactory,
  deps: [UserConfigurationAcquirer],
};
