import { InjectionToken } from '@angular/core';
import { Observable } from 'rxjs';

export const PARTNER_ID_TOKEN = 'PARTNER_ID_TOKEN';
export const PARTNER_ID_TOKEN_FOR_EXTERNAL_DEP = 'PARTNER_ID';
/**
 * @deprecated Token is confusing because its name is singular, but the value
 * returned is an array. Use USER_INFO_TOKEN and pull the market from there.
 */
export const MARKET_ID_TOKEN = new InjectionToken<Observable<string>>('MARKET_ID_TOKEN');
export const PARTNER_ID_FROM_HOST_TOKEN = new InjectionToken<Observable<string>>('PARTNER_ID_FROM_HOST_TOKEN');
export const ACCESS_ALL_MARKET_TOKEN = `ACCESS_ALL_MARKET_TOKEN`;
export const SALESPERSON_ID_TOKEN = 'SALESPERSON_ID_TOKEN';
export const SALESPERSON_NAME_TOKEN = 'SALESPERSON_NAME_TOKEN';
export const SALES_OPPORTUNITIES_SDK_TOKEN = 'SALES_OPPORTUNITIES_SDK_TOKEN';
export const CONTACTS_SDK_TOKEN = 'CONTACTS_SDK_TOKEN';
export const BUSINESS_SDK_TOKEN = 'BUSINESS_SDK_TOKEN';
export const ADD_TO_CAMPAIGNS_TOKEN = 'ADD_TO_CAMPAIGNS_TOKEN';
