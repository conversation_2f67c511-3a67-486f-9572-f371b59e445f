import { LoggedInUserInfoService } from '../../logged-in-user-info';
import { ACCESS_ALL_MARKET_TOKEN } from './tokens';

const accessToAllMarketFactory = (loggedInUser: LoggedInUserInfoService) => {
  return loggedInUser.hasAccessToAllAccountsInMarket$;
};
export const AccessToAllMarketProvider = {
  provide: ACCESS_ALL_MARKET_TOKEN,
  useFactory: accessToAllMarketFactory,
  deps: [LoggedInUserInfoService],
};
