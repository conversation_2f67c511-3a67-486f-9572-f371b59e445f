import { LoggedInUserInfoService } from '../../logged-in-user-info';
import { SALESPERSON_ID_TOKEN, SALESPERSON_NAME_TOKEN } from './tokens';
import { SalespeopleService } from '../../salespeople/salespeople.service';
import { map, switchMap } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { inject, InjectionToken } from '@angular/core';

const salesPersonIdFactory = (loggedInUser: LoggedInUserInfoService) => {
  return loggedInUser.salesPersonId$;
};
export const SalespersonIdProvider = {
  provide: SALESPERSON_ID_TOKEN,
  useFactory: salesPersonIdFactory,
  deps: [LoggedInUserInfoService],
};

const salesPersonNameFactory = (loggedInUser: LoggedInUserInfoService, salespeople: SalespeopleService) => {
  return loggedInUser.loggedInUserInfo$.pipe(
    switchMap((l) => salespeople.getSalesPersonBySubjectId$(l.salespersonId, l.partnerId)),
    map((sp) => sp.fullName),
  );
};
export const SalespersonNameProvider = {
  provide: SALESPERSON_NAME_TOKEN,
  useFactory: salesPersonNameFactory,
  deps: [LoggedInUserInfoService, SalespeopleService],
};

export const SalespersonNameToken = new InjectionToken<Observable<string>>(
  '[Sales Center]: provider for SalespersonName',
  {
    providedIn: 'root',
    factory: function (): Observable<string> {
      return salesPersonNameFactory(inject(LoggedInUserInfoService), inject(SalespeopleService));
    },
  },
);
