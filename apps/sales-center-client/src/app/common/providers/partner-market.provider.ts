import { LoggedInUserInfoService } from '../../logged-in-user-info';
import {
  PARTNER_ID_TOKEN,
  MARKET_ID_TOKEN,
  PARTNER_ID_FROM_HOST_TOKEN,
  PARTNER_ID_TOKEN_FOR_EXTERNAL_DEP,
} from './tokens';
import { map } from 'rxjs/operators';
import { PartnerService } from '../../partner/partner.service';

const partnerIdFactory = (loggedInUser: LoggedInUserInfoService) => {
  return loggedInUser.partnerId$;
};
export const PartnerIdProvider = {
  provide: PARTNER_ID_TOKEN,
  useFactory: partnerIdFactory,
  deps: [LoggedInUserInfoService],
};
// Required for LexiconModule and PartnerSDK
export const PartnerIdProviderForExternalDeps = {
  provide: PARTNER_ID_TOKEN_FOR_EXTERNAL_DEP,
  useFactory: partnerIdFactory,
  deps: [LoggedInUserInfoService],
};

// FIXME: return singular market or change the name to be plural
// name suggests a single id and is injected with type Observable<string> in places which could result in unwanted behaviour.
const marketIdFactory = (loggedInUser: LoggedInUserInfoService) => {
  return loggedInUser.getMarketId().pipe(map((id) => [id]));
};
export const MarketIdProvider = {
  provide: MARKET_ID_TOKEN,
  useFactory: marketIdFactory,
  deps: [LoggedInUserInfoService],
};

const partnerIdFromHostFactory = (partner: PartnerService) => {
  return partner.partnerId$;
};
export const PartnerIdFromHostProvider = {
  provide: PARTNER_ID_FROM_HOST_TOKEN,
  useFactory: partnerIdFromHostFactory,
  deps: [PartnerService],
};
