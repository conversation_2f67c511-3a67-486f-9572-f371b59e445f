import { inject, InjectionToken } from '@angular/core';
// eslint-disable-next-line @nx/enforce-module-boundaries
import {
  getDefaultMeetingSchedulerLinks,
  MeetingSchedulerConfig,
  getDefaultMeetingSchedulerIcons,
} from '@galaxy/meeting-scheduler';
import { SalesTeamsService } from '../../sales-teams';
import { distinctUntilChanged, filter, map, shareReplay } from 'rxjs/operators';
import { LoggedInUserInfoService } from '../../logged-in-user-info';
import { SalespersonNameToken } from './salesperson.provider';
import { SERVICE_KEYS } from '../../providers/integrations.provider';
import { EVENTS_AND_MEETINGS_ROOT } from '../../urls';
import { SSCMeetingDetailsActionService } from '../../data-providers/meeting-scheduler/ssc-meeting-details-action.service';
import { USER_PARTNER_MARKET_TOKEN } from '../../core/feature-flag.service';
import { combineLatest, of } from 'rxjs';
import { Calendar, newSalesCenterApplicationContextProperties } from '@vendasta/meetings';
import { FeatureFlagService } from '../../core/feature-flag.service';
import {
  canAccessGroupsandServices,
  canAccessGroupCalendarSlugEditing,
  canAccessMeetingExpansion,
  canAccessMeetingEmailTemplate,
  canAccessServiceAndTeamLinks,
  canAccessMeetingEmbed,
  canAccessMeetingRoundrobin,
  canAccessMeetingPhysicalLocation,
  canAccessMeetingBeforeBuffer,
} from '../../features';

export const MEETING_SCHEDULER_CONFIG_TOKEN = new InjectionToken<MeetingSchedulerConfig>(
  '[Sales Center]: Config for MeetingScheduler',
  {
    providedIn: 'root',
    factory: function (): MeetingSchedulerConfig {
      const salesTeamService = inject(SalesTeamsService);
      const partnerId$ = inject(USER_PARTNER_MARKET_TOKEN).pipe(
        map((pm) => pm?.partnerId),
        distinctUntilChanged(),
        filter<string>(Boolean),
      );
      const marketId$ = inject(USER_PARTNER_MARKET_TOKEN).pipe(
        map((pm) => pm?.marketId),
        distinctUntilChanged(),
        filter<string>(Boolean),
      );
      const applicationContext$ = combineLatest([partnerId$, marketId$]).pipe(
        map(([partnerId, marketId]: [string, string]) =>
          newSalesCenterApplicationContextProperties({
            partner_id: partnerId,
            market_id: marketId,
          }),
        ),
      );

      const featureFlagService = inject(FeatureFlagService);

      const serviceAndTeamLinksEnabled$ = canAccessServiceAndTeamLinks(featureFlagService);
      const groupsAndServicesEnabled$ = canAccessGroupsandServices(featureFlagService);
      const meetingExpansionEnabled$ = canAccessMeetingExpansion(featureFlagService);
      const meetingEmailTemplateEnabled$ = canAccessMeetingEmailTemplate(featureFlagService);
      const meetingEmbedEnabled$ = canAccessMeetingEmbed(featureFlagService);
      const meetingRoundrobinEnabled$ = canAccessMeetingRoundrobin(featureFlagService);
      const meetingPhysicalLocationEnabled$ = canAccessMeetingPhysicalLocation(featureFlagService);
      const meetingBeforeBufferEnabled$ = canAccessMeetingBeforeBuffer(featureFlagService);

      const groupCalendarSlugEditingEnabled$ = canAccessGroupCalendarSlugEditing(featureFlagService);
      const salesTeamsForSalesPerson$ = salesTeamService.getTeamsForSalesPerson().pipe(
        distinctUntilChanged((a, b) => {
          if (!a) {
            return false;
          }
          if (!b) {
            return false;
          }
          const s = a.reduce((key, salesTeam) => `${key}-${salesTeam.groupId}`, '');
          const s2 = b.reduce((key, salesTeam) => `${key}-${salesTeam.groupId}`, '');
          return s === s2;
        }),
        shareReplay(1),
      );

      return {
        featureServicesAndTeamLinksEnabled$: serviceAndTeamLinksEnabled$,
        featureGroupsandServicesEnabled$: groupsAndServicesEnabled$,
        featureGroupCalendarSlugEditingEnabled$: groupCalendarSlugEditingEnabled$,
        featureMeetingExpansionEnabled$: meetingExpansionEnabled$,
        featureEmailTemplateEnabled$: meetingEmailTemplateEnabled$,
        featureEmbedCodeEnabled$: meetingEmbedEnabled$,
        featureRoundRobinEnabled$: meetingRoundrobinEnabled$,
        featurePhysicalLocationEnabled$: meetingPhysicalLocationEnabled$,
        featureMeetingBeforeBufferEnabled$: meetingBeforeBufferEnabled$,
        featureMicrosoftTeamsEnabled$: of(false),
        featureMyMeetingMultiHostEnabled$: of(false),
        featureDateRangeEnabled$: of(false),
        calendarService: {
          getCalendars: () =>
            salesTeamsForSalesPerson$.pipe(
              map((salesTeams) => {
                return (salesTeams || []).map(
                  (t) =>
                    ({
                      externalId: t.groupId,
                      displayName: t.teamName,
                      id: '',
                    }) as Calendar,
                );
              }),
            ),
        },
        userId$: inject(LoggedInUserInfoService).unifiedUserId$,
        hostId$: inject(LoggedInUserInfoService).salesPersonId$,
        applicationContext$: applicationContext$,
        recommendedPersonalCalendarSlugs$: inject(SalespersonNameToken).pipe(
          distinctUntilChanged(),
          map((fullName: string) => {
            if (!fullName) {
              return [];
            }
            const fullNameLowerCase = fullName.toLowerCase();
            const firstPart = fullNameLowerCase.split(/\s/g)[0].replace(/\W/g, '');
            const secondPart = fullNameLowerCase
              .slice(fullName.indexOf(firstPart) + firstPart.length + 1)
              .replace(/\W/g, '');
            const cleanedFullName = fullNameLowerCase.replace(/\W/g, '');
            const firstInitialLastName = `${firstPart.slice(0, 1)}${secondPart}`;
            return [
              firstInitialLastName,
              cleanedFullName,

              `${firstInitialLastName}1`,
              `${cleanedFullName}1`,

              `${firstInitialLastName}2`,
              `${cleanedFullName}2`,

              `${firstInitialLastName}3`,
              `${cleanedFullName}3`,

              `${firstInitialLastName}${Math.floor(Math.random() * 90 + 10)}`,
              `${cleanedFullName}${Math.floor(Math.random() * 90 + 10)}`,
            ];
          }),
        ),
        recommendedGroupCalendarSlugs$: salesTeamsForSalesPerson$.pipe(
          distinctUntilChanged(),
          map((teams) => {
            const result = {};
            teams.forEach((team) => {
              if (team.groupId && team.teamName) {
                const externalCalendarId = team.groupId;
                const lowered = team.teamName.toLowerCase();
                const cleaned = lowered.replace(/\s+/g, ' ').trim();
                const parts = cleaned.split(/\s/g);
                const dashSeparated = parts.join('-');
                const nonSeparated = parts.join();
                result[externalCalendarId] = [
                  dashSeparated,
                  nonSeparated,
                  `${dashSeparated}${Math.floor(Math.random() * 90 + 10)}`,
                  `${dashSeparated}${Math.floor(Math.random() * 90 + 10)}`,
                  `${nonSeparated}${Math.floor(Math.random() * 90 + 10)}`,
                  `${nonSeparated}${Math.floor(Math.random() * 90 + 10)}`,
                ];
              }
            });
            return result;
          }),
        ),

        links: getDefaultMeetingSchedulerLinks({
          meetingListPage: `/${EVENTS_AND_MEETINGS_ROOT}/`,
          settingsPage: `/${EVENTS_AND_MEETINGS_ROOT}/`,
          calendarPage: `/${EVENTS_AND_MEETINGS_ROOT}/`,
          eventTypePage: `/${EVENTS_AND_MEETINGS_ROOT}/`,
          groupPage: `/${EVENTS_AND_MEETINGS_ROOT}/`,
          servicePage: `/${EVENTS_AND_MEETINGS_ROOT}/`,
          integrationsPage: `/`,
        }),

        meetingIntegrationsServiceKeys: {
          integrationsPage: SERVICE_KEYS.integrationsPage,
          eventsAndMeetingsPage: SERVICE_KEYS.eventsAndMeetingsPage,
          eventsAndMeetingsPageCalendarConnected: SERVICE_KEYS.eventsAndMeetingsPageCalendarConnected,
          eventsAndMeetingsPageConferencingConnected: SERVICE_KEYS.eventsAndMeetingsPageConferencingConnected,
          settingsPageCalendarConnected: SERVICE_KEYS.settingsPageCalendarConnected,
          settingsPageConferencingConnected: SERVICE_KEYS.settingsPageConferencingConnected,
        },

        meetingNavigationLinkService: inject(SSCMeetingDetailsActionService),
        icons: getDefaultMeetingSchedulerIcons(),
      };
    },
  },
);
