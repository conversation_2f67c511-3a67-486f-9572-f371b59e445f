import { Component, Input, ChangeDetectionStrategy, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-status-banner',
  templateUrl: './banner.component.html',
  styleUrls: ['./banner.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class BannerComponent {
  @Input() statusText: string;
  @Input() type: string;
  @Input() border = true;
  @Input() actionTitle: string;
  @Input() processingAction = false;
  @Output() actionOnClick: EventEmitter<null> = new EventEmitter<null>();

  actionCallback(): void {
    this.actionOnClick.emit();
  }
}
