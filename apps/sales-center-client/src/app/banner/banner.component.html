<div
  [ngClass]="{
    warning: type === 'warn' || 'warn-soft',
    info: type === 'info',
    border: type !== 'warn-soft'
  }"
  class="banner-container"
>
  <div class="container">
    <div class="row">
      <div class="row-element col col-xs-12 col-sm-1">
        <mat-icon class="icon">
          {{ type === 'warn' ? 'warning' : 'info outline' }}
        </mat-icon>
      </div>
      <div class="row-element col col-xs-12 col-sm-9">
        <span class="status-text">{{ statusText }}</span>
      </div>
      <div class="row-element action col col-xs-12 col-sm-2" *ngIf="actionTitle && actionCallback">
        <ng-container *ngIf="processingAction; then processState; else normalState"></ng-container>
      </div>
      <div class="row-element action button-action col col-xs-12 col-sm-2" *ngIf="!actionTitle && actionCallback">
        <button mat-icon-button (click)="actionCallback()">
          <mat-icon>close</mat-icon>
        </button>
      </div>
    </div>
  </div>
</div>

<ng-template #processState>
  <mat-spinner class="loading-spinner" [diameter]="25" [strokeWidth]="3"></mat-spinner>
</ng-template>

<ng-template #normalState>
  <a (click)="actionCallback()">{{ actionTitle }}</a>
</ng-template>
