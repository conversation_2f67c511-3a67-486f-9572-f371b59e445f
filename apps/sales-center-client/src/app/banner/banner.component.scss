@use 'design-tokens' as *;

$standard-top-bottom-spacing: 3px;

.row-element {
  text-align: center;

  > * {
    display: inline-block;
  }

  > span {
    font-size: 12px;
  }
}

.banner-container {
  padding: 8px;
  border-radius: 2px;

  .message {
    display: flex;
    align-items: center;
    color: $secondary-font-color;
    font-family: Roboto, 'Helvetica Neue', sans-serif;

    span {
      margin-left: 12px;
    }
  }

  .action {
    margin: $standard-top-bottom-spacing auto;
    .mat-mdc-icon-button {
      max-height: 24px;
      max-width: 24px;
      line-height: 24px;
    }
    > a {
      margin-top: $standard-top-bottom-spacing;
    }
  }
}

.warning {
  background-color: $warn-background-color;
  .icon {
    color: $yellow;
  }
}
.border {
  border: 1px solid $warn-border-color;
}

.info {
  background-color: $glxy-blue-50;
  border: 1px solid $blue;
  .icon {
    margin-top: $standard-top-bottom-spacing;
    color: $blue;
  }
}

.status-text {
  margin: $standard-top-bottom-spacing * 2 auto;
  padding-top: $standard-top-bottom-spacing;
}

.button-action {
  padding-bottom: $standard-top-bottom-spacing 3px;
}
