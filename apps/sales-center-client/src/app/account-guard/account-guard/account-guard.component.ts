import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MANAGE_ACCOUNTS } from '../../urls';

@Component({
  selector: 'app-account-guard',
  template: '',
  standalone: false,
})
export class AccountGuardComponent implements OnInit {
  constructor(private readonly router: Router) {}

  ngOnInit(): void {
    this.navigateToManageAccountsPage();
  }

  private navigateToManageAccountsPage(): void {
    this.router.navigateByUrl(MANAGE_ACCOUNTS);
  }
}
