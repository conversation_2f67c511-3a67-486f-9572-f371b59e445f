import { TestBed } from '@angular/core/testing';

import { Salesperson } from '@galaxy/types';
import { AccountAnalyticsApiService, ArchivedFilter, Status } from '@vendasta/prospect';
import { Observable, of } from 'rxjs';
import { LoggedInUserInfoService } from '../logged-in-user-info';
import { PartnerMarket } from '../logged-in-user-info/logged-in-user-info.service';
import { SalespeopleService } from '../salespeople/salespeople.service';
import { AccountAnalyticsFilters, AccountAnalyticsService } from './account-analytics.service';

class AccountAnalyticsApiServiceStub {}

class SalespeopleServiceStub {
  public loadSalespeople(): Observable<Salesperson[]> {
    return of([]);
  }
}

class LoggedInUserInfoServiceStub {
  salesPersonId$ = of('UID-1234');
  hasAccessToAllAccountsInMarket$ = of(true);
  getPartnerIdAndMarketId$(): Observable<PartnerMarket> {
    return of(<PartnerMarket>{
      partnerId: 'ABC',
      marketId: 'whatever',
    });
  }
}

let service: AccountAnalyticsService;
let input: AccountAnalyticsFilters;

describe('AccountAnalyticsService', () => {
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        { provide: AccountAnalyticsApiService, useClass: AccountAnalyticsApiServiceStub },
        { provide: SalespeopleService, useClass: SalespeopleServiceStub },
        { provide: LoggedInUserInfoService, useClass: LoggedInUserInfoServiceStub },
      ],
    });
    service = TestBed.inject(AccountAnalyticsService);
    input = {};
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should have no assignee filters if not present in filters', () => {
    const result = service.convertFilters(input);
    expect(result.assignee).toEqual(undefined);
  });

  it('should add assignee to filters if present in filters', () => {
    input = {
      assignee: {
        partnerId: 'ABC',
        marketId: 'market',
        salespersonId: 'UID-9355e8d3-8881-4a04-ac74-57a087e1f3bf',
        firstName: 'John',
        lastName: 'Smth',
        fullName: 'John Smith',
        email: '<EMAIL>',
      },
    };
    const result = service.convertFilters(input);
    expect(result.assignee).toEqual('UID-9355e8d3-8881-4a04-ac74-57a087e1f3bf');
  });

  it('should have unread filter if passed true in filters', () => {
    input = { unread: true };
    const result = service.convertFilters(input);
    expect(result.attributeFilters.statuses.includes(Status.STATUS_UNREAD_ACTIVITY)).toBeTruthy();
  });

  it('should not have unread filter if passed false in filters', () => {
    input = { unread: false };
    const result = service.convertFilters(input);
    expect(result.attributeFilters.statuses.includes(Status.STATUS_UNREAD_ACTIVITY)).toBeFalsy();
  });

  it('should not have unread filter if not present in filters', () => {
    input = {};
    const result = service.convertFilters(input);
    expect(result.attributeFilters.statuses.includes(Status.STATUS_UNREAD_ACTIVITY)).toBeFalsy();
  });

  it('should have account-created filter if passed true in filters', () => {
    input = { 'account-created': true };
    const result = service.convertFilters(input);
    expect(result.attributeFilters.statuses.includes(Status.STATUS_ACCOUNT_CREATED)).toBeTruthy();
  });

  it('should not have account-created filter if passed false in filters', () => {
    input = { 'account-created': false };
    const result = service.convertFilters(input);
    expect(result.attributeFilters.statuses.includes(Status.STATUS_ACCOUNT_CREATED)).toBeFalsy();
  });

  it('should have ready-to-sell filter if passed true in filters', () => {
    input = { 'ready-to-sell': true };
    const result = service.convertFilters(input);
    expect(result.attributeFilters.statuses.includes(Status.STATUS_READY_TO_SELL)).toBeTruthy();
  });

  it('should not have ready-to-sell filter if passed false in filters', () => {
    input = { 'ready-to-sell': false };
    const result = service.convertFilters(input);
    expect(result.attributeFilters.statuses.includes(Status.STATUS_READY_TO_SELL)).toBeFalsy();
  });

  it('should have in-progress filter if passed true in filters', () => {
    input = { 'in-progress': true };
    const result = service.convertFilters(input);
    expect(result.attributeFilters.statuses.includes(Status.STATUS_IN_PROGRESS)).toBeTruthy();
  });

  it('should not have in-progress filter if passed false in filters', () => {
    input = { 'in-progress': false };
    const result = service.convertFilters(input);
    expect(result.attributeFilters.statuses.includes(Status.STATUS_IN_PROGRESS)).toBeFalsy();
  });

  it('should have follow-up-needed filter if passed true in filters', () => {
    input = { 'follow-up-needed': true };
    const result = service.convertFilters(input);
    expect(result.attributeFilters.statuses.includes(Status.STATUS_FOLLOW_UP_NEEDED)).toBeTruthy();
  });

  it('should not have follow-up-needed filter if passed false in filters', () => {
    input = { 'follow-up-needed': false };
    const result = service.convertFilters(input);
    expect(result.attributeFilters.statuses.includes(Status.STATUS_FOLLOW_UP_NEEDED)).toBeFalsy();
  });

  it('should have closed-won filter if passed true in filters', () => {
    input = { 'closed-won': true };
    const result = service.convertFilters(input);
    expect(result.attributeFilters.statuses.includes(Status.STATUS_CLOSED_WON)).toBeTruthy();
  });

  it('should not have closed-won filter if passed false in filters', () => {
    input = { 'closed-won': false };
    const result = service.convertFilters(input);
    expect(result.attributeFilters.statuses.includes(Status.STATUS_CLOSED_WON)).toBeFalsy();
  });

  it('should have closed-lost filter if passed true in filters', () => {
    input = { 'closed-lost': true };
    const result = service.convertFilters(input);
    expect(result.attributeFilters.statuses.includes(Status.STATUS_CLOSED_LOST)).toBeTruthy();
  });

  it('should not have closed-lost filter if passed false in filters', () => {
    input = { 'closed-lost': false };
    const result = service.convertFilters(input);
    expect(result.attributeFilters.statuses.includes(Status.STATUS_CLOSED_LOST)).toBeFalsy();
  });

  it('should have country filter if present in filters', () => {
    input = { country: 'US' };
    const result = service.convertFilters(input);
    expect(result.attributeFilters.countryCode).toEqual('US');
  });

  it('should not have country filter if not present in filters', () => {
    input = { country: null };
    const result = service.convertFilters(input);
    expect(result.attributeFilters.countryCode).toBeFalsy();
  });

  it('should have state filter if present in filters', () => {
    input = { state: 'AL' };
    const result = service.convertFilters(input);
    expect(result.attributeFilters.state).toEqual('AL');
  });

  it('should not have state filter if not present in filters', () => {
    input = { state: null };
    const result = service.convertFilters(input);
    expect(result.attributeFilters.state).toBeFalsy();
  });

  it('should have search if present in filters', () => {
    input = { search: 'text' };
    const result = service.convertFilters(input);
    expect(result.searchText).toEqual('text');
  });

  it('should not have search if not present in filters', () => {
    input = { search: null };
    const result = service.convertFilters(input);
    expect(result.searchText).toBeFalsy();
  });

  describe('isArchived', () => {
    it('should set ARCHIVED_FILTER_NOT_ARCHIVED if present in filters', () => {
      const value: Partial<keyof typeof ArchivedFilter> = 'ARCHIVED_FILTER_NOT_ARCHIVED';
      input = { isArchived: { value: value, description: '' } };
      const result = service.convertFilters(input);
      expect(result.attributeFilters.archivedFilter).toEqual(ArchivedFilter.ARCHIVED_FILTER_NOT_ARCHIVED);
    });

    it('should set ARCHIVED_FILTER_IS_ARCHIVED if present in filters', () => {
      const value: Partial<keyof typeof ArchivedFilter> = 'ARCHIVED_FILTER_IS_ARCHIVED';
      input = { isArchived: { value: value, description: '' } };
      const result = service.convertFilters(input);
      expect(result.attributeFilters.archivedFilter).toEqual(ArchivedFilter.ARCHIVED_FILTER_IS_ARCHIVED);
    });

    it('should set ARCHIVED_FILTER_ANY if present in filters', () => {
      const value: Partial<keyof typeof ArchivedFilter> = 'ARCHIVED_FILTER_ANY';
      input = { isArchived: { value: value, description: '' } };
      const result = service.convertFilters(input);
      expect(result.attributeFilters.archivedFilter).toEqual(ArchivedFilter.ARCHIVED_FILTER_ANY);
    });

    it('should not set if not present in filters', () => {
      input = { isArchived: undefined };
      const result = service.convertFilters(input);
      expect(result.attributeFilters.archivedFilter).toEqual(undefined);
    });
  });
});
