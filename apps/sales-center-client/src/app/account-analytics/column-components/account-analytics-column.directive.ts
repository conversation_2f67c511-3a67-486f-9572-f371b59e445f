import { Directive, Input } from '@angular/core';
import { ColumnDefinition, CustomCellComponent } from '@vendasta/va-filter2-table';
import { AccountAnalytics } from '../account-analytics.service';
import { TranslateService } from '@ngx-translate/core';

@Directive({
  // TODO: Fix this. We are converting to Galaxy, an are getting a lint error, but I have no idea what making this change would impact.
  // eslint-disable-next-line @angular-eslint/directive-selector
  selector: 'account-analytics-column',
  standalone: false,
})
export class AccountAnalyticsColumnDirective implements CustomCellComponent<AccountAnalytics> {
  @Input() columnDefinition: ColumnDefinition;
  @Input() translateService: TranslateService;
  @Input() element: AccountAnalytics;
}
