<mat-menu #actionsMenu="matMenu">
  <button mat-menu-item (click)="openOpportunityDialog()">
    {{ 'ACCOUNT_DETAILS.PAGE_MENU.CREATE_OPPORTUNITY' | translate }}
  </button>
  <button mat-menu-item (click)="openLogSalesActivity()">
    {{ 'SALES_ACTIVITY.LOG_CALL_EMAIL_MEETING' | translate }}
  </button>
  <button mat-menu-item *ngIf="canSendCampaigns$ | async" (click)="trackOpenEmail()">
    {{ 'CAMPAIGNS.START_CAMPAIGN' | translate }}
  </button>
  <ng-template matMenuContent>
    <button mat-menu-item [matMenuTriggerFor]="contactsMenu">
      {{ 'CONTACTS.COPY_EMAIL_TO_CLIPBOARD' | translate }}
    </button>
    <button mat-menu-item [matMenuTriggerFor]="callMenu">{{ 'CONTACTS.CALL_NOW' | translate }}</button>
  </ng-template>
</mat-menu>

<mat-menu #contactsMenu="matMenu">
  <ng-template matMenuContent>
    <ng-container *ngIf="contacts$ | glxyAsyncStatus | async as obs" [ngSwitch]="obs.status">
      <ng-container *ngSwitchCase="'loaded'">
        <ng-container *ngTemplateOutlet="emailContactsSubMenu; context: { $implicit: obs.value }"></ng-container>
      </ng-container>
      <ng-container *ngSwitchCase="'loading'">
        <ng-container *ngTemplateOutlet="loading"></ng-container>
      </ng-container>
      <ng-container *ngSwitchCase="'empty'">
        <ng-container *ngTemplateOutlet="noContacts"></ng-container>
      </ng-container>
    </ng-container>
  </ng-template>
</mat-menu>

<mat-menu #callMenu="matMenu">
  <ng-template matMenuContent>
    <ng-container *ngIf="contacts$ | glxyAsyncStatus | async as obs" [ngSwitch]="obs.status">
      <ng-container *ngSwitchCase="'loaded'">
        <ng-container *ngTemplateOutlet="callContactsSubMenu; context: { $implicit: obs.value }"></ng-container>
      </ng-container>
      <ng-container *ngSwitchCase="'loading'">
        <ng-container *ngTemplateOutlet="loading"></ng-container>
      </ng-container>
      <ng-container *ngSwitchCase="'empty'">
        <ng-container *ngTemplateOutlet="noContacts"></ng-container>
      </ng-container>
    </ng-container>
  </ng-template>
</mat-menu>

<a [matMenuTriggerFor]="actionsMenu">
  <mat-icon>more_vert</mat-icon>
</a>

<ng-template #addContactTemplate>
  <button mat-menu-item appAddContact [businessId]="businessId">
    {{ 'CONTACTS.ADD_CONTACT_FORM_TITLE' | translate }}
  </button>
  <mat-divider></mat-divider>
</ng-template>
<ng-template #loading>
  <ng-template [ngTemplateOutlet]="addContactTemplate"></ng-template>
  <button mat-menu-item disabled>{{ 'ACCOUNT_ANALYTICS.COLUMNS.LOADING_CONTACTS' | translate }}</button>
</ng-template>
<ng-template #noContacts>
  <button appAddContact [businessId]="businessId" mat-menu-item>
    {{ 'CONTACTS.EMPTY_CONTACTS_ADD_A_CONTACT' | translate }}
  </button>
</ng-template>

<ng-template #emailContactsSubMenu let-contacts>
  <ng-container *ngIf="contacts.length > 0; then displayEmailContacts; else noContacts"></ng-container>
  <ng-template #displayEmailContacts>
    <ng-template [ngTemplateOutlet]="addContactTemplate"></ng-template>
    <span class="menu-item" *ngFor="let contact of contacts">
      <button mat-menu-item [copyToClipBoardAndAlert]="{ email: contact.email }" [disabled]="!contact.email">
        {{ contact | contactFullName }}
      </button>
      <mat-icon appEditContact [contactToEdit]="contact" [businessId]="businessId" class="edit-icon">edit</mat-icon>
    </span>
  </ng-template>
</ng-template>

<ng-template #callContactsSubMenu let-contacts>
  <ng-container *ngIf="contacts.length > 0; then displayCallContacts; else noContacts"></ng-container>
  <ng-template #displayCallContacts>
    <ng-template [ngTemplateOutlet]="addContactTemplate"></ng-template>
    <span class="menu-item" *ngFor="let contact of contacts">
      <button mat-menu-item (click)="callContact(contact)" [disabled]="!contact.phoneNumber">
        {{ contact | contactFullName }}
      </button>
      <mat-icon appEditContact [contactToEdit]="contact" [businessId]="this.businessId" class="edit-icon">
        edit
      </mat-icon>
    </span>
  </ng-template>
</ng-template>
