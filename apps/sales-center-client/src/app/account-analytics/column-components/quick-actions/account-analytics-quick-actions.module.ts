import { NgModule } from '@angular/core';
import { SalesOpportunitiesModule } from '../../../sales-opportunities/sales-opportunities.module';
import { AccountAnalyticsQuickActionsComponent } from './account-analytics-quick-actions.component';
import { TranslateModule } from '@ngx-translate/core';
import { AsteriskService } from '../../../astbe_sdk';
import { CommonModule } from '@angular/common';
import { MatMenuModule } from '@angular/material/menu';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { ContactFormsModule } from '../../../common/contacts';
import { CopyToClipBoardAndAlertModule } from '../../../common';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { ContactPipeModule } from '../../../common/contacts/contact-pipe/contact-pipe.module';
import { GalaxySnackbarModule } from '@vendasta/galaxy/snackbar-service';

@NgModule({
  declarations: [AccountAnalyticsQuickActionsComponent],
  imports: [
    CommonModule,
    SalesOpportunitiesModule,
    TranslateModule,
    MatMenuModule,
    MatIconModule,
    MatDividerModule,
    ContactFormsModule,
    CopyToClipBoardAndAlertModule,
    GalaxyPipesModule,
    ContactPipeModule,
    GalaxySnackbarModule,
  ],
  exports: [AccountAnalyticsQuickActionsComponent],
  providers: [AsteriskService],
})
export class AccountAnalyticsQuickActionsModule {}
