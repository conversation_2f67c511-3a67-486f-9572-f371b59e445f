import { Component, Inject, Input, OnInit } from '@angular/core';
import { AccountAnalyticsColumnDirective } from '../account-analytics-column.directive';

import { MatDialog } from '@angular/material/dialog';
import { CampaignSidePanelDataInterface, Pipeline, SidePanelState, SidePanelStateService } from '@vendasta/sales-ui';
import { Observable } from 'rxjs';
import { AsteriskService } from '../../../astbe_sdk';
import {
  AddSalesActivityDialogComponent,
  AddSalesActivityDialogData,
} from '../../../common/sales-activity/add-sales-activity-dialog';
import { PartnerMarket } from '../../../logged-in-user-info/logged-in-user-info.service';
import {
  CreateSalesOpportunityComponent,
  OpportunityCreateDialogData,
} from '../../../sales-opportunities/create/create-sales-opportunities.component';

import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { SlideOutPanelService } from '../../../common';
import { AllContactFields } from '../../../common/contacts/contact-field.enum';
import { ContactShadow } from '../../../common/contacts/contact-v2';
import { ContactsV2Service } from '../../../common/contacts/contacts-v2.service';
import { ADD_TO_CAMPAIGNS_TOKEN } from '../../../common/providers/tokens';

@Component({
  selector: 'app-account-analytics-quick-actions-column',
  templateUrl: './account-analytics-column.html',
  styleUrls: ['./account-analytics-quick-actions.scss'],
  standalone: false,
})
export class AccountAnalyticsQuickActionsComponent extends AccountAnalyticsColumnDirective implements OnInit {
  contacts$: Observable<ContactShadow[]>;
  @Input() pipeline: Pipeline;
  @Input() businessId: string;
  @Input() businessName: string;
  @Input() partnerMarketKey: PartnerMarket;

  constructor(
    private readonly dialog: MatDialog,
    private readonly snackbarService: SnackbarService,
    private readonly contactService: ContactsV2Service,
    private readonly phoneService: AsteriskService,
    private readonly sidePanelStateService: SidePanelStateService,
    private readonly slideOutPanelService: SlideOutPanelService,
    @Inject(ADD_TO_CAMPAIGNS_TOKEN) public readonly canSendCampaigns$: Observable<boolean>,
  ) {
    super();
  }

  ngOnInit(): void {
    this.contacts$ = this.contactService.list$(this.businessId, ...AllContactFields);
  }

  callContact(contact: ContactShadow): void {
    const phoneNumber = contact.phoneNumber;
    if (!phoneNumber) {
      this.snackbarService.openErrorSnack('CONTACTS.CALL_NOW_MISSING_NUMBER_OR_EXTENSION_MESSAGE');
      return;
    }
    const phoneNumberNormalized = phoneNumber.replace(/ -+/g, '');
    this.phoneService.queueCall(phoneNumberNormalized, false);
  }

  openOpportunityDialog(): void {
    const dialogData: OpportunityCreateDialogData = {
      accountGroupId: this.businessId,
      navigationData: {
        navigateOnSuccess: false,
      },
    };
    this.dialog.open(CreateSalesOpportunityComponent, { data: dialogData });
  }

  openLogSalesActivity(): void {
    this.dialog.open(AddSalesActivityDialogComponent, {
      data: <AddSalesActivityDialogData>{
        accountGroupId: this.businessId,
        accountGroupName: this.businessName,
        marketId: this.partnerMarketKey.marketId,
        partnerId: this.partnerMarketKey.partnerId,
      },
    });
  }

  trackOpenEmail(): void {
    this.sidePanelStateService.switchState(SidePanelState.campaignCreate, <CampaignSidePanelDataInterface>{
      accountGroupId: this.businessId,
    });
    this.slideOutPanelService.openSlideOut();
  }
}
