import { Component } from '@angular/core';
import { AccountAnalyticsColumnDirective } from './account-analytics-column.directive';

@Component({
  selector: 'app-account-analytics-assignees-column',
  template: `
    <div *ngIf="element.assignees?.length > 0; else noAssignees">
      <span>{{ element.assigneeNames$ | async }}</span>
    </div>
    <ng-template #noAssignees>
      <div class="empty-cell" [matTooltip]="'PROSPECT_COMPONENT.TOOLTIPS.SALESPERSON' | translate">—</div>
    </ng-template>
  `,
  styleUrls: ['./account-analytics-column-components.scss'],
  standalone: false,
})
export class AccountAnalyticsAssigneeColumnComponent extends AccountAnalyticsColumnDirective {}
