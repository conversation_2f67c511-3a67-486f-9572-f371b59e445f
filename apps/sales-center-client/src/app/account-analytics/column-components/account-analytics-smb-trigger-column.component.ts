import { Component } from '@angular/core';
import { AccountAnalyticsColumnDirective } from './account-analytics-column.directive';

@Component({
  template: ` <div>{{ COLUMN_VALUE(columnDefinition.id, element.metrics) }}</div> `,
  styleUrls: ['./account-analytics-column-components.scss'],
  standalone: false,
})
export class AccountAnalyticsSmbTriggerColumnComponent extends AccountAnalyticsColumnDirective {
  COLUMN_VALUE(id: string, metrics: any): void {
    return metrics[id] || 0;
  }
}
