import { Component, Inject } from '@angular/core';
import { AccountAnalyticsColumnDirective } from './account-analytics-column.directive';

import { Pipeline, USER_PIPELINE_TOKEN } from '@vendasta/sales-ui';
import { Observable } from 'rxjs';
import { USER_PARTNER_MARKET_TOKEN } from '../../core/feature-flag.service';
import { PartnerMarket } from '../../logged-in-user-info/logged-in-user-info.service';

@Component({
  selector: 'app-account-analytics-name-column',
  template: `
    <div class="flex-cell">
      <div class="action-kebab">
        <app-account-analytics-quick-actions-column
          *ngIf="partnerMarket$ | async as pm"
          [pipeline]="pipeline$ | async"
          [businessId]="element.businessId"
          [businessName]="element.name"
          [partnerMarketKey]="pm"
          [translateService]="translateService"
        ></app-account-analytics-quick-actions-column>
      </div>
      <div class="business-name">
        <a class="hover-underline" routerLink="{{ element.manageAccountUrl }}">
          {{ element.name }}
        </a>
      </div>
    </div>
  `,
  styleUrls: ['./account-analytics-column-components.scss'],
  standalone: false,
})
export class AccountAnalyticsNameColumnComponent extends AccountAnalyticsColumnDirective {
  constructor(
    @Inject(USER_PIPELINE_TOKEN) readonly pipeline$: Observable<Pipeline>,
    @Inject(USER_PARTNER_MARKET_TOKEN) readonly partnerMarket$: Observable<PartnerMarket>,
  ) {
    super();
  }
}
