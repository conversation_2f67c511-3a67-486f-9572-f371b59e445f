import { Component } from '@angular/core';
import { AccountAnalyticsColumnDirective } from './account-analytics-column.directive';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-account-analytics-connected-accounts-column',
  template: ` <div>{{ COLUMN_VALUE(columnDefinition.id, element.attributes, translateService) }}</div> `,
  styleUrls: ['./account-analytics-column-components.scss'],
  standalone: false,
})
export class AccountAnalyticsActivityRegisteredColumnComponent extends AccountAnalyticsColumnDirective {
  COLUMN_VALUE(id: string, attributes: any, translator: TranslateService): string {
    if (!attributes[id]) {
      return '';
    }
    return attributes[id] === 'NULL' ? '' : translator.instant('COMMON.YES');
  }
}
