import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AccountAnalyticsComponent } from './account-analytics.component';
import { FilterModule, FilterService } from '@vendasta/va-filter2';
import { UIKitModule } from '@vendasta/uikit';
import { NavigationModule } from '../navigation/navigation.module';
import { AccountAnalyticsService } from './account-analytics.service';
import { AccountAnalyticsApiService, HostService } from '@vendasta/prospect';
import { AccountAnalyticsRoutingModule } from './account-analytics-routing.module';
import { TranslateModule } from '@ngx-translate/core';
import { AccountAnalyticsAssigneeColumnComponent } from './column-components/account-analytics-assignee-column.component';
import { AccountAnalyticsNameColumnComponent } from './column-components/account-analytics-name-column.component';
import { AccountAnalyticsFilterService } from './account-analytics-filter.service';
import { AccountAnalyticsSmbTriggerColumnComponent } from './column-components/account-analytics-smb-trigger-column.component';
import { AccountAnalyticsActivityRegisteredColumnComponent } from './column-components/account-analytics-activity-registered-column.component';
import {
  Filter2TableModule,
  TABLE_DEFINITION,
  VaFilteredMatTableService,
  VaTableSortService,
} from '@vendasta/va-filter2-table';
import { tableDefinitionFactory } from './account-analytics-table-model';
import { AccountAnalyticsQuickActionsModule } from './column-components/quick-actions/account-analytics-quick-actions.module';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AccountAnalyticsColumnDirective } from './column-components/account-analytics-column.directive';
import { LexiconModule } from '@galaxy/lexicon';
import baseTranslation from '../../assets/i18n/en_devel.json';
import { WEBLATE_COMPONENT_NAME } from '../constants';
import { SidePanelStateService, SlideOutPanelModule } from '@vendasta/sales-ui';
import { CampaignsModule } from '../campaigns';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { ContactPipeModule } from '../common/contacts/contact-pipe/contact-pipe.module';

@NgModule({
  declarations: [
    AccountAnalyticsComponent,
    AccountAnalyticsAssigneeColumnComponent,
    AccountAnalyticsNameColumnComponent,
    AccountAnalyticsSmbTriggerColumnComponent,
    AccountAnalyticsActivityRegisteredColumnComponent,
    AccountAnalyticsColumnDirective,
  ],
  imports: [
    AccountAnalyticsRoutingModule,
    CommonModule,
    FilterModule,
    NavigationModule,
    MatToolbarModule,
    UIKitModule,
    GalaxyPageModule,
    TranslateModule,
    LexiconModule.forChild({
      componentName: WEBLATE_COMPONENT_NAME,
      baseTranslation: baseTranslation,
    }),
    MatTooltipModule,
    Filter2TableModule,
    AccountAnalyticsQuickActionsModule,
    SlideOutPanelModule,
    CampaignsModule,
    ContactPipeModule,
  ],
  providers: [
    AccountAnalyticsService,
    AccountAnalyticsApiService,
    HostService,
    AccountAnalyticsFilterService,
    FilterService,
    { provide: TABLE_DEFINITION, useFactory: tableDefinitionFactory, deps: [AccountAnalyticsFilterService] },
    VaTableSortService,
    VaFilteredMatTableService,
    SidePanelStateService,
  ],
})
export class AccountAnalyticsModule {}
