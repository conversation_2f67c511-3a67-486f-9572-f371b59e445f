import { Component } from '@angular/core';
import { AccountAnalyticsService } from './account-analytics.service';
import { Observable } from 'rxjs';
import { SidePanelState, SidePanelStateData, SidePanelStateService } from '@vendasta/sales-ui';

@Component({
  selector: 'app-account-analytics',
  templateUrl: './account-analytics.component.html',
  styleUrls: ['./account-analytics.component.scss'],
  standalone: false,
})
export class AccountAnalyticsComponent {
  readonly sidePanelState$: Observable<SidePanelStateData<any>>;
  readonly campaignCreateSidePanelCase = SidePanelState.campaignCreate;

  constructor(
    readonly dataService: AccountAnalyticsService,
    private readonly sidePanelStateService: SidePanelStateService,
  ) {
    this.sidePanelState$ = this.sidePanelStateService.sidePanelState$;
  }
}
