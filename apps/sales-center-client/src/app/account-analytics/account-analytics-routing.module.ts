import { RouterModule, Routes } from '@angular/router';
import { NgModule } from '@angular/core';
import { RouteParamsService } from '@vendasta/route-params';
import { AccountAnalyticsComponent } from './account-analytics.component';

const routes: Routes = [
  {
    path: '',
    component: AccountAnalyticsComponent,
    resolve: { routeParams: RouteParamsService },
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AccountAnalyticsRoutingModule {}
