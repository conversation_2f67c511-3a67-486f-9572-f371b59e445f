import { DateColumnComponent, ColumnOrganizerType } from '@vendasta/va-filter2-table';
import { TableDefinition } from '@vendasta/va-filter2-table';
import { ColumnType, Pinned, SortDirection } from '@vendasta/va-filter2-table';
import { COMMON_TABLE_LABELS } from '../common/table-labels';
import { AccountAnalyticsAssigneeColumnComponent } from './column-components/account-analytics-assignee-column.component';
import { AccountAnalyticsNameColumnComponent } from './column-components/account-analytics-name-column.component';
import { AccountAnalyticsFilterService } from './account-analytics-filter.service';
import { AccountAnalyticsSmbTriggerColumnComponent } from './column-components/account-analytics-smb-trigger-column.component';
import { AccountAnalyticsActivityRegisteredColumnComponent } from './column-components/account-analytics-activity-registered-column.component';
import { AccountAnalytics } from './account-analytics.service';

const ACCOUNT_ANALYTICS_TABLE_DEF: TableDefinition = {
  id: 'account-analytics',
  labels: COMMON_TABLE_LABELS,
  columns: [
    {
      id: 'name',
      displayName: 'ACCOUNT_ANALYTICS.COLUMNS.NAME',
      cellComponent: AccountAnalyticsNameColumnComponent,
      sortOptions: {
        sortable: true,
        initialSortDirection: SortDirection.SORT_DIRECTION_ASCENDING,
      },
      pinned: Pinned.PINNED_LEFT,
      type: ColumnType.COLUMN_TYPE_STRING,
    },
    {
      id: 'assignees',
      displayName: 'ACCOUNT_ANALYTICS.COLUMNS.ASSIGNEES',
      cellComponent: AccountAnalyticsAssigneeColumnComponent,
    },
    {
      id: 'unreadActivity',
      displayName: 'ACCOUNT_ANALYTICS.COLUMNS.UNREAD_ACTIVITY',
      field: 'attributes.unreadActivity',
      cellComponent: AccountAnalyticsActivityRegisteredColumnComponent,
    },
    {
      id: 'status',
      displayName: 'ACCOUNT_ANALYTICS.COLUMNS.STATUS',
      field: 'statusString',
      translateCell: true,
      type: ColumnType.COLUMN_TYPE_STRING,
    },
    {
      id: 'city',
      displayName: 'ACCOUNT_ANALYTICS.COLUMNS.CITY',
      field: 'attributes.city',
      sortOptions: {
        sortable: true,
      },
      type: ColumnType.COLUMN_TYPE_STRING,
    },
    {
      id: 'state',
      displayName: 'ACCOUNT_ANALYTICS.COLUMNS.STATE',
      field: 'attributes.state',
      sortOptions: {
        sortable: true,
      },
      type: ColumnType.COLUMN_TYPE_STRING,
    },
    {
      id: 'country',
      displayName: 'ACCOUNT_ANALYTICS.COLUMNS.COUNTRY',
      field: 'attributes.countryCode',
      sortOptions: {
        sortable: true,
      },
      type: ColumnType.COLUMN_TYPE_STRING,
    },
    {
      id: 'lastSalesActivityDate',
      displayName: 'ACCOUNT_ANALYTICS.COLUMNS.LAST_SALES_ACTIVITY_DATE',
      field: 'attributes.lastSalesActivityDate',
      cellComponent: DateColumnComponent,
      sortOptions: {
        sortable: true,
      },
      type: ColumnType.COLUMN_TYPE_DATE,
    },
    {
      id: 'contactSalesCount',
      displayName: 'ACCOUNT_ANALYTICS.COLUMNS.CONTACT_SALES_COUNT',
      field: 'metrics.contactSalesCount',
      sortOptions: {
        sortable: true,
      },
      cellComponent: AccountAnalyticsSmbTriggerColumnComponent,
      type: ColumnType.COLUMN_TYPE_NUMBER,
    },
    {
      id: 'interestInPackageOrProduct',
      displayName: 'ACCOUNT_ANALYTICS.COLUMNS.INTEREST_IN_PACKAGE_OR_PRODUCT',
      field: 'metrics.interestInPackageOrProduct',
      sortOptions: {
        sortable: true,
      },
      cellComponent: AccountAnalyticsSmbTriggerColumnComponent,
      type: ColumnType.COLUMN_TYPE_NUMBER,
    },
    {
      id: 'viewStoreCount',
      displayName: 'ACCOUNT_ANALYTICS.COLUMNS.VIEW_STORE_COUNT',
      field: 'metrics.viewStoreCount',
      sortOptions: {
        sortable: true,
      },
      cellComponent: AccountAnalyticsSmbTriggerColumnComponent,
      type: ColumnType.COLUMN_TYPE_NUMBER,
    },
    {
      id: 'createSingleCustomerCount',
      displayName: 'ACCOUNT_ANALYTICS.COLUMNS.CREATE_SINGLE_CUSTOMER',
      field: 'metrics.createSingleCustomerCount',
      sortOptions: {
        sortable: true,
      },
      cellComponent: AccountAnalyticsSmbTriggerColumnComponent,
      type: ColumnType.COLUMN_TYPE_NUMBER,
    },
    {
      id: 'createBulkCustomerCount',
      displayName: 'ACCOUNT_ANALYTICS.COLUMNS.CREATE_BULK_CUSTOMER',
      field: 'metrics.createBulkCustomerCount',
      sortOptions: {
        sortable: true,
      },
      cellComponent: AccountAnalyticsSmbTriggerColumnComponent,
      type: ColumnType.COLUMN_TYPE_NUMBER,
    },
    {
      id: 'viewExecReportCount',
      displayName: 'ACCOUNT_ANALYTICS.COLUMNS.VIEWED_EXECUTIVE_REPORT',
      field: 'metrics.viewExecReportCount',
      sortOptions: {
        sortable: true,
      },
      cellComponent: AccountAnalyticsSmbTriggerColumnComponent,
      type: ColumnType.COLUMN_TYPE_NUMBER,
    },
    {
      id: 'updateBusinessProfileCount',
      displayName: 'ACCOUNT_ANALYTICS.COLUMNS.UPDATED_BUSINESS_PROFILE',
      field: 'metrics.updateBusinessProfileCount',
      sortOptions: {
        sortable: true,
      },
      cellComponent: AccountAnalyticsSmbTriggerColumnComponent,
      type: ColumnType.COLUMN_TYPE_NUMBER,
    },
    {
      id: 'lockHitCount',
      displayName: 'ACCOUNT_ANALYTICS.COLUMNS.VIEWED_LOCKED_PAGE',
      field: 'metrics.lockHitCount',
      sortOptions: {
        sortable: true,
      },
      cellComponent: AccountAnalyticsSmbTriggerColumnComponent,
      type: ColumnType.COLUMN_TYPE_NUMBER,
    },
    {
      id: 'facebookPageId',
      displayName: 'ACCOUNT_ANALYTICS.COLUMNS.CONNECTED_FACEBOOK',
      field: 'attributes.facebookPageId',
      sortOptions: {
        sortable: true,
      },
      cellComponent: AccountAnalyticsActivityRegisteredColumnComponent,
    },
    {
      id: 'googleMyBusinessPlaceId',
      displayName: 'ACCOUNT_ANALYTICS.COLUMNS.CONNECTED_GOOGLE_MY_BUSINESS',
      field: 'attributes.googleMyBusinessPlaceId',
      sortOptions: {
        sortable: true,
      },
      cellComponent: AccountAnalyticsActivityRegisteredColumnComponent,
    },
    {
      id: 'numberOfDaysUserWasActive',
      displayName: 'ACCOUNT_ANALYTICS.COLUMNS.DAYS_USERS_LOGGED_IN',
      field: 'metrics.numberOfDaysUserWasActive',
      sortOptions: {
        sortable: true,
      },
      cellComponent: AccountAnalyticsSmbTriggerColumnComponent,
      type: ColumnType.COLUMN_TYPE_NUMBER,
    },
    {
      id: 'isArchived',
      displayName: 'COMMON.ATTRIBUTES.ARCHIVED',
      field: 'attributes.isArchived',
      toStringFunction: (element: AccountAnalytics) => {
        const data = element?.attributes?.isArchived;
        if (data) {
          return data.toString();
        }
        return '';
      },
    },
  ],
  groups: [
    {
      name: 'Account Information',
      id: 'accountInformatin',
      columns: ['name', 'city', 'state', 'country'],
    },
    {
      name: 'Engagement',
      id: 'engagement',
      columns: [
        'contactSalesCount',
        'interestInPackageOrProduct',
        'viewStoreCount',
        'createSingleCustomerCount',
        'createBulkCustomerCount',
        'viewExecReportCount',
        'updateBusinessProfileCount',
        'lockHitCount',
        'facebookPageId',
        'googleMyBusinessPlaceId',
        'numberOfDaysUserWasActive',
      ],
    },
    {
      name: 'Sales',
      id: 'sales',
      columns: ['unreadActivity', 'assignees', 'lastSalesActivityDate', 'status', 'isArchived'],
    },
  ],
  columnOrganizerType: ColumnOrganizerType.ADVANCED_ORGANIZER,
};

export const tableDefinitionFactory = (filterService: AccountAnalyticsFilterService) => {
  const tableDefintion = ACCOUNT_ANALYTICS_TABLE_DEF;
  tableDefintion.filters = filterService.filters;
  return tableDefintion;
};
