import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Salesperson as CoreSalesperson } from '@vendasta/salesperson';
import { FilterGroup, FilterService, SearchFilterControl, SelectFilterControl } from '@vendasta/va-filter2';
import { Observable, combineLatest, of } from 'rxjs';
import { distinctUntilChanged, map, mergeMap, shareReplay, take, tap } from 'rxjs/operators';
import { CountryStateService } from '../common';
import { ArchivedFilterValue, NewCheckboxFilterControl, NewLatestSalesActivityFilter } from '../common/filter-controls';
import { LoggedInUserInfoService } from '../logged-in-user-info';
import { SalespeopleService } from '../salespeople/salespeople.service';

@Injectable()
export class AccountAnalyticsFilterService {
  filters: FilterGroup = new FilterGroup('account-analytics');

  constructor(
    private readonly salespeopleService: SalespeopleService,
    private readonly translateService: TranslateService,
    private readonly countryStateService: CountryStateService,
    private readonly filterService: FilterService,
    private readonly loggedInUserInfoService: LoggedInUserInfoService,
  ) {
    const salesPeople$ = this.salespeopleService.loadSalespeople(true).pipe(
      map((sscsp) =>
        sscsp.map(
          (sp) =>
            <CoreSalesperson>{
              partnerId: sp.partnerId,
              marketId: sp.marketId,
              salespersonId: sp.id,
              firstName: sp.fullName.split(' ')[0],
              lastName: sp.fullName.split(' ').reverse()[0],
              email: sp.email,
              fullName: sp.fullName,
            },
        ),
      ),
      shareReplay(1),
    );

    const assigneeMap$ = salesPeople$.pipe(
      map((salespeople) => {
        const salespersonMap = new Map<string, CoreSalesperson>();
        salespeople.forEach((salesperson) => {
          const unassigendSP: CoreSalesperson = {
            email: '',
            firstName: '',
            fullName: 'Unassigned',
            lastName: '',
            marketId: '',
            partnerId: '',
            salespersonId: 'unassigned',
          };
          salespersonMap.set('Unassigned', unassigendSP);
          salespersonMap.set(salesperson.fullName, salesperson);
        });
        return salespersonMap;
      }),
    );

    const statuses = [
      ['unread', 'COMMON.SALES_STATUS.UNREAD_ACTIVITY'],
      ['account-created', 'COMMON.SALES_STATUS.ACCOUNT_CREATED'],
      ['ready-to-sell', 'COMMON.SALES_STATUS.READY_TO_SELL'],
      ['in-progress', 'COMMON.SALES_STATUS.IN_PROGRESS'],
      ['follow-up-needed', 'COMMON.SALES_STATUS.FOLLOW_UP_NEEDED'],
      ['closed-won', 'COMMON.SALES_STATUS.CLOSED_WON'],
      ['closed-lost', 'COMMON.SALES_STATUS.COMMON.SALES_STATUS.CLOSED_LOST'],
    ];

    const stateSelectFilterControl = new SelectFilterControl('state', 'State');
    const assigneeControl = new SelectFilterControl('assignee', 'Assignee', assigneeMap$, null, {
      appliedValueMapper: (name, value) => ({ name, label: value.fullName }),
    });
    this.filters.addToolbarSection(new SearchFilterControl('search', 'Search'));

    this.filters.addSection(this.translateService.instant('COMMON.ASSIGNEE_SINGULAR'), [assigneeControl]);
    this.loggedInUserInfoService.hasAccessToAllAccountsInMarket$
      .pipe(take(1))
      .subscribe((hasAccessToAllAccountsInMarket) => {
        if (!hasAccessToAllAccountsInMarket) {
          this.filters.removeSection(this.translateService.instant('COMMON.ASSIGNEE_SINGULAR'));
        }
      });

    this.filters
      .addSection(
        this.translateService.instant('COMMON.SALES_STATUS_LABEL'),
        statuses
          .map(([k, v]) => NewCheckboxFilterControl(k, this.translateService.instant(v)))
          .concat([
            new SelectFilterControl(
              'isArchived',
              this.translateService.instant('COMMON.ATTRIBUTES.ARCHIVED'),
              this.populateIsArchivedFilter$(),
              {
                description: this.translateService.instant('COMMON.ATTRIBUTES.NOT_ARCHIVED'),
                value: 'ARCHIVED_FILTER_NOT_ARCHIVED',
              },
              {
                appliedValueMapper: (name, value) => ({
                  name,
                  label: `${this.translateService.instant('COMMON.ATTRIBUTES.ARCHIVED')}: ${value.description}`,
                }),
              },
            ),
          ]),
      )
      .addSection(this.translateService.instant('COMMON.LOCATION'), [
        new SelectFilterControl(
          'country',
          this.translateService.instant('ACCOUNT_ANALYTICS.COLUMNS.COUNTRY'),
          this.populateCountryOptions(),
        ),
        stateSelectFilterControl,
      ]);
    AddLatestSalesActivityFilterOptionsToFilterGroup(this.filters, this.translateService);

    stateSelectFilterControl.options$ = this.populateStateOptions();
    const loggedInUserId$ = this.salespeopleService.getCurrentSalesPerson().pipe(map((sp) => sp.id));
    const currentAssignee$ = combineLatest([salesPeople$, loggedInUserId$]).pipe(
      map(([people, userId]) => people.find((person) => person.salespersonId === userId)),
    );
    currentAssignee$
      .pipe(
        take(1),
        tap((assignee) => assigneeControl.setValue(assignee)),
      )
      .subscribe();
  }

  private populateCountryOptions(): Observable<Map<string, string>> {
    return this.countryStateService.getCountriesOptions().pipe(
      map((countries) => {
        const optionsMap = new Map<string, string>();

        const sortAtTop = ['US', 'CA'];
        const others = countries.filter((c) => sortAtTop.indexOf(c.code) === -1);
        countries
          .filter((c) => sortAtTop.indexOf(c.code) !== -1)
          .concat(others)
          .forEach((v) => {
            optionsMap.set(v.name, v.code);
          });
        return optionsMap;
      }),
    );
  }

  private populateStateOptions(): Observable<Map<string, string>> {
    return this.filters.valueChanges.pipe(
      map((f) => f['country']),
      distinctUntilChanged(),
      tap(() => {
        this.filterService.clearValue('state', '');
      }),
      mergeMap((country) => {
        if (!country) {
          return of(new Map<string, string>());
        }
        return this.countryStateService.getStatesOptions(country).pipe(
          map((states) => {
            const optionsMap = new Map<string, string>();
            states.map((s) => {
              optionsMap.set(s.name, s.code);
            });
            return optionsMap;
          }),
        );
      }),
    );
  }

  private populateIsArchivedFilter$(): Observable<Map<string, ArchivedFilterValue>> {
    return of(
      new Map<string, ArchivedFilterValue>([
        [
          this.translateService.instant('COMMON.ATTRIBUTES.NOT_ARCHIVED'),
          {
            value: 'ARCHIVED_FILTER_NOT_ARCHIVED',
            description: this.translateService.instant('COMMON.ATTRIBUTES.NOT_ARCHIVED'),
          },
        ],
        [
          this.translateService.instant('COMMON.ATTRIBUTES.ARCHIVED'),
          {
            value: 'ARCHIVED_FILTER_IS_ARCHIVED',
            description: this.translateService.instant('COMMON.ATTRIBUTES.ARCHIVED'),
          },
        ],
        [
          this.translateService.instant('COMMON.ANY'),
          {
            value: 'ARCHIVED_FILTER_ANY',
            description: this.translateService.instant('COMMON.ANY'),
          },
        ],
      ]),
    );
  }
}

export function AddLatestSalesActivityFilterOptionsToFilterGroup(fs: FilterGroup, ts: TranslateService): void {
  fs.addSection(ts.instant('COMMON.ACTIVITY'), [NewLatestSalesActivityFilter(ts)]);
}
