import { Injectable, OnDestroy } from '@angular/core';
import { Salesperson } from '@galaxy/types';
import {
  AccountAnalyticsApiService,
  AccountFiltersInterface,
  AccountInterface,
  SortDirection as ApiSortDirection,
  ArchivedFilter,
  ListAccountAnalyticsRequestInterface,
  Status,
} from '@vendasta/prospect';
import { SubscriptionList } from '@vendasta/rx-utils';
import { Salesperson as CoreSalesperson } from '@vendasta/salesperson';
import { LoadRequest, SortDirection, TableDataService } from '@vendasta/va-filter2-table';
import { BehaviorSubject, Observable, ReplaySubject, combineLatest, of } from 'rxjs';
import { map, mergeMap, scan, shareReplay, switchMap, take } from 'rxjs/operators';
import {
  ArchivedFilterValue,
  SalesActivityFilterOption,
  SalesActivityFilterOptionToSdk,
} from '../common/filter-controls';
import { StatusToTranslationString } from '../common/sales-status/i18n';
import { LoggedInUserInfoService } from '../logged-in-user-info';
import { PartnerMarket } from '../logged-in-user-info/logged-in-user-info.service';
import { SalespeopleService } from '../salespeople/salespeople.service';
import { ACCOUNT_INFO_ROOT } from '../urls';

export interface AccountAnalytics extends AccountInterface {
  statusString?: string;
  countryString?: string;
  manageAccountUrl: string;
  assigneeNames$: Observable<string>;
  businessId?: string;
}

interface UserInfoForLoadRequest {
  canAccessMarkets: boolean;
  salesPersonId: string;
}

export interface AccountAnalyticsFilters {
  assignee?: CoreSalesperson;
  unread?: boolean;
  'account-created'?: boolean;
  'ready-to-sell'?: boolean;
  'in-progress'?: boolean;
  'follow-up-needed'?: boolean;
  'closed-won'?: boolean;
  'closed-lost'?: boolean;
  country?: string;
  state?: string;
  search?: string;
  latestSalesActivity?: SalesActivityFilterOption;
  isArchived?: ArchivedFilterValue;
}

@Injectable({
  providedIn: 'root',
})
export class AccountAnalyticsService implements OnDestroy, TableDataService<AccountAnalytics> {
  public readonly salesPeople$: Observable<Salesperson[]>;
  private readonly subscriptions = SubscriptionList.new();
  private partnerId: string;
  private marketId: string;
  private readonly totalResults$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  public readonly totalResults$: Observable<number> = this.totalResults$$.asObservable();
  private readonly userInfoForLoadRequest$$ = new ReplaySubject<UserInfoForLoadRequest>(1);

  constructor(
    private readonly apiService: AccountAnalyticsApiService,
    private readonly salespeopleService: SalespeopleService,
    private readonly loggedInUserInfoService: LoggedInUserInfoService,
  ) {
    const infoForRequest$ = combineLatest([
      this.loggedInUserInfoService.salesPersonId$,
      this.loggedInUserInfoService.hasAccessToAllAccountsInMarket$,
    ]).pipe(take(1), shareReplay({ bufferSize: 1, refCount: true }));

    this.subscriptions.add(infoForRequest$, ([id, hasAccess]) =>
      this.userInfoForLoadRequest$$.next(<UserInfoForLoadRequest>{
        canAccessMarkets: hasAccess,
        salesPersonId: id,
      }),
    );

    this.salesPeople$ = this.salespeopleService.loadSalespeople(false).pipe(shareReplay(1));

    this.subscriptions.add(this.loggedInUserInfoService.getPartnerIdAndMarketId$(), (ids: PartnerMarket) => {
      this.partnerId = ids.partnerId;
      this.marketId = ids.marketId;
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }

  convertFilters(filters: AccountAnalyticsFilters): AccountFiltersInterface {
    const f: AccountFiltersInterface = { attributeFilters: { statuses: [] } };
    if (filters['assignee']) {
      f.assignee = filters['assignee'].salespersonId;
    }
    if (filters['unread']) {
      f.attributeFilters.statuses.push(Status.STATUS_UNREAD_ACTIVITY);
    }
    if (filters['account-created']) {
      f.attributeFilters.statuses.push(Status.STATUS_ACCOUNT_CREATED);
    }
    if (filters['ready-to-sell']) {
      f.attributeFilters.statuses.push(Status.STATUS_READY_TO_SELL);
    }
    if (filters['in-progress']) {
      f.attributeFilters.statuses.push(Status.STATUS_IN_PROGRESS);
    }
    if (filters['follow-up-needed']) {
      f.attributeFilters.statuses.push(Status.STATUS_FOLLOW_UP_NEEDED);
    }
    if (filters['closed-won']) {
      f.attributeFilters.statuses.push(Status.STATUS_CLOSED_WON);
    }
    if (filters['closed-lost']) {
      f.attributeFilters.statuses.push(Status.STATUS_CLOSED_LOST);
    }
    if (filters['country']) {
      f.attributeFilters.countryCode = filters['country'];
    }
    if (filters['state']) {
      f.attributeFilters.state = filters['state'];
    }
    if (filters['search']) {
      f.searchText = filters['search'];
    }
    if (filters.latestSalesActivity) {
      f.latestSalesActivityFilter = SalesActivityFilterOptionToSdk(filters.latestSalesActivity);
    }
    if (filters.isArchived) {
      f.attributeFilters.archivedFilter = ArchivedFilter[filters.isArchived.value];
    }
    return f;
  }

  private sendLoadRequest(info: UserInfoForLoadRequest, request: LoadRequest): Observable<AccountAnalytics[]> {
    const apiRequest: ListAccountAnalyticsRequestInterface = {
      partnerId: this.partnerId,
      marketId: this.marketId,
      filters: this.convertFilters(request.filters),
      sorting: { sortingOptions: [] },
      paging: {
        pageIndex: request.pageIndex,
        pageSize: request.pageSize,
      },
    };

    if (!info.canAccessMarkets) {
      apiRequest.filters.assignee = info.salesPersonId;
    }

    if (request.sortingOptions) {
      apiRequest.sorting.sortingOptions = request.sortingOptions
        .map((sort) => {
          return {
            fieldName: sort.field,
            direction:
              sort.direction === SortDirection.SORT_DIRECTION_ASCENDING
                ? ApiSortDirection.SORT_DIRECTION_ASCENDING
                : ApiSortDirection.SORT_DIRECTION_DESCENDING,
          };
        })
        .filter((sort) => sort.direction !== SortDirection.SORT_DIRECTION_UNSET.valueOf());
    }

    return this.apiService.list(apiRequest).pipe(
      map((resp) => {
        this.totalResults$$.next(resp.paging.totalResults);

        if (!resp.accounts) {
          return [];
        }

        return resp.accounts.map((a) => {
          const assigneeNames$ = of(a.assignees ? a.assignees : []).pipe(
            mergeMap((assignees) => assignees),
            mergeMap((assignee) => {
              return this.salesPeople$.pipe(
                map((salesPeople) => salesPeople.find((person) => person.id === assignee)),
                map((salesPerson) => (salesPerson ? salesPerson.fullName : '')),
              );
            }),
            scan((all, current) => {
              if (all.length > 0) {
                return all + ', ' + current;
              }
              return current;
            }, ''),
          );

          const newFields: AccountAnalytics = {
            assigneeNames$: assigneeNames$,
            manageAccountUrl: `/${ACCOUNT_INFO_ROOT}/${a.businessId}`,
            statusString: a.attributes.status ? StatusToTranslationString(a.attributes.status) : 'invalid',
            businessId: a.businessId,
          };
          return Object.assign(a, newFields);
        });
      }),
    );
  }

  load(request: LoadRequest): Observable<AccountAnalytics[]> {
    return this.userInfoForLoadRequest$$.pipe(switchMap((user) => this.sendLoadRequest(user, request)));
  }
}
