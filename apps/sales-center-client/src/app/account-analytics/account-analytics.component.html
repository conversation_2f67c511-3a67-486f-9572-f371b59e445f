<glxy-page [pagePadding]="false">
  <glxy-page-toolbar>
    <glxy-page-title>
      <!-- leaving this here as I don't have time to pull out the business name -->
      <!-- This should turn into using the `<glxy-page-nav-button>` and `<glxy-page-title>` in the futute -->
      <app-nav-breadcrumbs [breadCrumbs]="[{ label: 'Account Analytics' }]"></app-nav-breadcrumbs>
    </glxy-page-title>
  </glxy-page-toolbar>

  <sales-ui-slide-out-panel mode="over">
    <div content>
      <div class="filter-container">
        <va-filtered-mat-table [tableDataService]="dataService"></va-filtered-mat-table>
      </div>
    </div>
    <ng-container drawer-content *ngIf="sidePanelState$ | async as sidePanelState">
      <div [ngSwitch]="sidePanelState.state">
        <app-campaign-side-panel
          *ngSwitchCase="campaignCreateSidePanelCase"
          [accountGroupID]="sidePanelState.data.accountGroupId"
        ></app-campaign-side-panel>
      </div>
    </ng-container>
  </sales-ui-slide-out-panel>
</glxy-page>
