import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { LexiconModule } from '@galaxy/lexicon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { LeadQualificationType, LeadsApiService } from '@vendasta/prospect';
import { VaStencilsModule } from '@vendasta/uikit';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import {
  Filter2TableModule,
  TABLE_DEFINITION,
  VaFilteredMatTableService,
  VaTableSortService,
} from '@vendasta/va-filter2-table';
import baseTranslation from '../../assets/i18n/en_devel.json';
import { WEBLATE_COMPONENT_NAME } from '../constants';
import { LoggedInUserInfoService } from '../logged-in-user-info';
import { NavigationModule } from '../navigation/navigation.module';
import { LeadTableColumnDirective } from './column-components/lead-table-columns';
import { LeadTableCompanyNameColumnComponent } from './column-components/lead-table-company-name-column.component';
import { LeadTablePersonNameColumnComponent } from './column-components/lead-table-person-name-column.component';
import { LeadDetailsComponent } from './lead-details/lead-details.component';
import { LeadDetailsService } from './lead-details/lead-details.service';
import { LEAD_ID, leadIDFactory } from './lead.providers';
import { LeadsTableFilterService } from './leads-table-filter.service';
import { tableDefinitionFactory } from './leads-table-model';
import {
  LeadsTableService,
  TABLE_SERVICE_FOR_QUALIFIED_LEADS,
  TABLE_SERVICE_FOR_UNQUALIFIED_LEADS,
  tableServiceFactory,
} from './leads-table.service';
import { LeadsTableComponent } from './leads-table/leads-table.component';
import { LeadsComponent } from './leads.component';
import { SnackbarLinkToAccountComponent } from './snackbar-link-to-account/snackbar-link-to-account.component';

@NgModule({
  imports: [
    CommonModule,
    NavigationModule,
    TranslateModule,
    LexiconModule.forChild({
      componentName: WEBLATE_COMPONENT_NAME,
      baseTranslation: baseTranslation,
    }),
    Filter2TableModule,
    MatButtonModule,
    MatCardModule,
    RouterModule.forChild([
      {
        path: '',
        component: LeadsComponent,
      },
    ]),
    MatDividerModule,
    MatSidenavModule,
    MatIconModule,
    MatProgressSpinnerModule,
    FormsModule,
    VaStencilsModule,
    MatTableModule,
    MatRadioModule,
    MatTabsModule,
    GalaxyAlertModule,
    GalaxyPageModule,
  ],
  exports: [LeadsComponent],
  declarations: [
    LeadsComponent,
    LeadDetailsComponent,
    LeadTableColumnDirective,
    LeadTableCompanyNameColumnComponent,
    LeadTablePersonNameColumnComponent,
    SnackbarLinkToAccountComponent,
    LeadsTableComponent,
  ],
  providers: [
    VaFilteredMatTableService,
    VaTableSortService,
    LeadsTableFilterService,
    {
      provide: TABLE_DEFINITION,
      useFactory: tableDefinitionFactory,
      deps: [LeadsTableFilterService],
    },
    {
      provide: LEAD_ID,
      useFactory: leadIDFactory,
      deps: [ActivatedRoute],
    },
    {
      provide: TABLE_SERVICE_FOR_UNQUALIFIED_LEADS,
      useFactory: (
        partnerMarketService: LoggedInUserInfoService,
        leadsApiService: LeadsApiService,
        alertService: SnackbarService,
        translate: TranslateService,
      ) =>
        tableServiceFactory(
          partnerMarketService,
          leadsApiService,
          alertService,
          translate,
          LeadQualificationType.LEAD_QUALIFICATION_TYPE_UNQUALIFIED,
        ),
      deps: [LoggedInUserInfoService, LeadsApiService, TranslateService],
    },
    {
      provide: TABLE_SERVICE_FOR_QUALIFIED_LEADS,
      useFactory: (
        partnerMarketService: LoggedInUserInfoService,
        leadsApiService: LeadsApiService,
        alertService: SnackbarService,
        translate: TranslateService,
      ) =>
        tableServiceFactory(
          partnerMarketService,
          leadsApiService,
          alertService,
          translate,
          LeadQualificationType.LEAD_QUALIFICATION_TYPE_QUALIFIED,
        ),
      deps: [LoggedInUserInfoService, LeadsApiService, TranslateService],
    },
    LeadsTableService,
    LeadDetailsService,
  ],
})
export class LeadsModule {}
