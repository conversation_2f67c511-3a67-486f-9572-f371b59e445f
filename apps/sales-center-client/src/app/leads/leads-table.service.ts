import { Injectable, InjectionToken, OnDestroy } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, Observable, Subscription, of } from 'rxjs';
import { catchError, map, retry, switchMap } from 'rxjs/operators';

import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { LeadInterface, LeadQualificationType, LeadsApiService } from '@vendasta/prospect';
import { LoadRequest, TableDataService } from '@vendasta/va-filter2-table';

import { ListLeadsForPartnerRequestInterface } from '@vendasta/prospect';
import { LoggedInUserInfoService, PartnerMarket } from '../logged-in-user-info/logged-in-user-info.service';

export const TABLE_SERVICE_FOR_UNQUALIFIED_LEADS: InjectionToken<string> = new InjectionToken(
  'TABLE_SERVICE_FOR_UNQUALIFIED_LEADS',
);
export const TABLE_SERVICE_FOR_QUALIFIED_LEADS: InjectionToken<string> = new InjectionToken(
  'TABLE_SERVICE_FOR_QUALIFIED_LEADS',
);

@Injectable()
export class LeadsTableService implements TableDataService<LeadInterface>, OnDestroy {
  private readonly pageOfLeads$$ = new BehaviorSubject<LeadInterface[]>([]);
  readonly pageOfLeads$ = this.pageOfLeads$$.asObservable();

  private readonly totalResults$$ = new BehaviorSubject<number>(0);
  readonly totalResults$ = this.totalResults$$.asObservable();

  private readonly loading$$ = new BehaviorSubject<boolean>(false);
  readonly loading$ = this.loading$$.asObservable();

  private cursors: { [id: number]: string } = { 0: '' };

  private readonly partnerMarket$ = this.partnerMarketService.partnerIdAndMarketId$;

  private readonly subscriptions: Subscription[] = [];

  constructor(
    private readonly partnerMarketService: LoggedInUserInfoService,
    private readonly leadsApiService: LeadsApiService,
    private readonly alertService: SnackbarService,
    private readonly translate: TranslateService,
    private readonly qualifiedFilter: LeadQualificationType,
  ) {}

  reloadTableData(): Observable<LeadInterface[]> {
    const loadRequest = <LoadRequest>{
      pageIndex: 0,
    };
    return this.load(loadRequest);
  }

  load(loadRequest: LoadRequest): Observable<LeadInterface[]> {
    this.loading$$.next(true);
    const listLeads$ = this.partnerMarket$.pipe(
      map((partnerMarket) => this.buildListLeadsRequest(partnerMarket, loadRequest)),
      switchMap((leadsRequest) => this.leadsApiService.listLeadsForPartner(leadsRequest)),
      retry(2),
      catchError(() => {
        this.alertService.openErrorSnack('LEADS.ERROR');
        return of({
          leads: [],
          pagingMetadata: {
            nextCursor: '',
            hasMore: false,
            totalResults: 0,
          },
        });
      }),
    );
    const loadSubscription = listLeads$.subscribe((response) => {
      this.cursors[loadRequest.pageIndex + 1] = response.pagingMetadata.nextCursor;
      this.pageOfLeads$$.next(response.leads);
      this.totalResults$$.next(response.pagingMetadata.totalResults);
      this.loading$$.next(false);
    });
    this.subscriptions.push(loadSubscription);
    return this.pageOfLeads$;
  }

  private buildListLeadsRequest(
    partnerMarket: PartnerMarket,
    loadRequest: LoadRequest,
  ): ListLeadsForPartnerRequestInterface {
    if (loadRequest.pageIndex === 0) {
      this.cursors = { 0: '' };
    }
    const nextCursor = this.cursors[loadRequest.pageIndex] || '';

    return {
      partnerId: partnerMarket.partnerId,
      pagingOptions: {
        pageSize: loadRequest.pageSize,
        cursor: nextCursor,
      },
      leadQualificationType: this.qualifiedFilter,
    };
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
  }
}

export const tableServiceFactory = (
  partnerMarketService: LoggedInUserInfoService,
  leadsApiService: LeadsApiService,
  alertService: SnackbarService,
  translate: TranslateService,
  qualificationFilter: LeadQualificationType,
) => {
  return new LeadsTableService(partnerMarketService, leadsApiService, alertService, translate, qualificationFilter);
};
