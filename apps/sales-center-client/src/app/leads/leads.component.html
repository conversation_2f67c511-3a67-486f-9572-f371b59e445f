<glxy-page [pagePadding]="false">
  <glxy-page-toolbar>
    <glxy-page-title>
      {{ 'LEADS.TITLE' | translate }}
    </glxy-page-title>
  </glxy-page-toolbar>

  <app-lead-details
    [showQualificationActions]="showQualificationActions$ | async"
    (closeButtonClicked)="detailsPanelCloseButtonClicked()"
    (leadQualified)="leadQualified()"
  >
    <mat-tab-group
      animationDuration="0ms"
      mat-stretch-tabs="false"
      mat-align-tabs="start"
      (selectedTabChange)="onTabClick($event)"
      [selectedIndex]="selectedTabIndex$ | async"
    >
      <mat-tab label="{{ 'LEADS.TABS.UNQUALIFIED' | translate }}">
        <app-leads-table [tableDataService]="unqualifiedTableService"></app-leads-table>
      </mat-tab>
      <mat-tab label="{{ 'LEADS.TABS.QUALIFIED' | translate }}">
        <app-leads-table [tableDataService]="qualifiedTableService"></app-leads-table>
      </mat-tab>
    </mat-tab-group>
  </app-lead-details>
</glxy-page>
