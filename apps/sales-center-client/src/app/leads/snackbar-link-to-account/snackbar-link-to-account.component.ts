import { Component, Inject } from '@angular/core';
import { MAT_SNACK_BAR_DATA } from '@angular/material/snack-bar';

@Component({
  selector: 'app-snackbar-link-to-account',
  templateUrl: './snackbar-link-to-account.component.html',
  styleUrls: ['./snackbar-link-to-account.component.scss'],
  standalone: false,
})
export class SnackbarLinkToAccountComponent {
  message: string;
  accountGroupId: string;

  constructor(@Inject(MAT_SNACK_BAR_DATA) public data: any) {
    this.message = data.message;
    this.accountGroupId = data.accountGroupId;
  }
}
