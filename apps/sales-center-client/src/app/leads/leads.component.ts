import { Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { LeadInterface, LeadQualificationType } from '@vendasta/prospect';
import { SubscriptionList } from '@vendasta/rx-utils';
import { VaFilteredMatTableService } from '@vendasta/va-filter2-table';
import { Observable } from 'rxjs';
import { distinctUntilChanged, map } from 'rxjs/operators';
import {
  LeadsTableService,
  TABLE_SERVICE_FOR_QUALIFIED_LEADS,
  TABLE_SERVICE_FOR_UNQUALIFIED_LEADS,
} from './leads-table.service';

enum LeadsTabIndex {
  unqualified,
  qualified,
}

type LeadsTab = keyof typeof LeadsTabIndex;

@Component({
  selector: 'app-page-leads',
  templateUrl: './leads.component.html',
  styleUrls: ['./leads.component.scss'],
  standalone: false,
})
export class LeadsComponent implements OnDestroy, OnInit {
  qualification = LeadQualificationType;

  selectedTab$: Observable<LeadsTab>;
  selectedTabIndex$: Observable<LeadsTabIndex>;
  showQualificationActions$: Observable<boolean>;
  private readonly subscriptions = SubscriptionList.new();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly vaFilteredMatTableService: VaFilteredMatTableService<LeadInterface>,
    @Inject(TABLE_SERVICE_FOR_UNQUALIFIED_LEADS) readonly unqualifiedTableService: LeadsTableService,
    @Inject(TABLE_SERVICE_FOR_QUALIFIED_LEADS) readonly qualifiedTableService: LeadsTableService,
  ) {}

  ngOnInit(): void {
    this.selectedTab$ = this.route.queryParamMap.pipe(
      map((params) => params.get('tab')),
      distinctUntilChanged(),
      map((tab) => (tab === 'qualified' ? 'qualified' : 'unqualified')),
    );
    this.selectedTabIndex$ = this.selectedTab$.pipe(map((tab) => LeadsTabIndex[tab]));
    this.showQualificationActions$ = this.selectedTabIndex$.pipe(map((tab) => tab === LeadsTabIndex.unqualified));
  }

  onTabClick(event: MatTabChangeEvent): void {
    this.openTab(<LeadsTab>LeadsTabIndex[event.index]);
  }

  detailsPanelCloseButtonClicked(): void {
    this.closeSidePanel();
  }

  private closeSidePanel(): void {
    this.navigate({ leadId: null });
  }

  leadQualified(): void {
    this.subscriptions.addAll(
      this.unqualifiedTableService.reloadTableData().subscribe(() => {
        this.vaFilteredMatTableService.resetPaging();
      }),
    );
    this.subscriptions.addAll(this.qualifiedTableService.reloadTableData().subscribe());
    this.closeSidePanel();
  }

  private openTab(tab: LeadsTab): void {
    this.navigate({ tab: tab });
  }

  private navigate(params: Params): void {
    this.router
      .navigate([], {
        relativeTo: this.route,
        queryParams: params,
        queryParamsHandling: 'merge',
      })
      .then();
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }
}
