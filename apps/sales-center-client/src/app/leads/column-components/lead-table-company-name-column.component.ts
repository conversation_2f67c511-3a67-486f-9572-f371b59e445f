import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { LeadTableColumnDirective } from './lead-table-columns';

@Component({
  selector: 'app-lead-table-company-name-column',
  template: ` <a class="hover-underline" (click)="openLead(element.leadId)">{{ element.business.companyName }}</a> `,
  styleUrls: ['./lead-table-columns.scss'],
  standalone: false,
})
export class LeadTableCompanyNameColumnComponent extends LeadTableColumnDirective {
  constructor(
    private readonly router: Router,
    private readonly route: ActivatedRoute,
  ) {
    super();
  }

  openLead(leadId: string): void {
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { leadId: leadId },
      queryParamsHandling: 'merge',
    });
  }
}
