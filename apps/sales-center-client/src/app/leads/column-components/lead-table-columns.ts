import { Directive, Input } from '@angular/core';
import { LeadInterface } from '@vendasta/prospect';
import { ColumnDefinition, CustomCellComponent } from '@vendasta/va-filter2-table';

@Directive({
  // TODO: Change this. Upgrading to Galaxy and we have no idea how changing this could impact things
  // eslint-disable-next-line @angular-eslint/directive-selector
  selector: 'app-lead-table-column',
  standalone: false,
})
export class LeadTableColumnDirective implements CustomCellComponent<LeadInterface> {
  @Input() columnDefinition: ColumnDefinition;
  @Input() element: LeadInterface;
}
