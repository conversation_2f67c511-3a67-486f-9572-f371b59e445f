/*
This component wraps the va-filtered-mat-table so that you don't get
`ExpressionChangedAfterItHasBeenCheckedError`s.
*/

import { Component, Input, ViewChild } from '@angular/core';
import { LeadInterface } from '@vendasta/prospect';
import { VaFilteredMatTableComponent, VaFilteredMatTableService, VaTableSortService } from '@vendasta/va-filter2-table';
import { FilterService } from '@vendasta/va-filter2';
import { LeadsTableService } from '../leads-table.service';

@Component({
  selector: 'app-leads-table',
  templateUrl: './leads-table.component.html',
  providers: [VaTableSortService, VaFilteredMatTableService, FilterService],
  standalone: false,
})
export class LeadsTableComponent {
  @ViewChild('table') table: VaFilteredMatTableComponent<LeadInterface>;
  @Input() tableDataService: LeadsTableService;
}
