import { Inject, Injectable } from '@angular/core';
import { MatSnackBar, MatSnackBarConfig } from '@angular/material/snack-bar';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import {
  AssociateLeadAndAccountGroupRequestInterface,
  CreateAccountGroupFromLeadRequestInterface,
  FindDuplicateAccountsRequestInterface,
  GetLeadRequestInterface,
  LeadInterface,
  LeadsApiService,
  ProspectApiService,
} from '@vendasta/prospect';

import { AccountGroup, AccountGroupApiService, ProjectionFilter } from '@galaxy/account-group';
import { ObservableWorkState } from '@vendasta/rx-utils/work-state';
import { BehaviorSubject, EMPTY, Observable, combineLatest, of } from 'rxjs';
import { catchError, filter, map, retry, switchMap, take, withLatestFrom } from 'rxjs/operators';
import { MARKET_ID_TOKEN, PARTNER_ID_TOKEN } from '../../common/providers';
import { LEAD_ID } from '../lead.providers';
import { SnackbarLinkToAccountComponent } from '../snackbar-link-to-account/snackbar-link-to-account.component';

@Injectable()
export class LeadDetailsService {
  readonly lead$: Observable<LeadInterface> = combineLatest([this.partnerId$, this.leadID$]).pipe(
    filter(([, leadId]) => !!leadId),
    map(
      ([partnerId, leadId]) =>
        <GetLeadRequestInterface>{
          leadId: leadId,
          partnerId: partnerId,
        },
    ),
    switchMap((request) => this.leadsApiService.getLead(request)),
    retry(1),
    catchError(() => {
      const message = this.translate.instant('LEADS.DETAILS.LOAD_ERROR');
      this.showErrorMessage(message);
      return EMPTY;
    }),
    map((f) => f.lead),
  );

  private readonly showAllRows$$ = new BehaviorSubject(false);
  readonly showAllRows$ = this.showAllRows$$.asObservable();
  readonly showDetails$ = this.leadID$.pipe(map((leadID) => Boolean(leadID)));

  private readonly accountGroupWorkstate = new ObservableWorkState<AccountGroup[]>();
  readonly isLoading$: Observable<boolean> = this.accountGroupWorkstate.isLoading$;
  readonly duplicateAccountGroups$ = this.accountGroupWorkstate.workResults$;
  displayedDuplicates$: Observable<AccountGroup[]>;
  initialRowsToShow = 2;

  private readonly duplicatesInDifferentMarket$$ = new BehaviorSubject<string[]>([]);
  readonly duplicatesInDifferentMarket$ = this.duplicatesInDifferentMarket$$.asObservable();
  readonly marketID$ = this.marketIDs$.pipe(map((marketIDs) => marketIDs[0]));

  private readonly isCreating$$ = new BehaviorSubject<boolean>(false);
  readonly isCreating$ = this.isCreating$$.asObservable();
  private readonly isLinking$$ = new BehaviorSubject<boolean>(false);
  readonly isLinking$ = this.isLinking$$.asObservable();

  constructor(
    private readonly leadsApiService: LeadsApiService,
    private readonly prospectApiService: ProspectApiService,
    private readonly accountGroupApiService: AccountGroupApiService,
    private readonly alertService: SnackbarService,
    private readonly snackBar: MatSnackBar,
    private readonly translate: TranslateService,
    @Inject(PARTNER_ID_TOKEN) private readonly partnerId$: Observable<string>,
    @Inject(MARKET_ID_TOKEN) private readonly marketIDs$: Observable<string[]>,
    @Inject(LEAD_ID) readonly leadID$: Observable<string>,
  ) {
    this.leadID$.subscribe(() => this.loadPotentialDuplicateAccountGroups());
  }

  createAccountFromLead(): Observable<string> {
    return combineLatest([this.lead$, this.marketID$]).pipe(
      map(
        ([lead, marketId]) =>
          <CreateAccountGroupFromLeadRequestInterface>{
            leadId: lead.leadId,
            partnerId: lead.partnerId,
            marketId: marketId,
          },
      ),
      switchMap((request) => {
        this.isCreating$$.next(true);
        return this.leadsApiService.createAccountGroupFromLead(request);
      }),
      retry(1),
      map((response) => {
        this.isCreating$$.next(false);
        return response.accountGroupId;
      }),
      take(1),
    );
  }

  associateLeadAndAccount(accountGroupId: string): Observable<void> {
    return this.lead$.pipe(
      map(
        (lead) =>
          <AssociateLeadAndAccountGroupRequestInterface>{
            leadId: lead.leadId,
            accountGroupId: accountGroupId,
            partnerId: lead.partnerId,
          },
      ),
      switchMap((request) => {
        this.isLinking$$.next(true);
        return this.leadsApiService.associateLeadAndAccountGroup(request);
      }),
      retry(1),
      catchError(() => {
        throw new Error();
      }),
      take(1),
      switchMap(() => {
        this.isLinking$$.next(false);
        return of(null);
      }),
    );
  }

  loadPotentialDuplicateAccountGroups(): void {
    this.accountGroupWorkstate
      .startWork(
        combineLatest([this.lead$, this.marketID$]).pipe(
          map(
            ([lead, marketID]) =>
              <FindDuplicateAccountsRequestInterface>{
                partnerId: lead?.partnerId,
                marketId: marketID,
                contactEmail: lead?.person?.email,
                phoneNumber: lead?.person?.phoneNumber,
              },
          ),
          switchMap((request) => this.prospectApiService.findDuplicateAccounts(request)),
          withLatestFrom(this.marketID$),
          switchMap(([response, currentMarket]) => {
            const duplicatesInDifferentMarket = response.duplicateAccounts
              ?.filter((val) => val.marketId !== currentMarket)
              .map((account) => account.accountGroupId);
            this.duplicatesInDifferentMarket$$.next(duplicatesInDifferentMarket);

            const accountGroupsInSpecifiedMarket = response.duplicateAccounts
              ?.filter((account) => account.marketId === currentMarket)
              .map((account) => account.accountGroupId);

            if (accountGroupsInSpecifiedMarket?.length > 0) {
              return this.accountGroupApiService.getMulti(
                accountGroupsInSpecifiedMarket,
                new ProjectionFilter({ napData: true }),
              );
            }
            return of([]);
          }),
          retry(1),
          catchError(() => {
            const message = this.translate.instant('LEADS.DETAILS.LOAD_ERROR');
            this.showErrorMessage(message);
            return EMPTY;
          }),
          take(1),
        ),
      )
      .then();

    this.displayedDuplicates$ = combineLatest([this.accountGroupWorkstate.workResults$, this.showAllRows$]).pipe(
      map(([accountGroups, showAllRows]) =>
        showAllRows ? accountGroups : [...accountGroups].splice(0, this.initialRowsToShow),
      ),
    );
  }

  close(): void {
    this.showAllRows$$.next(false);
  }

  toggleShowAllRows(): void {
    this.showAllRows$$.next(!this.showAllRows$$.getValue());
  }

  public showSuccessWithLinkToAccount(message: string, accountGroupId: string): void {
    const config = new MatSnackBarConfig();
    config.viewContainerRef = null;
    config.panelClass = ['snackbar-success'];
    config.duration = 15000;
    config.data = {
      message: message,
      accountGroupId: accountGroupId,
    };

    this.snackBar.openFromComponent(SnackbarLinkToAccountComponent, config);
  }

  public showErrorMessage(message: string): void {
    const config = new MatSnackBarConfig();
    config.panelClass = 'snackbar-error';
    config.duration = 10000;

    this.alertService.openWithOptions(message, { panelClass: config.panelClass, duration: config.duration });
    this.isCreating$$.next(false);
    this.isLinking$$.next(false);
  }
}
