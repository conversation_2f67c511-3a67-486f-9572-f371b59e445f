<mat-drawer-container class="side-panel-container" autosize [hasBackdrop]="false">
  <mat-drawer [opened]="showDetails$ | async" class="side-panel-sidenav" mode="over" position="end">
    <div class="panel-header">
      <p>{{ 'LEADS.DETAILS.TITLE' | translate }}</p>
      <mat-icon (click)="close()">close</mat-icon>
    </div>
    <div class="margin-around" *ngIf="lead$ | async">
      <ng-container *ngIf="showQualificationActions; then dupes"></ng-container>
      <ng-container *ngIf="lead$ | async as lead">
        <div class="lead-detail__key">
          {{ 'LEADS.LEAD_DATA.NAME_FULL' | translate }}
        </div>
        <div class="lead-detail__value">
          {{ lead?.person?.firstName || '-' }}
          {{ lead?.person?.lastName || '-' }}
        </div>
        <mat-divider></mat-divider>
        <div class="lead-detail__key">
          {{ 'LEADS.LEAD_DATA.EMAIL' | translate }}
        </div>
        <div class="lead-detail__value">{{ lead?.person?.email || '-' }}</div>
        <mat-divider></mat-divider>
        <div class="lead-detail__key">
          {{ 'LEADS.LEAD_DATA.PHONE' | translate }}
        </div>
        <div class="lead-detail__value">
          {{ lead?.person?.phoneNumber || '-' }}
        </div>
        <mat-divider></mat-divider>
        <div class="lead-detail__key">
          {{ 'LEADS.LEAD_DATA.COMPANY_NAME' | translate }}
        </div>
        <div class="lead-detail__value">
          {{ lead?.business?.companyName || '-' }}
        </div>
        <mat-divider></mat-divider>
        <div class="lead-detail__key">
          {{ 'LEADS.LEAD_DATA.COMPANY_WEBSITE' | translate }}
        </div>
        <div class="lead-detail__value">
          {{ lead?.business?.website || '-' }}
        </div>
        <mat-divider></mat-divider>
        <div class="lead-detail__key">
          {{ 'LEADS.LEAD_DATA.CAMPAIGN_NAME' | translate }}
        </div>
        <div class="lead-detail__value">{{ lead?.campaignName || '-' }}</div>
        <mat-divider></mat-divider>
        <div class="lead-detail__key">
          {{ 'LEADS.LEAD_DATA.AD_NAME' | translate }}
        </div>
        <div class="lead-detail__value">{{ lead?.adName || '-' }}</div>
        <mat-divider></mat-divider>
        <div class="lead-detail__key">
          {{ 'LEADS.LEAD_DATA.OPPORTUNITY' | translate }}
        </div>
        <div class="lead-detail__value">
          {{ lead?.opportunity?.description || '-' }}
        </div>
      </ng-container>
      <ng-container *ngIf="shouldShowCreateAccountButton$ | async">
        <button
          style="float: right"
          mat-raised-button
          color="primary"
          (click)="createAccountFromLead()"
          *ngIf="(isCreating$ | async) === false; else loadingSpinner"
        >
          {{ 'LEADS.DETAILS.CREATE_ACCOUNT' | translate }}
        </button>
      </ng-container>
    </div>
  </mat-drawer>
  <ng-content></ng-content>
</mat-drawer-container>

<ng-template #dupes>
  <ng-container *ngIf="(loading$ | async) === false; else dupeLoadingState">
    <ng-container *ngIf="displayedAccountGroups$ | async">
      <div
        class="possible-duplicate"
        *ngIf="(duplicateAccountGroups$ | async)?.length > 0; else duplicatePresentInDifferentMarket"
      >
        <div class="warning-header">
          <mat-icon class="report-problem-icon">report_problem</mat-icon>
          <div>
            <strong>
              {{ 'LEADS.DETAILS.POTENTIAL_DUPLICATES_PANEL.TITLE' | translate }}
            </strong>
            {{ duplicateExistsWarningMessage$ | async | translate }}
          </div>
        </div>

        <mat-radio-group [value]="selectedAccountGroup$ | async" (change)="selectedAccountGroup$$.next($event.value)">
          <div class="dupe-account-list">
            <div
              class="dupe-account-item"
              *ngFor="let duplicate of displayedAccountGroups$ | async as displayedAccountGroups"
            >
              <div class="business-title">
                <ng-container *ngIf="displayedAccountGroups.length > 1; else noButtonList">
                  <mat-radio-button [value]="duplicate?.accountGroupId">
                    {{ duplicate?.napData?.companyName }}
                  </mat-radio-button>
                </ng-container>
                <ng-template #noButtonList>
                  {{ duplicate?.napData?.companyName }}
                </ng-template>
              </div>
              <div class="business-subtitle">{{ address(duplicate) }}</div>
              <ng-container *ngIf="displayedAccountGroups.length > 1">
                <mat-divider class="margin-above-and-below"></mat-divider>
              </ng-container>
            </div>
            <div
              class="dupe-account-list show-more-button"
              *ngIf="(duplicateAccountGroups$ | async).length > initialRowsToShow && (showAllRows$ | async) === false"
              (click)="toggleShowAllRows()"
            >
              {{ 'LEADS.DETAILS.POTENTIAL_DUPLICATES_PANEL.SHOW_ALL' | translate }}
              <mat-icon>keyboard_arrow_down</mat-icon>
            </div>
          </div>
        </mat-radio-group>

        <div class="button-container">
          <button
            *ngIf="(isCreating$ | async) === false; else loadingSpinner"
            class="create-account-button"
            mat-button
            color="primary"
            (click)="createAccountFromLead()"
            [disabled]="(isLinking$ | async) === true"
          >
            {{ 'LEADS.DETAILS.CREATE_ACCOUNT' | translate }}
          </button>
          <button
            *ngIf="(isLinking$ | async) === false; else loadingSpinner"
            class="link-account-button"
            mat-raised-button
            color="primary"
            (click)="associateLeadAndAccount(selectedAccountGroup$)"
            [disabled]="(isCreating$ | async) === true"
          >
            {{ 'LEADS.DETAILS.LINK_ACCOUNT' | translate }}
          </button>
        </div>
      </div>
    </ng-container>
  </ng-container>
</ng-template>

<ng-template #dupeLoadingState>
  <div class="shimmer-container">
    <div class="row-container">
      <div class="stencil-shimmer shimmer-circle"></div>
      <div class="stencil-shimmer shimmer-header"></div>
    </div>

    <div class="stencil-shimmer shimmer-row"></div>
    <div class="stencil-shimmer shimmer-row"></div>
    <div class="stencil-shimmer shimmer-row"></div>
  </div>
</ng-template>

<ng-template #duplicatePresentInDifferentMarket>
  <div class="warning-message" *ngIf="(duplicatesInOtherMarket$ | async)?.length > 0">
    <glxy-alert type="warning">
      <strong>
        {{ 'LEADS.DETAILS.POTENTIAL_DUPLICATES_PANEL.TITLE' | translate }}
      </strong>
      {{ duplicateExistsWarningMessage$ | async | translate }}
    </glxy-alert>
  </div>
</ng-template>

<ng-template #loadingSpinner>
  <mat-spinner class="loading-spinner" diameter="30"></mat-spinner>
</ng-template>
