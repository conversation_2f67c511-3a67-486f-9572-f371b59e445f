import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { AccountGroup } from '@galaxy/account-group';
import { TranslateService } from '@ngx-translate/core';
import { LeadInterface } from '@vendasta/prospect';
import { SubscriptionList } from '@vendasta/rx-utils';
import { BehaviorSubject, EMPTY, Observable, combineLatest } from 'rxjs';
import { catchError, map, switchMap, take } from 'rxjs/operators';
import { LeadDetailsService } from './lead-details.service';

@Component({
  selector: 'app-lead-details',
  templateUrl: './lead-details.component.html',
  styleUrls: ['./lead-details.component.scss'],
  standalone: false,
})
export class LeadDetailsComponent implements OnDestroy, OnInit {
  @Input() showQualificationActions: boolean;
  @Output() closeButtonClicked = new EventEmitter<void>();
  @Output() leadQualified = new EventEmitter<void>();
  readonly lead$: Observable<LeadInterface> = this.leadDetailsService.lead$;
  readonly loading$: Observable<boolean> = this.leadDetailsService.isLoading$;
  readonly duplicateAccountGroups$: Observable<AccountGroup[]> = this.leadDetailsService.duplicateAccountGroups$;
  readonly displayedAccountGroups$: Observable<AccountGroup[]> = this.leadDetailsService.displayedDuplicates$;
  readonly showAllRows$: Observable<boolean> = this.leadDetailsService.showAllRows$;
  readonly initialRowsToShow: number = this.leadDetailsService.initialRowsToShow;
  readonly showDetails$ = this.leadDetailsService.showDetails$;
  private readonly subscriptions = SubscriptionList.new();
  readonly duplicatesInOtherMarket$: Observable<string[]> = this.leadDetailsService.duplicatesInDifferentMarket$;
  shouldShowCreateAccountButton$: Observable<boolean>;
  duplicateExistsWarningMessage$: Observable<string>;

  selectedAccountGroup$$ = new BehaviorSubject<string>('');
  selectedAccountGroup$ = this.selectedAccountGroup$$.asObservable();
  isCreating$: Observable<boolean>;
  isLinking$: Observable<boolean>;

  constructor(
    private readonly leadDetailsService: LeadDetailsService,
    private readonly translate: TranslateService,
  ) {}

  ngOnInit(): void {
    this.shouldShowCreateAccountButton$ = this.shouldShowCreateNewAccountButton();
    this.duplicateExistsWarningMessage$ = this.getduplicateExistsWarningMessage();
    this.defaultSelectedAccountGroup();
    this.isCreating$ = this.leadDetailsService.isCreating$;
    this.isLinking$ = this.leadDetailsService.isLinking$;
  }

  defaultSelectedAccountGroup(): void {
    this.subscriptions.addAll(
      this.displayedAccountGroups$.pipe().subscribe((displayedAccounts) => {
        if (displayedAccounts.length === 1) {
          this.selectedAccountGroup$$.next(displayedAccounts[0].accountGroupId);
        } else {
          this.selectedAccountGroup$$.next('');
        }
      }),
    );
  }

  createAccountFromLead(): void {
    this.subscriptions.addAll(
      this.leadDetailsService.createAccountFromLead().subscribe({
        next: (accountGroupId) => {
          this.showCreateSuccess(accountGroupId);
          this.leadQualified.next();
        },
        error: () => {
          this.showCreateError();
        },
      }),
    );
  }

  associateLeadAndAccount(selectedAccountGroup$: Observable<string>): void {
    this.subscriptions.addAll(
      selectedAccountGroup$
        .pipe(
          switchMap((accountGroupId) => {
            return this.leadDetailsService.associateLeadAndAccount(accountGroupId);
          }),
          catchError(() => {
            this.subscriptions.addAll(
              this.lead$.pipe(map((lead) => this.showLinkError(lead.business.companyName))).subscribe(),
            );
            return EMPTY;
          }),
        )
        .subscribe(() => {
          this.showLinkSuccess(selectedAccountGroup$);
          this.leadQualified.next();
        }),
    );
  }

  toggleShowAllRows(): void {
    this.leadDetailsService.toggleShowAllRows();
  }

  close(): void {
    this.leadDetailsService.close();
    this.closeButtonClicked.emit();
  }

  address(accountGroup: AccountGroup): string {
    if (!accountGroup.napData) {
      return '';
    }
    const streetAddress: string = accountGroup.napData.address ? `${accountGroup.napData.address}, ` : '';
    const city: string = accountGroup.napData.city ? `${accountGroup.napData.city}, ` : '';
    const state: string = accountGroup.napData.state ? `, ${accountGroup.napData.state}` : '';
    return streetAddress + city + state;
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }

  private shouldShowCreateNewAccountButton(): Observable<boolean> {
    return combineLatest([this.duplicateAccountGroups$, this.loading$, this.duplicatesInOtherMarket$]).pipe(
      map(([duplicateAccountGroups, isLoading, duplicatesInOtherMarket]) => {
        return (
          duplicateAccountGroups?.length < 1 && !isLoading && !duplicatesInOtherMarket && this.showQualificationActions
        );
      }),
    );
  }

  private getduplicateExistsWarningMessage(): Observable<string> {
    return combineLatest([this.duplicateAccountGroups$, this.duplicatesInOtherMarket$]).pipe(
      map(([duplicateInCurrentMarket, duplicatesInOtherMarket]: [AccountGroup[], string[]]) => {
        if (duplicateInCurrentMarket.length === 0 && duplicatesInOtherMarket.length === 0) {
          return '';
        }

        if (duplicateInCurrentMarket.length === 0 && duplicatesInOtherMarket.length > 0) {
          return duplicatesInOtherMarket.length > 1
            ? 'LEADS.DETAILS.POTENTIAL_DUPLICATES_PANEL.MULTIPLE_IN_DIFFERENT'
            : 'LEADS.DETAILS.POTENTIAL_DUPLICATES_PANEL.ONE_IN_DIFFERENT';
        }

        if (duplicateInCurrentMarket.length > 0 && duplicatesInOtherMarket.length === 0) {
          return duplicateInCurrentMarket.length > 1
            ? 'LEADS.DETAILS.POTENTIAL_DUPLICATES_PANEL.MULTIPLE_IN_CURRENT'
            : 'LEADS.DETAILS.POTENTIAL_DUPLICATES_PANEL.ONE_IN_CURRENT';
        }

        if (duplicateInCurrentMarket.length > 1) {
          if (duplicatesInOtherMarket.length > 1) {
            return 'LEADS.DETAILS.POTENTIAL_DUPLICATES_PANEL.MULTIPLE_IN_BOTH';
          }
          return 'LEADS.DETAILS.POTENTIAL_DUPLICATES_PANEL.MULTIPLE_IN_CURRENT_ONE_IN_DIFFERENT';
        }
        if (duplicatesInOtherMarket.length > 1) {
          return 'LEADS.DETAILS.POTENTIAL_DUPLICATES_PANEL.ONE_IN_CURRENT_MULTIPLE_IN_DIFFERENT';
        }
        return 'LEADS.DETAILS.POTENTIAL_DUPLICATES_PANEL.ONE_IN_BOTH';
      }),
    );
  }

  private showCreateSuccess(accountGroupId: string): void {
    this.leadDetailsService.showSuccessWithLinkToAccount(
      this.translate.instant('LEADS.DETAILS.CREATE_ACCOUNT_SUCCESS'),
      accountGroupId,
    );
  }

  private showCreateError(): void {
    this.leadDetailsService.showErrorMessage(this.translate.instant('LEADS.DETAILS.CREATE_ACCOUNT_ERROR'));
  }

  private showLinkSuccess(selecetedAccountGroup: Observable<string>): void {
    selecetedAccountGroup.pipe(take(1)).subscribe((accountGroupId) => {
      this.leadDetailsService.showSuccessWithLinkToAccount(
        this.translate.instant('LEADS.DETAILS.LINK_ACCOUNT_SUCCESS'),
        accountGroupId,
      );
    });
  }

  private showLinkError(businessName: string): void {
    this.leadDetailsService.showErrorMessage(
      this.translate.instant('LEADS.DETAILS.LINK_ACCOUNT_ERROR', {
        businessName: businessName,
      }),
    );
  }
}
