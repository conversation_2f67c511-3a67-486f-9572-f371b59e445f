@use 'design-tokens' as *;
@import 'uikit';
@import '../../../constants';

@include va-stencil();

.panel-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  p {
    font-weight: 500;
  }

  mat-icon {
    cursor: pointer;
  }
}

.lead-details {
  margin-top: 16px;
}

.lead-detail__key {
  margin-top: 8px;
  color: $gray;
  font-weight: 400;
}

.lead-detail__value:not(:last-child) {
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 0 0 12px 0;
}

.create-account-button {
  margin-top: 16px;
}

.link-account-button {
  margin-top: 16px;
  margin-left: 16px;
}

.margin-around {
  margin: 16px;
}

.side-panel-sidenav-content {
  display: flex;
  height: 100vh;
  align-items: center;
  justify-content: center;
}

.side-panel-sidenav {
  padding: 20px;
  width: 50%;
  @media screen and (max-width: $media--tablet-minimum) {
    width: 100%;
  }
}

.possible-duplicate ::ng-deep {
  padding-bottom: 0;
  box-shadow: none;
  border: 1px solid $dark-yellow;
  margin-bottom: 16px;
  background: $warn-background-color;
  padding: 16px 24px 16px 16px;
  border-radius: 4px;

  .report-problem-icon {
    color: $warn-icon-color;
    margin-right: 14px;
  }
}

.button-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}

.dupe-account-list {
  padding-left: 20px;
  display: flex;
  flex-direction: column;
  margin-top: 8px;
  margin-bottom: 8px;
  margin-left: 16px;

  .dupe-account-item {
    border-radius: 8px;
    margin-bottom: 10px;

    .business-title {
      font-size: 16px;
      font-weight: 500;
    }

    .business-subtitle {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.54);
      padding-left: 0;
      margin-top: 8px;
    }
  }
}

.show-more-button {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding-left: 0px;
  cursor: pointer;
  color: $blue;
  font-weight: 500;
}

.row-container {
  display: flex;
  flex-direction: row;
}

.shimmer-container {
  padding: 8px;
  margin-bottom: 16px;
  border: 1px solid $border-color;
  border-radius: 4px;
}

.shimmer-circle {
  margin: 8px;
  margin-bottom: 24px;
  height: 36px;
  width: 36px;
  border-radius: 50%;
}

.shimmer-header {
  margin: 8px;
  margin-left: 28px;
  margin-bottom: 24px;
  height: 36px;
  width: 80%;
}

.shimmer-row {
  margin: 8px;
  margin-left: 80px;
  height: 24px;
  width: 80%;
}

.warning-message {
  margin-bottom: 16px;

  glxy-alert {
    border: 1px solid $warn-icon-color;
    color: $black;
    p {
      font-style: normal;
    }
  }
}

.warning-header {
  display: flex;
  flex-direction: row;
  margin-bottom: 36px;
}

.margin-above-and-below {
  margin-top: 16px;
  margin-bottom: 16px;
}

.loading-spinner {
  margin-right: 72px;
  margin-top: 16px;
  margin-left: 92px;
}
