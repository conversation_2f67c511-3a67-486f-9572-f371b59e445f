import { ColumnOrganizerType, DateColumnComponent } from '@vendasta/va-filter2-table';
import { TableDefinition } from '@vendasta/va-filter2-table';
import { ColumnType } from '@vendasta/va-filter2-table';
import { COMMON_TABLE_LABELS } from '../common/table-labels';
import { LeadsTableFilterService } from './leads-table-filter.service';
import { LeadTableCompanyNameColumnComponent } from './column-components/lead-table-company-name-column.component';
import { LeadTablePersonNameColumnComponent } from './column-components/lead-table-person-name-column.component';

const LEADS_TABLE_DEF: TableDefinition = {
  id: 'leads',
  labels: COMMON_TABLE_LABELS,
  columns: [
    {
      id: 'companyName',
      field: 'business.companyName',
      displayName: 'LEADS.LEAD_DATA.COMPANY_NAME',
      type: ColumnType.COLUMN_TYPE_STRING,
      cellComponent: LeadTableCompanyNameColumnComponent,
    },
    {
      id: 'companyWebsite',
      field: 'business.website',
      displayName: 'LEADS.LEAD_DATA.COMPANY_WEBSITE',
      type: ColumnType.COLUMN_TYPE_STRING,
    },
    {
      id: 'personEmail',
      field: 'person.email',
      displayName: 'LEADS.LEAD_DATA.EMAIL',
      type: ColumnType.COLUMN_TYPE_STRING,
    },
    {
      id: 'personPhone',
      field: 'person.phoneNumber',
      displayName: 'LEADS.LEAD_DATA.PHONE',
      type: ColumnType.COLUMN_TYPE_STRING,
    },
    {
      id: 'personName',
      displayName: 'LEADS.LEAD_DATA.NAME_FULL',
      type: ColumnType.COLUMN_TYPE_STRING,
      cellComponent: LeadTablePersonNameColumnComponent,
    },
    {
      id: 'campaignName',
      field: 'campaignName',
      displayName: 'LEADS.LEAD_DATA.CAMPAIGN_NAME',
      type: ColumnType.COLUMN_TYPE_STRING,
    },
    {
      id: 'adName',
      field: 'adName',
      displayName: 'LEADS.LEAD_DATA.AD_NAME',
      type: ColumnType.COLUMN_TYPE_STRING,
    },
    {
      id: 'created',
      field: 'createdTime',
      displayName: 'LEADS.LEAD_DATA.CREATED_DATE',
      type: ColumnType.COLUMN_TYPE_DATE,
      cellComponent: DateColumnComponent,
    },
  ],
  groups: [],
  columnOrganizerType: ColumnOrganizerType.ADVANCED_ORGANIZER,
};

export const tableDefinitionFactory = (filterService: LeadsTableFilterService) => {
  const tableDefintion = LEADS_TABLE_DEF;
  tableDefintion.filters = filterService.filters;
  return tableDefintion;
};
