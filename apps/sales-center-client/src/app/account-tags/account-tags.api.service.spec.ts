import { AccountTagsApiService } from './account-tags.api.service';
import { TestScheduler } from 'rxjs/testing';

let sched: TestScheduler;

const MOCK_TAGS = ['Tag1', 'Tag2', 'Tag3', 'Tag4', 'Tag5'];
const MOCK_AGID = 'AG-MOCK8675309';
const MOCK_USER_PARAMS = { partnerId: 'mockPartnerId', marketId: 'mockMarketID' };

class MockUserService {
  getPartnerIdAndMarketId$ = jest.fn(() => {
    return sched.createColdObservable('-x', { x: { ...MOCK_USER_PARAMS } });
  });
}

class MockEnvironmentService {
  getEnvironment = jest.fn(() => {
    return 'http://mock-vbc-site.appspot.com';
  });
}

class MockApiService {
  tags: string[];
  constructor() {
    this.tags = MOCK_TAGS;
  }
  post = jest.fn((url?, params?) => {
    const param = params.updates.find((val) => val.param === 'accountGroupId');
    const agid = param.value;

    if (agid === MOCK_AGID) {
      return sched.createColdObservable('--x', { x: this.tags });
    }
    return sched.createColdObservable('--#', { x: undefined });
  });
}
describe('AccountTagsApiService', () => {
  let service: AccountTagsApiService;
  let mockUserService: MockUserService;
  let mockApi: MockApiService;
  let mockEnvironment: MockEnvironmentService;

  beforeEach(() => {
    sched = new TestScheduler((a, b) => expect(a).toEqual(b));
    mockUserService = new MockUserService();
    mockApi = new MockApiService();
    mockEnvironment = new MockEnvironmentService();
    service = new AccountTagsApiService(mockUserService as any, mockApi as any, mockEnvironment as any);
  });
  afterEach(() => {
    sched.flush();
  });

  it('should load the tags for the proper account group id', () => {
    const accountTags$ = service.getAccountTags(MOCK_AGID);
    sched.expectObservable(accountTags$).toBe('---x', { x: MOCK_TAGS });
  });

  it('should handle an api error', () => {
    const accountTags$ = service.getAccountTags(undefined);
    sched.expectObservable(accountTags$).toBe('---#', { x: undefined });
  });
});
