import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { LoggedInUserInfoService } from '../logged-in-user-info';
import { ApiService } from '../common';
import { shareReplay, switchMap } from 'rxjs/operators';
import { HttpParams } from '@angular/common/http';
import { Environment, EnvironmentService } from '@galaxy/core';

export interface TagResponse {
  tags: string[];
}
export const GET_ACCOUNT_TAGS_URL = '/internalApi/v3/tags/get/';

@Injectable()
export class AccountTagsApiService {
  constructor(
    private readonly userService: LoggedInUserInfoService,
    private readonly api: ApiService,
    private readonly environmentService: EnvironmentService,
  ) {}

  public getAccountTags(accountGroupId: string): Observable<TagResponse> {
    let vbcApiHost = 'https://vbc-demo.appspot.com';
    if (this.environmentService.getEnvironment() === Environment.PROD) {
      vbcApiHost = 'https://vbc-prod.appspot.com';
    }
    return this.userService.getPartnerIdAndMarketId$().pipe(
      switchMap((val) => {
        let params: HttpParams = new HttpParams();
        params = params.set('partnerId', val.partnerId);
        params = params.set('accountGroupId', accountGroupId);
        return this.api.post(vbcApiHost + GET_ACCOUNT_TAGS_URL, params);
      }),
      shareReplay(1),
    );
  }
}
