import { A11yModule } from '@angular/cdk/a11y';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyButtonGroupModule } from '@vendasta/galaxy/button-group';
import { HostService } from '@vendasta/meetings';
import { ProductAnalyticsModule, ProductAnalyticsService } from '@vendasta/product-analytics';
import { Businesses } from '@vendasta/sales';
import { MarketService } from '../easy-account-create/business/providers/market.service';
import {
  AssignedSalespersonDialogComponent,
  FormatPhoneNumberPipe,
} from './assigned-salesperson-dialog/assigned-salesperson-dialog.component';
import { AssignedSalespersonNavFooterCardComponent } from './assigned-salesperson-nav-footer-card/assigned-salesperson-nav-footer-card.component';
import { AssignedSalespersonService } from './assigned-salesperson.service';
import { ContactUsComponent } from './contact-us/contact-us.component';

@NgModule({
  declarations: [
    AssignedSalespersonNavFooterCardComponent,
    AssignedSalespersonDialogComponent,
    FormatPhoneNumberPipe,
    ContactUsComponent,
  ],
  exports: [
    AssignedSalespersonNavFooterCardComponent,
    AssignedSalespersonDialogComponent,
    ContactUsComponent,
    TranslateModule,
  ],
  imports: [
    CommonModule,
    MatDialogModule,
    MatInputModule,
    MatButtonModule,
    GalaxyButtonGroupModule,
    MatIconModule,
    MatFormFieldModule,
    FormsModule,
    TranslateModule,
    MatProgressSpinnerModule,
    A11yModule,
    ProductAnalyticsModule,
  ],
  providers: [Businesses, AssignedSalespersonService, HostService, MarketService, ProductAnalyticsService],
})
export class AssignedSalespersonModule {}
