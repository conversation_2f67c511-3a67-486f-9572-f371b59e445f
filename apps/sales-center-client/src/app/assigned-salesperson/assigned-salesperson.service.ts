import { Inject, Injectable } from '@angular/core';
import { Salesperson, SalespersonService } from '@vendasta/sales';
import { combineLatest, Observable, of } from 'rxjs';
import { catchError, delay, map, mergeMap, publishReplay, refCount, retryWhen, switchMap, take } from 'rxjs/operators';
import { MatDialogConfig } from '@angular/material/dialog';
import { HostService } from '@vendasta/meetings';
import { PartnerService } from '@galaxy/partner';
import { LoggedInUserInfoService } from '../logged-in-user-info';
import { MarketService } from '../easy-account-create/business/providers/market.service';

const DEFAULT_IMAGE_URL =
  'https://lh3.googleusercontent.com/70DGcv8fyAzy1n_cWh8GyVbsCIWzPGZMdBFiwJa8Ot-Ss5Rf_oZY8hKOG2wAttY4_iuU5TlO9NLlDrBGuEJBj6DHwRnGd2CJ32J2Im3c1g';

@Injectable()
export class AssignedSalespersonService {
  salesperson$: Observable<Salesperson>;
  dialogConfig$: Observable<MatDialogConfig>;
  bookMeetingUrl$: Observable<string>;
  currentUser$: Observable<Salesperson>;

  defaultImageUrl = DEFAULT_IMAGE_URL;
  delayTimes = [5, 5, 10, 10, 30, 30, 30, 60, 60, 60, 80];

  constructor(
    private salespersonService: SalespersonService,
    private meetingHostService: HostService,
    private partnerService: PartnerService,
    private loggedInUserService: LoggedInUserInfoService,
    private marketService: MarketService,
    @Inject('PARTNER_ID') private readonly partnerId$: Observable<string>,
  ) {
    let newPartner = false;
    const assignedSalesPerson$: Observable<{ accountGroupId: string; salesperson: Salesperson }> = combineLatest([
      this.partnerId$,
      this.loggedInUserService.loggedInUserInfo$,
      this.marketService.currentMarket$,
    ]).pipe(
      switchMap(([partnerId, userService, market]) => {
        const today = new Date();
        if (userService.partnerCreated) {
          const partnerCreatedDate = new Date(userService.partnerCreated).getTime();
          if (today.setDate(today.getDate() - 1) < partnerCreatedDate * 1000) {
            newPartner = true;
          }
        } else if (userService.partnerCreatedAt) {
          // check if partner was created within 1 day.
          if (today.setDate(today.getDate() - 1) < userService.partnerCreatedAt * 1000) {
            newPartner = true;
          }
        }
        return this.salespersonService.getAssignedSalespersonForPartner(partnerId, market.market_id);
      }),
      retryWhen((attempts) => {
        return attempts.pipe(
          mergeMap((error, i) => {
            if (!newPartner || i >= this.delayTimes.length || error.status >= 500) {
              throw new Error('Could not retrieve salesperson.');
            } else {
              return of(error).pipe(delay(this.delayTimes[i] * 1000));
            }
          }),
        );
      }),
      publishReplay(1),
      refCount(),
    );

    this.salesperson$ = assignedSalesPerson$.pipe(
      map((a) => a?.salesperson),
      catchError(() => of(null)),
      publishReplay(1),
      refCount(),
    );

    this.dialogConfig$ = this.salesperson$.pipe(
      map((salesperson) => {
        if (salesperson) {
          const dialogConfig = new MatDialogConfig();
          dialogConfig.disableClose = false;
          dialogConfig.autoFocus = true;
          dialogConfig.width = '550px';
          dialogConfig.maxHeight = '400px';
          dialogConfig.data = {
            imgUrl: salesperson?.photoUrlHttps,
            name: `${salesperson?.firstName || ''} ${salesperson?.lastName || ''}`.trim(),
            title: salesperson?.jobTitle,
            telephone: salesperson?.phoneNumber?.length > 0 ? salesperson.phoneNumber[0] : '',
            country: salesperson?.countryCode,
          };
          return dialogConfig;
        }
      }),
      publishReplay(1),
      refCount(),
    );

    const isHostConfigured$: Observable<boolean> = this.salesperson$.pipe(
      switchMap((s) => {
        if (!s) {
          return of(false);
        }
        return this.meetingHostService.isHostConfigured({ hostId: s.salespersonId }).pipe(
          take(1),
          map((resp) => resp.isConfigured),
        );
      }),
    );

    this.currentUser$ = combineLatest([this.partnerId$, this.loggedInUserService.unifiedUserId$]).pipe(
      switchMap(([partnerId, userId]) => {
        return this.salespersonService.getSalespersonByUserId(partnerId, userId);
      }),
    );

    this.bookMeetingUrl$ = combineLatest([this.currentUser$, isHostConfigured$, this.salesperson$]).pipe(
      switchMap(([currentUser, configured, salesperson]) => {
        if (configured) {
          return this.meetingHostService.getBookingUrl({
            hostId: salesperson?.salespersonId,
            metadata: {
              msm_firstName: currentUser.firstName,
              msm_lastName: currentUser.lastName,
              msm_email: currentUser.email,
            },
          });
        }
        return of('');
      }),
      publishReplay(1),
      refCount(),
    );
  }
}
