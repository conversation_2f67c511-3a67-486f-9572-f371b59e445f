<ng-container *ngIf="pageData$ | async as pageData">
  <div *ngIf="pageData.salesperson" class="salesperson-nav-card menu-open">
    <img
      *ngIf="pageData.salesperson.photoUrlHttps"
      class="salesperson-photo"
      src="{{ pageData.salesperson.photoUrlHttps }}"
      alt="Salesperson Photo"
      (click)="openDialog(pageData.dialogConfig)"
    />

    <div class="contact-details">
      <div class="talk-to-vendasta contact-option" (click)="openDialog(pageData.dialogConfig)">
        {{ 'ASSIGNED_SALESPERSON.CONTACT_SALESPERSON.TALK_TO_SALES' | translate }}
      </div>

      <div class="contact-options">
        <div
          *ngIf="pageData.bookMeetingUrl"
          class="book-a-meeting contact-option"
          (click)="bookMeeting(pageData.bookMeetingUrl)"
        >
          <mat-icon class="icon">today</mat-icon>
          {{ 'ASSIGNED_SALESPERSON.CONTACT_SALESPERSON.BOOK_MEETING' | translate }}
        </div>
        <div class="contact contact-option" (click)="openDialog(pageData.dialogConfig)">
          <mat-icon class="icon">question_answer</mat-icon>
          {{ 'ASSIGNED_SALESPERSON.CONTACT_SALESPERSON.CONTACT' | translate }}
        </div>
      </div>
    </div>
  </div>
</ng-container>
