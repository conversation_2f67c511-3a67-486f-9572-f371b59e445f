import { Component, OnInit } from '@angular/core';
import { combineLatest, Observable } from 'rxjs';
import { Salesperson } from '@vendasta/sales';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { AssignedSalespersonService } from '../assigned-salesperson.service';
import { AssignedSalespersonDialogComponent } from '../assigned-salesperson-dialog/assigned-salesperson-dialog.component';
import { map } from 'rxjs/operators';

@Component({
  selector: 'app-assigned-salesperson-nav-footer-card',
  templateUrl: './assigned-salesperson-nav-footer-card.component.html',
  styleUrls: ['./assigned-salesperson-nav-footer-card.component.scss'],
  standalone: false,
})
export class AssignedSalespersonNavFooterCardComponent implements OnInit {
  pageData$: Observable<{
    salesperson: Salesperson;
    dialogConfig: MatDialogConfig;
    bookMeetingUrl: string;
  }>;

  constructor(
    private dialog: MatDialog,
    private assignedSalespersonService: AssignedSalespersonService,
  ) {}

  ngOnInit(): void {
    const salesperson$ = this.assignedSalespersonService.salesperson$;
    const dialogConfig$ = this.assignedSalespersonService.dialogConfig$;
    const bookMeetingUrl$ = this.assignedSalespersonService.bookMeetingUrl$;

    this.pageData$ = combineLatest([salesperson$, dialogConfig$, bookMeetingUrl$]).pipe(
      map(([salesperson, config, meetingUrl]) => {
        return { salesperson: salesperson, dialogConfig: config, bookMeetingUrl: meetingUrl };
      }),
    );
  }

  openDialog(dialogConfig: MatDialogConfig): void {
    this.dialog.open(AssignedSalespersonDialogComponent, dialogConfig);
  }

  bookMeeting(bookMeetingUrl: string): void {
    window.open(bookMeetingUrl, '_blank');
  }
}
