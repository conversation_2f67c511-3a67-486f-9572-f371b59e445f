@use 'design-tokens' as *;

.salesperson-nav-card {
  display: flex;
  align-items: center;
  height: 72px;
}

.salesperson-photo {
  margin: auto 0 auto 8px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.salesperson-photo:hover {
  cursor: pointer;
  opacity: 0.8;
}

.contact-details {
  display: flex;
  flex-direction: column;
  font-size: 14px;
  font-weight: 500;
  margin-left: 8px;
}

.talk-to-vendasta {
  display: contents;
}

.contact-options {
  margin-top: 6px;
  display: flex;
}

.book-a-meeting {
  align-items: center;
  display: flex;
  margin-right: 18px;
}

.contact {
  align-items: center;
  display: flex;
}

.icon {
  margin-right: 4px;
  font-size: 14px;
  height: 14px;
  width: 14px;
}
