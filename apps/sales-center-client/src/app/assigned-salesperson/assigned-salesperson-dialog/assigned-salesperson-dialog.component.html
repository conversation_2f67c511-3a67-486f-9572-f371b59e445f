<h2 mat-dialog-title>{{ 'ASSIGNED_SALESPERSON.CONTACT_SALESPERSON.CONTACT' | translate }} Vendasta</h2>

<div class="dialog-content">
  <div class="salesperson-container row">
    <div>
      <img src="{{ imgUrl }}" />
    </div>
    <div class="contact-container">
      <div class="person-name">{{ name }}</div>
      <div class="person-title">{{ title }}</div>
      <div class="person-away-message">{{ awayMessage$ | async }}</div>
      <ng-container *ngIf="bookMeetingUrl$ | async as bookMeetingUrl; else phoneOnly">
        <div class="schedule-or-call">
          <a href="{{ bookMeetingUrl }}" target="_blank" class="schedule">
            {{ 'ASSIGNED_SALESPERSON.CONTACT_SALESPERSON.SCHEDULE_MEETING' | translate }}
          </a>
          <span class="or-call-text">
            &nbsp;{{ 'ASSIGNED_SALESPERSON.CONTACT_SALESPERSON.OR_CALL' | translate }}&nbsp;
          </span>
          <span class="contact-details">
            <a href="tel:{{ tel }}">
              {{ tel | formatPhoneNumber : countryCode }}
            </a>
          </span>
        </div>
      </ng-container>
      <ng-template #phoneOnly>
        <div class="contact-details">
          <div>
            <a href="tel:{{ tel }}">
              {{ tel | formatPhoneNumber : countryCode }}
            </a>
          </div>
        </div>
      </ng-template>
    </div>
  </div>
</div>
