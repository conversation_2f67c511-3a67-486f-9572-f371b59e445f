@use 'design-tokens' as *;

div {
  display: block;
}

.dialog-content {
  padding: 16px 24px 0;
  min-height: 150px;

  .salesperson-container {
    display: flex;

    img {
      width: 70px;
      height: 70px;
      border-radius: 50%;
      margin-right: 16px;
    }

    .person-name {
      font-size: 16px;
      font-weight: 700;
    }

    .person-title {
      color: $dark-gray;
    }

    .person-away-message {
      color: $gray;
    }

    .schedule-or-call {
      display: flex;
      flex-direction: row;
    }

    .schedule {
      font-weight: 500;
    }

    .contact-details {
      color: #1e88e5;
    }
  }

  .contact-container {
    max-height: 80px;
    overflow: scroll;
    -ms-overflow-style: none;
    align-self: center;
  }

  .contact-container::-webkit-scrollbar {
    display: none;
  }

  .contact-text-area {
    width: 100%;
    padding-top: 10px;
  }

  .contact-text-area textarea.sales-message-text {
    color: rgb(30, 30, 30);
    caret-color: black;
  }
}

::ng-deep .mat-mdc-dialog-container {
  max-height: unset;
}

button.send-message {
  mat-progress-spinner {
    filter: grayscale(1) brightness(2.5);
    margin: 6px 4px 6px 4px;
  }
}

button.disabled-button {
  background-color: rgba(29, 131, 220, 0.5);
}

@media (max-width: $media--desktop-minimum) {
  .or-call-text {
    display: none;
  }
  .schedule-or-call {
    display: block !important;
  }
  .contact-details {
    display: inline;

    &::before {
      content: '\a';
      white-space: pre;
    }
  }
}
