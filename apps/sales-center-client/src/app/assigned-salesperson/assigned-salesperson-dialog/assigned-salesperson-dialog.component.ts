import { Component, Inject, OnInit, Pipe, PipeTransform } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { map, switchMap } from 'rxjs/operators';
import { PhoneNumberFormatPipe } from '@vendasta/uikit';
import { Observable, combineLatest } from 'rxjs';
import { AssignedSalespersonService } from '../assigned-salesperson.service';
import { SalesActivityApiService } from '../../sales-activity/sales-activity-api.service';
import { PARTNER_ID_TOKEN } from '../../common/providers';
import { IAMService, ListAuxiliaryDataResponse } from '@vendasta/iamv2';
import { Salesperson } from '@vendasta/sales';

const DEFAULT_IMAGE_URL =
  'https://lh3.googleusercontent.com/70DGcv8fyAzy1n_cWh8GyVbsCIWzPGZMdBFiwJa8Ot-Ss5Rf_oZY8hKOG2wAttY4_iuU5TlO9NLlDrBGuEJBj6DHwRnGd2CJ32J2Im3c1g';

@Component({
  templateUrl: './assigned-salesperson-dialog.component.html',
  styleUrls: ['./assigned-salesperson-dialog.component.scss'],
  standalone: false,
})
export class AssignedSalespersonDialogComponent implements OnInit {
  name: string;
  title: string;
  tel: string;
  imgUrl: string;
  countryCode: string;
  bookMeetingUrl$: Observable<string>;
  awayMessage$: Observable<string>;

  constructor(
    private dialogRef: MatDialogRef<AssignedSalespersonDialogComponent>,
    @Inject(MAT_DIALOG_DATA)
    data: {
      imgUrl: string;
      name: string;
      title: string;
      telephone: string;
      country: string;
    },
    private salesActivityService: SalesActivityApiService,
    private assignedSalespersonService: AssignedSalespersonService,
    @Inject(PARTNER_ID_TOKEN) readonly partnerId$: Observable<string>,
    private readonly userService: IAMService,
  ) {
    this.name = data.name;
    this.title = data.title;
    this.imgUrl = data.imgUrl ? data.imgUrl : DEFAULT_IMAGE_URL;
    this.tel = data.telephone;
    this.countryCode = data.country;
  }

  ngOnInit(): void {
    this.bookMeetingUrl$ = this.assignedSalespersonService.bookMeetingUrl$;
    this.awayMessage$ = combineLatest([this.partnerId$, this.assignedSalespersonService.salesperson$]).pipe(
      switchMap(([partnerId, salesperson]: [string, Salesperson | undefined]) => {
        return this.userService.listUserAuxiliaryData(
          {
            partnerId: partnerId,
            objectId: salesperson?.salespersonId,
          },
          {
            cursor: '',
            pageSize: 100,
          },
        );
      }),
      map((resp: ListAuxiliaryDataResponse) => {
        let awayMessage = '';
        const objectSchema = JSON.parse(resp.jsonSchema);
        const objectData = JSON.parse(resp.jsonData);
        for (const property in objectSchema.properties) {
          if (objectSchema.properties[property].title === 'Away Message') {
            awayMessage = <string>objectData[property];
            break;
          }
        }
        return awayMessage;
      }),
    );
  }

  close(): void {
    this.dialogRef.close();
  }
}

@Pipe({
  name: 'formatPhoneNumber',
  standalone: false,
})
export class FormatPhoneNumberPipe implements PipeTransform {
  formatPhoneNumber: PhoneNumberFormatPipe = new PhoneNumberFormatPipe();

  transform(phoneNumber: string, country: string): string {
    return this.formatPhoneNumber.transform(phoneNumber, country);
  }
}
