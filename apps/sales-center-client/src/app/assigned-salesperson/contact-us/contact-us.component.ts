import { Component, OnInit } from '@angular/core';
import { Observable } from 'rxjs';
import { take } from 'rxjs/operators';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { AssignedSalespersonDialogComponent } from '../assigned-salesperson-dialog/assigned-salesperson-dialog.component';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { AssignedSalespersonService } from '../assigned-salesperson.service';

@Component({
  selector: 'app-contact-us',
  templateUrl: './contact-us.component.html',
  standalone: false,
})
export class ContactUsComponent implements OnInit {
  dialogConfig$: Observable<MatDialogConfig<any>>;

  constructor(
    private readonly dialog: MatDialog,
    private assignedSalespersonService: AssignedSalespersonService,
    private snowplowService: ProductAnalyticsService,
  ) {}

  ngOnInit(): void {
    this.dialogConfig$ = this.assignedSalespersonService.dialogConfig$;
  }

  openContactUsDialog(): void {
    this.dialogConfig$.pipe(take(1)).subscribe((dialogConfig) => {
      this.dialog.open(AssignedSalespersonDialogComponent, dialogConfig);
    });
  }
}
