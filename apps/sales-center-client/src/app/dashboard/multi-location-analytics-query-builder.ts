import {
  CompositeFilter,
  CompositeFilterOperator,
  FieldFilter,
  FieldFilterOperator,
  Filter,
  GroupBy,
  GroupByOperator,
  Measure,
  MeasureAggregate,
  MeasureAggregateOperator,
  PropertyType,
  QueryMetricsRequest,
  ResourceId,
  TypedValue,
} from '@vendasta/multi-location-analytics';
import { OPPORTUNITIES_EPOCH } from '@galaxy/leaderboard';

const SALES_ACTIVITIES_METRIC_NAME = 'sales_activities';
const SALES_OPPORTUNITIES_METRIC_NAME = 'sales_opportunities';
const SNAPSHOT_PERFORMANCE_METRIC_NAME = 'snapshot_performance';

export function buildDashboardSalesActivityRequest(
  partnerId: string,
  marketId: string,
  beginDate: Date,
  endDate: Date,
): QueryMetricsRequest {
  const request = new QueryMetricsRequest({
    metricName: SALES_ACTIVITIES_METRIC_NAME,
    partnerId: partnerId,
    dateRange: {
      start: beginDate,
      end: endDate,
    },
  });
  const emailSent = new Measure({
    aggregate: new MeasureAggregate({
      alias: 'emailsent',
      measure: 'sales_person_action',
      aggOp: MeasureAggregateOperator.COUNT,
      filter: new Filter({
        fieldFilter: new FieldFilter({
          dimension: 'sales_person_action',
          operator: FieldFilterOperator.EQUAL,
          value: new TypedValue({
            value: 'email-sent',
            valueType: PropertyType.PROPERTY_TYPE_STRING,
          }),
        }),
      }),
    }),
  });
  const emailReceived = new Measure({
    aggregate: new MeasureAggregate({
      alias: 'emailrecieved',
      measure: 'sales_person_action',
      aggOp: MeasureAggregateOperator.COUNT,
      filter: new Filter({
        fieldFilter: new FieldFilter({
          dimension: 'sales_person_action',
          operator: FieldFilterOperator.EQUAL,
          value: new TypedValue({
            value: 'email-received',
            valueType: PropertyType.PROPERTY_TYPE_STRING,
          }),
        }),
      }),
    }),
  });
  const inboundCall = new Measure({
    aggregate: new MeasureAggregate({
      alias: 'inboundcall',
      measure: 'sales_person_action',
      aggOp: MeasureAggregateOperator.COUNT,
      filter: new Filter({
        fieldFilter: new FieldFilter({
          dimension: 'sales_person_action',
          operator: FieldFilterOperator.EQUAL,
          value: new TypedValue({
            value: 'inbound-call',
            valueType: PropertyType.PROPERTY_TYPE_STRING,
          }),
        }),
      }),
    }),
  });
  const outboundCall = new Measure({
    aggregate: new MeasureAggregate({
      alias: 'outboundcall',
      measure: 'sales_person_action',
      aggOp: MeasureAggregateOperator.COUNT,
      filter: new Filter({
        fieldFilter: new FieldFilter({
          dimension: 'sales_person_action',
          operator: FieldFilterOperator.EQUAL,
          value: new TypedValue({
            value: 'outbound-call',
            valueType: PropertyType.PROPERTY_TYPE_STRING,
          }),
        }),
      }),
    }),
  });
  const meeting = new Measure({
    aggregate: new MeasureAggregate({
      alias: 'meeting',
      measure: 'sales_person_action',
      aggOp: MeasureAggregateOperator.COUNT,
      filter: new Filter({
        fieldFilter: new FieldFilter({
          dimension: 'sales_person_action',
          operator: FieldFilterOperator.EQUAL,
          value: new TypedValue({
            value: 'meeting',
            valueType: PropertyType.PROPERTY_TYPE_STRING,
          }),
        }),
      }),
    }),
  });
  const snapshotCreated = new Measure({
    aggregate: new MeasureAggregate({
      alias: 'snapshotcreated',
      measure: 'sales_person_action',
      aggOp: MeasureAggregateOperator.COUNT,
      filter: new Filter({
        fieldFilter: new FieldFilter({
          dimension: 'sales_person_action',
          operator: FieldFilterOperator.EQUAL,
          value: new TypedValue({
            value: 'snapshot-created',
            valueType: PropertyType.PROPERTY_TYPE_STRING,
          }),
        }),
      }),
    }),
  });
  const resourceId = new ResourceId({
    marketId: {
      marketIds: [marketId],
    },
  });
  const groupBy = new GroupBy({
    groupByOperator: GroupByOperator.OPERATOR_ROLLUP,
    dimension: [
      {
        dimension: 'sales_person_id',
      },
    ],
  });
  request.measures = [emailSent, emailReceived, inboundCall, outboundCall, meeting, snapshotCreated];
  request.resourceIds = [resourceId];
  request.groupBy = groupBy;

  return request;
}

export function buildDashboardSalesOpportunitiesRequest(
  partnerId: string,
  marketId: string,
  beginDate: Date,
  endDate: Date,
): QueryMetricsRequest {
  const request = new QueryMetricsRequest({
    metricName: SALES_OPPORTUNITIES_METRIC_NAME,
    partnerId: partnerId,
    dateRange: {
      start: OPPORTUNITIES_EPOCH,
      end: endDate,
    },
  });

  const filter = new Filter({
    compositeFilter: new CompositeFilter({
      op: CompositeFilterOperator.OR,
      filters: [
        {
          fieldFilter: new FieldFilter({
            dimension: 'is_closed',
            operator: FieldFilterOperator.EQUAL,
            value: {
              value: false,
              valueType: PropertyType.PROPERTY_TYPE_BOOL,
            },
          }),
        },
        {
          compositeFilter: new CompositeFilter({
            op: CompositeFilterOperator.AND,
            filters: [
              {
                fieldFilter: {
                  dimension: 'actual_closed_date',
                  operator: FieldFilterOperator.GREATER_THAN_OR_EQUAL,
                  value: {
                    value: beginDate,
                    valueType: PropertyType.PROPERTY_TYPE_TIMESTAMP,
                  },
                },
              },
              {
                fieldFilter: {
                  dimension: 'actual_closed_date',
                  operator: FieldFilterOperator.LESS_THAN_OR_EQUAL,
                  value: {
                    value: endDate,
                    valueType: PropertyType.PROPERTY_TYPE_TIMESTAMP,
                  },
                },
              },
            ],
          }),
        },
      ],
    }),
  });

  const closedWon = new Measure({
    aggregate: new MeasureAggregate({
      alias: 'wonprojectedrevenue',
      measure: 'projected_first_year_value',
      aggOp: MeasureAggregateOperator.SUM,
      filter: new Filter({
        fieldFilter: new FieldFilter({
          dimension: 'pipeline_stage',
          operator: FieldFilterOperator.EQUAL,
          value: new TypedValue({
            value: 'closed-won',
            valueType: PropertyType.PROPERTY_TYPE_STRING,
          }),
        }),
      }),
    }),
  });

  const closedLost = new Measure({
    aggregate: new MeasureAggregate({
      alias: 'lostprojectedrevenue',
      measure: 'projected_first_year_value',
      aggOp: MeasureAggregateOperator.SUM,
      filter: new Filter({
        fieldFilter: new FieldFilter({
          dimension: 'pipeline_stage',
          operator: FieldFilterOperator.EQUAL,
          value: new TypedValue({
            value: 'closed-lost',
            valueType: PropertyType.PROPERTY_TYPE_STRING,
          }),
        }),
      }),
    }),
  });

  const open = new Measure({
    aggregate: new MeasureAggregate({
      alias: 'openprobablerevenue',
      measure: 'probable_first_year_value',
      aggOp: MeasureAggregateOperator.SUM,
      filter: new Filter({
        fieldFilter: new FieldFilter({
          dimension: 'pipeline_stage',
          operator: FieldFilterOperator.EQUAL,
          value: new TypedValue({
            value: 'open',
            valueType: PropertyType.PROPERTY_TYPE_STRING,
          }),
        }),
      }),
    }),
  });

  const resourceId = new ResourceId({
    marketId: {
      marketIds: [marketId],
    },
  });

  const groupBy = new GroupBy({
    groupByOperator: GroupByOperator.OPERATOR_ROLLUP,
    dimension: [
      {
        dimension: 'sales_person_id',
      },
    ],
  });

  request.filter = filter;
  request.measures = [closedWon, closedLost, open];
  request.resourceIds = [resourceId];
  request.groupBy = groupBy;

  return request;
}

const SNAPSHOT_SECTIONS = [
  'listingsgrade',
  'reviewsgrade',
  'socialgrade',
  'websitegrade',
  'seograde',
  'advertisinggrade',
];
const GRADE_VALUE = {
  A_GRADE: 'A',
  B_GRADE: 'B',
  C_GRADE: 'C',
  D_GRADE: 'D',
  F_GRADE: 'F',
};

export function buildDashboardSnapshotPerformanceRequest(
  partnerId: string,
  marketId: string,
  beginDate: Date,
  endDate: Date,
): QueryMetricsRequest {
  const request = new QueryMetricsRequest({
    metricName: SNAPSHOT_PERFORMANCE_METRIC_NAME,
    partnerId: partnerId,
    dateRange: {
      start: beginDate,
      end: endDate,
    },
  });

  const gradeMeasures = buildSnapshotGradeMeasures(SNAPSHOT_SECTIONS);

  const resourceId = new ResourceId({
    marketId: {
      marketIds: [marketId],
    },
  });

  const groupBy = new GroupBy({
    groupByOperator: GroupByOperator.OPERATOR_ROLLUP,
    dimension: [
      {
        dimension: 'sales_person_id',
      },
    ],
  });

  request.measures = gradeMeasures;

  request.resourceIds = [resourceId];
  request.groupBy = groupBy;

  return request;
}

// For each section specified, build 5 measures (one for each grade - A, B, C, D, F)
function buildSnapshotGradeMeasures(measures: string[]): Measure[] {
  const measuresGrades: Measure[] = [];

  measures.forEach((measure) => {
    const measureAGrade = buildGradeMeasure(measure, GRADE_VALUE.A_GRADE);
    const measureBGrade = buildGradeMeasure(measure, GRADE_VALUE.B_GRADE);
    const measureCGrade = buildGradeMeasure(measure, GRADE_VALUE.C_GRADE);
    const measureDGrade = buildGradeMeasure(measure, GRADE_VALUE.D_GRADE);
    const measureFGrade = buildGradeMeasure(measure, GRADE_VALUE.F_GRADE);

    measuresGrades.push(measureAGrade, measureBGrade, measureCGrade, measureDGrade, measureFGrade);
  });

  return measuresGrades;
}

function buildGradeMeasure(measure: string, grade: string): any {
  return new Measure({
    aggregate: new MeasureAggregate({
      alias: measure + '_' + grade.toLowerCase(),
      measure: measure,
      aggOp: MeasureAggregateOperator.COUNT,
      filter: new Filter({
        fieldFilter: new FieldFilter({
          dimension: measure,
          operator: FieldFilterOperator.EQUAL,
          value: new TypedValue({
            value: grade,
            valueType: PropertyType.PROPERTY_TYPE_STRING,
          }),
        }),
      }),
    }),
  });
}
