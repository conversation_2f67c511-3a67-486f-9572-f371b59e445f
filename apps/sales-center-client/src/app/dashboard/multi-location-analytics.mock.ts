export const allSalesPeopleResponse = {
  metricResults: [
    {
      resourceId: {
        marketId: {
          marketIds: ['vendasta'],
        },
      },
      measuresSchema: {
        properties: [
          {
            name: 'emailsent',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'emailrecieved',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'inboundcall',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'outboundcall',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'meeting',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'snapshotcreated',
            type: 'PROPERTY_TYPE_INT64',
          },
        ],
      },
      dimensionsSchema: {
        properties: [
          {
            name: 'sales_person_id',
          },
        ],
      },
      metrics: {
        metrics: [
          {
            dimension: null,
            measures: ['851', '19', '10', '923', '48', '0'],
            results: {},
          },
          {
            dimension: 'UID-aae7c996-b4f9-405e-a63c-a13b2a766854', // <PERSON>
            measures: ['109', '0', '1', '0', '9', '0'],
            results: {},
          },
          {
            dimension: 'UID-828e5c4c-31b4-40fe-9a38-f0277eee998d', // Andy Bernard
            measures: ['425', '11', '9', '322', '2', '0'],
            results: {},
          },
          {
            dimension: 'SP-DBK5G2DC',
            measures: ['172', '7', '0', '224', '0', '0'],
            results: {},
          },
        ],
      },
    },
  ],
};

export const allSalesPeopleOpportunitiesResponse = {
  metricResults: [
    {
      resourceId: {
        marketId: {
          marketIds: ['vendasta'],
        },
      },
      measuresSchema: {
        properties: [
          {
            name: 'closedwon',
            type: 'PROPERTY_TYPE_DOUBLE',
          },
          {
            name: 'closedlost',
            type: 'PROPERTY_TYPE_DOUBLE',
          },
          {
            name: 'open',
            type: 'PROPERTY_TYPE_DOUBLE',
          },
        ],
      },
      dimensionsSchema: {
        properties: [
          {
            name: 'sales_person_id',
          },
        ],
      },
      metrics: {
        metrics: [
          {
            dimension: 'UID-ca38e2f5-18e6-4d15-a562-8178d8660d20',
            measures: [15915956, 428113378, 347208070],
            results: {},
          },
          {
            dimension: 'SP-12345',
            measures: [2343432, null, 72163830],
            results: {},
          },
          {
            dimension: 'UID-e2a0fa1c-27dd-44d5-82f2-767ad4c58028',
            measures: [2343432, -480, null],
            results: {},
          },
        ],
      },
    },
  ],
};

export const snapshotPerformanceResponse = {
  metricResults: [
    {
      resourceId: {
        marketId: {
          marketIds: ['vendasta'],
        },
      },
      measuresSchema: {
        properties: [
          {
            name: 'listingsgrade_a',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'listingsgrade_b',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'listingsgrade_c',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'listingsgrade_d',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'listingsgrade_f',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'reviewsgrade_a',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'reviewsgrade_b',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'reviewsgrade_c',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'reviewsgrade_d',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'reviewsgrade_f',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'socialgrade_a',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'socialgrade_b',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'socialgrade_c',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'socialgrade_d',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'socialgrade_f',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'websitegrade_a',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'websitegrade_b',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'websitegrade_c',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'websitegrade_d',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'websitegrade_f',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'seograde_a',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'seograde_b',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'seograde_c',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'seograde_d',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'seograde_f',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'advertisinggrade_a',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'advertisinggrade_b',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'advertisinggrade_c',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'advertisinggrade_d',
            type: 'PROPERTY_TYPE_INT64',
          },
          {
            name: 'advertisinggrade_f',
            type: 'PROPERTY_TYPE_INT64',
          },
        ],
      },
      dimensionsSchema: {
        properties: [
          {
            name: 'sales_person_id',
          },
        ],
      },
      metrics: {
        metrics: [
          {
            dimension: 'UID-ca38e2f5-18e6-4d15-a562-8178d8660d20',
            measures: [
              '2',
              '4',
              '6',
              '8',
              '10',
              '1',
              '3',
              '5',
              '7',
              '9',
              '2',
              '4',
              '6',
              '8',
              '10',
              '1',
              '3',
              '5',
              '7',
              '9',
              '2',
              '4',
              '6',
              '8',
              '10',
              '1',
              '3',
              '5',
              '7',
              '9',
            ],
            results: {},
          },
          {
            dimension: 'SP-12345',
            measures: [
              '0',
              '1',
              '2',
              '3',
              '4',
              '5',
              '6',
              '7',
              '8',
              '9',
              '0',
              '1',
              '2',
              '3',
              '4',
              '5',
              '6',
              '7',
              '8',
              '9',
              '0',
              '1',
              '2',
              '3',
              '4',
              '5',
              '6',
              '7',
              '8',
              '9',
            ],
            results: {},
          },
          {
            dimension: 'UID-e2a0fa1c-27dd-44d5-82f2-767ad4c58028',
            measures: [
              '3',
              '6',
              '9',
              '12',
              '15',
              '4',
              '8',
              '12',
              '16',
              '20',
              '3',
              '6',
              '9',
              '12',
              '15',
              '4',
              '8',
              '12',
              '16',
              '20',
              '3',
              '6',
              '9',
              '12',
              '15',
              '4',
              '8',
              '12',
              '16',
              '20',
            ],
            results: {},
          },
          {
            dimension: '',
            measures: [
              '3',
              '6',
              '9',
              '12',
              '15',
              '4',
              '8',
              '12',
              '16',
              '20',
              '3',
              '6',
              '9',
              '12',
              '15',
              '4',
              '8',
              '12',
              '16',
              '20',
              '3',
              '6',
              '9',
              '12',
              '15',
              '4',
              '8',
              '12',
              '16',
              '20',
            ],
            results: {},
          },
        ],
      },
    },
  ],
};
