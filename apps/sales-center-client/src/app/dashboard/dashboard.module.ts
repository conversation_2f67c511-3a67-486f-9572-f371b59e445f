import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DashboardComponent } from './dashboard.component';
import { NavigationModule } from '../navigation/navigation.module';
import { HttpClient } from '@angular/common/http';
import { SalespersonService } from '@vendasta/salesperson';
import { DashboardRouting } from './dashboard.routing';
import { FiltersComponent } from './filters/filters.component';
import {
  CommonPipesModule,
  DateRangeSelectorModule,
  EmptyStateModule,
  VaCardsModule,
  VaFilterChipsModule,
  VaFilterModule,
} from '@vendasta/uikit';
import { TranslateModule } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { FilterChipsModule, SalespersonMultiSelectModule, SalesTeamMultiSelectModule } from '@vendasta/sales-ui';
import { FilterModule } from '@vendasta/va-filter2';
import { FiltersService } from './filters/filters.service';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { SalesActivityComponent } from './sales-activity/sales-activity.component';
import { MatDividerModule } from '@angular/material/divider';
import { SalesActivityService } from './sales-activity/sales-activity.service';
import { TaskStatusComponent } from './task-status/task-status.component';
import { TaskStatusService } from './task-status/task-status.service';
import { SalesOpportunitiesComponent } from './sales-opportunities/sales-opportunities.component';
import { SalesOpportunitiesService } from './sales-opportunities/sales-opportunities.service';
import { MatSelectModule } from '@angular/material/select';
import { ChartCardComponent } from './chart-card/chart-card.component';
import { UpcomingMeetingsComponent } from './upcoming-meetings/upcoming-meetings.component';
import { MatTableModule } from '@angular/material/table';
import { ClipboardModule } from '@angular/cdk/clipboard';
import { EmptyStateCardComponent } from './chart-card/empty-state-card/empty-state-card.component';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { CancelMeetingButtonModule, MeetingTypeModule, RescheduleMeetingButtonModule } from '@galaxy/meeting-scheduler';
import { RecentlyUpdatedAccountsModule } from '../recently-updated-accounts/recently-updated-accounts.module';
import { LexiconModule } from '@galaxy/lexicon';
import baseTranslation from '../../assets/i18n/en_devel.json';
import { WEBLATE_COMPONENT_NAME } from '../constants';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyWrapModule } from '@vendasta/galaxy/galaxy-wrap';
import { RecentlyOnlineModule } from '../recently-online/recently-online.module';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { getLocale } from '@vendasta/galaxy/utility/locale';
import { DateFnsAdapter, MAT_DATE_FNS_FORMATS } from '@angular/material-date-fns-adapter';

@NgModule({
  declarations: [
    DashboardComponent,
    FiltersComponent,
    SalesActivityComponent,
    TaskStatusComponent,
    SalesOpportunitiesComponent,
    ChartCardComponent,
    UpcomingMeetingsComponent,
    EmptyStateCardComponent,
  ],
  imports: [
    CommonModule,
    DashboardRouting,
    NavigationModule,
    VaFilterModule,
    TranslateModule,
    LexiconModule.forChild({
      componentName: WEBLATE_COMPONENT_NAME,
      baseTranslation: baseTranslation,
    }),
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatMenuModule,
    MatSelectModule,
    FilterModule,
    FilterChipsModule,
    DateRangeSelectorModule,
    VaCardsModule,
    VaFilterChipsModule,
    MatDatepickerModule,
    MatDividerModule,
    MatTableModule,
    CommonPipesModule,
    EmptyStateModule,
    ClipboardModule,
    GalaxyPageModule,
    GalaxyWrapModule,
    MeetingTypeModule,
    RescheduleMeetingButtonModule,
    CancelMeetingButtonModule,
    RecentlyUpdatedAccountsModule,
    RecentlyOnlineModule,
    GalaxyFormFieldModule,
    SalesTeamMultiSelectModule,
    SalespersonMultiSelectModule,
  ],
  providers: [
    SalespersonService,
    HttpClient,
    FiltersService,
    SalesActivityService,
    TaskStatusService,
    SalesOpportunitiesService,
    { provide: MAT_DATE_LOCALE, useValue: getLocale() },
    {
      provide: DateAdapter,
      useClass: DateFnsAdapter,
      deps: [MAT_DATE_LOCALE],
    },
    { provide: MAT_DATE_FORMATS, useValue: MAT_DATE_FNS_FORMATS },
  ],
  exports: [DashboardComponent, EmptyStateCardComponent],
  bootstrap: [DashboardComponent],
})
export class DashboardModule {}
