<app-chart-card
  [style]="{ display: isEmptyState ? 'none' : 'block' }"
  [cardText]="taskStatusChartCard"
  (footerEventEmitter)="viewFurtherInsights()"
>
  <div class="chart-container" [style]="{ display: (loading$ | async) ? 'none' : 'block' }">
    <canvas #taskStatus></canvas>
  </div>
  <div *ngIf="loading$ | async" [style]="{ display: 'block' }" class="stencil-shimmer shimmer"></div>
</app-chart-card>

<ng-container *ngIf="isEmptyState">
  <app-empty-state-card [emptyState]="taskStatusEmptyState"></app-empty-state-card>
</ng-container>
