import { Salesperson } from '@galaxy/types';
import { MetricResponsesMetricResponseInterface } from '@vendasta/task';
import { ChartCard } from '../chart-card/chart-card';

const COMPELETED = 'Completed';
const IN_PROGRESS = 'In Progress';
const OPEN = 'Open';
const WAITING_ON_CUSTOMER = 'Waiting on Customer';

export interface TaskStatusChartData {
  salespeople: SalespersonTasks[];
}

export interface SalespersonTasks {
  name?: string;
  id?: string;
  tasks?: Tasks;
}

export interface Tasks {
  completed: number;
  inProgress: number;
  open: number;
  waitingOnCustomer: number;
  total: number;
}

export const TASK_STATUS_CARD: ChartCard = {
  header: {
    title: { key: 'DASHBOARD.TASK_STATUS.CHART_TITLE' },
  },
  footer: {
    cta: { key: 'DASHBOARD.COMMON.FURTHER_INSIGHTS' },
  },
};

export function createEmptyDashboardSalespersonTaskStat(sp: Salesperson): SalespersonTasks {
  return {
    name: sp.fullName,
    id: sp.id,
    tasks: {
      completed: 0,
      inProgress: 0,
      open: 0,
      waitingOnCustomer: 0,
      total: 0,
    },
  };
}

export function createDashboardSalespersonTaskStat(
  sp: Salesperson,
  m: MetricResponsesMetricResponseInterface,
): SalespersonTasks {
  let completedTasks = 0;
  let inProgressTasks = 0;
  let openTasks = 0;
  let waitingOnCustomerTasks = 0;

  m.subMetricResponses.forEach((sm) => {
    if (sm.metricResponses[0].count.key === COMPELETED) {
      completedTasks = sm.metricResponses[0].count.count;
    }
    if (sm.metricResponses[0].count.key === IN_PROGRESS) {
      inProgressTasks = sm.metricResponses[0].count.count;
    }
    if (sm.metricResponses[0].count.key === OPEN) {
      openTasks = sm.metricResponses[0].count.count;
    }
    if (sm.metricResponses[0].count.key === WAITING_ON_CUSTOMER) {
      waitingOnCustomerTasks = sm.metricResponses[0].count.count;
    }
  });

  return {
    name: sp.fullName,
    id: sp.id,
    tasks: {
      completed: completedTasks,
      inProgress: inProgressTasks,
      open: openTasks,
      waitingOnCustomer: waitingOnCustomerTasks,
      total: m.count.count,
    },
  };
}
