import { ChartConfiguration } from 'chart.js';

export const COLORS = {
  completed: '#4285F4',
  inProgress: '#34A853',
  open: '#47bec6',
  waitingOnCustomer: '#8A2BE2',
  overdue: '#FF6D01',
};

export const TASK_STATUS_CHART_CONFIG: ChartConfiguration = {
  type: 'bar',
  data: {
    datasets: [],
  },
  options: {
    indexAxis: 'y',
    elements: {
      bar: {
        borderRadius: 0,
      },
    },
    scales: {
      x: {
        stacked: true,
        min: 0,
        beginAtZero: true,
      },
      y: {
        stacked: true,
        grid: {
          display: false,
        },
      },
    },
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        align: 'center',
        labels: {
          boxWidth: 10,
          font: {
            size: 10,
          },
        },
      },
    },
  },
};
