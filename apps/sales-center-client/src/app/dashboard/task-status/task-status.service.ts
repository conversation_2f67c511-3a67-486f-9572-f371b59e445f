import { Inject, Injectable } from '@angular/core';
import { Salesperson } from '@galaxy/types';
import { PersonaType } from '@vendasta/iam';
import {
  GetMetricsFiltersInterface,
  Metric,
  MetricResponsesMetricResponseInterface,
  RangeField,
  Status,
  TaskSdkService,
} from '@vendasta/task';
import { Observable, combineLatest } from 'rxjs';
import { map, shareReplay, take } from 'rxjs/operators';
import { ALL_SALESPEOPLE } from '../../data-providers/salespeople';
import { DashboardRequestFilterData } from '../filters/filters.service';
import {
  SalespersonTasks,
  TaskStatusChartData,
  createDashboardSalespersonTaskStat,
  createEmptyDashboardSalespersonTaskStat,
} from './task-status';

const ACCESS = [PersonaType.sales_person];

@Injectable()
export class TaskStatusService {
  constructor(
    private readonly taskSdk: TaskSdkService,
    @Inject(ALL_SALESPEOPLE) private readonly allSalespeople$: Observable<Salesperson[]>,
  ) {}

  getSalespersonTaskStats(requestFilters: DashboardRequestFilterData): Observable<TaskStatusChartData> {
    const filterRequest = this.buildDashboardTaskStatusRequest(
      requestFilters.partnerMarket.partnerId,
      requestFilters.dateFilter.start,
      requestFilters.dateFilter.end,
      [Status.WaitingOnCustomer, Status.Open, Status.InProgress, Status.Completed],
    );

    return combineLatest([
      this.taskSdk.getMetric(Metric.METRIC_STATUS_PER_ASSIGNEE, filterRequest),
      this.allSalespeople$,
    ]).pipe(
      take(1),
      map(([m, s]) => this.createSalesTaskChartData(m, s)),
      shareReplay(1),
    );
  }

  buildDashboardTaskStatusRequest(
    partnerId: string,
    startDate: Date,
    endDate: Date,
    statuses: Status[],
  ): GetMetricsFiltersInterface {
    return <GetMetricsFiltersInterface>{
      metadata: { keyValues: [{ key: 'partner', values: [{ stringValue: partnerId }] }] },
      rangeStart: startDate,
      rangeEnd: endDate,
      assignees: [], // get all salespeople available
      dateRangeField: RangeField.RANGE_FIELD_DUE_DATE,
      access: ACCESS,
      status: statuses,
    };
  }

  createSalesTaskChartData(
    metrics: MetricResponsesMetricResponseInterface[],
    salespeople: Salesperson[],
  ): TaskStatusChartData {
    const salespersonTasks: SalespersonTasks[] = this.matchMetricsWithSalespeople(salespeople, metrics);
    return this.sortSalespersonTaskStatusStats(salespersonTasks);
  }

  sortSalespersonTaskStatusStats(data: SalespersonTasks[]): TaskStatusChartData {
    return <TaskStatusChartData>{
      salespeople: data.sort((s1, s2) => {
        return s2.tasks.total - s1.tasks.total;
      }),
    };
  }

  matchMetricsWithSalespeople(
    salespeople: Salesperson[],
    metrics: MetricResponsesMetricResponseInterface[],
  ): SalespersonTasks[] {
    const salespersonTasks: SalespersonTasks[] = [];

    salespeople.forEach((sp) => {
      if (metrics) {
        const metric = metrics.find((m) => sp.id === m.count.key);
        if (metric) {
          const salespersonData = createDashboardSalespersonTaskStat(sp, metric);
          salespersonTasks.push(salespersonData);
        } else {
          const emptySalespersonData = createEmptyDashboardSalespersonTaskStat(sp);
          salespersonTasks.push(emptySalespersonData);
        }
      } else {
        const emptySalespersonData = createEmptyDashboardSalespersonTaskStat(sp);
        salespersonTasks.push(emptySalespersonData);
      }
    });
    return salespersonTasks;
  }
}
