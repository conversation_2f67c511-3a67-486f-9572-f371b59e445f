import { Salesperson } from '@galaxy/types';
import { PersonaType } from '@vendasta/iam';
import {
  GetMetricsFiltersInterface,
  Metric,
  MetricResponsesMetricResponse,
  RangeField,
  Status,
  TaskSdkService,
} from '@vendasta/task';
import { of as observableOf } from 'rxjs';
import { TestScheduler } from 'rxjs/testing';
import { PartnerMarket } from '../../logged-in-user-info/logged-in-user-info.service';
import { DashboardRequestFilterData } from '../filters/filters.service';
import { TaskStatusChartData } from './task-status';
import { TaskStatusService } from './task-status.service';

let sched: TestScheduler;

const mockFilters: DashboardRequestFilterData = {
  dateFilter: {
    start: new Date(2020, 3, 12),
    end: new Date(2020, 4, 11),
  },
  partnerMarket: new PartnerMarket('ABC', 'whatever'),
};

const mockAllSalespeople = observableOf(<Salesperson[]>[
  {
    fullName: '<PERSON>',
    id: 'UID-aae7c996-b4f9-405e-a63c-a13b2a766854',
  },
  {
    fullName: '<PERSON>pert',
    id: 'UID-aaaaaaaa',
  },
  {
    fullName: 'Andy Bernard',
    id: 'UID-828e5c4c-31b4-40fe-9a38-f0277eee998d',
  },
]);

const metricResponse: MetricResponsesMetricResponse[] = [
  {
    count: {
      key: 'UID-aae7c996-b4f9-405e-a63c-a13b2a766854',
      count: 5,
    },
    subMetricResponses: [
      {
        metricResponses: [
          {
            count: {
              key: 'Completed',
              count: 3,
            },
          },
        ],
      },
      {
        metricResponses: [
          {
            count: {
              key: 'In Progress',
              count: 2,
            },
          },
        ],
      },
    ],
  },
];

describe('TaskStatusService', () => {
  let service: TaskStatusService;
  let taskSdk: TaskSdkService;

  beforeEach(() => {
    sched = new TestScheduler((a, b) => expect(a).toEqual(b));
    taskSdk = {
      getMetric: jest.fn(() => observableOf(metricResponse)),
      setDueDate: jest.fn(() => observableOf(1)),
      search: jest.fn(() => observableOf(null)),
    } as unknown as TaskSdkService;
  });
  afterEach(() => {
    sched.flush();
  });

  describe('getSalespersonTaskStats', () => {
    it('should call task sdk with correct metric and filters', () => {
      service = new TaskStatusService(taskSdk, mockAllSalespeople);
      service.getSalespersonTaskStats(mockFilters);

      const expectedFilters: GetMetricsFiltersInterface = {
        metadata: { keyValues: [{ key: 'partner', values: [{ stringValue: 'ABC' }] }] },
        rangeStart: new Date(2020, 3, 12),
        rangeEnd: new Date(2020, 4, 11),
        assignees: [],
        dateRangeField: RangeField.RANGE_FIELD_DUE_DATE,
        access: [PersonaType.sales_person],
        status: [Status.WaitingOnCustomer, Status.Open, Status.InProgress, Status.Completed],
      };

      expect(taskSdk.getMetric).toBeCalledWith(Metric.METRIC_STATUS_PER_ASSIGNEE, expectedFilters);
    });

    it('should return chart data based on task sdk response', () => {
      service = new TaskStatusService(taskSdk, mockAllSalespeople);

      const expectedTaskStats = <TaskStatusChartData>{
        salespeople: [
          {
            name: 'Dwight Schrute',
            id: 'UID-aae7c996-b4f9-405e-a63c-a13b2a766854',
            tasks: {
              completed: 3,
              inProgress: 2,
              open: 0,
              total: 5,
              waitingOnCustomer: 0,
            },
          },
          {
            name: 'Jim Halpert',
            id: 'UID-aaaaaaaa',
            tasks: {
              completed: 0,
              inProgress: 0,
              open: 0,
              total: 0,
              waitingOnCustomer: 0,
            },
          },
          {
            name: 'Andy Bernard',
            id: 'UID-828e5c4c-31b4-40fe-9a38-f0277eee998d',
            tasks: {
              completed: 0,
              inProgress: 0,
              open: 0,
              total: 0,
              waitingOnCustomer: 0,
            },
          },
        ],
      };

      const stats = service.getSalespersonTaskStats(mockFilters);
      sched.expectObservable(stats).toBe('(x|)', { x: expectedTaskStats });
    });

    it('should create empty SalespersonTasks when task sdk returns no metrics', () => {
      taskSdk = {
        getMetric: jest.fn(() => observableOf(undefined)),
        setDueDate: jest.fn(() => observableOf(1)),
        search: jest.fn(() => observableOf(null)),
      } as unknown as TaskSdkService;
      service = new TaskStatusService(taskSdk, mockAllSalespeople);

      const expectedTaskStats = <TaskStatusChartData>{
        salespeople: [
          {
            name: 'Dwight Schrute',
            id: 'UID-aae7c996-b4f9-405e-a63c-a13b2a766854',
            tasks: {
              completed: 0,
              inProgress: 0,
              open: 0,
              total: 0,
              waitingOnCustomer: 0,
            },
          },
          {
            name: 'Jim Halpert',
            id: 'UID-aaaaaaaa',
            tasks: {
              completed: 0,
              inProgress: 0,
              open: 0,
              total: 0,
              waitingOnCustomer: 0,
            },
          },
          {
            name: 'Andy Bernard',
            id: 'UID-828e5c4c-31b4-40fe-9a38-f0277eee998d',
            tasks: {
              completed: 0,
              inProgress: 0,
              open: 0,
              total: 0,
              waitingOnCustomer: 0,
            },
          },
        ],
      };

      const stats = service.getSalespersonTaskStats(mockFilters);
      sched.expectObservable(stats).toBe('(x|)', { x: expectedTaskStats });
    });
  });

  describe('sortSalespersonTaskStatusStats', () => {
    it('should sort tasks in descending order by total', () => {
      service = new TaskStatusService(taskSdk, mockAllSalespeople);
      const s1 = { name: 's1', id: '1', tasks: { completed: 5, inProgress: 3, total: 8 } };
      const s2 = { name: 's2', id: '2', tasks: { completed: 8, inProgress: 8, total: 16 } };
      const s3 = { name: 's3', id: '3', tasks: { completed: 10, inProgress: 9, total: 19 } };
      const salespersonTasks = [s1, s2, s3];
      const expectedSalespersonTasks = [s3, s2, s1];

      const sortedTasks = service.sortSalespersonTaskStatusStats(salespersonTasks as any);
      expect(sortedTasks).toEqual({ salespeople: expectedSalespersonTasks });
    });
  });
});
