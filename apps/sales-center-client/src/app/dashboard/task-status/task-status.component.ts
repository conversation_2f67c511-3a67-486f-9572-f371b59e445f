import { Compo<PERSON>, Element<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Chart, ChartConfiguration } from 'chart.js';
import { BehaviorSubject, combineLatest, Observable, Subscription } from 'rxjs';
import { map, switchMap, tap } from 'rxjs/operators';
import { TASKS_OVERVIEW } from '../../urls';
import { ChartCard } from '../chart-card/chart-card';
import { EmptyStateCard } from '../chart-card/empty-state-card/empty-state-card';
import { combineSalespeopleSalesteamFilters, DEFAULT_VISIBLE_STATS } from '../filters/filters';
import { DashboardFilterData, DashboardRequestFilterData, FiltersService } from '../filters/filters.service';
import { SalespersonTasks, TaskStatusChartData, TASK_STATUS_CARD } from './task-status';
import { COLORS, TASK_STATUS_CHART_CONFIG } from './task-status.config';
import { TaskStatusService } from './task-status.service';

@Component({
  selector: 'app-dashboard-task-status',
  templateUrl: './task-status.component.html',
  styleUrls: ['./task-status.component.scss', '../dashboard.component.scss'],
  standalone: false,
})
export class TaskStatusComponent implements OnInit, OnDestroy {
  @ViewChild('taskStatus', { static: true }) taskStatusContainer: ElementRef;
  private taskStatusChartContext: CanvasRenderingContext2D;
  private taskStatusChart: Chart;
  private readonly subscriptions: Subscription[] = [];
  filteredTaskStatusChartData$: Observable<TaskStatusChartData>;
  private taskStatusData$: Observable<TaskStatusChartData>;

  private requestFilters$: Observable<DashboardRequestFilterData>;
  private dashboardFilters$: Observable<DashboardFilterData>;
  private topPerformers$: Observable<boolean>;

  public taskStatusChartCard: ChartCard = TASK_STATUS_CARD;
  public taskStatusEmptyState: EmptyStateCard;
  public isEmptyState = false;
  private readonly loading$$ = new BehaviorSubject<boolean>(true);
  readonly loading$ = this.loading$$.asObservable();

  constructor(
    private readonly router: Router,
    private readonly translate: TranslateService,
    private readonly filtersService: FiltersService,
    private readonly taskStatusService: TaskStatusService,
  ) {
    this.configureEmptyStates();
  }

  private configureEmptyStates(): void {
    this.taskStatusEmptyState = {
      cardText: {
        header: {
          title: { key: 'DASHBOARD.TASK_STATUS.CHART_TITLE' },
        },
      },
      config: {
        title: { key: 'DASHBOARD.TASK_STATUS.EMPTY_STATE.TITLE' },
        description: { key: 'DASHBOARD.TASK_STATUS.EMPTY_STATE.DESCRIPTION' },
        icon: 'assignment_late',
        cta: { text: 'Create a task', actionFunction: () => this.goToTasksPage() },
      },
    };
  }

  private goToTasksPage(): void {
    this.router.navigate([`/${TASKS_OVERVIEW}`]);
  }

  ngOnInit(): void {
    this.requestFilters$ = this.filtersService.dashboardRequestFilters$;
    this.dashboardFilters$ = this.filtersService.dashboardFilters$;
    this.topPerformers$ = this.filtersService.getTopPerformers$;

    this.drawTaskStatusChart();

    this.taskStatusData$ = this.requestFilters$.pipe(
      tap(() => this.loading$$.next(true)),
      switchMap((rfilters) => this.taskStatusService.getSalespersonTaskStats(rfilters)),
    );

    this.filteredTaskStatusChartData$ = combineLatest([this.taskStatusData$, this.dashboardFilters$]).pipe(
      tap(() => this.loading$$.next(true)),
      map(([d, f]) => this.filterData(d, f)),
    );

    this.subscriptions.push(
      combineLatest([this.filteredTaskStatusChartData$, this.topPerformers$])
        .pipe(
          map(([sortedData, topPerformer]) => {
            return this.getPerformers(sortedData, topPerformer);
          }),
        )
        .subscribe((stats) => {
          this.isEmptyState = this.isEmpty(stats);
          this.updateTaskStatusChartData(stats);
          this.loading$$.next(false);
        }),
    );
  }

  drawTaskStatusChart(): void {
    const taskStatusChartConfig: ChartConfiguration = TASK_STATUS_CHART_CONFIG;
    this.taskStatusChartContext = (this.taskStatusContainer.nativeElement as HTMLCanvasElement).getContext('2d');
    this.taskStatusChart = new Chart(this.taskStatusChartContext, {
      type: taskStatusChartConfig.type,
      options: taskStatusChartConfig.options,
      data: taskStatusChartConfig.data,
    });
  }

  filterData(newData: TaskStatusChartData, filters: DashboardFilterData): TaskStatusChartData {
    const salespeopleFilter = filters.salespeopleFilter;
    const salesteamFilter = filters.salesteamFilter;
    const filteredData: TaskStatusChartData = { salespeople: [] };
    let allSalespeopleFiltered: string[] = [];

    if (salespeopleFilter.length > 0 || salesteamFilter.length > 0) {
      allSalespeopleFiltered = combineSalespeopleSalesteamFilters(salespeopleFilter, salesteamFilter);

      allSalespeopleFiltered.forEach((s) => {
        const salesperson = newData.salespeople.find((fs) => s === fs.id);
        if (salesperson) {
          filteredData.salespeople.push(salesperson);
        }
      });
      return this.taskStatusService.sortSalespersonTaskStatusStats(filteredData.salespeople);
    } else {
      return newData;
    }
  }

  updateTaskStatusChartData(newData: TaskStatusChartData): void {
    const data = {
      labels: [],
      datasets: [],
    };

    const completed = newData.salespeople.map((sp) => sp.tasks.completed);
    const inProgress = newData.salespeople.map((sp) => sp.tasks.inProgress);
    const open = newData.salespeople.map((sp) => sp.tasks.open);
    const waitingOnCustomer = newData.salespeople.map((sp) => sp.tasks.waitingOnCustomer);

    newData.salespeople.forEach((s) => {
      data.labels.push(s.name);
    });

    data.datasets.push(
      {
        label: this.translate.instant('DASHBOARD.TASK_STATUS.COMPLETED'),
        data: completed,
        backgroundColor: COLORS.completed,
        barPercentage: 1,
      },
      {
        label: this.translate.instant('DASHBOARD.TASK_STATUS.IN_PROGRESS'),
        data: inProgress,
        backgroundColor: COLORS.inProgress,
        barPercentage: 1,
      },
      {
        label: this.translate.instant('DASHBOARD.TASK_STATUS.OPEN'),
        data: open,
        backgroundColor: COLORS.open,
        barPercentage: 1,
      },
      {
        label: this.translate.instant('DASHBOARD.TASK_STATUS.WAITING_ON_CUSTOMER'),
        data: waitingOnCustomer,
        backgroundColor: COLORS.waitingOnCustomer,
        barPercentage: 1,
      },
    );
    this.taskStatusChart.data = data;
    this.taskStatusChart.update();
  }

  viewFurtherInsights(): void {
    this.router.navigate(['tasks/overview']);
  }

  getPerformers(data: TaskStatusChartData, getTopPerformer: boolean): TaskStatusChartData {
    let performers: SalespersonTasks[];
    if (getTopPerformer) {
      performers = data.salespeople.slice(0, DEFAULT_VISIBLE_STATS);
    } else {
      performers = data.salespeople.slice(-DEFAULT_VISIBLE_STATS).reverse();
    }
    return <TaskStatusChartData>{
      salespeople: performers,
    };
  }

  isEmpty(tasksStatusData: TaskStatusChartData): boolean {
    if (tasksStatusData.salespeople[0]?.tasks?.total === 0) {
      return true;
    }
    return false;
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
  }
}
