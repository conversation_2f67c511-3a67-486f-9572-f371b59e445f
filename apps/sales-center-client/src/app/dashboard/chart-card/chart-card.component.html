<va-card>
  <va-card-break-group>
    <div class="title-link-container">
      <va-card-header [title]="cardText.header.title"></va-card-header>
      <div class="header-actions">
        <ng-content select="[chart-card-actions]"></ng-content>
      </div>
    </div>

    <va-card-break-line></va-card-break-line>

    <va-card-content [ngClass]="{ 'empty-state': action && action.style === 'empty-state' }">
      <ng-content></ng-content>
    </va-card-content>

    <va-card-link-button
      *ngIf="cardText.footer && canViewFurtherInsights"
      [text]="cardText.footer.cta"
      (linkClick)="onFooterClick()"
    >
      <mat-icon class="link-icon-content">{{ icon }}</mat-icon>
    </va-card-link-button>
  </va-card-break-group>
</va-card>
