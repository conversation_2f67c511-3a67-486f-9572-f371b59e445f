import { Component, Input } from '@angular/core';
import { EmptyStateCard, EMPTY_STATE_STYLE } from './empty-state-card';
import { Action } from '../chart-card';

@Component({
  selector: 'app-empty-state-card',
  templateUrl: './empty-state-card.component.html',
  styleUrls: ['./empty-state-card.component.scss'],
  standalone: false,
})
export class EmptyStateCardComponent {
  @Input() emptyState: EmptyStateCard;

  public emptyStateStyle: Action = EMPTY_STATE_STYLE;
}
