<app-chart-card [cardText]="emptyState.cardText" [action]="emptyStateStyle">
  <div class="empty-state">
    <uikit-empty-state
      [iconName]="emptyState.config.icon"
      [svgIconName]="emptyState.config.svgIcon"
      [title]="emptyState.config.title.key | translate"
      [ctaSecondaryText]="emptyState.config?.cta?.text"
      (ctaSecondaryEvent)="emptyState.config?.cta?.actionFunction()"
    >
      <span state-description>
        {{ emptyState?.config?.description?.key | translate }}
      </span>
      <ng-content state-description select="[empty-state-card-dynamic-content]"></ng-content>
    </uikit-empty-state>
  </div>
</app-chart-card>
