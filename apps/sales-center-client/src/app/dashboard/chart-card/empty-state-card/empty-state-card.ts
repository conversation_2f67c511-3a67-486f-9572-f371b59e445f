import { I18NDescriptor } from '@vendasta/uikit';
import { ChartCard, Action } from '../chart-card';

export const EMPTY_STATE_STYLE: Action = {
  style: 'empty-state',
};

export interface EmptyStateCard {
  cardText: ChartCard;
  config: EmptyStateConfig;
}

export interface EmptyStateConfig {
  title: I18NDescriptor;
  description?: I18NDescriptor;
  icon?: string;
  svgIcon?: string;
  cta?: EmptyStateCta;
}

export interface EmptyStateCta {
  text: string;
  action?: string;
  actionFunction?: () => any;
}
