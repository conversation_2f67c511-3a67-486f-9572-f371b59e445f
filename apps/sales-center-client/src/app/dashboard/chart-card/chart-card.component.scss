@use 'design-tokens' as *;

.title-link-container {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .link {
    color: $blue;
    cursor: pointer;
    font-weight: 400;
    font-size: 14px;
    padding: 16px;

    mat-icon {
      vertical-align: middle;
    }
  }
}

.header-actions {
  margin-right: $gutter-width-dense * 0.5;
}

.empty-state {
  background-color: $lighter-gray;
  border-radius: 0 0 5px 5px;
  height: 100%;
}

va-card-content,
va-card {
  height: 100%;
}

.action_content {
  display: inline;
  padding: 8px;
}
