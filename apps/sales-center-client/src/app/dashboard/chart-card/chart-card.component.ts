import { Component, EventEmitter, Input, Output } from '@angular/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Action, ChartCard } from './chart-card';

@Component({
  selector: 'app-chart-card',
  templateUrl: './chart-card.component.html',
  styleUrls: ['./chart-card.component.scss'],
  standalone: false,
})
export class ChartCardComponent {
  @Input() cardText: ChartCard;
  @Input() action?: Action;
  @Input() icon?: string = 'insert_chart';
  @Input() canViewFurtherInsights?: boolean = true;

  @Output() footerEventEmitter = new EventEmitter();

  constructor(private readonly alertService: SnackbarService) {}

  onFooterClick(): void {
    this.footerEventEmitter.emit('click');
  }

  displayCopiedToClipboardMessage(): void {
    this.alertService.openSuccessSnack('MEETING_LIST.ALERTS.LINK_COPIED');
  }
}
