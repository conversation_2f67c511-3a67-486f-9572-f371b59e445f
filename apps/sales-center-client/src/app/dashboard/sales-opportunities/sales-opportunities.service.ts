import { Inject, Injectable } from '@angular/core';
import { Salesperson } from '@galaxy/types';
import { MetricResult, MultiLocationAnalyticsService } from '@vendasta/multi-location-analytics';
import { Observable, combineLatest } from 'rxjs';
import { map, shareReplay, take } from 'rxjs/operators';
import { ALL_SALESPEOPLE } from '../../data-providers/salespeople';
import { DashboardRequestFilterData } from '../filters/filters.service';
import { buildDashboardSalesOpportunitiesRequest } from '../multi-location-analytics-query-builder';
import { extractMetricsFromResponse } from '../multi-location-analytics-utils';
import {
  SalesOpportunitiesChartData,
  SalespersonOpportunities,
  createDashboardSalespersonOpportunitiesStat,
  createEmptyDashboardSalespersonOpportunities,
} from './sales-opportunities';

@Injectable()
export class SalesOpportunitiesService {
  constructor(
    private readonly analyticsSdk: MultiLocationAnalyticsService,
    @Inject(ALL_SALESPEOPLE) private readonly allSalespeople$: Observable<Salesperson[]>,
  ) {}

  getSalespersonOpportunitiesStats(filters: DashboardRequestFilterData): Observable<SalesOpportunitiesChartData> {
    const request = buildDashboardSalesOpportunitiesRequest(
      filters.partnerMarket.partnerId,
      filters.partnerMarket.marketId,
      filters.dateFilter.start,
      filters.dateFilter.end,
    );
    return combineLatest([this.analyticsSdk.queryMetrics(request), this.allSalespeople$]).pipe(
      take(1),
      map(([metrics, salespeople]) => {
        return {
          metrics: extractMetricsFromResponse(metrics),
          salespeople: salespeople,
        };
      }),
      map((m) => this.createSalesOpportunitiesChartData(m.metrics, m.salespeople)),
      shareReplay(1),
    );
  }

  private createSalesOpportunitiesChartData(
    metrics: MetricResult[],
    allSalespeople: Salesperson[],
  ): SalesOpportunitiesChartData {
    const salespersonOpportunities = this.matchMetricsWithSalespeople(allSalespeople, metrics);
    const sortSalespersonOpportunities = this.sortSalespersonOpportunitiesStats(salespersonOpportunities);
    return this.formatSalespersonOpportunity(sortSalespersonOpportunities);
  }

  formatSalespersonOpportunity(data: SalesOpportunitiesChartData): SalesOpportunitiesChartData {
    return {
      salespeople: data.salespeople.map((salesperson) => {
        return {
          ...salesperson,
          opportunities: {
            closedwon: this.formatDecimals(salesperson.opportunities.closedwon),
            closedlost: this.formatDecimals(salesperson.opportunities.closedlost),
            open: this.formatDecimals(salesperson.opportunities.open),
            total: this.formatDecimals(salesperson.opportunities.total),
          },
        };
      }),
    };
  }

  formatDecimals(n: number | string): string {
    return Number(n)
      .toFixed(2)
      .replace(/[.,]00$/, '');
  }

  sortSalespersonOpportunitiesStats(data: SalespersonOpportunities[]): SalesOpportunitiesChartData {
    return <SalesOpportunitiesChartData>{
      salespeople: data.sort((s1, s2) => {
        return Number(s2.opportunities.total) - Number(s1.opportunities.total);
      }),
    };
  }

  private matchMetricsWithSalespeople(salespeople: Salesperson[], metrics: MetricResult[]): SalespersonOpportunities[] {
    const salespersonOpportunities: SalespersonOpportunities[] = [];

    salespeople.forEach((s) => {
      const metric = metrics.find((m) => s.id === m.dimension);
      if (metric) {
        const salespersonData = createDashboardSalespersonOpportunitiesStat(s, metric);
        salespersonOpportunities.push(salespersonData);
      } else {
        const emptySalespersonData = createEmptyDashboardSalespersonOpportunities(s);
        salespersonOpportunities.push(emptySalespersonData);
      }
    });
    return salespersonOpportunities;
  }
}
