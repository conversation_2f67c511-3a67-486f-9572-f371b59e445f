import { Salesperson } from '@galaxy/types';
import { MultiLocationAnalyticsService } from '@vendasta/multi-location-analytics';
import { of as observableOf } from 'rxjs';
import { TestScheduler } from 'rxjs/testing';
import { PartnerMarket } from '../../logged-in-user-info/logged-in-user-info.service';
import { DashboardRequestFilterData } from '../filters/filters.service';
import { allSalesPeopleOpportunitiesResponse } from '../multi-location-analytics.mock';
import { SalesOpportunitiesChartData } from './sales-opportunities';
import { SalesOpportunitiesService } from './sales-opportunities.service';

let sched: TestScheduler;

class MockAnalyticsSdk implements Pick<MultiLocationAnalyticsService, 'queryMetrics'> {
  response: any;

  constructor(response: any) {
    this.response = response;
  }

  queryMetrics = jest.fn(() => {
    return sched.createColdObservable('-x', { x: this.response });
  });
}

const mockFilters: DashboardRequestFilterData = {
  dateFilter: {
    start: new Date(2020, 3, 12),
    end: new Date(2020, 4, 11),
  },
  partnerMarket: new PartnerMarket('ABC', 'whatever'),
};

const mockAllSalespeople = observableOf(<Salesperson[]>[
  {
    fullName: 'Adam Silver',
    id: 'UID-ca38e2f5-18e6-4d15-a562-8178d8660d20',
  },
  {
    fullName: 'Agatha Murphy',
    id: 'UID-1234',
  },
  {
    fullName: 'Gilbert Locke',
    id: 'UID-e2a0fa1c-27dd-44d5-82f2-767ad4c58028',
  },
]);

describe('SalesOpportunitiesService', () => {
  let service: SalesOpportunitiesService;
  let mockAnalyticsSdk: MockAnalyticsSdk;

  beforeEach(() => {
    sched = new TestScheduler((a, b) => expect(a).toEqual(b));
  });
  afterEach(() => {
    sched.flush();
  });

  describe('getSalespersonOpportunitiesStats', () => {
    it('should return chart data based on multi location analytics response', () => {
      mockAnalyticsSdk = new MockAnalyticsSdk(allSalesPeopleOpportunitiesResponse);
      service = new SalesOpportunitiesService(
        mockAnalyticsSdk as any as MultiLocationAnalyticsService,
        mockAllSalespeople,
      );

      const expectedSalespersonStats = <SalesOpportunitiesChartData>{
        salespeople: [
          {
            name: 'Adam Silver',
            id: 'UID-ca38e2f5-18e6-4d15-a562-8178d8660d20',
            opportunities: {
              closedwon: '159159.56',
              closedlost: '4281133.78',
              open: '3472080.70',
              total: '7912374.04',
            },
          },
          {
            name: 'Gilbert Locke',
            id: 'UID-e2a0fa1c-27dd-44d5-82f2-767ad4c58028',
            opportunities: {
              closedwon: '23434.32',
              closedlost: '0',
              open: '0',
              total: '23434.32',
            },
          },
          {
            name: 'Agatha Murphy',
            id: 'UID-1234',
            opportunities: {
              closedwon: '0',
              closedlost: '0',
              open: '0',
              total: '0',
            },
          },
        ],
      };

      const stats = service.getSalespersonOpportunitiesStats(mockFilters);
      sched.expectObservable(stats).toBe('-(x|)', { x: expectedSalespersonStats });
    });

    it('should create empty SalespersonOpportunitiesStats when multi location analytics response is empty', () => {
      mockAnalyticsSdk = new MockAnalyticsSdk({ metricResults: [] });
      service = new SalesOpportunitiesService(
        mockAnalyticsSdk as any as MultiLocationAnalyticsService,
        mockAllSalespeople,
      );
      const expectedSalespersonStats = <SalesOpportunitiesChartData>{
        salespeople: [
          {
            name: 'Adam Silver',
            id: 'UID-ca38e2f5-18e6-4d15-a562-8178d8660d20',
            opportunities: {
              closedwon: '0',
              closedlost: '0',
              open: '0',
              total: '0',
            },
          },
          {
            name: 'Agatha Murphy',
            id: 'UID-1234',
            opportunities: {
              closedwon: '0',
              closedlost: '0',
              open: '0',
              total: '0',
            },
          },
          {
            name: 'Gilbert Locke',
            id: 'UID-e2a0fa1c-27dd-44d5-82f2-767ad4c58028',
            opportunities: {
              closedwon: '0',
              closedlost: '0',
              open: '0',
              total: '0',
            },
          },
        ],
      };

      const stats = service.getSalespersonOpportunitiesStats(mockFilters);
      sched.expectObservable(stats).toBe('-(x|)', { x: expectedSalespersonStats });
    });
  });
});
