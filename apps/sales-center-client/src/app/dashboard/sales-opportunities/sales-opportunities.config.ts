import { ChartConfiguration } from 'chart.js';

export const COLORS = {
  open: '#4385F4',
  closedwon: '#34A853',
  closedlost: '#EA4435',
};

export const SALES_OPPORTUNITIES_CHART_CONFIG: ChartConfiguration = {
  type: 'bar',
  data: {
    datasets: [],
  },
  options: {
    indexAxis: 'y',
    elements: {
      bar: {
        borderWidth: 0,
      },
    },
    scales: {
      x: {
        stacked: true,
        min: 0,
        beginAtZero: true,
        ticks: {
          callback: function (value): string {
            // display x axis items with $ and comma separation
            return '$' + value.toLocaleString();
          },
        },
      },

      y: {
        stacked: true,
        grid: {
          display: false,
        },
      },
    },
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        align: 'center',
        labels: {
          boxWidth: 10,
          font: {
            size: 10,
          },
        },
      },

      tooltip: {
        callbacks: {
          label: function (tooltipItem): string {
            // display tooltip items with $ and comma separation
            return '$' + tooltipItem.dataset[tooltipItem.datasetIndex].data[tooltipItem.dataIndex].toLocaleString();
          },
        },
      },
    },
  },
};
