import { Compo<PERSON>, ElementR<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Chart, ChartConfiguration } from 'chart.js';
import { BehaviorSubject, combineLatest, Observable, Subscription } from 'rxjs';
import { map, switchMap, tap } from 'rxjs/operators';
import { PIPELINE_TABLE } from '../../urls';
import { ChartCard } from '../chart-card/chart-card';
import { EmptyStateCard } from '../chart-card/empty-state-card/empty-state-card';
import { combineSalespeopleSalesteamFilters, DEFAULT_VISIBLE_STATS } from '../filters/filters';
import { DashboardFilterData, DashboardRequestFilterData, FiltersService } from '../filters/filters.service';
import {
  SalesOpportunitiesChartData,
  SalespersonOpportunities,
  SALES_OPPORTUNITY_CARD,
  SALES_OPPORTUNITY_EMPTY_STATE_CARD,
} from './sales-opportunities';
import { COLORS, SALES_OPPORTUNITIES_CHART_CONFIG } from './sales-opportunities.config';
import { SalesOpportunitiesService } from './sales-opportunities.service';

@Component({
  selector: 'app-dashboard-sales-opportunities',
  templateUrl: './sales-opportunities.component.html',
  styleUrls: ['./sales-opportunities.component.scss', '../dashboard.component.scss'],
  standalone: false,
})
export class SalesOpportunitiesComponent implements OnInit, OnDestroy {
  @ViewChild('salesOpportunities', { static: true }) salesOpportunitiesContainer: ElementRef;
  private salesOpportunitiesChartContext: CanvasRenderingContext2D;
  private salesOpportunitiesChart: Chart;
  private readonly subscriptions: Subscription[] = [];

  filteredSalesOpportunitiesChartData$: Observable<SalesOpportunitiesChartData>;
  private salesOpportunitiesData$: Observable<SalesOpportunitiesChartData>;

  private requestFilters$: Observable<DashboardRequestFilterData>;
  private dashboardFilters$: Observable<DashboardFilterData>;
  private topPerformers$: Observable<boolean>;

  public salesOpportunitiesChartCard: ChartCard = SALES_OPPORTUNITY_CARD;
  public salesOpportunityEmptyState: EmptyStateCard = SALES_OPPORTUNITY_EMPTY_STATE_CARD;
  public isEmptyState = false;
  private readonly loading$$ = new BehaviorSubject<boolean>(true);
  readonly loading$ = this.loading$$.asObservable();

  constructor(
    private readonly router: Router,
    private readonly translate: TranslateService,
    private readonly filtersService: FiltersService,
    private readonly salesOpportunitiesService: SalesOpportunitiesService,
  ) {}

  ngOnInit(): void {
    this.requestFilters$ = this.filtersService.dashboardRequestFilters$;
    this.dashboardFilters$ = this.filtersService.dashboardFilters$;
    this.topPerformers$ = this.filtersService.getTopPerformers$;

    this.drawSalesOpportuniesChart();

    this.salesOpportunitiesData$ = this.requestFilters$.pipe(
      tap(() => this.loading$$.next(true)),
      switchMap((rfilters) => this.salesOpportunitiesService.getSalespersonOpportunitiesStats(rfilters)),
    );

    this.filteredSalesOpportunitiesChartData$ = combineLatest([
      this.salesOpportunitiesData$,
      this.dashboardFilters$,
    ]).pipe(
      tap(() => this.loading$$.next(true)),
      map(([d, f]) => {
        return this.filterData(d, f);
      }),
    );

    this.subscriptions.push(
      combineLatest([this.filteredSalesOpportunitiesChartData$, this.topPerformers$])
        .pipe(
          map(([sortedData, topPerformer]) => {
            return this.getPerformers(sortedData, topPerformer);
          }),
        )
        .subscribe((stats) => {
          this.isEmptyState = this.isEmpty(stats);
          this.updateSalesOpportunitiesChartData(stats);
          this.loading$$.next(false);
        }),
    );
  }

  drawSalesOpportuniesChart(): void {
    const salesOpportunitiesChartConfig: ChartConfiguration = SALES_OPPORTUNITIES_CHART_CONFIG;
    this.salesOpportunitiesChartContext = (
      this.salesOpportunitiesContainer.nativeElement as HTMLCanvasElement
    ).getContext('2d');
    this.salesOpportunitiesChart = new Chart(this.salesOpportunitiesChartContext, {
      type: salesOpportunitiesChartConfig.type,
      options: salesOpportunitiesChartConfig.options,
      data: salesOpportunitiesChartConfig.data,
    });
  }

  updateSalesOpportunitiesChartData(newData: SalesOpportunitiesChartData): void {
    const data = {
      labels: [],
      datasets: [],
    };

    const open = [];
    const won = [];
    const lost = [];

    newData.salespeople.forEach((s) => {
      data.labels.push(s.name);
      open.push(s.opportunities.open);
      won.push(s.opportunities.closedwon);
      lost.push(s.opportunities.closedlost);
    });

    data.datasets.push(
      {
        label: this.translate.instant('DASHBOARD.SALES_OPPORTUNITIES.OPEN'),
        data: open,
        backgroundColor: COLORS.open,
        barPercentage: 1,
      },
      {
        label: this.translate.instant('COMMON.WON'),
        data: won,
        backgroundColor: COLORS.closedwon,
        barPercentage: 1,
      },
      {
        label: this.translate.instant('COMMON.LOST'),
        data: lost,
        backgroundColor: COLORS.closedlost,
        barPercentage: 1,
      },
    );
    this.salesOpportunitiesChart.data = data;
    this.salesOpportunitiesChart.update();
  }

  filterData(newData: SalesOpportunitiesChartData, filters: DashboardFilterData): SalesOpportunitiesChartData {
    const salespeopleFilter = filters.salespeopleFilter;
    const salesteamFilter = filters.salesteamFilter;
    const filteredData: SalesOpportunitiesChartData = { salespeople: [] };
    let allSalespeopleFiltered: string[] = [];

    if (salespeopleFilter.length > 0 || salesteamFilter.length > 0) {
      allSalespeopleFiltered = combineSalespeopleSalesteamFilters(salespeopleFilter, salesteamFilter);

      allSalespeopleFiltered.forEach((s) => {
        const salesperson = newData.salespeople.find((fs) => s === fs.id);
        if (salesperson) {
          filteredData.salespeople.push(salesperson);
        }
      });
      return this.salesOpportunitiesService.sortSalespersonOpportunitiesStats(filteredData.salespeople);
    } else {
      return newData;
    }
  }

  getPerformers(data: SalesOpportunitiesChartData, getTopPerformer: boolean): SalesOpportunitiesChartData {
    let performers: SalespersonOpportunities[];
    if (getTopPerformer) {
      performers = data.salespeople.slice(0, DEFAULT_VISIBLE_STATS);
    } else {
      performers = data.salespeople.slice(-DEFAULT_VISIBLE_STATS).reverse();
    }
    return <SalesOpportunitiesChartData>{
      salespeople: performers,
    };
  }

  viewFurtherInsights(): void {
    this.router.navigate([PIPELINE_TABLE]);
  }

  isEmpty(salesActivityData: SalesOpportunitiesChartData): boolean {
    if (salesActivityData.salespeople[0]?.opportunities?.total === 0) {
      return true;
    }
    return false;
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
  }
}
