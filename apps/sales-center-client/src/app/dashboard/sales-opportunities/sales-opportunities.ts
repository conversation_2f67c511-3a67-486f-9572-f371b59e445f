import { Salesperson } from '@galaxy/types';
import { MetricResult } from '@vendasta/multi-location-analytics';
import { ChartCard } from '../chart-card/chart-card';
import { EmptyStateCard } from '../chart-card/empty-state-card/empty-state-card';

export interface SalesOpportunitiesChartData {
  salespeople: SalespersonOpportunities[];
}

export interface SalespersonOpportunities {
  name?: string;
  id?: string;
  opportunities?: SalesOpportunities;
}

export interface SalesOpportunities {
  closedwon: number | string;
  closedlost: number | string;
  open: number | string;
  total: number | string;
}

export interface MeasureRevenueValues {
  closedWonValue: number;
  closedLostValue: number;
  openOpportunityValue: number;
}

export const SALES_OPPORTUNITY_CARD: ChartCard = {
  header: {
    title: { key: 'DASHBOARD.SALES_OPPORTUNITIES.CHART_TITLE' },
  },
  footer: {
    cta: { key: 'DASHBOARD.COMMON.FURTHER_INSIGHTS' },
  },
};

export const SALES_OPPORTUNITY_EMPTY_STATE_CARD: EmptyStateCard = {
  cardText: {
    header: {
      title: { key: 'DASHBOARD.SALES_OPPORTUNITIES.CHART_TITLE' },
    },
  },
  config: {
    title: { key: 'DASHBOARD.SALES_OPPORTUNITIES.EMPTY_STATE.TITLE' },
    description: { key: 'DASHBOARD.SALES_OPPORTUNITIES.EMPTY_STATE.DESCRIPTION' },
    svgIcon: 'pipeline',
  },
};

export function createEmptyDashboardSalespersonOpportunities(sp: Salesperson): SalespersonOpportunities {
  return {
    name: sp.fullName,
    id: sp.id,
    opportunities: {
      closedwon: 0,
      closedlost: 0,
      open: 0,
      total: 0,
    },
  };
}

export function createDashboardSalespersonOpportunitiesStat(
  sp: Salesperson,
  m: MetricResult,
): SalespersonOpportunities {
  const measures = convertSalesOpportunitiesMetricsToMeasureRevenueValues(m);

  return {
    name: sp.fullName,
    id: sp.id,
    opportunities: {
      closedwon: measures.closedWonValue,
      closedlost: measures.closedLostValue,
      open: measures.openOpportunityValue,
      total: calculateTotal(measures),
    },
  };
}

function convertSalesOpportunitiesMetricsToMeasureRevenueValues(m: MetricResult): MeasureRevenueValues {
  // need to divide by 100 to get value in dollars
  const closedWonValue = m.measures[0] !== null ? parseInt(m.measures[0], 10) / 100 : 0;
  const closedLostValue = m.measures[1] !== null ? parseInt(m.measures[1], 10) / 100 : 0;
  const openOpportunityValue = m.measures[2] !== null ? parseInt(m.measures[2], 10) / 100 : 0;

  return <MeasureRevenueValues>{
    closedWonValue: Math.max(closedWonValue, 0),
    closedLostValue: Math.max(closedLostValue, 0),
    openOpportunityValue: Math.max(openOpportunityValue, 0),
  };
}

function calculateTotal(measures: MeasureRevenueValues): number {
  return measures.closedWonValue + measures.closedLostValue + measures.openOpportunityValue;
}
