import { of as observableOf } from 'rxjs';
import { publishReplay, refCount } from 'rxjs/operators';
import { FiltersService } from './filters.service';
import { DateRangeFilter } from '@vendasta/uikit';
import { PartnerMarket } from '../../logged-in-user-info/logged-in-user-info.service';

const mockPartnerMarket = observableOf(<PartnerMarket>{
  partnerId: 'ABC',
  marketId: 'whatever',
});

describe('FiltersService', () => {
  let dateRangeServiceMock: any;
  let service: FiltersService;

  beforeEach(() => {
    dateRangeServiceMock = {
      getFilter: jest.fn(),
      setKey: jest.fn(),
    };

    dateRangeServiceMock.getFilter = jest.fn().mockReturnValue(
      observableOf({
        start: new Date(),
        end: new Date(),
      } as DateRangeFilter).pipe(publishReplay(1), refCount()),
    );

    service = new FiltersService(dateRangeServiceMock, mockPartnerMarket);
  });

  describe('resetDateRange()', () => {
    it('should trigger a reset on the date range service instance', () => {
      service.resetDateRange();
      expect(dateRangeServiceMock.setKey).toHaveBeenCalled();
    });
  });

  describe('getDateRangeFilter()', () => {
    it('calls the getFilter method on the date range service', () => {
      service.getDateRangeFilter();
      expect(dateRangeServiceMock.getFilter).toHaveBeenCalled();
    });
  });

  describe('getFilters()', () => {
    it('should return an object consisting of all filter service filters', (done) => {
      service.dashboardRequestFilters$.subscribe((filters) => {
        const { dateFilter, partnerMarket } = filters;
        expect(dateFilter).toBeDefined();
        expect(dateFilter.start).toBeDefined();
        expect(dateFilter.end).toBeDefined();
        expect(partnerMarket.partnerId).toBe('ABC');
        expect(partnerMarket.marketId).toBe('whatever');
        done();
      });
    });
  });
});
