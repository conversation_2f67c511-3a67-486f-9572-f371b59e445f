export const DEFAULT_VISIBLE_STATS = 5;

export function combineSalespeopleSalesteamFilters(salespeopleFilter: string[], salesteamFilter: string[]): string[] {
  let combinedFiltersWithoutDuplicates: string[] = [];

  if (salespeopleFilter.length > 0 && salesteamFilter.length > 0) {
    const combinedFilters = salesteamFilter.concat(salespeopleFilter);
    combinedFiltersWithoutDuplicates = combinedFilters.filter((s, index) => combinedFilters.indexOf(s) === index);
    return combinedFiltersWithoutDuplicates;
  } else if (salespeopleFilter.length > 0) {
    return salespeopleFilter;
  } else {
    return salesteamFilter;
  }
}
