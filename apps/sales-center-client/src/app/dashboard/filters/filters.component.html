<va-filter2 class="dashboard-va-filter">
  <va-filter-toolbar-actions>
    <sales-ui-filter-chip-container>
      <va-filter-chip-container
        [filters]="filters"
        (onFilterDisable)="onFilterDisable($event)"
        [config]="config"
      ></va-filter-chip-container>
      <sales-ui-filter-chip
        *ngIf="selectedSalespeopleNames$ | async as names"
        [canRemove]="true"
        (clicked)="openSalespeopleSelector()"
        (delete)="clearSalespeopleFilters()"
      >
        <filter-chip-title>{{ 'COMMON.SALESPEOPLE_LABEL' | translate }}:</filter-chip-title>
        <filter-chip-value class="chip-value">{{ names }}</filter-chip-value>
      </sales-ui-filter-chip>
      <sales-ui-filter-chip
        *ngIf="selectedSalesTeamNames$ | async as names"
        [canRemove]="true"
        (clicked)="openSalesTeamSelector()"
        (delete)="clearSalesTeamFilters()"
      >
        <filter-chip-title>{{ 'COMMON.SALES_TEAM_LABEL' | translate }}:</filter-chip-title>
        <filter-chip-value class="chip-value">{{ names }}</filter-chip-value>
      </sales-ui-filter-chip>
      <filter-menu-items>
        <button (click)="openSalespeopleSelector()" mat-menu-item>
          {{ 'COMMON.SALESPEOPLE_LABEL' | translate }}...
        </button>
        <button (click)="openSalesTeamSelector()" mat-menu-item>{{ 'COMMON.SALES_TEAM_LABEL' | translate }}...</button>
      </filter-menu-items>
    </sales-ui-filter-chip-container>
  </va-filter-toolbar-actions>

  <va-filter-content>
    <ng-content></ng-content>
  </va-filter-content>
</va-filter2>
