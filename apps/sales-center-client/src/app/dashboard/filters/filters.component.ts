import { Component, OnDestroy, OnInit } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { MatDialog } from '@angular/material/dialog';
import { SubscriptionList } from '@vendasta/rx-utils';
import { DateRangeSelectorComponent, I18NDescriptor } from '@vendasta/uikit';
import {
  SalespersonMultiSelectComponent,
  SalespersonMultiSelectStoreService,
  SalespersonOption,
  SalesTeamMultiSelectComponent,
  SalesTeamMultiSelectStoreService,
  SalesTeamOption,
} from '@vendasta/sales-ui';
import { SalesTeamsService } from '../../sales-teams';
import { FiltersService } from './filters.service';

const NO_SALESPEOPLE_IN_TEAM_PLACEHOLDER = 'NO-ID';
const DATE_RANGE_FILTER_ID = 'dashboard-filters-date-range';

@Component({
  selector: 'app-st-filters',
  templateUrl: './filters.component.html',
  styleUrls: ['./filters.component.scss'],
  standalone: false,
})
export class FiltersComponent implements OnInit, OnDestroy {
  selectedSalespeopleNames$: Observable<string>;
  selectedSalesTeamNames$: Observable<string>;
  private readonly subscriptions = SubscriptionList.new();
  private readonly _destroyed$$: Subject<void> = new Subject<void>();

  dateFilterValue: I18NDescriptor = {
    key: '',
  };

  filters = [
    {
      title: {
        key: 'COMMON.FILTER.DATE.CHIP',
      },
      text: this.dateFilterValue,
      enabled: true,
      id: DATE_RANGE_FILTER_ID,
      dialog: {
        component: DateRangeSelectorComponent,
      },
    },
  ];

  config = [
    {
      canAddAndRemove: false,
    },
  ];

  constructor(
    private readonly dialog: MatDialog,
    private readonly spStore: SalespersonMultiSelectStoreService,
    private readonly stStore: SalesTeamMultiSelectStoreService,
    private readonly salesTeamsService: SalesTeamsService,
    private readonly filtersService: FiltersService,
  ) {
    this.subscriptions.add(this.spStore.selectedSalespeople$.pipe(map((sp) => (sp || []).map((s) => s.id))), (ids) =>
      this.setSelectedSalespeople(ids),
    );

    this.subscriptions.add(this.stStore.selectedSalesTeams$.pipe(map((st) => (st || []).map((s) => s.id))), (ids) => {
      this.salesTeamsService.salesTeamIdsToArrayOfSalesPeopleIds(ids);
    });
    this.subscriptions.add(this.salesTeamsService.arrayOfSalesTeamMembers$, (arrayOfSalesPersonIds) => {
      return this.filterBySalesTeamMemberIds(arrayOfSalesPersonIds);
    });
    this.selectedSalespeopleNames$ = this.spStore.selectedSalespeople$.pipe(
      map((sp: SalespersonOption[]): string => sp.map((s) => s.fullName).join(', ')),
    );
    this.selectedSalesTeamNames$ = this.stStore.selectedSalesTeams$.pipe(
      map((sp: SalesTeamOption[]): string => sp.map((s) => s.teamName).join(', ')),
    );
  }

  ngOnInit(): void {
    this.filtersService
      .getDateRangeFilter()
      .pipe(takeUntil(this._destroyed$$))
      .subscribe((filter) => {
        if (!filter) {
          return;
        }

        this.dateFilterValue.key = filter.i18nKey;
      });
  }

  private filterBySalesTeamMemberIds(arrayOfSalesPersonIds): void {
    const spIds = [];
    this.convert2DSalesTeamMembersToSalespeople(arrayOfSalesPersonIds, spIds);
    if (arrayOfSalesPersonIds && arrayOfSalesPersonIds.length > 0 && spIds.length === 0) {
      this.setSelectedSalesTeamMembers([NO_SALESPEOPLE_IN_TEAM_PLACEHOLDER]);
    } else {
      this.setSelectedSalesTeamMembers(spIds);
    }
  }

  private setSelectedSalespeople(salespeopleIds: string[]): void {
    this.filtersService.setSalespeopleIds(salespeopleIds);
  }

  private setSelectedSalesTeamMembers(salesTeamMemberIds: string[]): void {
    this.filtersService.setSalesteamIds(salesTeamMemberIds);
  }

  openSalespeopleSelector(): void {
    this.dialog.open(SalespersonMultiSelectComponent, { width: '400px' });
  }

  openSalesTeamSelector(): void {
    this.dialog.open(SalesTeamMultiSelectComponent, { width: '400px' });
  }

  clearSalespeopleFilters(): void {
    this.spStore.setSelectedSalespeople([]);
  }

  clearSalesTeamFilters(): void {
    this.stStore.setSelectedSalesTeams([]);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onFilterDisable(event: any): void {
    this.filtersService.resetDateRange();
  }

  private convert2DSalesTeamMembersToSalespeople(salesTeamMembers: string[][], salespeople: string[]): void {
    for (const idx in salesTeamMembers) {
      if (salesTeamMembers[idx]) {
        salesTeamMembers[idx].map((newId) => {
          if (!salespeople.includes(newId)) {
            salespeople.push(newId);
          }
        });
      }
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
    this._destroyed$$.next();
    this._destroyed$$.complete();
  }
}
