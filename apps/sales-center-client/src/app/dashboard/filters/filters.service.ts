import { Inject, Injectable } from '@angular/core';
import { DateFilterOptions, DateRangeFilter, DateRangeSelectorService } from '@vendasta/uikit';
import { Observable, combineLatest, BehaviorSubject } from 'rxjs';
import { distinctUntilChanged, shareReplay, map } from 'rxjs/operators';
import { PartnerMarket } from '../../logged-in-user-info/logged-in-user-info.service';
import { USER_PARTNER_MARKET_TOKEN } from '../../core/feature-flag.service';

export interface DashboardRequestFilterData {
  dateFilter?: DateRangeFilter;
  partnerMarket?: PartnerMarket;
}

export interface DashboardFilterData {
  salespeopleFilter?: string[];
  salesteamFilter?: string[];
}

@Injectable({
  providedIn: 'root',
})
export class FiltersService {
  private readonly salespeopleIds$$: BehaviorSubject<string[]> = new BehaviorSubject<string[]>([]);
  private readonly salesTeamMembersIds$$: BehaviorSubject<string[]> = new BehaviorSubject<string[]>([]);
  readonly dashboardRequestFilters$: Observable<DashboardRequestFilterData>;
  readonly dashboardFilters$: Observable<DashboardFilterData>;
  readonly getTopPerformers$$ = new BehaviorSubject<boolean>(true);
  readonly salespeopleIds$ = this.salespeopleIds$$.asObservable();
  readonly salesTeamMembersIds$ = this.salesTeamMembersIds$$.asObservable();
  readonly getTopPerformers$ = this.getTopPerformers$$.asObservable();

  constructor(
    private readonly dateRangeService: DateRangeSelectorService,
    @Inject(USER_PARTNER_MARKET_TOKEN) private readonly partnerMarket$: Observable<PartnerMarket>,
  ) {
    this.resetDateRange();
    this.dashboardRequestFilters$ = combineLatest([this.getDateRangeFilter(), this.partnerMarket$]).pipe(
      map(([dateFilter, partnerMarket]) => {
        const { start, end } = (dateFilter || {}) as DateRangeFilter;
        return {
          dateFilter: {
            start,
            end,
          } as DateRangeFilter,
          partnerMarket: partnerMarket,
        } as DashboardRequestFilterData;
      }),
      distinctUntilChanged(),
      shareReplay(1),
    );

    this.dashboardFilters$ = combineLatest([this.salespeopleIds$, this.salesTeamMembersIds$]).pipe(
      map(([salespeopleIds, salesTeamMembersIds]) => {
        return {
          salespeopleFilter: salespeopleIds,
          salesteamFilter: salesTeamMembersIds,
        } as DashboardFilterData;
      }),
      distinctUntilChanged(),
      shareReplay(1),
    );
  }

  /**
   * Trigger the DateRangeService to reset.
   */
  resetDateRange(): void {
    this.dateRangeService.setKey(DateFilterOptions.ThirtyDay);
  }

  /**
   * Get the filter being created by the DateRangeService.
   */
  getDateRangeFilter(): Observable<DateRangeFilter> {
    return this.dateRangeService.getFilter();
  }

  /**
   * Set the filter for salespeople.
   */
  setSalespeopleIds(salespeopleIds: string[]): void {
    this.salespeopleIds$$.next(salespeopleIds);
  }

  /**
   * Set the filter for showing top or bottom performers.
   */
  setTopPerformers(getTopPerformers: boolean): void {
    this.getTopPerformers$$.next(getTopPerformers);
  }

  /**
   * Set the filter for salesteam.
   */
  setSalesteamIds(salespeopleIds: string[]): void {
    this.salesTeamMembersIds$$.next(salespeopleIds);
  }
}
