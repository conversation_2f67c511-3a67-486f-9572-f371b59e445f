import { Component, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { MeetingSchedulerStoreService } from '@galaxy/meeting-scheduler';
import { SubscriptionList } from '@vendasta/rx-utils';
import moment from 'moment-timezone';
import { BehaviorSubject, Observable } from 'rxjs';
import { distinctUntilChanged, map } from 'rxjs/operators';
import { Action, ChartCard } from '../chart-card/chart-card';
import { EmptyStateCard } from '../chart-card/empty-state-card/empty-state-card';
import {
  Guest,
  JoinMeeting,
  SCHEDULE_MEETING_LINK,
  UPCOMING_MEETINGS_CARD,
  UpcomingMeeting,
  UpcomingMeetingsTableData,
  createDashboardSalespersonUpcomingMeetingsTableData,
} from './upcoming-meetings';

export interface UpcomingMeetingsTable {
  meetingId: string;
  date: {
    start: Date;
    end: Date;
  };
  isToday: boolean;
  joinMeeting: JoinMeeting;
  guests: Guest[];
  comment: string;
}

export interface GroupBy {
  date: Date;
  isToday: boolean;
  isGroupBy: boolean;
}

@Component({
  selector: 'app-upcoming-meetings',
  templateUrl: './upcoming-meetings.component.html',
  styleUrls: ['./upcoming-meetings.component.scss'],
  standalone: false,
})
export class UpcomingMeetingsComponent implements OnInit, OnDestroy {
  displayedColumns: Array<keyof UpcomingMeeting | 'actions'> = ['date', 'joinMeeting', 'guests', 'comment', 'actions'];
  meetings$: Observable<UpcomingMeeting[]>;
  dataSource: (UpcomingMeetingsTable | GroupBy)[];

  upcomingMeetingsChartCard: ChartCard = UPCOMING_MEETINGS_CARD;
  upcomingMeetingsAction: Action = SCHEDULE_MEETING_LINK;
  upcomingMeetingsEmptyState: EmptyStateCard;
  upcomingMeetingsNotConfiguredState: EmptyStateCard;
  isSchedulerConfigured$: Observable<boolean>;
  isEmptyState = false;

  private readonly loading$$ = new BehaviorSubject<boolean>(true);
  readonly loading$ = this.loading$$.asObservable();

  private readonly subscriptions = SubscriptionList.new();

  constructor(
    private readonly meetingSchedulerStoreService: MeetingSchedulerStoreService,
    private readonly router: Router,
  ) {
    this.configureEmptyStates();
    this.isSchedulerConfigured$ = this.meetingSchedulerStoreService.loadHasHostEverBeenConfigured();
  }

  ngOnInit(): void {
    const now = new Date();
    const tenYearsLater = new Date(now);
    tenYearsLater.setMonth(now.getMonth() + 120);
    this.meetings$ = this.meetingSchedulerStoreService
      .loadPersonalMeetings({
        pageSize: 3,
        timeZone: { id: moment.tz.guess() },
        filters: {
          timeSpan: {
            start: now,
            end: tenYearsLater,
          },
        },
      })
      .pipe(
        map((meetings) => createDashboardSalespersonUpcomingMeetingsTableData(meetings)),
        map((meetings) => {
          this.isEmptyState = this.isEmpty(meetings);
          return meetings.meetings.slice(0, 3).map((m) => {
            return {
              meetingId: m.meetingId,
              date: m.date,
              dateAsIndex: m.dateAsIndex,
              isToday: m.isToday,
              joinMeeting: m.joinMeeting,
              guests: m.guests,
              comment: m.comment,
            };
          });
        }),
        distinctUntilChanged(),
      );

    this.subscriptions.add(this.meetings$, (ms) => {
      this.transformMapToObservable(this.groupBy(<UpcomingMeeting[]>ms, (m) => m.dateAsIndex));
      this.loading$$.next(false);
    });
  }

  groupBy(upcomingMeetings: UpcomingMeeting[], keyGetter: any): Map<string, UpcomingMeeting[]> {
    const mp: Map<string, UpcomingMeeting[]> = new Map();
    upcomingMeetings.forEach((meetings) => {
      const key = keyGetter(meetings);
      const collection = mp.get(key);
      if (!collection) {
        mp.set(key, [meetings]);
      } else {
        collection.push(meetings);
      }
    });
    return mp;
  }

  transformMapToObservable(mp: Map<string, UpcomingMeeting[]>): void {
    // clear dataSource
    this.dataSource = [];

    for (const [, meetings] of mp) {
      // add groupBy header
      this.dataSource.push({ date: meetings[0].date.start, isToday: meetings[0].isToday, isGroupBy: true });
      for (const meeting of meetings) {
        this.dataSource.push({
          meetingId: meeting.meetingId,
          date: meeting.date,
          isToday: meeting.isToday,
          joinMeeting: meeting.joinMeeting,
          guests: meeting.guests,
          comment: meeting.comment,
        });
      }
    }
  }

  isGroup(index, meetings): boolean {
    return meetings.isGroupBy;
  }

  viewAllMeetings(): void {
    this.router.navigate(['events-and-meetings/schedule']);
  }

  viewMeetingsSettings(): void {
    this.router.navigate(['/events-and-meetings/settings']);
  }

  isEmpty(upcomingMeetingsData: UpcomingMeetingsTableData): boolean {
    if (upcomingMeetingsData.meetings.length === 0) {
      return true;
    }
    return false;
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }

  private configureEmptyStates(): void {
    this.upcomingMeetingsNotConfiguredState = {
      cardText: {
        header: {
          title: { key: 'MEETING_LIST.TITLE' },
        },
      },
      config: {
        title: { key: 'DASHBOARD.UPCOMING_MEETINGS.NOT_CONFIGURED_STATE.TITLE' },
        description: { key: 'DASHBOARD.UPCOMING_MEETINGS.NOT_CONFIGURED_STATE.DESCRIPTION' },
        icon: 'event_busy',
        cta: {
          text: 'Create booking link',
          actionFunction: this.viewMeetingsSettings.bind(this),
        },
      },
    };
    this.upcomingMeetingsEmptyState = {
      cardText: {
        header: {
          title: { key: 'MEETING_LIST.TITLE' },
        },
      },
      config: {
        title: { key: 'DASHBOARD.UPCOMING_MEETINGS.EMPTY_STATE_TITLE' },
        icon: 'calendar_today',
      },
    };
  }
}
