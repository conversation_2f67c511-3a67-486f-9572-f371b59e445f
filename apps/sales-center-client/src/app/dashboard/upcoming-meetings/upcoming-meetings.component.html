<ng-container *ngIf="isSchedulerConfigured$ | async; else schedulernotconfigured">
  <div *ngIf="!isEmptyState; else nomeetings">
    <app-chart-card
      class="meeting-card"
      [cardText]="upcomingMeetingsChartCard"
      [action]="upcomingMeetingsAction"
      (footerEventEmitter)="viewAllMeetings()"
      icon="event"
    >
      <meeting-scheduler-copy-personal-booking-link-dropdown
        chart-card-actions
      ></meeting-scheduler-copy-personal-booking-link-dropdown>

      <div [style]="{ display: 'block' }">
        <table mat-table class="meeting-table" [dataSource]="dataSource">
          <ng-container matColumnDef="date">
            <th mat-header-cell *matHeaderCellDef></th>
            <td mat-cell *matCellDef="let element">
              <p class="bold">
                {{ element.date.start | date : 'h:mm aa' | lowercase }} -
                {{ element.date.end | date : 'h:mm aa' | lowercase }}
              </p>
            </td>
          </ng-container>

          <ng-container matColumnDef="joinMeeting">
            <th mat-header-cell *matHeaderCellDef></th>
            <td mat-cell *matCellDef="let element">
              <div *ngIf="element.joinMeeting" class="join-meeting-container">
                <mat-icon [svgIcon]="element?.joinMeeting?.icon"></mat-icon>
                <p>
                  <a class="join-meeting-link" target="_blank" [attr.href]="element?.joinMeeting?.url">
                    {{ 'COMMON.ACTION_LABELS.JOIN' | translate }}
                  </a>
                </p>
              </div>
            </td>
          </ng-container>

          <ng-container matColumnDef="guests">
            <th mat-header-cell *matHeaderCellDef></th>
            <td mat-cell *matCellDef="let element">
              <span *ngFor="let guest of element.guests">
                <p>{{ guest?.firstName }} {{ guest?.lastName }}</p>
              </span>
            </td>
          </ng-container>

          <ng-container matColumnDef="comment">
            <th mat-header-cell *matHeaderCellDef></th>
            <td mat-cell *matCellDef="let element" class="truncate-cell hide-comment">
              {{ element?.comment }}
            </td>
          </ng-container>

          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef></th>
            <td mat-cell *matCellDef="let element">
              <mat-menu #meetingActionMenu="matMenu">
                <meeting-scheduler-reschedule-meeting-button
                  [meeting]="element"
                ></meeting-scheduler-reschedule-meeting-button>
                <meeting-scheduler-cancel-meeting-button [meeting]="element"></meeting-scheduler-cancel-meeting-button>
              </mat-menu>
              <button mat-icon-button color="primary" [matMenuTriggerFor]="meetingActionMenu">
                <mat-icon>more_vert</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>

          <!-- Group header -->
          <ng-container matColumnDef="groupHeader">
            <td class="header" colspan="999" mat-cell *matCellDef="let element">
              <p>
                <span *ngIf="element.isToday" class="today">
                  {{ 'COMMON.DATES.TODAY' | translate }}
                </span>
                {{ element.date | date : 'EEEE, MMM d, y' }}
              </p>
            </td>
          </ng-container>

          <tr mat-row *matRowDef="let row; columns: ['groupHeader']; when: isGroup"></tr>
        </table>
      </div>

      <div *ngIf="loading$ | async" [style]="{ display: 'block' }" class="stencil-shimmer shimmer"></div>
    </app-chart-card>
  </div>
</ng-container>

<ng-template #schedulernotconfigured>
  <app-empty-state-card [emptyState]="upcomingMeetingsNotConfiguredState"></app-empty-state-card>
</ng-template>

<ng-template #nomeetings>
  <app-empty-state-card [emptyState]="upcomingMeetingsEmptyState">
    <meeting-scheduler-copy-personal-booking-link-dropdown
      empty-state-card-dynamic-content
    ></meeting-scheduler-copy-personal-booking-link-dropdown>
  </app-empty-state-card>
</ng-template>
