@use 'design-tokens' as *;

table {
  width: 100%;
  height: 100%;
  transition: none;
  box-shadow: none;
}

.meeting-card ::ng-deep va-card-content {
  padding: 0px;
}

.bold {
  font-weight: bold;
}

.header {
  background-color: $lighter-gray;
}

.meeting-table {
  margin: 0px;
}

tr.mat-mdc-header-row {
  height: 0px;
}

.truncate-cell {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.join-meeting-container {
  display: flex;
  .join-meeting-link {
    color: $blue;
    cursor: pointer;
    font-weight: 400;
    font-size: 14px;
    padding: 8px;
  }
  mat-icon {
    align-self: center;
  }
}

.mat-column-date {
  flex: 0 0 30%;
  width: 30%;
}

.mat-column-joinMeeting {
  flex: 0 0 15%;
  width: 15%;
}

.mat-column-guests {
  flex: 0 0 20%;
  width: 20%;
}

.mat-column-comment {
  flex: 0 0 35%;
  width: 35%;
}

.today {
  font-weight: bold;
  font-size: 14px;
}

.shimmer {
  margin: 5px;
  width: 100%;
  height: 100px;
}

td.mat-mdc-cell:first-of-type {
  padding-left: 16px;
}

@media screen and (max-width: $media--tablet-minimum) {
  .hide-comment {
    display: none;
  }

  .mat-column-date {
    flex: 0 0 40%;
    width: 40%;
  }

  .mat-column-joinMeeting {
    flex: 0 0 20%;
    width: 20%;
  }

  .mat-column-guests {
    flex: 0 0 30%;
    width: 30%;
  }

  th.mat-mdc-header-cell:last-of-type,
  td.mat-mdc-cell:last-of-type,
  td.mat-mdc-footer-cell:last-of-type {
    padding-right: 0;
  }
}

@media screen and (max-width: 420px) {
  .hide-comment {
    display: none;
  }

  .mat-column-date {
    flex: 0 0 50%;
    width: 50%;
  }

  .mat-column-joinMeeting {
    flex: 0 0 20%;
    width: 20%;
  }

  .mat-column-guests {
    flex: 0 0 30%;
    width: 30%;
  }

  th.mat-mdc-header-cell:last-of-type,
  td.mat-mdc-cell:last-of-type,
  td.mat-mdc-footer-cell:last-of-type {
    padding-right: 0;
  }
}
