import { ChartCard, Action } from '../chart-card/chart-card';
import { Meeting } from '@vendasta/meetings';

export interface UpcomingMeetingsTableData {
  meetings: UpcomingMeeting[];
}

export interface UpcomingMeeting {
  meetingId: string;
  date: {
    start: Date;
    end: Date;
  };
  joinMeeting: JoinMeeting;
  dateAsIndex: string; // use to groupBy
  isToday: boolean;
  guests: Guest[];
  comment: string;
}

export interface Guest {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
}

export interface JoinMeeting {
  url: string;
  icon: string;
}

export function createDashboardSalespersonUpcomingMeetingsTableData(meetings: Meeting[]): UpcomingMeetingsTableData {
  const salespersonUpcomingMeetings: UpcomingMeetingsTableData = { meetings: [] };

  meetings.forEach((m) => {
    const meeting: UpcomingMeeting = {
      meetingId: m.id,
      date: {
        start: m.start,
        end: m.end,
      },
      joinMeeting: createJoinMeeting(m.joinMeetingUrl),
      dateAsIndex: calculateYearMonthDay(m.start),
      isToday: hasMeetingToday(m.start),
      guests: m.attendees,
      comment: m.description,
    };
    salespersonUpcomingMeetings.meetings.push(meeting);
  });

  return salespersonUpcomingMeetings;
}

function createJoinMeeting(meetingUrl: string): JoinMeeting {
  const url = meetingUrl.replace('https', 'http');
  if (url.includes('google')) {
    return { url: url, icon: 'meet-icon' };
  } else if (url.includes('zoom')) {
    return { url: url, icon: 'zoom-icon' };
  }
}

function calculateYearMonthDay(date: Date): string {
  return date.getFullYear().toString() + date.getMonth().toString() + date.getDate().toString();
}

function hasMeetingToday(date: Date): boolean {
  const today = new Date();
  if (
    date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear()
  ) {
    return true;
  }
  return false;
}

export const UPCOMING_MEETINGS_CARD: ChartCard = {
  header: {
    title: { key: 'MEETING_LIST.TITLE' },
  },
  footer: {
    cta: { key: 'COMMON.ACTION_LABELS.VIEW_ALL' },
  },
};

export const SCHEDULE_MEETING_LINK: Action = {
  text: { key: 'COMMON.ACTION_LABELS.COPY' },
  icon: 'file_copy',
  extraText: { key: 'COMMON.ACTION_LABELS.BOOKING_LINK' },
  extraIcon: 'open_in_new',
};
