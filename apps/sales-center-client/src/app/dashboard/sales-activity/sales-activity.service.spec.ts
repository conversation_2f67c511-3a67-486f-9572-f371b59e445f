import { Salesperson } from '@galaxy/types';
import { MultiLocationAnalyticsService } from '@vendasta/multi-location-analytics';
import { of as observableOf } from 'rxjs';
import { TestScheduler } from 'rxjs/testing';
import { PartnerMarket } from '../../logged-in-user-info/logged-in-user-info.service';
import { DashboardRequestFilterData } from '../filters/filters.service';
import { allSalesPeopleResponse } from '../multi-location-analytics.mock';
import { SalesActivityChartData } from './sales-activity';
import { SalesActivityService } from './sales-activity.service';

let sched: TestScheduler;

class MockAnalyticsSdk implements Pick<MultiLocationAnalyticsService, 'queryMetrics'> {
  response: any;

  constructor(response: any) {
    this.response = response;
  }

  queryMetrics = jest.fn(() => {
    return sched.createColdObservable('-x', { x: this.response });
  });
}

const mockFilters: DashboardRequestFilterData = {
  dateFilter: {
    start: new Date(2020, 3, 12),
    end: new Date(2020, 4, 11),
  },
  partnerMarket: new PartnerMarket('ABC', 'whatever'),
};

const mockAllSalespeople = observableOf(<Salesperson[]>[
  {
    fullName: 'Dwight Schrute',
    id: 'UID-aae7c996-b4f9-405e-a63c-a13b2a766854',
  },
  {
    fullName: 'Jim Halpert',
    id: 'UID-aaaaaaaa',
  },
  {
    fullName: 'Andy Bernard',
    id: 'UID-828e5c4c-31b4-40fe-9a38-f0277eee998d',
  },
]);

describe('SalesActivityService', () => {
  let service: SalesActivityService;
  let mockAnalyticsSdk: MockAnalyticsSdk;

  beforeEach(() => {
    sched = new TestScheduler((a, b) => expect(a).toEqual(b));
  });
  afterEach(() => {
    sched.flush();
  });

  describe('getSalespersonActivityStats', () => {
    it('should return chart data based on multi location analytics response', () => {
      mockAnalyticsSdk = new MockAnalyticsSdk(allSalesPeopleResponse);
      service = new SalesActivityService(mockAnalyticsSdk as any as MultiLocationAnalyticsService, mockAllSalespeople);

      const expectedSalespersonStats = <SalesActivityChartData>{
        salespeople: [
          {
            name: 'Andy Bernard',
            id: 'UID-828e5c4c-31b4-40fe-9a38-f0277eee998d',
            activity: {
              emailsSent: 425,
              emailsReceived: 11,
              inboundCalls: 9,
              outboundCalls: 322,
              meetings: 2,
              snapshotsCreated: 0,
              total: 769,
            },
          },
          {
            name: 'Dwight Schrute',
            id: 'UID-aae7c996-b4f9-405e-a63c-a13b2a766854',
            activity: {
              emailsSent: 109,
              emailsReceived: 0,
              inboundCalls: 1,
              outboundCalls: 0,
              meetings: 9,
              snapshotsCreated: 0,
              total: 119,
            },
          },
          {
            name: 'Jim Halpert',
            id: 'UID-aaaaaaaa',
            activity: {
              emailsSent: 0,
              emailsReceived: 0,
              inboundCalls: 0,
              outboundCalls: 0,
              meetings: 0,
              snapshotsCreated: 0,
              total: 0,
            },
          },
        ],
      };

      const stats = service.getSalespersonActivityStats(mockFilters);
      sched.expectObservable(stats).toBe('-(x|)', { x: expectedSalespersonStats });
    });

    it('should create empty SalespersonActivityStat when multi location analytics response is empty', () => {
      mockAnalyticsSdk = new MockAnalyticsSdk({ metricResults: [] });
      service = new SalesActivityService(mockAnalyticsSdk as any as MultiLocationAnalyticsService, mockAllSalespeople);
      const expectedSalespersonStats = <SalesActivityChartData>{
        salespeople: [
          {
            name: 'Dwight Schrute',
            id: 'UID-aae7c996-b4f9-405e-a63c-a13b2a766854',
            activity: {
              emailsSent: 0,
              emailsReceived: 0,
              inboundCalls: 0,
              outboundCalls: 0,
              meetings: 0,
              snapshotsCreated: 0,
              total: 0,
            },
          },
          {
            name: 'Jim Halpert',
            id: 'UID-aaaaaaaa',
            activity: {
              emailsSent: 0,
              emailsReceived: 0,
              inboundCalls: 0,
              outboundCalls: 0,
              meetings: 0,
              snapshotsCreated: 0,
              total: 0,
            },
          },
          {
            name: 'Andy Bernard',
            id: 'UID-828e5c4c-31b4-40fe-9a38-f0277eee998d',
            activity: {
              emailsSent: 0,
              emailsReceived: 0,
              inboundCalls: 0,
              outboundCalls: 0,
              meetings: 0,
              snapshotsCreated: 0,
              total: 0,
            },
          },
        ],
      };

      const stats = service.getSalespersonActivityStats(mockFilters);
      sched.expectObservable(stats).toBe('-(x|)', { x: expectedSalespersonStats });
    });
  });
});
