import { Salesperson } from '@galaxy/types';
import { MetricResult } from '@vendasta/multi-location-analytics';
import { ChartCard } from '../chart-card/chart-card';
import { EmptyStateCard } from '../chart-card/empty-state-card/empty-state-card';

export interface SalesActivityChartData {
  salespeople: SalespersonActivity[];
}

export interface SalespersonActivity {
  name: string;
  id: string;
  activity: SalesActivity;
}

export interface SalesActivity {
  snapshotsCreated: number;
  outboundCalls: number;
  inboundCalls: number;
  emailsSent: number;
  emailsReceived: number;
  meetings: number;
  total: number;
}

export interface MeasureCount {
  snapshotsCreatedCount: number;
  outboundCallsCount: number;
  inboundCallsCount: number;
  emailsSentCount: number;
  emailsReceivedCount: number;
  meetingsCount: number;
}

export const SALES_ACTIVITY_CARD: ChartCard = {
  header: {
    title: { key: 'DASHBOARD.SALES_ACTIVITY.CHART_TITLE' },
  },
  footer: {
    cta: { key: 'DASHBOARD.COMMON.FURTHER_INSIGHTS' },
  },
};

export const SALES_ACTIVITY_EMPTY_STATE_CARD: EmptyStateCard = {
  cardText: {
    header: {
      title: { key: 'DASHBOARD.SALES_ACTIVITY.CHART_TITLE' },
    },
  },
  config: {
    title: { key: 'DASHBOARD.SALES_ACTIVITY.EMPTY_STATE.TITLE' },
    description: { key: 'DASHBOARD.SALES_ACTIVITY.EMPTY_STATE.DESCRIPTION' },
    icon: 'group',
  },
};

export function createEmptyDashboardSalespersonActivityStat(sp: Salesperson): SalespersonActivity {
  return {
    name: sp.fullName,
    id: sp.id,
    activity: {
      emailsSent: 0,
      emailsReceived: 0,
      inboundCalls: 0,
      outboundCalls: 0,
      meetings: 0,
      snapshotsCreated: 0,
      total: 0,
    },
  };
}

export function createDashboardSalespersonActivityStat(sp: Salesperson, m: MetricResult): SalespersonActivity {
  const measures: MeasureCount = {
    emailsSentCount: parseInt(m.measures[0], 10),
    emailsReceivedCount: parseInt(m.measures[1], 10),
    inboundCallsCount: parseInt(m.measures[2], 10),
    outboundCallsCount: parseInt(m.measures[3], 10),
    meetingsCount: parseInt(m.measures[4], 10),
    snapshotsCreatedCount: parseInt(m.measures[5], 10),
  };

  return {
    name: sp.fullName,
    id: sp.id,
    activity: {
      emailsSent: measures.emailsSentCount,
      emailsReceived: measures.emailsReceivedCount,
      inboundCalls: measures.inboundCallsCount,
      outboundCalls: measures.outboundCallsCount,
      meetings: measures.meetingsCount,
      snapshotsCreated: measures.snapshotsCreatedCount,
      total: calculateTotal(measures),
    },
  };
}

function calculateTotal(m: MeasureCount): number {
  return (
    m.emailsSentCount +
    m.emailsReceivedCount +
    m.inboundCallsCount +
    m.outboundCallsCount +
    m.meetingsCount +
    m.snapshotsCreatedCount
  );
}
