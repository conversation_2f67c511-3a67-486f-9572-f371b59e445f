import { Inject, Injectable } from '@angular/core';
import { Salesperson } from '@galaxy/types';
import { MetricResult, MultiLocationAnalyticsService } from '@vendasta/multi-location-analytics';
import { Observable, combineLatest } from 'rxjs';
import { map, shareReplay, take } from 'rxjs/operators';
import { ALL_SALESPEOPLE } from '../../data-providers/salespeople';
import { DashboardRequestFilterData } from '../filters/filters.service';
import { buildDashboardSalesActivityRequest } from '../multi-location-analytics-query-builder';
import { extractMetricsFromResponse } from '../multi-location-analytics-utils';
import {
  SalesActivityChartData,
  SalespersonActivity,
  createDashboardSalespersonActivityStat,
  createEmptyDashboardSalespersonActivityStat,
} from './sales-activity';

@Injectable()
export class SalesActivityService {
  constructor(
    private readonly analyticsSdk: MultiLocationAnalyticsService,
    @Inject(ALL_SALESPEOPLE) private readonly allSalespeople$: Observable<Salesperson[]>,
  ) {}

  getSalespersonActivityStats(filters: DashboardRequestFilterData): Observable<SalesActivityChartData> {
    const request = buildDashboardSalesActivityRequest(
      filters.partnerMarket.partnerId,
      filters.partnerMarket.marketId,
      filters.dateFilter.start,
      filters.dateFilter.end,
    );
    return combineLatest([this.analyticsSdk.queryMetrics(request), this.allSalespeople$]).pipe(
      take(1),
      map(([metrics, salespeople]) => {
        return {
          metrics: extractMetricsFromResponse(metrics),
          salespeople: salespeople,
        };
      }),
      map((m) => this.createSalesActivityChartData(m.metrics, m.salespeople)),
      shareReplay(1),
    );
  }

  private createSalesActivityChartData(metrics: MetricResult[], allSalespeople: Salesperson[]): SalesActivityChartData {
    const salespersonActivity = this.matchMetricsWithSalespeople(allSalespeople, metrics);
    return this.sortSalespersonActivityStats(salespersonActivity);
  }

  sortSalespersonActivityStats(data: SalespersonActivity[]): SalesActivityChartData {
    return <SalesActivityChartData>{
      salespeople: data.sort((s1, s2) => {
        return s2.activity.total - s1.activity.total;
      }),
    };
  }

  private matchMetricsWithSalespeople(salespeople: Salesperson[], metrics: MetricResult[]): SalespersonActivity[] {
    const salespersonActivity: SalespersonActivity[] = [];

    salespeople.forEach((s) => {
      const metric = metrics.find((m) => s.id === m.dimension);
      if (metric) {
        const salespersonData = createDashboardSalespersonActivityStat(s, metric);
        salespersonActivity.push(salespersonData);
      } else {
        const emptySalespersonData = createEmptyDashboardSalespersonActivityStat(s);
        salespersonActivity.push(emptySalespersonData);
      }
    });
    return salespersonActivity;
  }
}
