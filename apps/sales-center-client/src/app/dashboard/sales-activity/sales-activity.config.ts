import { ChartConfiguration } from 'chart.js';

export const COLORS = {
  emailsSent: '#4caf50',
  inboundCalls: '#ffc107',
  snapshotsCreated: '#1e88e5',
  outboundCalls: '#e53935',
  emailsReceived: '#ffa000',
  meetings: '#46BDC6',
};

export const SALES_ACTIVITY_CHART_CONFIG: ChartConfiguration = {
  type: 'bar',
  data: {
    datasets: [],
  },
  options: {
    indexAxis: 'y',
    elements: {
      bar: {
        borderWidth: 0,
      },
    },
    scales: {
      x: {
        stacked: true,
        min: 0,
        beginAtZero: true,
      },
      y: { stacked: true, grid: { display: false } },
    },
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        align: 'center',
        labels: {
          boxWidth: 10,
          font: { size: 10 },
        },
      },
    },
  },
};
