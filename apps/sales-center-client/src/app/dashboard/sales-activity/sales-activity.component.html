<app-chart-card
  [style]="{ display: isEmptyState ? 'none' : 'block' }"
  [cardText]="salesActivityChartCard"
  (footerEventEmitter)="viewFurtherInsights()"
  [canViewFurtherInsights]="accessLeaderboards$ | async"
>
  <div class="chart-container" [style]="{ display: (loading$ | async) ? 'none' : 'block' }">
    <canvas #salesActivity></canvas>
  </div>
  <div *ngIf="loading$ | async" [style]="{ display: 'block' }" class="stencil-shimmer shimmer"></div>
</app-chart-card>

<ng-container *ngIf="isEmptyState">
  <app-empty-state-card [emptyState]="salesActivityEmptyState"></app-empty-state-card>
</ng-container>
