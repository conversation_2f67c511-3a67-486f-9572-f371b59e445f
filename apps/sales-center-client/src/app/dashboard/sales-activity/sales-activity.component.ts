import { Component, ElementR<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Chart, ChartConfiguration } from 'chart.js';
import { BehaviorSubject, combineLatest, Observable, Subscription } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { SSCAccessService } from '../../access';
import { AppPage } from '../../access/page-access/app-page.enum';
import { ChartCard } from '../chart-card/chart-card';
import { EmptyStateCard } from '../chart-card/empty-state-card/empty-state-card';
import { combineSalespeopleSalesteamFilters, DEFAULT_VISIBLE_STATS } from '../filters/filters';
import { DashboardFilterData, DashboardRequestFilterData, FiltersService } from '../filters/filters.service';
import {
  SalesActivityChartData,
  SalespersonActivity,
  SALES_ACTIVITY_CARD,
  SALES_ACTIVITY_EMPTY_STATE_CARD,
} from './sales-activity';
import { COLORS, SALES_ACTIVITY_CHART_CONFIG } from './sales-activity.config';
import { SalesActivityService } from './sales-activity.service';

@Component({
  selector: 'app-dashboard-sales-activity',
  templateUrl: './sales-activity.component.html',
  styleUrls: ['./sales-activity.component.scss', '../dashboard.component.scss'],
  standalone: false,
})
export class SalesActivityComponent implements OnInit, OnDestroy {
  @ViewChild('salesActivity', { static: true }) salesActivityContainer: ElementRef;
  private salesActivityChartContext: CanvasRenderingContext2D;
  private salesActivityChart: Chart;
  private readonly subscriptions: Subscription[] = [];
  filteredSalesActivityChartData$: Observable<SalesActivityChartData>;
  private salesActivityData$: Observable<SalesActivityChartData>;

  private requestFilters$: Observable<DashboardRequestFilterData>;
  private dashboardFilters$: Observable<DashboardFilterData>;
  private topPerformers$: Observable<boolean>;

  public salesActivityChartCard: ChartCard = SALES_ACTIVITY_CARD;
  public salesActivityEmptyState: EmptyStateCard = SALES_ACTIVITY_EMPTY_STATE_CARD;
  public isEmptyState = false;
  private readonly loading$$ = new BehaviorSubject<boolean>(true);
  readonly loading$ = this.loading$$.asObservable();
  readonly accessLeaderboards$: Observable<boolean>;

  constructor(
    private readonly router: Router,
    private readonly translate: TranslateService,
    private readonly salesActivityService: SalesActivityService,
    private readonly filtersService: FiltersService,
    private readonly accessService: SSCAccessService,
  ) {
    this.accessLeaderboards$ = this.accessService.hasAccessToPage(AppPage.LeaderboardPage);
  }

  ngOnInit(): void {
    this.requestFilters$ = this.filtersService.dashboardRequestFilters$;
    this.dashboardFilters$ = this.filtersService.dashboardFilters$;
    this.topPerformers$ = this.filtersService.getTopPerformers$;

    this.drawSalesActivityChart();

    this.salesActivityData$ = this.requestFilters$.pipe(
      switchMap((rfilters) => {
        this.loading$$.next(true);
        return this.salesActivityService.getSalespersonActivityStats(rfilters);
      }),
    );

    this.filteredSalesActivityChartData$ = combineLatest([this.salesActivityData$, this.dashboardFilters$]).pipe(
      map(([d, f]) => {
        this.loading$$.next(true);
        return this.filterData(d, f);
      }),
    );

    this.subscriptions.push(
      combineLatest([this.filteredSalesActivityChartData$, this.topPerformers$])
        .pipe(
          map(([sortedData, topPerformer]) => {
            return this.getPerformers(sortedData, topPerformer);
          }),
        )
        .subscribe((stats) => {
          this.loading$$.next(false);
          this.isEmptyState = this.isEmpty(stats);
          this.updateSalesActivityChartData(stats);
        }),
    );
  }

  drawSalesActivityChart(): void {
    const salesActivityChartConfig: ChartConfiguration = SALES_ACTIVITY_CHART_CONFIG;
    this.salesActivityChartContext = (this.salesActivityContainer.nativeElement as HTMLCanvasElement).getContext('2d');
    this.salesActivityChart = new Chart(this.salesActivityChartContext, {
      type: salesActivityChartConfig.type,
      options: salesActivityChartConfig.options,
      data: salesActivityChartConfig.data,
    });
  }

  filterData(newData: SalesActivityChartData, filters: DashboardFilterData): SalesActivityChartData {
    const salespeopleFilter = filters.salespeopleFilter;
    const salesteamFilter = filters.salesteamFilter;
    const filteredData: SalesActivityChartData = { salespeople: [] };
    let allSalespeopleFiltered: string[] = [];

    if (salespeopleFilter.length > 0 || salesteamFilter.length > 0) {
      allSalespeopleFiltered = combineSalespeopleSalesteamFilters(salespeopleFilter, salesteamFilter);

      allSalespeopleFiltered.forEach((s) => {
        const salesperson = newData.salespeople.find((fs) => s === fs.id);
        if (salesperson) {
          filteredData.salespeople.push(salesperson);
        }
      });
      return this.salesActivityService.sortSalespersonActivityStats(filteredData.salespeople);
    } else {
      return newData;
    }
  }

  updateSalesActivityChartData(newData: SalesActivityChartData): void {
    const data = {
      labels: [],
      datasets: [],
    };
    const snapshotsCreated = [];
    const outboundCalls = [];
    const inboundCalls = [];
    const emailsSent = [];
    const emailsReceived = [];
    const meetings = [];

    newData.salespeople.forEach((s) => {
      data.labels.push(s.name);
      snapshotsCreated.push(s.activity.snapshotsCreated);
      outboundCalls.push(s.activity.outboundCalls);
      inboundCalls.push(s.activity.inboundCalls);
      emailsSent.push(s.activity.emailsSent);
      emailsReceived.push(s.activity.emailsReceived);
      meetings.push(s.activity.meetings);
    });

    data.datasets.push(
      {
        label: this.translate.instant('DASHBOARD.SALES_ACTIVITY.SNAPSHOTS_CREATED'),
        data: snapshotsCreated,
        backgroundColor: COLORS.snapshotsCreated,
        barPercentage: 1,
      },
      {
        label: this.translate.instant('DASHBOARD.SALES_ACTIVITY.OUTBOUND_CALLS'),
        data: outboundCalls,
        backgroundColor: COLORS.outboundCalls,
        barPercentage: 1,
      },
      {
        label: this.translate.instant('DASHBOARD.SALES_ACTIVITY.INBOUND_CALLS'),
        data: inboundCalls,
        backgroundColor: COLORS.inboundCalls,
        barPercentage: 1,
      },
      {
        label: this.translate.instant('DASHBOARD.SALES_ACTIVITY.EMAILS_SENT'),
        data: emailsSent,
        backgroundColor: COLORS.emailsSent,
        barPercentage: 1,
      },
      {
        label: this.translate.instant('DASHBOARD.SALES_ACTIVITY.EMAILS_RECEIVED'),
        data: emailsReceived,
        backgroundColor: COLORS.emailsReceived,
        barPercentage: 1,
      },
      {
        label: this.translate.instant('DASHBOARD.SALES_ACTIVITY.MEETINGS'),
        data: meetings,
        backgroundColor: COLORS.meetings,
        barPercentage: 1,
      },
    );
    this.salesActivityChart.data = data;
    this.salesActivityChart.update();
    this.loading$$.next(false);
  }

  viewFurtherInsights(): void {
    this.router.navigate(['leaderboard/activity']);
  }

  getPerformers(data: SalesActivityChartData, getTopPerformer: boolean): SalesActivityChartData {
    let performers: SalespersonActivity[];
    if (getTopPerformer) {
      performers = data.salespeople.slice(0, DEFAULT_VISIBLE_STATS);
    } else {
      performers = data.salespeople.slice(-DEFAULT_VISIBLE_STATS).reverse();
    }
    return <SalesActivityChartData>{
      salespeople: performers,
    };
  }

  isEmpty(salesActivityData: SalesActivityChartData): boolean {
    if (salesActivityData.salespeople[0]?.activity?.total === 0) {
      return true;
    }
    return false;
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
  }
}
