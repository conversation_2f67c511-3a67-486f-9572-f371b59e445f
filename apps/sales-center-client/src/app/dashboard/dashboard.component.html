<glxy-page>
  <glxy-page-toolbar>
    <glxy-page-title>
      Dashboard
      <!-- TODO Scrummibears i18n -->
    </glxy-page-title>
    <glxy-page-actions>
      <glxy-form-field [bottomSpacing]="'none'">
        <mat-select [value]="selectedPerformers$ | async" (selectionChange)="setTopPerformer($event.value)">
          <mat-option value="top">
            {{ 'DASHBOARD.SELECTOR.TOP_PERFORMERS' | translate }}
          </mat-option>
          <mat-option value="bottom">
            {{ 'DASHBOARD.SELECTOR.BOTTOM_PERFORMERS' | translate }}
          </mat-option>
        </mat-select>
      </glxy-form-field>
    </glxy-page-actions>
  </glxy-page-toolbar>

  <app-st-filters>
    <div class="row row-gutters row-space">
      <div class="col col-xs-12 col-sm-6 col-md-4">
        <app-dashboard-sales-activity></app-dashboard-sales-activity>
      </div>
      <div class="col col-xs-12 col-sm-6 col-md-4">
        <app-dashboard-task-status></app-dashboard-task-status>
      </div>
      <div class="col col-xs-12 col-sm-6 col-md-4">
        <app-dashboard-sales-opportunities></app-dashboard-sales-opportunities>
      </div>
    </div>
  </app-st-filters>

  <div class="row row-gutters row-space">
    <ng-container>
      <div class="col col-xs-12 col-sm-6 col-md-4">
        <app-upcoming-meetings></app-upcoming-meetings>
      </div>
    </ng-container>
    <ng-container *ngIf="showRecentlyUpdated$ | async">
      <div class="col col-xs-12 col-sm-6 col-md-4">
        <app-chart-card [cardText]="recentlyUpdatedCardText">
          <app-recently-updated-accounts-card></app-recently-updated-accounts-card>
        </app-chart-card>
      </div>
      <div class="col col-xs-12 col-sm-6 col-md-4">
        <app-chart-card class="recently-online" [cardText]="recentlyOnlineCardText">
          <app-recently-online></app-recently-online>
        </app-chart-card>
      </div>
    </ng-container>
  </div>
</glxy-page>
