import { MetricResult, QueryMetricsResponse } from '@vendasta/multi-location-analytics';

export function extractMetricsFromResponse(response: QueryMetricsResponse): MetricResult[] {
  if (
    response &&
    response.metricResults &&
    response.metricResults.length > 0 &&
    response.metricResults[0].metrics &&
    response.metricResults[0].metrics.metrics
  ) {
    return response.metricResults[0].metrics.metrics;
  }
  return [];
}
