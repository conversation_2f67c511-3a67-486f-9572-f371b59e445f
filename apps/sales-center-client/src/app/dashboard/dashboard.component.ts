import { Component, Inject, OnInit } from '@angular/core';
import { BarController, BarElement, CategoryScale, Chart, Legend, LinearScale, Title, Tooltip } from 'chart.js';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ACCESS_RECENTLY_UPDATED_CARD_TOKEN } from '../features';
import { ChartCard } from './chart-card/chart-card';
import { PerformerOrder } from './dashboard';
import { FiltersService } from './filters/filters.service';

@Component({
  selector: 'app-st-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
  standalone: false,
})
export class DashboardComponent implements OnInit {
  selectedPerformers$: Observable<PerformerOrder>;
  recentlyUpdatedCardText: ChartCard = {
    header: {
      title: { key: 'RECENTLY_UPDATED_ACCOUNTS.TITLE' },
    },
  };
  recentlyOnlineCardText: ChartCard = {
    header: {
      title: { key: 'RECENTLY_ONLINE_ACCOUNTS.TITLE' },
    },
  };

  constructor(
    private readonly filtersService: FiltersService,
    @Inject(ACCESS_RECENTLY_UPDATED_CARD_TOKEN) readonly showRecentlyUpdated$: Observable<boolean>,
  ) {
    Chart.register(BarController, BarElement, CategoryScale, LinearScale, Legend, Title, Tooltip);
  }

  ngOnInit(): void {
    this.selectedPerformers$ = this.filtersService.getTopPerformers$.pipe(map((p) => (p ? 'top' : 'bottom')));
  }

  setTopPerformer(order: string): void {
    const performerOrder = order === 'top';
    this.filtersService.setTopPerformers(performerOrder);
  }
}
