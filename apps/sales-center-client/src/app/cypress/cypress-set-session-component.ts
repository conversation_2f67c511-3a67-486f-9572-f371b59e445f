import { Component } from '@angular/core';
import { SessionService } from '@galaxy/core';
import { ActivatedRoute } from '@angular/router';
import { tap } from 'rxjs/operators';

@Component({
  selector: 'app-cypress-set-session',
  template: ` <div>🌲Cypress login🌲</div> `,
  standalone: false,
})
export class CypressSetSessionComponent {
  constructor(
    readonly session: SessionService,
    readonly router: ActivatedRoute,
  ) {
    this.router.params
      .pipe(
        tap((params) => {
          const sessionId = params['sessionId'];
          if (sessionId) {
            this.session.setSessionId(sessionId);
            console.log('session set to ' + sessionId);
          }
        }),
      )
      .subscribe();
  }
}
