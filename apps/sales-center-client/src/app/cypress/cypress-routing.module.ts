// Note, this module isn't actually used since lazy loading it will cause it to overtake the default "/" route, breaking local

import { RouterModule, Routes } from '@angular/router';
import { NgModule } from '@angular/core';
import { CypressSetSessionComponent } from './cypress-set-session-component';

const routes: Routes = [
  {
    path: '',
    component: CypressSetSessionComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class CypressRoutingModule {}
