import { Observable } from 'rxjs';

export interface AccessService {
  hasAccessToViews(views: Views[]): Observable<Map<Views, boolean>>;
}

// This Views enum contains all of the views in partner center client that users might request access to
// These should be used to decide if something is viewable and/or accessible to a user or not.
// These are NOT a substitute for proper access checks in the backend
// Intended to be used with hasAccessToViews and ViewAccessApiService
export const enum Views {
  CrmCompanyEdit = 'crm-company-edit',
  CrmContactEdit = 'crm-contact-edit',
}
