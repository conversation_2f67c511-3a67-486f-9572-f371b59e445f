import { CommonModule, DatePipe } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialog, MatDialogConfig, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, Router } from '@angular/router';
import { take } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';

import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { Namespace, NamespaceInterface } from '@vendasta/ai-assistants';

import { PromptModuleEditorComponent } from './prompt-module-editor/prompt-module-editor.component';
import { PromptModule } from './prompt-module';
import { PromptModuleManagementService } from './prompt-module-management.service';
import { NamespaceUtils } from '../shared/utils';
import { NamespaceSelectorComponent } from '../namespace-selector/namespace-selector.component';

enum FilterNamespaceType {
  ALL = 'ALL',
  GLOBAL = 'GLOBAL',
  SYSTEM = 'SYSTEM',
  ACCOUNT_GROUP = 'ACCOUNT_GROUP',
  PARTNER = 'PARTNER',
}

@Component({
  selector: 'app-prompt-management',
  standalone: true,
  imports: [
    CommonModule,
    DatePipe,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatListModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    GalaxyAlertModule,
    GalaxyLoadingSpinnerModule,
    GalaxyPageModule,
    GalaxyFormFieldModule,
    NamespaceSelectorComponent,
  ],
  providers: [PromptModuleManagementService],
  templateUrl: './prompt-module-management.component.html',
  styleUrls: ['./prompt-module-management.component.scss'],
})
export class PromptModuleManagementComponent implements OnInit {
  private readonly promptModuleManagementService = inject(PromptModuleManagementService);
  private readonly dialog = inject(MatDialog);
  private readonly activeRoute = inject(ActivatedRoute);
  private readonly router = inject(Router);

  protected readonly NamespaceUtils = NamespaceUtils;
  protected readonly FilterNamespaceType = FilterNamespaceType;

  prompts: PromptModule[] = [];
  filteredPrompts: PromptModule[] = [];
  isLoading = true;
  DIALOG_WIDTH = '900px';

  namespaceTypeOptions = [
    { value: FilterNamespaceType.ALL, label: 'All' },
    { value: FilterNamespaceType.GLOBAL, label: 'Global' },
    { value: FilterNamespaceType.SYSTEM, label: 'System' },
    { value: FilterNamespaceType.ACCOUNT_GROUP, label: 'Account group' },
    { value: FilterNamespaceType.PARTNER, label: 'Partner' },
  ];

  ngOnInit() {
    const editParam = this.activeRoute.snapshot.queryParamMap.get('edit');
    const namespaceTypeParam = this.activeRoute.snapshot.queryParamMap.get('namespaceType');
    const accountGroupIdParam = this.activeRoute.snapshot.queryParamMap.get('accountGroupId');
    const partnerIdParam = this.activeRoute.snapshot.queryParamMap.get('partnerId');

    if (editParam) {
      let namespace;

      if (namespaceTypeParam === 'Global') {
        namespace = new Namespace({ globalNamespace: {} });
      } else if (namespaceTypeParam === 'System') {
        namespace = new Namespace({ systemNamespace: {} });
      } else if (namespaceTypeParam === 'Account Group' && accountGroupIdParam) {
        namespace = new Namespace({
          accountGroupNamespace: { accountGroupId: accountGroupIdParam },
        });
      } else if (namespaceTypeParam === 'Partner' && partnerIdParam) {
        namespace = new Namespace({
          partnerNamespace: { partnerId: partnerIdParam },
        });
      } else {
        namespace = new Namespace({ globalNamespace: {} }); // Default to global
      }

      this.editPromptModule(editParam, namespace);
    }

    this.refreshPrompts();
  }

  protected async refreshPrompts(namespace?: NamespaceInterface) {
    this.isLoading = true;

    this.prompts = await this.promptModuleManagementService.listPromptModules(namespace);
    this.filteredPrompts = this.prompts;
    this.isLoading = false;
  }

  async editPromptModule(promptId: string = null, namespace: Namespace = null) {
    const conf: MatDialogConfig = {
      width: this.DIALOG_WIDTH,
      data: {
        promptId: promptId,
        namespace: namespace,
      },
    };

    await this.setQueryParam(promptId, namespace);
    const editor = this.dialog.open(PromptModuleEditorComponent, conf);
    editor
      .afterClosed()
      .pipe(take(1))
      .subscribe(async () => {
        await this.setQueryParam(null, null);
        await this.refreshPrompts();
      });
  }

  async setQueryParam(promptId: string, namespace: Namespace) {
    const queryParams: any = {
      edit: null,
      namespaceType: null,
      accountGroupId: null,
      partnerId: null,
    };

    if (promptId && namespace) {
      queryParams.edit = promptId;

      if (namespace.globalNamespace) {
        queryParams.namespaceType = 'Global';
      } else if (namespace.systemNamespace) {
        queryParams.namespaceType = 'System';
      } else if (namespace.accountGroupNamespace) {
        queryParams.namespaceType = 'Account Group';
        queryParams.accountGroupId = namespace.accountGroupNamespace.accountGroupId;
      } else if (namespace.partnerNamespace) {
        queryParams.namespaceType = 'Partner';
        queryParams.partnerId = namespace.partnerNamespace.partnerId;
      }
    }

    await this.router.navigate([], {
      relativeTo: this.activeRoute,
      queryParams,
      queryParamsHandling: 'merge',
    });
  }

  createNewPromptModule() {
    this.editPromptModule();
  }
}
