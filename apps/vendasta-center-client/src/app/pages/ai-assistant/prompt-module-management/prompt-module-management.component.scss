@import 'design-tokens';

mat-card {
  padding: 16px;
}

mat-list-item {
  height: auto !important;
  margin-bottom: 8px;
  margin-top: 8px;
}

.prompt {
  display: flex;
  flex-direction: column;
  text-align: justify;
}

.title {
  font-size: $font-preset-3-size;
  font-weight: bold;
  margin-bottom: $spacing-1;
}

.description {
  margin-bottom: $spacing-1;
}

.instructions-label {
  font-weight: 500;
  margin-top: 8px;
}

.prompt-id {
  margin-bottom: $spacing-1;
}

.text {
  width: 100%;
  white-space: pre-line;
  word-break: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.5;
  margin: 0;
  font-style: italic;
  color: $glxy-grey-700;
}

.text .quote {
  display: inline;
  color: inherit;
}

.last-updated {
  margin: 0;
  display: flex;
  align-items: center;
  margin-top: $spacing-1;
  color: $glxy-grey-500;
  font-style: italic;
}

.filter-container {
  margin: $spacing-3 0;
  padding: $spacing-3;
  background-color: white;
  border: 1px solid $border-color;
  border-radius: $spacing-1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-3;
    align-items: flex-start;
  }

  .namespace-type-filter {
    min-width: 250px;
    max-width: 400px;
    width: 100%;
  }

  .namespace-id-filter {
    min-width: 250px;
    flex: 1;
  }
}

glxy-form-field {
  width: 100%;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  gap: 1rem;
}

.loading-text {
  color: var(--glxy-text-secondary);
  font-size: 1rem;
}

.half-size-icon {
  transform: scale(0.5);
}
